import 'dart:async';

import 'package:flutter/services.dart';

class EspBlufiUtil {
  static const methodChannel = MethodChannel('esp_blufi');

  static const espBlufiEventChannel = EventChannel('esp_blufi_stream');

  static Stream onEsoBlufiResult =
      espBlufiEventChannel.receiveBroadcastStream().asBroadcastStream();
  static StreamSubscription? _espBlufiResultSubscription;

  static Future onEspBlufiResultListener(
      {required Function(String) onSuccess,
      required Function(PlatformException) onError}) async {
    _espBlufiResultSubscription = onEsoBlufiResult.listen((event) {
      onSuccess(event);
    }, onError: (e) {
      onError(e);
    });
    return _espBlufiResultSubscription;
  }

  /// 初始化
  static Future initEspBlufi() async {
    return await methodChannel.invokeMethod("initEspBlufi");
  }

  /// 扫描设备
  static Future scanEspBlufi() async {
    return await methodChannel.invokeMethod("scanEspBlufi");
  }

  /// 停止扫描
  static Future stopEspBlufi() async {
    return await methodChannel.invokeMethod("stopEspBlufi");
  }

  /// 开始连接设备
  static Future connectDevice(String deviceName) async {
    Map<String, dynamic> arguments = {'deviceName': deviceName};
    return await methodChannel.invokeMethod("connectDevice", arguments);
  }

  ///扫描wifi
  static Future scanWifi() async {
    return await methodChannel.invokeMethod("scanWifi");
  }

  ///配置wifi
  static Future configureWifi(String wifiName, String wifiPwd) async {
    Map<String, dynamic> arguments = {'wifiName': wifiName, 'wifiPwd': wifiPwd};
    return await methodChannel.invokeMethod("configureWifi", arguments);
  }

  /// 设备状态
  static Future statusDevice() async {
    return await methodChannel.invokeMethod("statusDevice");
  }

  /// 断开设备连接
  static Future cloneConnect() async {
    return await methodChannel.invokeMethod("cloneConnect");
  }
}
