import 'dart:async';

import 'package:flutter/services.dart';

class XhsUtil {
  static const methodChannel = MethodChannel('xhs_init_config');

  static const exhsEventChannel = EventChannel('xhs_sdk_stream');

  static Stream onXhsResult =
      exhsEventChannel.receiveBroadcastStream().asBroadcastStream();
  static StreamSubscription? _xhsResultSubscription;

  ///SDK初始化状态 true为成功
  static bool _isInstall = false;

  static bool get isInstall => _isInstall;

  static Future onXhsResultListener(
      {required Function(String) onSuccess,
      required Function(PlatformException) onError}) async {
    _xhsResultSubscription = onXhsResult.listen((event) {
      onSuccess(event);
    }, onError: (e) {
      onError(e);
    });
    return _xhsResultSubscription;
  }

  /// 初始化
  static Future initXhs(String appKey) async {
    Map<String, dynamic> arguments = {'appKey': appKey};
    bool flag = await methodChannel.invokeMethod("initXhs", arguments);
    _isInstall = flag;
  }

  ///分享图片
  static Future shareXhs(String title, String content, String image) async {
    Map<String, dynamic> arguments = {
      'title': title,
      "content": content,
      "image": image
    };
    return await methodChannel.invokeMethod("shareXhs", arguments);
  }

  ///是否支持分享笔记到小红书
  static Future isSupportShareNote() async {
    return await methodChannel.invokeMethod("supportShareNote");
  }
}
