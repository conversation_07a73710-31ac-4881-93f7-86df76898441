import 'package:get/get.dart';
import 'package:volc_engine_rtc/volc_engine_rtc.dart';

class ByteRtcUtil extends GetxService {
  static ByteRtcUtil get to => Get.find();

  RTCVideo? _rtcVideo;

  RTCVideo? get rtcVideo => _rtcVideo;

  RTCRoom? _rtcRoom;

  RTCRoom? get rtcRoom => _rtcRoom;

  final RTCVideoEventHandler _videoHandler = RTCVideoEventHandler();

  RTCVideoEventHandler get videoHandler => _videoHandler;

  final RTCRoomEventHandler _roomHandler = RTCRoomEventHandler();

  RTCRoomEventHandler get roomHandler => _roomHandler;

  final RTCASREngineEventHandler _rtcAsrEngineEventHandler =
      RTCASREngineEventHandler();

  RTCASREngineEventHandler get rtcAsrEngineEventHandler =>
      _rtcAsrEngineEventHandler;

  ///创建引擎
  Future<bool> createEngine(String appId, String uId) async {
    _rtcVideo = await RTCVideo.createRTCVideo(
        RTCVideoContext(appId, eventHandler: _videoHandler));
    if (_rtcVideo == null) {
      return false;
    }

    _rtcVideo?.setAudioScene(AudioSceneType.highQualityChatRoom);

    _rtcVideo?.setAnsMode(AnsMode.automatic);

    ///设置音频频谱检测
    AudioPropertiesConfig audioConfig = AudioPropertiesConfig(
      enableVoicePitch: true,
    );

    _rtcVideo?.enableAudioPropertiesReport(audioConfig);

    return true;
  }

  ///创建房间
  createRoom(String roomId) async {
    _rtcRoom = await _rtcVideo?.createRTCRoom(roomId);
    _rtcRoom?.setRTCRoomEventHandler(_roomHandler);
  }

  ///加入房间
  joinRoom(String token, String userId) async {
    UserInfo userInfo = UserInfo(uid: userId);
    RoomConfig roomConfig = RoomConfig(
        isAutoPublish: true,
        isAutoSubscribeAudio: true,
        isAutoSubscribeVideo: false,
        profile: RoomProfile.chatRoom);
    _rtcRoom?.joinRoom(
        token: token, userInfo: userInfo, roomConfig: roomConfig);
  }

  destroyEngine() {
    if (_rtcVideo != null) {
      _rtcVideo?.destroy();
    }
    if (_rtcRoom != null) {
      _rtcRoom?.destroy();
    }
  }
}
