import 'dart:math';

import 'package:decimal/decimal.dart';

class NumUtil {
  static double limitNumber(double number) {
    return number < 50 ? 2.0 : 3.5;
  }

  ///获取指定范围的随机数
  static int randomRange(int s, int e) {
    return s + Random().nextInt(e - s);
  }

  /// 如果小数点后的值 大于0 返回全部  如果小数点后的值 == 0  返回整数
  ///isMoney 默认显示钱 钱的显示(单位：传入的是分，返回的是元)
  static String moneyDoubleOrInt(double money,
      {int position = 4, bool isMoney = true}) {
    String str = '';
    if (isMoney) {
      str = '${money / 100}';
    } else {
      str = '$money';
    }
    List sList = str.split('.');
    if (sList.length == 2 && double.parse(sList[1]) > 0) {
      //默认保留4位 并去除无效0尾数
      return Decimal.parse(formatNum(str, position: position)).toString();
    } else {
      return '${int.parse(sList[0])}';
    }
  }

  ///position是小数点后显示几位
  static String formatNum(dynamic nums, {int position = 2}) {
    double num;
    if (nums is double) {
      num = nums;
    } else {
      num = double.parse(nums.toString());
    }
    if ((num.toString().length - num.toString().lastIndexOf(".") - 1) <
        position) {
      return (num.toStringAsFixed(position)
          .substring(0, num.toString().lastIndexOf(".") + position + 1)
          .toString());
    } else {
      return (num.toString()
          .substring(0, num.toString().lastIndexOf(".") + position + 1)
          .toString());
    }
  }
}
