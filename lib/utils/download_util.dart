import 'dart:isolate';
import 'dart:ui';
import 'package:flutter_downloader/flutter_downloader.dart';
import 'package:getx_xiaopa/network/http_client.dart';

@pragma('vm:entry-point')
class DownloadUtil {
  static final ReceivePort _port = ReceivePort();

  static bool _isCallbackRegistered = false;

  static void initialize() async {
    await FlutterDownloader.initialize(debug: true, ignoreSsl: true);
    if (!_isCallbackRegistered) {
      if (IsolateNameServer.lookupPortByName('downloader_send_port') != null) {
        IsolateNameServer.removePortNameMapping('downloader_send_port');
      }
      IsolateNameServer.registerPortWithName(
          _port.sendPort, 'downloader_send_port');
      _port.listen(_handleDownloadCallback);
      FlutterDownloader.registerCallback(downloadCallback);
      _isCallbackRegistered = true;
    }
  }

  static final Map<String, void Function(String, int, int)> _listeners = {};

  static void addListener(
      String tag, void Function(String id, int status, int progress) callback) {
    _listeners[tag] = callback;
  }

  static void removeListener(String tag) {
    _listeners.remove(tag);
  }

  static void _handleDownloadCallback(dynamic data) {
    String id = data[0];
    int status = data[1];
    int progress = data[2];

    for (final listener in _listeners.values) {
      listener(id, status, progress);
    }
  }

  @pragma('vm:entry-point')
  static void downloadCallback(String id, int status, int progress) {
    final SendPort? send =
        IsolateNameServer.lookupPortByName('downloader_send_port');
    send?.send([id, status, progress]);
  }

  ///开始任务
  static Future<String> startTask(String url, String savedDir,
      {String? fileName}) async {
    String taskId = await FlutterDownloader.enqueue(
          url: fileUrl(url),
          savedDir: savedDir,
          showNotification: false,
          openFileFromNotification: false,
          fileName: fileName,
          requiresStorageNotLow: false,
        ) ??
        '';
    return taskId;
  }

  ///取消任务
  ///isAll:是否取消全部
  static void cancelTask({String? taskId, bool isAll = false}) {
    if (isAll) {
      FlutterDownloader.cancelAll();
    } else {
      FlutterDownloader.cancel(taskId: taskId!);
    }
  }

  ///暂停任务
  static void pauseTask({required String taskId}) {
    FlutterDownloader.pause(taskId: taskId);
  }

  ///恢复任务
  static void resumeTask({required String taskId}) async {
    FlutterDownloader.resume(taskId: taskId);
  }

  ///重试失败任务
  static void retryTask({required String taskId}) async {
    await FlutterDownloader.retry(taskId: taskId);
  }

  ///打开并预览下载的文件
  static void open({required String taskId}) {
    FlutterDownloader.open(taskId: taskId);
  }
}
