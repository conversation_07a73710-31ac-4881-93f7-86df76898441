import 'dart:async';

import 'package:flutter/material.dart';
import 'package:getx_xiaopa/network/http_client.dart';
import 'package:getx_xiaopa/utils/utils.dart';
import 'package:just_audio/just_audio.dart';

class SoundEffectPlayer {
  final double volume;
  final int maxPlayers;
  final List<AudioPlayer> _players = [];
  int _currentIndex = 0;
  StreamSubscription<PlayerState>? _playerStateSub;

  SoundEffectPlayer({this.volume = 1.0, this.maxPlayers = 4}) {
    for (int i = 0; i < maxPlayers; i++) {
      final player = AudioPlayer();
      _players.add(player);
    }
  }

  Future<void> play(String assetPath,
      {bool isCall = false,
      VoidCallback? onStartPlay,
      VoidCallback? onComplete}) async {
    try {
      final player = _players[_currentIndex];
      _currentIndex = (_currentIndex + 1) % maxPlayers;

      await player.stop(); // 停止当前播放器（避免上一轮未完成）
      if (assetPath.startsWith("/")) {
        await player.setFilePath(assetPath);
      } else {
        await player.setAsset(assetPath);
      }
      await player.setVolume(volume);
      if (isCall) {
        await _playerStateSub?.cancel();
        _playerStateSub = player.playerStateStream.listen((state) {
          if (state.processingState == ProcessingState.ready && state.playing) {
            onStartPlay?.call();
          }
          if (state.processingState == ProcessingState.completed) {
            onComplete?.call();
            _playerStateSub?.cancel();
          }
        });
      }
      await player.play();
    } catch (e) {
      logD('播放提示音错误: $e');
    }
  }

  Future<void> playNetAudio(String url,
      {VoidCallback? onStartPlay, VoidCallback? onComplete}) async {
    try {
      final player = _players[_currentIndex];
      _currentIndex = (_currentIndex + 1) % maxPlayers;

      await player.stop(); // 停止当前播放器（避免上一轮未完成）
      await player.setUrl(fileUrl(url));
      await player.setVolume(volume);

      await _playerStateSub?.cancel();
      _playerStateSub = player.playerStateStream.listen((state) {
        if (state.processingState == ProcessingState.ready && state.playing) {
          onStartPlay?.call();
        }
        if (state.processingState == ProcessingState.completed) {
          onComplete?.call();
          _playerStateSub?.cancel();
        }
      });
      await player.play();
    } catch (e) {
      logD('播放提示音错误: $e');
    }
  }

  stop() async {
    final player = _players[_currentIndex];
    await player.stop();
  }

  Future<void> dispose() async {
    for (final player in _players) {
      try {
        await player.stop();
        await player.dispose();
      } catch (_) {}
    }
    _players.clear();
  }
}
