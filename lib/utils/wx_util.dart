import 'dart:async';

import 'package:flutter/services.dart';

class WxUtil {
  static const methodChannel = MethodChannel('wx_init_config');

  static const wxEventChannel = EventChannel('wx_sdk_stream');

  static Stream onWxResult =
      wxEventChannel.receiveBroadcastStream().asBroadcastStream();
  static StreamSubscription? _wxResultSubscription;

  static Future onWxResultListener(
      {required Function(String) onSuccess,
      required Function(PlatformException) onError}) async {
    _wxResultSubscription = onWxResult.listen((event) {
      onSuccess(event);
    }, onError: (e) {
      onError(e);
    });
    return _wxResultSubscription;
  }

  ///SDK初始化状态 true为成功
  static bool _isInstall = false;

  static bool get isInstall => _isInstall;

  /// 初始化
  static Future initWx(String appKey, String universalLink) async {
    Map<String, dynamic> arguments = {
      'appKey': appKey,
      "universalLink": universalLink
    };
    bool flag = await methodChannel.invokeMethod("initWx", arguments);
    _isInstall = flag;
  }

  ///是否安装微信
  static Future isInstalledWx() async {
    return await methodChannel.invokeMethod("isInstalledWx");
  }

  ///分享会话
  static Future shareSession(String imagePath) async {
    Map<String, dynamic> arguments = {'imagePath': imagePath};
    await methodChannel.invokeMethod("shareSession", arguments);
  }

  ///分享朋友圈
  static Future shareTimeLine(String imagePath) async {
    Map<String, dynamic> arguments = {'imagePath': imagePath};
    await methodChannel.invokeMethod("shareTimeLine", arguments);
  }

  ///拉起客服
  static Future<bool> launchCustomerService(String corpId, String url) async {
    var map = {
      "corpId": corpId,
      "url": url,
    };
    bool flag = await methodChannel.invokeMethod("launchCustomerService", map);
    return flag;
  }

  ///微信支付
  static launchWxPay(
      String appId,
      String partnerId,
      String prepayId,
      String sign,
      String packageValue,
      String nonceStr,
      String timeStamp) async {
    var map = {
      "appId": appId,
      "partnerId": partnerId,
      "prepayId": prepayId,
      "sign": sign,
      "packageValue": packageValue,
      "nonceStr": nonceStr,
      "timeStamp": timeStamp
    };
    await methodChannel.invokeMethod("launchWxPay", map);
  }
}
