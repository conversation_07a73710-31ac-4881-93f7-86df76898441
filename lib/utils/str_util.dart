import 'dart:convert';

import 'package:flutter/services.dart';
import 'package:getx_xiaopa/utils/utils.dart';

class StrUtil {
  ///position是小数点后显示几位
  static String formatNum(dynamic nums, {int position = 2}) {
    double num;
    if (nums is double) {
      num = nums;
    } else {
      num = double.parse(nums.toString());
    }
    if ((num.toString().length - num.toString().lastIndexOf(".") - 1) <
        position) {
      return (num.toStringAsFixed(position)
          .substring(0, num.toString().lastIndexOf(".") + position + 1)
          .toString());
    } else {
      return (num.toString()
          .substring(0, num.toString().lastIndexOf(".") + position + 1)
          .toString());
    }
  }

  ///取出中间文本
  static String extractMiddleText(String str, String leftStr, String rightStr) {
    String temp = '';
    if (str.isEmpty || leftStr.isEmpty || rightStr.isEmpty) {
      return temp;
    }

    int sLeft = str.indexOf(leftStr);
    if (sLeft == -1) {
      return temp;
    }
    int leftPosition = sLeft + leftStr.length;

    int sRight = str.indexOf(rightStr, leftPosition);
    if (sRight <= sLeft || sRight == -1) {
      return temp;
    }

    temp = str.substring(leftPosition, sRight);

    // logD("左边开始位置：$sLeft == $leftPosition   右边开始位置：$sRight  取出的文本：$temp");

    return temp;
  }

  /// 防止文字自动换行
  /// 当中英文混合，或者中文与数字或者特殊符号，或则英文单词时，文本会被自动换行，
  /// 这样会导致，换行时上一行可能会留很大的空白区域
  /// 把每个字符插入一个0宽的字符， \u{200B}
  static String breakWord(String text) {
    if (text.isEmpty) {
      return text;
    }
    String breakWord = ' ';
    for (var element in text.runes) {
      breakWord += String.fromCharCode(element);
      breakWord += '\u200B'; //'\u200B'不可见空格符
    }
    return breakWord;
  }

  static String? unpack(Uint8List message) {
    const int kSubtitleHeaderSize = 8;

    if (message.length < kSubtitleHeaderSize) {
      return null;
    }
    // 找到第一个非0的位置（也可以自定义规则）
    int startIndex = message.indexWhere((b) => b != 0);
    int endCount = _countTrailingZeros(message);

    int magic = (message[startIndex + 0] << 24) |
        (message[startIndex + 1] << 16) |
        (message[startIndex + 2] << 8) |
        (message[startIndex + 3]);
    // logD("0x${magic.toRadixString(16)}");

    if (magic != int.parse('0x73756276') && magic != int.parse('0x636f6e76')) {
      return null;
    }

    int length = (message[startIndex + 4] << 24) |
        (message[startIndex + 5] << 16) |
        (message[startIndex + 6] << 8) |
        (message[startIndex + 7]);

    // logD(
    //     "长度：$length == ${message.length - startIndex - kSubtitleHeaderSize} == ${message.length} == $startIndex");

    if (message.length - kSubtitleHeaderSize - startIndex - endCount !=
        length) {
      return null;
    }

    if (length > 0) {
      final payload = message.sublist(startIndex + kSubtitleHeaderSize,
          startIndex + kSubtitleHeaderSize + length);
      return utf8.decode(payload);
    } else {
      return '';
    }
  }

  static int _countTrailingZeros(List<int> data) {
    int count = 0;
    for (int i = data.length - 1; i >= 0; i--) {
      if (data[i] == 0) {
        count++;
      } else {
        break;
      }
    }
    return count;
  }

  static String? extractJsonFromUint8List(Uint8List data) {
    const int jsonStartOffset = 12;

    if (data.length <= jsonStartOffset) return null;

    final start = data.indexOf(123, jsonStartOffset); // '{'
    final end = data.lastIndexOf(125); // '}'

    if (start != -1 && end != -1 && end > start) {
      try {
        return utf8.decode(data.sublist(start, end + 1));
      } catch (e) {
        logD('解析失败: $e');
      }
    }
    return null;
  }

  /// 比较两个字符串，返回不同的部分
  static String compareStrings(String oldStr, String newStr) {
    if (oldStr.isEmpty) return newStr;

    int minLength =
        oldStr.length < newStr.length ? oldStr.length : newStr.length;
    int diffIndex = 0;

    // 找到第一个不同的字符位置
    for (int i = 0; i < minLength; i++) {
      if (oldStr[i] != newStr[i]) {
        diffIndex = i;
        break;
      }
      diffIndex = i + 1;
    }

    // 返回新字符串中不同的部分
    return newStr.substring(diffIndex);
  }

  static String maskPhone(String phone) {
    if (phone.isEmpty) return '';
    try {
      String maskedNational = '';
      if (phone.length >= 7) {
        maskedNational = phone.replaceRange(3, 7, '****');
      } else {
        final len = phone.length;
        final start = (len * 0.3).floor();
        final end = len - (len * 0.3).floor();

        if (end <= start) {
          maskedNational = phone;
        } else {
          maskedNational = phone.substring(0, start) +
              '*' * (end - start) +
              phone.substring(end);
        }
      }

      return maskedNational;
    } catch (e) {
      return phone;
    }
  }
}
