import 'package:get/get.dart';
import 'package:open_file/open_file.dart';
import 'package:file_picker/file_picker.dart';

import '../extension/widget_extension.dart';

class FileUtil {
  /// 打开文件
  static Future<void> openFile(String filePath) async {
    try {
      final result = await OpenFile.open(filePath);
      if (result.type != ResultType.done) {
        CToast.showToast("file_util_error_one".tr);
      }
    } catch (e) {
      CToast.showToast("file_util_error_two".tr);
    }
  }

  /// 选择文件
  static Future<FilePickerResult?> pickFile({
    List<String>? allowedExtensions,
    FileType type = FileType.any,
  }) async {
    try {
      return await FilePicker.platform.pickFiles(
        type: type,
        allowedExtensions: allowedExtensions,
      );
    } catch (e) {
      CToast.showToast("file_util_error_three".tr);
      return null;
    }
  }

  /// 选择多个文件
  static Future<FilePickerResult?> pickFiles({
    List<String>? allowedExtensions,
    FileType type = FileType.any,
  }) async {
    try {
      return await FilePicker.platform.pickFiles(
        type: type,
        allowMultiple: true,
        allowedExtensions: allowedExtensions,
      );
    } catch (e) {
      CToast.showToast("file_util_error_three".tr);
      return null;
    }
  }
}
