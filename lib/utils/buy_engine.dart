import 'dart:async';
import 'dart:io';

import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:getx_xiaopa/extension/widget_extension.dart';
import 'package:getx_xiaopa/utils/utils.dart';
import 'package:in_app_purchase/in_app_purchase.dart';
// ignore: depend_on_referenced_packages
import 'package:in_app_purchase_storekit/in_app_purchase_storekit.dart';
// ignore: depend_on_referenced_packages
import 'package:in_app_purchase_android/in_app_purchase_android.dart';

class ReceiptHelper {
  static const payEventChannel = MethodChannel('get_receipt');

  ///获取receipt
  static Future<String> getReceipt(bool forceRetry) async {
    var map = {"forceRetry": forceRetry};
    return await payEventChannel.invokeMethod("getLatestReceipt", map);
  }
}

class BuyEngine {
  // 1️⃣ 静态实例（懒加载）
  static final BuyEngine _instance = BuyEngine._internal();

  // 2️⃣ 私有构造函数
  BuyEngine._internal() {
    _initializeInAppPurchase();
  }

  // 3️⃣ 提供一个工厂构造函数返回同一个实例
  factory BuyEngine() {
    return _instance;
  }

  late StreamSubscription<List<PurchaseDetails>> _subscription;
  late InAppPurchase _inAppPurchase;

  ///内购的商品对象集合
  late List<ProductDetails> _products;

  void _initializeInAppPurchase() {
    ///初始化in_app_purchase插件
    _inAppPurchase = InAppPurchase.instance;

    ///监听购买事件
    final Stream<List<PurchaseDetails>> purchaseUpdated =
        _inAppPurchase.purchaseStream;
    _subscription = purchaseUpdated.listen((purchaseDetailsList) {
      _listenToPurchaseUpdated(purchaseDetailsList);
    }, onDone: () {
      onClose();
    }, onError: (error) {
      logD("购买失败：$error");
    });

    logD("初始化内购成功");
  }

  void resumePurchase() {
    _inAppPurchase.restorePurchases();
  }

  ///加载全部商品
  Future<String> buyProduct(String productId) async {
    if (productId.isEmpty) return "recharge_pay_error_one".tr;

    logD('商品id：$productId');

    List<String> outProducts = [productId];

    final bool available = await _inAppPurchase.isAvailable();
    if (!available) {
      return "buy_engine_error_one".tr;
    }

    logD("连接成功-开始查询全部商品");

    List<String> kIds = outProducts;

    final ProductDetailsResponse response =
        await _inAppPurchase.queryProductDetails(kIds.toSet());
    logD("商品获取结果：${response.productDetails.length}");

    if (response.notFoundIDs.isNotEmpty) {
      return "buy_engine_error_two".tr;
    }

    ///处理查询到的商品列表
    List<ProductDetails> products = response.productDetails;
    if (products.isNotEmpty) {
      _products = products;
    }

    logD("全部商品加载完成，可以启动购买了，总共商品数量为：${products.length}");

    return startPurchase(productId);
  }

  ///调用此函数启动购买过程
  Future<String> startPurchase(String productId) async {
    logD("购买的商品id为：$productId");
    if (_products.isNotEmpty) {
      try {
        ProductDetails productDetails = _getProduct(productId);

        logD(
            "一切正常，开始购买，信息如下：id：${productDetails.id} title: ${productDetails.title}  desc:${productDetails.description} "
            "price:${productDetails.price}  currencyCode:${productDetails.currencyCode}  currencySymbol:${productDetails.currencySymbol}");

        await _inAppPurchase.buyConsumable(
            purchaseParam: PurchaseParam(productDetails: productDetails));
      } on PlatformException catch (e) {
        CToast.dismiss();
        if (Platform.isIOS && e.code == 'storekit2_purchase_cancelled') {
          logD("用户取消购买（已拦截异常）");
          return ""; // 取消时不提示错误
        } else {
          logD("购买失败: ${e.code} ${e.message}");
          return "buy_engine_error_three".trArgs(['${e.message}']);
        }
      } catch (e) {
        return "buy_engine_error_three".trArgs(['${e.printError}']);
      }
    } else {
      return "buy_engine_error_four".tr;
    }
    return "";
  }

  // 根据产品ID获取产品信息
  ProductDetails _getProduct(String productId) {
    return _products.firstWhere((product) => product.id == productId);
  }

  /// 内购的购买更新监听
  void _listenToPurchaseUpdated(
      List<PurchaseDetails> purchaseDetailsList) async {
    for (PurchaseDetails purchase in purchaseDetailsList) {
      if (purchase.status == PurchaseStatus.pending) {
        // 等待支付完成
        _handlePending();
      } else if (purchase.status == PurchaseStatus.canceled) {
        // 取消支付
        _handleCancel(purchase);
      } else if (purchase.status == PurchaseStatus.error) {
        // 购买失败
        _handleError(purchase.error!);
      } else if (purchase.status == PurchaseStatus.purchased ||
          purchase.status == PurchaseStatus.restored) {
        //完成购买, 到服务器验证
        if (Platform.isAndroid) {
          var googleDetail = purchase as GooglePlayPurchaseDetails;
          checkAndroidPayInfo(googleDetail);
        } else if (Platform.isIOS) {
          var appstoreDetail = purchase as SK2PurchaseDetails;
          bus.emit("iosInAppPurchase", appstoreDetail);
        }
      }
    }
  }

  /// 购买失败
  void _handleError(IAPError iapError) {
    logD("Purchase_Failed：${iapError.code} message${iapError.message}");
  }

  /// 等待支付
  void _handlePending() {
    logD("等待支付");
  }

  /// 取消支付
  void _handleCancel(PurchaseDetails purchase) {
    _inAppPurchase.completePurchase(purchase);
  }

  /// Android支付成功的校验
  void checkAndroidPayInfo(GooglePlayPurchaseDetails googleDetail) async {
    _inAppPurchase.completePurchase(googleDetail);
    logD("安卓支付交易ID为${googleDetail.purchaseID}");
    logD("安卓支付验证收据为${googleDetail.verificationData.serverVerificationData}");
  }

  /// Apple支付成功的校验
  void checkApplePayInfo(SK2PurchaseDetails appstoreDetail) async {
    await _inAppPurchase.completePurchase(appstoreDetail);

    // bus.emit("iosInAppPurchase", appstoreDetail);

    // logD("Apple支付交易ID为${appstoreDetail.purchaseID}");
    // logD(
    //     "Apple支付验证收据为${appstoreDetail.verificationData.serverVerificationData}");
  }

  void onClose() {
    if (Platform.isIOS) {
      final InAppPurchaseStoreKitPlatformAddition iosPlatformAddition =
          _inAppPurchase
              .getPlatformAddition<InAppPurchaseStoreKitPlatformAddition>();
      iosPlatformAddition.setDelegate(null);
    }
    _subscription.cancel();
  }
}
