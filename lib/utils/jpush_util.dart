// import 'package:flutter/foundation.dart';
// import 'package:get/get.dart';
// import 'package:getx_xiaopa/storage/storage.dart';
// import 'package:getx_xiaopa/utils/utils.dart';
// import 'package:jpush_flutter/jpush_flutter.dart';
// import 'package:jpush_flutter/jpush_interface.dart';

// class JPushUtil {
//   static final JPushUtil _instance = JPushUtil._internal();
//   factory JPushUtil() => _instance;
//   JPushUtil._internal();

//   final JPushFlutterInterface _jPush = JPush.newJPush();

//   ///初始化推送
//   initialize() async {
//     _jPush.setup(
//       appKey: JPUSHConfig.appKey,
//       channel: 'theChannel',
//       production: kReleaseMode,
//       debug: kDebugMode,
//     );

//     _jPush.addEventHandler(
//       onReceiveNotification: (event) => _onReceiveNotification(event),
//       onOpenNotification: (event) => _onOpenNotification(event),
//       onReceiveMessage: (event) => _onReceiveMessage(event),
//     );

//     // 获取注册 ID，可绑定到用户信息
//     _jPush.getRegistrationID().then((rid) {
//       // logD("JPush RegistrationID：$rid");

//       UserStore.to.setJpushRegistrationId(rid);

//       /// 上传 rid 到你的服务器（绑定 userId）
//     });

//     // 检查冷启动是否来自推送
//     _jPush.getLaunchAppNotification().then((notification) {
//       if (notification.isNotEmpty) {
//         _handlePushNavigation(notification);
//       }
//     });
//   }

//   /// 接收到通知
//   Future<void> _onReceiveNotification(Map<String, dynamic> message) async {
//     logD("收到通知：$message");
//   }

//   /// 点击通知时
//   Future<void> _onOpenNotification(Map<String, dynamic> message) async {
//     logD("点击通知：$message");
//     _handlePushNavigation(message);
//   }

//   /// 接收到自定义消息（不显示通知）
//   Future<void> _onReceiveMessage(Map<String, dynamic> message) async {
//     logD("自定义消息：$message");
//     // 你也可以在这里展示 flutter_local_notifications
//     // _showNotification(message['message'] ?? '新通知');
//   }

//   // void _showNotification(String content) {
//   //   final fireDate = DateTime.now().add(const Duration(seconds: 1));

//   //   final localNotification = LocalNotification(
//   //     id: DateTime.now().millisecondsSinceEpoch ~/ 1000, // 保证唯一id
//   //     title: "",
//   //     buildId: 1,
//   //     content: content,
//   //     fireTime: fireDate,
//   //     subtitle: "",
//   //     badge: 1,
//   //     extra: {"key": "value"}, // 自定义参数
//   //   );

//   //   _jPush.sendLocalNotification(localNotification).then((res) {
//   //     logD("显示本地通知结果: $res");
//   //   });
//   // }

//   /// 跳转逻辑统一处理
//   void _handlePushNavigation(Map<dynamic, dynamic> message) {
//     final extras = message['extras'] ?? {};
//     final activityId = extras['activity_id']?.toString();

//     if (activityId != null && activityId.isNotEmpty) {
//       // 判断当前是否已经是目标页面
//       final currentRoute = Get.currentRoute;
//       final targetRoute = '/activity/$activityId';
//       if (currentRoute != targetRoute) {
//         Get.toNamed(targetRoute);
//       }
//     }
//   }
// }
