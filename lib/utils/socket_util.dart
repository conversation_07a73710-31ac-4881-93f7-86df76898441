import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:getx_xiaopa/network/http_client.dart';
import 'package:getx_xiaopa/storage/storage.dart';
import 'package:getx_xiaopa/utils/utils.dart';
import 'package:web_socket_channel/web_socket_channel.dart';

class SocketUtil {
  static SocketUtil? _instance;

  WebSocketChannel? _channel;

  final String _serverUrl; //ws连接路径

  bool _isConnected = false; //连接状态

  ///连接状态
  bool _isManuallyDisconnected = false;

  ///是否为主动断开
  Timer? _heartbeatTimer; //心跳定时器
  Timer? _reconnectTimer; //重新连接定时器
  final Duration _reconnectInterval = const Duration(seconds: 10);

  String? fallbackIp = isProduction
      ? 'ws://*************:8825/client/bot/device/appsocket'
      : 'ws://*************:8821/client/bot/device/appsocket'; // 备用 IP（可选）

  static SocketUtil? getInstance({String? serverUrl}) {
    _instance ??= SocketUtil(serverUrl ?? '');
    return _instance;
  }

  SocketUtil(this._serverUrl) {
    _startConnection();
  }

  void _startConnection() async {
    // Uri uri = Uri.parse('$fallbackIp?$_serverUrl');
    // if (fallbackIp != null) {
    //   // 换 IP 连接
    //   logD("ip socket地址：$uri");
    //   try {
    //     _channel = WebSocketChannel.connect(uri);
    //     _channel?.stream.listen(
    //       (data) {
    //         if (!_isConnected) {
    //           _isConnected = true;
    //           logD("ip socket连接成功：$_serverUrl");
    //         }

    //         _onMessageReceived(data); // 把消息转发出去
    //       },
    //       onError: _onError,
    //       onDone: _onDone,
    //     );
    //   } catch (e) {
    //     _onError(e);
    //   }
    // }
    Uri uri = Uri.parse('$socketUrl?$_serverUrl');
    try {
      if (_serverUrl.isEmpty) return;
      // 先尝试域名解析
      final result = await InternetAddress.lookup(uri.host);
      logD("DNS解析成功：$result");
      if (result.isEmpty) throw Exception("DNS解析失败");

      _channel = WebSocketChannel.connect(uri);
      _channel?.stream.listen(
        (data) {
          if (!_isConnected) {
            _isConnected = true;
            logD("socket连接成功：$uri");
          }

          _onMessageReceived(data); // 把消息转发出去
        },
        onError: _onError,
        onDone: _onDone,
      );

      // 连接成功后发送登录信息();
      // _sendInitialData();
    } catch (e) {
      if (fallbackIp != null) {
        // 换 IP 连接
        final ipUri = Uri.parse('$fallbackIp?$_serverUrl');
        try {
          _channel = WebSocketChannel.connect(ipUri);
          _channel?.stream.listen(
            (data) {
              if (!_isConnected) {
                _isConnected = true;
                logD("ip socket连接成功：$ipUri");
              }

              _onMessageReceived(data); // 把消息转发出去
            },
            onError: _onError,
            onDone: _onDone,
          );
        } catch (e) {
          _onError(e);
        }
      }
    }
  }

  //发送信息
  void sendMessage(dynamic message) {
    // logD("socket请求参数：$message");
    // final jsonString = jsonEncode(message); // 将消息对象转换为 JSON 字符串
    if (_channel == null) return;
    _channel?.sink.add(message); // 发送 JSON 字符串
  }

  // 处理接收到的消息
  void _onMessageReceived(dynamic message) {
    logD('处理接收到的消息Received==========================: $message');

    bus.emit("socket_received", jsonDecode(message));
  }

  _onError(dynamic error) {
    // 处理错误
    logD('异常错误: $error');
    _isConnected = false;
    if (!_isManuallyDisconnected) {
      // 如果不是主动断开连接，则尝试重连
      _reconnect();
    }
  }

  //关闭
  void _onDone() {
    logD('WebSocket 连接已关闭');
    _isConnected = false;
    if (!_isManuallyDisconnected) {
      // 如果不是主动断开连接，则尝试重连
      _reconnect();
    }
  }

  // 重连
  void _reconnect() {
    // 避免频繁重连，启动重连定时器
    _reconnectTimer = Timer(_reconnectInterval, () {
      _isConnected = false;
      _reconnectTimer?.cancel();

      if (_channel != null) {
        _channel?.sink.close(); // 关闭之前的连接
      }

      ///主动断开直接return
      if (_isManuallyDisconnected || UserStore.to.authorization == "") return;
      logD('重连====================$socketUrl?$_serverUrl');
      _startConnection();
    });
  }

  //断开连接
  Future<void> disconnect() async {
    logD('socket断开连接');
    _isConnected = false;
    _isManuallyDisconnected = true;
    _instance = null;

    /// _reconnectTimer?.cancel();
    if (_channel != null) {
      await _channel?.sink.close();
    }
  }

  //开始心跳
  void _startHeartbeat() {
    _heartbeatTimer = Timer.periodic(const Duration(seconds: 20), (_) {
      sendHeartbeat();
    });
  }

  //停止心跳
  void _stopHeartbeat() {
    _heartbeatTimer?.cancel();
  }

  //重置心跳
  // ignore: unused_element
  void _resetHeartbeat() {
    _stopHeartbeat();
    _startHeartbeat(); //开始心跳
  }

  // 发送心跳消息到服务器
  void sendHeartbeat() {
    if (_isConnected) {
      final message = {"cmd": 1, "data": {}};
      final jsonString = jsonEncode(message); // 将消息对象转换为 JSON 字符串
      _channel?.sink.add(jsonString); // 发送心跳
      logD('连接成功发送心跳消息到服务器$message');
    }
  }
}
