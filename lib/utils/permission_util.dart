import 'dart:io';
import 'dart:ui';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:getx_xiaopa/extension/widget_extension.dart';
import 'package:getx_xiaopa/values/values.dart';
import 'package:getx_xiaopa/widget/widget.dart';
import 'package:permission_handler/permission_handler.dart';

class PermissionUtil {
  ///权限检测：麦克风
  Future<bool> isMicroPhone() async {
    PermissionStatus status = await Permission.microphone.status;
    return status.isGranted;
  }

  ///权限申请:麦克风
  static microPhone(BuildContext context, {required Function action}) async {
    PermissionStatus status = await Permission.microphone.status;
    if (!status.isGranted) {
      if (Platform.isAndroid) {
        _showNotification(
            title: "permission_util_micro_title".tr,
            message: "permission_util_micro_content".tr);
      }
    }
    if (status.isGranted) {
      await action();
    } else {
      status = await Permission.microphone.request();
      if (status.isDenied || status.isPermanentlyDenied) {
        _openSetting(
            title: "permission_util_micro_title_one".tr,
            content: "permission_util_micro_content".tr);
      } else if (status.isGranted) {
        _overlayEntry?.remove();
        await action();
      }
    }
  }

  ///权限申请:蓝牙
  static bluetooth(BuildContext context, {required Function action}) async {
    PermissionStatus status = await Permission.bluetooth.status;

    if (!status.isGranted) {
      if (Platform.isAndroid) {
        _showNotification(
            title: "permission_util_bluetooth_title".tr,
            message: "permission_util_bluetooth_content".tr);
      }
    }
    if (status.isGranted) {
      await action();
    } else {
      status = await Permission.bluetooth.request();
      if (status.isDenied || status.isPermanentlyDenied) {
        _openSetting(
            title: "permission_util_bluetooth_title_one".tr,
            content: "permission_util_bluetooth_content".tr);
      } else if (status.isGranted) {
        _overlayEntry?.remove();
        await action();
      }
    }
  }

  ///权限申请:蓝牙 android 12+及以上版本
  static bluetoothScan(BuildContext context, {required Function action}) async {
    PermissionStatus status = await Permission.bluetoothScan.status;
    if (!status.isGranted) {
      if (Platform.isAndroid) {
        _showNotification(
            title: "permission_util_bluetooth_title".tr,
            message: "permission_util_bluetooth_content".tr);
      }
    }
    if (status.isGranted) {
      await action();
    } else {
      status = await Permission.bluetoothScan.request();
      if (status.isDenied || status.isPermanentlyDenied) {
        _openSetting(
            title: "permission_util_bluetooth_title_one".tr,
            content: "permission_util_bluetooth_content".tr);
      } else if (status.isGranted) {
        _overlayEntry?.remove();
        await action();
      }
    }
  }

  ///权限申请:蓝牙 android 12+及以上版本
  static bluetoothConnect(BuildContext context,
      {required Function action}) async {
    PermissionStatus status = await Permission.bluetoothConnect.status;
    if (!status.isGranted) {
      if (Platform.isAndroid) {
        _showNotification(
            title: "permission_util_bluetooth_title".tr,
            message: "permission_util_bluetooth_content".tr);
      }
    }
    if (status.isGranted) {
      await action();
    } else {
      status = await Permission.bluetoothConnect.request();
      if (status.isDenied || status.isPermanentlyDenied) {
        _openSetting(
            title: "permission_util_bluetooth_title_one".tr,
            content: "permission_util_bluetooth_content".tr);
      } else if (status.isGranted) {
        _overlayEntry?.remove();
        await action();
      }
    }
  }

  ///权限申请:定位
  static location(BuildContext context, {required Function action}) async {
    PermissionStatus status = await Permission.location.status;

    if (!status.isGranted) {
      if (Platform.isAndroid) {
        _showNotification(
            title: "permission_util_location_title".tr,
            message: "permission_util_location_content".tr);
      }
    }

    if (status.isGranted) {
      await action();
    } else {
      status = await Permission.location.request();
      if (status.isDenied || status.isPermanentlyDenied) {
        _openSetting(
            title: "permission_util_location_title_one".tr,
            content: "permission_util_location_content".tr);
      }
      if (status.isGranted) {
        _overlayEntry?.remove();
        await action();
      }
    }
  }

  ///媒体库权限（Android 10 及以上版本中）
  static media(BuildContext context, {required Function action}) async {
    if (Platform.isAndroid) {
      DeviceInfoPlugin deviceInfo = DeviceInfoPlugin();
      AndroidDeviceInfo info = await deviceInfo.androidInfo;
      String androidVersion = info.version.release;

      var permissions = [];
      // Android 13 及以上版本需要请求新的媒体权限
      if (int.parse(androidVersion) >= 13) {
        permissions = [
          Permission.photos,
          // Permission.videos,
          // Permission.audio,
          Permission.accessMediaLocation,
        ];
      } else {
        permissions = [
          Permission.accessMediaLocation,
          Permission.mediaLibrary,
          Permission.storage,
        ];
      }

      ///获取当前状态
      Map<Permission, PermissionStatus> status = {};
      for (Permission permission in permissions) {
        status[permission] = await permission.status;
      }

      ///找出未授权的权限
      List<Permission> toRequest = status.entries
          .where((e) => e.value != PermissionStatus.granted)
          .map((m) => m.key)
          .toList();

      if (toRequest.isNotEmpty) {
        _showNotification(
            title: "permission_util_media_title".tr,
            message: "permission_util_media_content".tr);
        Map<Permission, PermissionStatus> requestResults =
            await toRequest.request();
        status.addAll(requestResults);

        bool allGranted = true;
        status.forEach((permission, status) {
          if (!status.isGranted) {
            allGranted = false;
          }
        });

        if (!allGranted) {
          _openSetting(
              title: "permission_util_media_title_one".tr,
              content: "permission_util_media_content".tr);
        } else {
          _overlayEntry?.remove();
          await action();
        }
      } else {
        await action();
      }
    } else {
      // iOS 平台
      PermissionStatus status = await Permission.photos.status;
      if (!status.isGranted) {
        if (Platform.isAndroid) {
          _showNotification(
              title: "permission_util_media_title".tr,
              message: "permission_util_media_content".tr);
        }
      }

      if (status.isGranted) {
        await action();
      } else {
        status = await Permission.photos.request();
        if (status.isDenied || status.isPermanentlyDenied) {
          _openSetting(
              title: "permission_util_media_title_one".tr,
              content: "permission_util_media_content".tr);
        }
        if (status.isGranted) {
          _overlayEntry?.remove();
          await action();
        }
      }
    }
  }

  ///存储的权限（Android 10 及以上版本）
  static externalStorage(BuildContext context,
      {required Function action}) async {
    PermissionStatus status = await Permission.manageExternalStorage.status;
    if (!status.isGranted) {
      if (Platform.isAndroid) {
        _showNotification(
            title: "permission_util_media_title".tr,
            message: "permission_util_media_content".tr);
      }
    }

    if (status.isGranted) {
      await action();
    } else {
      status = await Permission.manageExternalStorage.request();
      if (status.isDenied || status.isPermanentlyDenied) {
        _openSetting(
            title: "permission_util_media_title_one".tr,
            content: "permission_util_media_content".tr);
      }
      if (status.isGranted) {
        _overlayEntry?.remove();
        await action();
      }
    }
  }

  ///存储权限
  static storage(BuildContext context, {required Function action}) async {
    PermissionStatus status = await Permission.storage.status;
    if (Platform.isAndroid) {
      DeviceInfoPlugin deviceInfo = DeviceInfoPlugin();
      AndroidDeviceInfo info = await deviceInfo.androidInfo;
      String androidVersion = info.version.release;

      if (!status.isGranted && int.parse(androidVersion) < 13) {
        if (Platform.isAndroid) {
          _showNotification(
              title: "permission_util_media_title".tr,
              message: "permission_util_media_content".tr);
        }
      }
      if (status.isGranted) {
        await action();
      } else {
        status = await Permission.storage.request();
        if (status.isDenied || status.isPermanentlyDenied) {
          if (int.parse(androidVersion) >= 13) {
            await action();
          } else {
            _openSetting(
                title: "permission_util_media_title_one".tr,
                content: "permission_util_media_content".tr);
          }
        } else if (status.isGranted) {
          _overlayEntry?.remove();
          await action();
        }
      }
    } else {
      if (!status.isGranted) {
        if (Platform.isAndroid) {
          _showNotification(
              title: "permission_util_media_title".tr,
              message: "permission_util_media_content".tr);
        }
      }
      if (status.isGranted) {
        await action();
      } else {
        status = await Permission.storage.request();
        if (status.isDenied || status.isPermanentlyDenied) {
          _openSetting(
              title: "permission_util_media_title_one".tr,
              content: "permission_util_media_content".tr);
        } else if (status.isGranted) {
          _overlayEntry?.remove();
          await action();
        }
      }
    }
  }

  ///检测是否开通通知权限
  Future<bool> isNotification() async {
    PermissionStatus status = await Permission.notification.status;
    return status.isGranted;
  }

  ///通知权限
  static notification(BuildContext context, {required Function action}) async {
    PermissionStatus status = await Permission.notification.status;
    if (!status.isGranted) {
      if (Platform.isAndroid) {
        _showNotification(
            title: "permission_util_notification_title".tr,
            message: "permission_util_notification_content".tr);
      }
    }
    if (status.isGranted) {
      await action();
    } else {
      status = await Permission.notification.request();
      if (status.isDenied || status.isPermanentlyDenied) {
        _openSetting(
            title: "permission_util_notification_title_one".tr,
            content: "permission_util_notification_content".tr);
      }
      if (status.isGranted) {
        _overlayEntry?.remove();
        await action();
      }
    }
  }

  ///忽略电池优化
  static ignoringBattery() async {
    PermissionStatus status =
        await Permission.ignoreBatteryOptimizations.status;
    if (status.isDenied) {
      status = await Permission.ignoreBatteryOptimizations.request();
    }
  }

  static OverlayEntry? _overlayEntry;

  static _openSetting({required String title, String content = ""}) async {
    await Get.dialog(CommonDialog(
      mTitle: title,
      mContent: content,
      mConfirm: "permission_overlay_btn".tr,
      confirmAction: () {
        openAppSettings();
      },
    ));
    if (_overlayEntry != null) {
      _overlayEntry?.remove();
    }
  }

  static _showNotification({String title = "", String message = ""}) {
    _overlayEntry = OverlayEntry(builder: (context) {
      return Positioned(
          top: ScreenUtil().statusBarHeight + 10.h,
          left: 10.w,
          right: 10.w,
          child: Material(
            color: Colors.transparent,
            child: ClipRRect(
              borderRadius: BorderRadius.circular(15),
              child: BackdropFilter(
                filter: ImageFilter.blur(sigmaX: 7.0, sigmaY: 7.0),
                child: Container(
                  padding: EdgeInsets.all(10.r),
                  decoration: BoxDecoration(
                    color: Colors.grey.withValues(alpha: 0.2),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        title,
                        style:
                            StyleConfig.witheStyle(fontWeight: FontWeight.w700),
                      ),
                      SizedBox(height: 5.h),
                      Text(
                        message,
                        style: StyleConfig.witheStyle(fontSize: 14),
                      )
                    ],
                  ),
                ),
              ),
            ),
          ).inkWell(() {
            _overlayEntry?.remove();
          }));
    });
    Overlay.of(Get.context!).insert(_overlayEntry!);
  }
}
