import 'package:dio/dio.dart';
import 'package:getx_xiaopa/network/error_interceptor.dart';
import 'package:getx_xiaopa/network/http_client.dart';
import 'package:getx_xiaopa/storage/storage.dart';

/// gpt对话需要中断取消接收数据 retrofit暂时没找到取消的方法 所以另外写一个工具
class ChatUtil {
  ChatUtil(); //构造函数

  CancelToken token = CancelToken();

  Dio dio = Dio(BaseOptions(headers: {
    'Authorization': UserStore.to.authorization,
  }, responseType: ResponseType.stream))
    ..interceptors.add(ErrorInterceptor());

  Future sendChatGPT(map) async {
    Response rsp = await dio.post('$baseUrl/client/brain/chat/completion',
        data: map, cancelToken: token);
    // logD('GPT请求结果：${rsp.statusCode} == ${rsp.statusMessage}');
    return rsp.data;
  }

  cancelToken() {
    token.cancel();
  }
}
