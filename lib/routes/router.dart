// ignore_for_file: non_constant_identifier_names

import 'package:get/get.dart';
import 'package:getx_xiaopa/pages/application/application_view.dart';
import 'package:getx_xiaopa/pages/console/console_pairing/console_pairing_view.dart';
import 'package:getx_xiaopa/pages/console/console_schedule/index.dart';
import 'package:getx_xiaopa/pages/console/console_schedule/schedule_add/index.dart';
import 'package:getx_xiaopa/pages/console/console_schedule/schedule_all/view.dart';
import 'package:getx_xiaopa/pages/console/console_schedule/schedule_plan/view.dart';
import 'package:getx_xiaopa/pages/console/console_schedule/schedule_today/index.dart';
import 'package:getx_xiaopa/pages/console/console_setting/console_clear/index.dart';
import 'package:getx_xiaopa/pages/console/console_setting/console_compiled/index.dart';
import 'package:getx_xiaopa/pages/console/console_setting/console_language/index.dart';
import 'package:getx_xiaopa/pages/console/console_setting/console_role/index.dart';
import 'package:getx_xiaopa/pages/console/console_setting/console_setting_view.dart';
import 'package:getx_xiaopa/pages/console/console_setting/console_sound/sound_exchange/index.dart';
import 'package:getx_xiaopa/pages/console/console_setting/console_sound/sound_record/index.dart';
import 'package:getx_xiaopa/pages/console/console_setting/console_sound/index.dart';
import 'package:getx_xiaopa/pages/console/console_setting/console_update/console_update_view.dart';
import 'package:getx_xiaopa/pages/console/console_wifi/console_wifi_view.dart';
import 'package:getx_xiaopa/pages/diary/diary_analysis/diary_analysis_view.dart';
import 'package:getx_xiaopa/pages/diary/diary_edit/diary_edit_view.dart';
import 'package:getx_xiaopa/pages/diary/diary_mood/diary_mood_view.dart';
import 'package:getx_xiaopa/pages/diary/diary_record/diary_record_view.dart';
import 'package:getx_xiaopa/pages/diary/diary_share/diary_share_view.dart';
import 'package:getx_xiaopa/pages/game/game_view.dart';
import 'package:getx_xiaopa/pages/login/login_view.dart';
import 'package:getx_xiaopa/pages/mine/mine_device/view.dart';
import 'package:getx_xiaopa/pages/mine/mine_feedback/view.dart';
import 'package:getx_xiaopa/pages/mine/mine_info/mine_info_view.dart';
import 'package:getx_xiaopa/pages/mine/mine_phone/mine_phone_view.dart';
import 'package:getx_xiaopa/pages/mine/mine_setting/mine_cancel/mine_cancel_view.dart';
import 'package:getx_xiaopa/pages/mine/mine_setting/mine_language/index.dart';
import 'package:getx_xiaopa/pages/mine/mine_setting/mine_pwd/mine_pwd_view.dart';
import 'package:getx_xiaopa/pages/mine/mine_setting/mine_setting_view.dart';
import 'package:getx_xiaopa/pages/period/index.dart';
import 'package:getx_xiaopa/pages/period/period_analysis/period_color/view.dart';
import 'package:getx_xiaopa/pages/period/period_analysis/period_cycle/index.dart';
import 'package:getx_xiaopa/pages/period/period_analysis/index.dart';
import 'package:getx_xiaopa/pages/period/period_analysis/period_day/index.dart';
import 'package:getx_xiaopa/pages/period/period_analysis/period_flux/index.dart';
import 'package:getx_xiaopa/pages/period/period_analysis/period_pain/view.dart';
import 'package:getx_xiaopa/pages/period/period_explain/index.dart';
import 'package:getx_xiaopa/pages/period/period_setting/index.dart';
import 'package:getx_xiaopa/pages/period/period_setting/period_edit/view.dart';
import 'package:getx_xiaopa/pages/period/period_setting/period_setting_edit/view.dart';
import 'package:getx_xiaopa/pages/recharge/index.dart';
import 'package:getx_xiaopa/pages/recharge/record/index.dart';
import 'package:getx_xiaopa/pages/register/register_view.dart';
import 'package:getx_xiaopa/pages/task/task_achievement/task_achievement_view.dart';
import 'package:getx_xiaopa/pages/task/task_detail/task_detail_view.dart';
import 'package:getx_xiaopa/pages/task/task_explan/tasl_explan_index.dart';
import 'package:getx_xiaopa/pages/task/task_setting/task_setting_view.dart';
import 'package:getx_xiaopa/pages/task/task_share/task_share_view.dart';
import 'package:getx_xiaopa/pages/task/task_view.dart';

class AppRoutes {
  static String APPLICATION = '/application';

  static String LOGIN = '/login';
  static String REGISTER = '/register';

  static String CONSOLE_PAIRING = '/console_pairing';
  static String CONSOLE_WIFI = '/console_wifi';
  static String CONSOLE_SETTING = '/console_setting';
  static String CONSOLE_UPDATE = '/console_update';
  static String CONSOLE_LANGUAGE = '/console_language';
  static String CONSOLE_COMPILED = '/console_compiled';
  static String CONSOLE_SOUND = '/console_sound';
  static String CONSOLE_CLEAR = '/console_clear';
  static String CONSOLE_ROLE = '/console_role';
  static String CONSOLE_SCHEDULE = '/console_schedule';
  static String SCHEDULE_ADD = '/schedule_add';
  static String SCHEDULE_TODAY = '/schedule_today';
  static String SCHEDULE_PLAN = '/schedule_plan';
  static String SCHEDULE_ALL = '/schedule_all';

  static String SOUND_RECORD = '/sound_record';
  static String SOUND_EXCHANGE = '/sound_exchange';

  static String TASK = '/task';
  static String TASK_DETAIL = '/task_detail';
  static String TASK_ACHIEVEMENT = '/task_achievement';
  static String TASK_SETTING = '/task_setting';
  static String TASK_SHARE = '/task_share';
  static String TASK_EXPLAN = '/task_explan';

  static String DIARY_RECORD = '/diary_record';
  static String DIARY_MOOD = '/diary_mood';
  static String DIARY_EDIT = '/diary_edit';
  static String DIARY_ANALYSIS = '/diary_analysis';
  static String DIARY_SHARE = '/diary_share';

  static String GAME = '/game';

  static String SWITCH_BRAIN = '/switch_brain';

  static String MINE_LANGUAGE = '/mine_language';
  static String MINE_PWD = '/mine_pwd';
  static String MIME_INFO = '/mine_info';
  static String MIME_SETTING = '/mine_setting';
  static String MINE_PHONE = '/mine_phone';
  static String MINE_CANCEL = '/mine_cancel';
  static String MINE_FEEDBACK = '/mine_feedback';
  static String MINE_DEVICE = '/mine_device';

  static String RECHARGE = '/recharge';
  static String RECHARGE_RECORD = '/recharge_record';

  static String PERIOD = '/period';
  static String PERIOD_ANALYSIS = '/period_analysis';
  static String PERIOD_CYCLE = '/period_cycle';
  static String PERIOD_DAY = '/period_day';
  static String PERIOD_COLOR = '/period_color';
  static String PERIOD_FLUX = '/period_flux';
  static String PERIOD_PAIN = '/period_pain';

  static String PERIOD_SETTING = '/period_setting';
  static String PERIOD_EDIT = '/period_edit';
  static String PERIOD_SETTING_EDIT = '/period_setting_edit';
  static String PERIOD_EXPLAIN = '/period_explain';

  static List<GetPage> routes = [
    GetPage(name: APPLICATION, page: () => ApplicationPage()),
    GetPage(name: LOGIN, page: () => LoginPage()),
    GetPage(name: REGISTER, page: () => RegisterPage()),
    GetPage(name: CONSOLE_PAIRING, page: () => ConsolePairingPage()),
    GetPage(name: CONSOLE_WIFI, page: () => ConsoleWifiPage()),
    GetPage(name: CONSOLE_SETTING, page: () => ConsoleSettingPage()),
    GetPage(name: CONSOLE_UPDATE, page: () => ConsoleUpdatePage()),
    GetPage(name: CONSOLE_COMPILED, page: () => ConsoleCompiledPage()),
    GetPage(name: CONSOLE_LANGUAGE, page: () => ConsoleLanguagePage()),
    GetPage(name: CONSOLE_SOUND, page: () => ConsoleSoundPage()),
    GetPage(name: SOUND_RECORD, page: () => SoundRecordPage()),
    GetPage(name: SOUND_EXCHANGE, page: () => SoundExchangePage()),
    GetPage(name: CONSOLE_CLEAR, page: () => ConsoleClearPage()),
    GetPage(name: CONSOLE_ROLE, page: () => ConsoleRolePage()),
    GetPage(name: CONSOLE_SCHEDULE, page: () => ConsoleSchedulePage()),
    GetPage(name: SCHEDULE_ADD, page: () => ScheduleAddPage()),
    GetPage(name: SCHEDULE_TODAY, page: () => ScheduleTodayPage()),
    GetPage(name: SCHEDULE_PLAN, page: () => SchedulePlanPage()),
    GetPage(name: SCHEDULE_ALL, page: () => ScheduleAllPage()),
    GetPage(name: TASK, page: () => TaskPage()),
    GetPage(name: TASK_DETAIL, page: () => TaskDetailPage()),
    GetPage(name: TASK_ACHIEVEMENT, page: () => TaskAchievementPage()),
    GetPage(name: TASK_SETTING, page: () => TaskSettingPage()),
    GetPage(name: TASK_SHARE, page: () => TaskSharePage()),
    GetPage(name: TASK_EXPLAN, page: () => TaskExplanPage()),
    GetPage(name: DIARY_RECORD, page: () => DiaryRecordPage()),
    GetPage(name: DIARY_MOOD, page: () => DiaryMoodPage()),
    GetPage(name: DIARY_EDIT, page: () => DiaryEditPage()),
    GetPage(name: DIARY_ANALYSIS, page: () => DiaryAnalysisPage()),
    GetPage(name: DIARY_SHARE, page: () => DiarySharePage()),
    GetPage(name: GAME, page: () => GamePage()),
    GetPage(name: MINE_PWD, page: () => MinePwdPage()),
    GetPage(name: MIME_INFO, page: () => MineInfoPage()),
    GetPage(name: MIME_SETTING, page: () => MineSettingPage()),
    GetPage(name: MINE_LANGUAGE, page: () => MineLanguagePage()),
    GetPage(name: MINE_PHONE, page: () => MinePhonePage()),
    GetPage(name: MINE_CANCEL, page: () => MineCancelPage()),
    GetPage(name: MINE_FEEDBACK, page: () => MineFeedbackPage()),
    GetPage(name: MINE_DEVICE, page: () => MineDevicePage()),
    GetPage(name: RECHARGE, page: () => RechargePage()),
    GetPage(name: RECHARGE_RECORD, page: () => RecordPage()),
    GetPage(name: PERIOD, page: () => PeriodPage()),
    GetPage(name: PERIOD_ANALYSIS, page: () => PeriodAnalysisPage()),
    GetPage(name: PERIOD_CYCLE, page: () => PeriodCyclePage()),
    GetPage(name: PERIOD_DAY, page: () => PeriodDayPage()),
    GetPage(name: PERIOD_COLOR, page: () => PeriodColorPage()),
    GetPage(name: PERIOD_FLUX, page: () => PeriodFluxPage()),
    GetPage(name: PERIOD_PAIN, page: () => PeriodPainPage()),
    GetPage(name: PERIOD_SETTING, page: () => PeriodSettingPage()),
    GetPage(name: PERIOD_EDIT, page: () => PeriodEditPage()),
    GetPage(name: PERIOD_SETTING_EDIT, page: () => PeriodSettingEditPage()),
    GetPage(name: PERIOD_EXPLAIN, page: () => PeriodExplainPage()),
  ];
}
