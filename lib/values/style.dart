import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class StyleConfig {
  /// 白色样式
  static TextStyle witheStyle(
      {int fontSize = 16,
      FontWeight fontWeight = FontWeight.w400,
      double opacity = 1,
      fontFamily = "Alibaba"}) {
    return TextStyle(
      color: Colors.white.withValues(alpha: opacity),
      fontWeight: fontWeight,
      fontSize: fontSize.sp,
      fontFamilyFallback: [fontFamily, "AlibabaPuHuiTi"],
    );
  }

  /// 黑色样式
  static TextStyle blackStyle(
      {int fontSize = 16,
      FontWeight fontWeight = FontWeight.w400,
      double opacity = 1,
      fontFamily = "Alibaba"}) {
    return TextStyle(
      color: Colors.black.withValues(alpha: opacity),
      fontWeight: fontWeight,
      fontSize: fontSize.sp,
      fontFamilyFallback: [fontFamily, "AlibabaPuHuiTi"],
    );
  }

  /// 其他颜色样式
  static TextStyle otherStyle(
      {required Color color,
      double fontSize = 16,
      FontWeight fontWeight = FontWeight.w400,
      fontFamily = "Alibaba",
      double? height,
      double? wordSpacing,
      double? letterSpacing,
      TextOverflow? overflow}) {
    return TextStyle(
        color: color,
        fontWeight: fontWeight,
        fontSize: fontSize.sp,
        fontFamilyFallback: [fontFamily, "AlibabaPuHuiTi"],
        height: height,
        wordSpacing: wordSpacing,
        letterSpacing: letterSpacing,
        overflow: overflow);
  }
}
