import 'dart:async';
import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:getx_xiaopa/extension/widget_extension.dart';
import 'package:getx_xiaopa/pages/splash/splash_view.dart';
import 'package:getx_xiaopa/routes/router.dart';
import 'package:getx_xiaopa/storage/storage.dart';
import 'package:getx_xiaopa/translation/translations.dart';
import 'package:getx_xiaopa/utils/utils.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';

import 'global.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  if (kReleaseMode) {
    runZonedGuarded(() async {
      await Global.init();

      FlutterError.onError = (errorDetails) {
        /// Flutter 框架错误（同步/Widget相关）
        final log = [
          '🚨 Flutter Error v${UserStore.to.version} -- ${Platform.isAndroid ? "Android" : "iOS"}',
          'Time: ${DateTime.now()}',
          'Exception: ${errorDetails.exceptionAsString()}',
          'Stacktrace:\n${errorDetails.stack}',
        ].join('\n');
        FileStore.to.saveCrashLog(log);
      };
      runApp(const MainApp());
    }, (error, stackTrace) {
      /// 异步任务中的未捕获异常
      final log = [
        '🚨 RunZonedGuarded Error v${UserStore.to.version} -- ${Platform.isAndroid ? "Android" : "iOS"}',
        'Time: ${DateTime.now()}',
        'Exception: ${error.toString()}',
        'Stacktrace:\n$stackTrace',
      ].join('\n');
      FileStore.to.saveCrashLog(log);
    });
  } else {
    Global.init().then((value) => runApp(const MainApp()));
  }
}

class MainApp extends StatefulWidget {
  const MainApp({super.key});

  @override
  State<MainApp> createState() => _MainAppState();
}

class _MainAppState extends State<MainApp> {
  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    ///显示布局边界
    debugPaintSizeEnabled = false;
    return ScreenUtilInit(
        designSize: const Size(375, 812),
        minTextAdapt: true,
        splitScreenMode: true,
        builder: (BuildContext context, Widget? child) {
          return GetMaterialApp(
            debugShowCheckedModeBanner: false,
            getPages: AppRoutes.routes,
            title: "app_name".tr,
            localizationsDelegates: const [
              RefreshLocalizations.delegate,
              GlobalMaterialLocalizations.delegate,
              GlobalWidgetsLocalizations.delegate,
              GlobalCupertinoLocalizations.delegate, //iOS
            ],
            translations: Translation(),
            supportedLocales: ConfigStore.to.languages,
            locale: ConfigStore.to.locale,
            defaultTransition: Transition.rightToLeft,
            builder: (context, widget) {
              return MediaQuery(
                //设置文字大小不随系统设置改变
                data: MediaQuery.of(context)
                    .copyWith(textScaler: const TextScaler.linear(1.0)),
                child: FlutterEasyLoading(child: widget),
              );
            },
            home: SplashPage(),
            theme: lightTheme,
            darkTheme: darkTheme,
            themeMode: ThemeMode.light,
            routingCallback: (routing) {},
          ).gestureDetector(() {
            ///点击隐藏键盘
            FocusScopeNode currentFocus = FocusScope.of(context);
            if (!currentFocus.hasPrimaryFocus &&
                currentFocus.focusedChild != null) {
              FocusManager.instance.primaryFocus?.unfocus();
            }
          });
        });
  }
}
