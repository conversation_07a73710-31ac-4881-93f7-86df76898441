// ignore_for_file: must_be_immutable

import 'dart:convert';
import 'dart:io';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/cupertino.dart';
import 'package:getx_xiaopa/r.dart';

class Images extends StatelessWidget {
  final String path;
  double? width;
  double scale;
  double? height;
  Color? color;
  BoxFit? boxFit;
  String? placeholder;
  bool gaplessPlayback;
  
  Images(
      {super.key,
      required this.path,
      this.width,
      this.height,
      this.scale = 1.0,
      this.color,
      this.boxFit,
      this.gaplessPlayback = false,
      this.placeholder = ''});

  @override
  Widget build(BuildContext context) {
    if (path.startsWith('http')) {
      return CachedNetworkImage(
        imageUrl: path,
        color: color,
        width: width,
        height: height,
        fit: boxFit,
        placeholder: (context, url) => placeholder!.isNotEmpty
            ? Image.asset(
                placeholder!,
                fit: boxFit,
              )
            : const CupertinoActivityIndicator(),
        errorWidget: (context, url, error) => Image.asset(
          R.load_fail_png,
        ),
      );
    } else if (path.startsWith('data:image/png;base64')) {
      return Image.memory(
        base64.decode(path.split(",")[1]),
        width: width,
        height: height,
        scale: scale,
        fit: boxFit,
        gaplessPlayback: true, //防止重绘
      );
    } else if (path.startsWith("/")) {
      return Image.file(
        File(path),
        width: width,
        height: height,
        color: color,
        fit: boxFit,
        scale: scale,
      );
    } else if (path.isNotEmpty) {
      return Image.asset(
        path,
        width: width,
        height: height,
        color: color,
        fit: boxFit,
        scale: scale,
        gaplessPlayback: gaplessPlayback,
      );
    } else {
      return Image.asset(
        R.load_fail_png,
        width: width,
        height: height,
        color: color,
        fit: boxFit,
        scale: scale,
      );
    }
  }
}
