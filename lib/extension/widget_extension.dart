import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:get/get.dart';

extension WidgetExtension on Widget {
  Widget gestureDetector(Function() onTap) => GestureDetector(
        onTap: onTap,
        child: this,
      );

  Widget inkWell(Function() onTap) => InkWell(
        onTap: onTap,
        highlightColor: Colors.transparent,
        splashColor: Colors.transparent,
        child: this,
      );
}

extension SafePageController on PageController {
  void safeJumpToPage(int page) {
    // positions 必须只有 1 个才能正常访问 position
    if (positions.length != 1) {
      debugPrint(
          "⚠️ PageController.safeJumpToPage: positions.length = ${positions.length}, jumpToPage Prevent Too many elements");
      return;
    }

    // 防止非法页码
    if (page < 0) {
      debugPrint("⚠️ PageController.safeJumpToPage: page $page < 0");
      return;
    }

    try {
      jumpToPage(page);
    } catch (e, s) {
      debugPrint("❌ safeJumpToPage error: $e\n$s");
    }
  }
}

abstract mixin class CToast {
  static showToast(
    String msg, {
    double fontSize = 14,
    Toast toast = Toast.LENGTH_SHORT,
    Color bgColor = Colors.black,
    Color textColor = Colors.white,
    ToastGravity gravity = ToastGravity.CENTER,
    Function()? dismiss,
  }) {
    CToast.cancel();
    Fluttertoast.showToast(
            msg: msg.tr,
            gravity: gravity,
            toastLength: toast,
            fontSize: fontSize.sp,
            textColor: textColor,
            backgroundColor: bgColor)
        .then((value) {
      if (dismiss != null) {
        dismiss();
      }
    });
  }

  /// 提示文字
  static showLoading({String? title, double textFont = 14.0}) {
    CToast.dismiss();
    EasyLoading.instance
      ..displayDuration = const Duration(milliseconds: 2000)
      ..indicatorType = EasyLoadingIndicatorType.circle
      ..loadingStyle = EasyLoadingStyle.custom
      ..maskType = EasyLoadingMaskType.black
      ..indicatorSize = 45.0
      ..radius = 6.0
      ..backgroundColor = Colors.transparent
      ..indicatorColor = Colors.yellow
      ..textColor = Colors.yellow
      ..maskColor = Colors.transparent
      ..textStyle = TextStyle(fontSize: textFont.sp)
      ..userInteractions = false;
    EasyLoading.show(
      maskType: EasyLoadingMaskType.clear,
      status: title,
      indicator: const CupertinoActivityIndicator(
        radius: 14,
        color: Colors.white,
      ),
    );
  }

  static void dismiss() {
    EasyLoading.dismiss();
  }

  static void cancel() {
    Fluttertoast.cancel();
  }
}
