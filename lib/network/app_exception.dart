// ignore_for_file: constant_identifier_names, use_super_parameters

import 'dart:io';

import 'package:dio/dio.dart';
import 'package:get/get.dart';
import 'package:getx_xiaopa/extension/widget_extension.dart';
import 'package:getx_xiaopa/utils/utils.dart';

/// 自定义异常
class AppException implements Exception {
  static const String CLIENT_NET_ERROR = "网络异常，请检网络连接状态";
  static const String SERVER_NET_ERROR = "系统繁忙，请稍后再试!";
  static const String NET_CONNECT_ERROR = "网络未连接，请检查后重试";
  static const String TOO_MANY_REQUEST = "顾客太多，请稍等";

  late final String message;
  late final int code;

  AppException(this.code, this.message);

  @override
  String toString() {
    return message;
  }

  factory AppException.create(DioException error) {
    CToast.dismiss();
    switch (error.type) {
      case DioExceptionType.cancel:
        return BadRequestException(-1, "newwork_cancel".tr);
      case DioExceptionType.connectionTimeout: //连接超时
      case DioExceptionType.sendTimeout: //请求超时
        return BadRequestException(-1, "network_client_net_error".tr);
      case DioExceptionType.receiveTimeout: //响应超时
        return BadRequestException(-1, "network_server_net_error".tr);
      case DioExceptionType.badResponse:
        try {
          int errCode = error.response!.statusCode!;
          // String errMsg = error.response.statusMessage;
          // return ErrorEntity(code: errCode, message: errMsg);
          switch (errCode) {
            case 400:
              logE('请求语法错误');
              return BadRequestException(
                  errCode, "network_server_net_error".tr);
            case 401: //没有权限
            case 403: //服务器拒绝执行
            case 404: //无法连接服务器
            case 405: //请求方法被禁止
            case 500: //服务器内部错误
            case 502: //无效的请求
            case 503: //服务器挂了
            case 505: //不支持HTTP协议请求
              return UnauthorisedException(
                  errCode, "network_server_net_error".tr);
            case 429:
              return AppException(errCode, "network_too_many_request".tr);
            default:
              // return ErrorEntity(code: errCode, message: "未知错误");
              return AppException(errCode, error.response!.statusMessage!);
          }
        } on Exception catch (_) {
          logE('不通1');
          return AppException(-1, "network_net_connect_error".tr);
        }
      case DioExceptionType.unknown:
        if (error.error is SocketException) {
          return AppException(-1, "network_net_connect_error".tr);
        } else {
          logE('不通2 $error');
          return AppException(-1, "network_net_connect_error".tr);
        }
      default:
        logE('不通3');
        return AppException(-1, "network_net_connect_error".tr);
    }
  }
}

/// 请求错误
class BadRequestException extends AppException {
  BadRequestException([code, message]) : super(code, message);
}

/// 未认证异常
class UnauthorisedException extends AppException {
  UnauthorisedException([code, message]) : super(code, message);
}
