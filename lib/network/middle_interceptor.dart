import 'package:dio/dio.dart';
import 'package:get/get.dart' hide Response;
import 'package:getx_xiaopa/extension/widget_extension.dart';
import 'package:getx_xiaopa/routes/router.dart';
import 'package:getx_xiaopa/storage/storage.dart';
import 'package:getx_xiaopa/utils/utils.dart';

class MiddleInterceptor extends QueuedInterceptor {
  var requestStartTime = 0;

  @override
  Future<void> onRequest(
      RequestOptions options, RequestInterceptorHandler handler) async {
    options.headers['Authorization'] = UserStore.to.authorization;
    options.headers['Accept-Language'] = ConfigStore.to.locale.languageCode;
    // logD(
    //     "请求接口：${options.baseUrl}${options.path}\n请求头： ${options.headers}\n请求参数：${options.data}\n返回类型：${options.responseType}");
    requestStartTime = DateTime.now().millisecondsSinceEpoch;
    return handler.next(options);
  }

  @override
  void onResponse(Response response, ResponseInterceptorHandler handler) async {
    CToast.dismiss();

    // if (response.headers['Authorization'] != null) {
    //   UserStore.to.setAuthorization(response.headers['Authorization']![0]);
    // }

    //打印返回数据
    var options = response.requestOptions;
    logD(
        "请求接口：${options.baseUrl}${options.path}\n请求头： ${options.headers}\n请求参数：${options.data}\n返回类型：${options.responseType}\n接口请求耗时(s): ${(DateTime.now().millisecondsSinceEpoch - requestStartTime) / 1000}\n返回数据：${response.data}}",
        tag: 'requestUrl');

    if (response.data != null && response.data['code'] == 102) {
      ///维护或者被顶号
      if (UserStore.to.isLoggingOut) {
        UserStore.to.setLoggingOut(false);

        logD('${response.data['code']} == ${response.data['data']}');
        CToast.showToast(response.data['msg']);
        await UserStore.to.removeUserInfo();

        // 延迟一点再跳转，避免 Get.offAllNamed 多次冲突
        Future.delayed(const Duration(milliseconds: 300), () {
          Get.offAllNamed(AppRoutes.LOGIN);
        });
      }

      DioException e = DioException(
          requestOptions: response.requestOptions,
          message: response.data['msg']);
      handler.reject(e);
      return;
    }
    super.onResponse(response, handler);
  }
}
