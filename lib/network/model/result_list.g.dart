// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'result_list.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

ResultList<T> _$ResultListFromJson<T>(
  Map<String, dynamic> json,
  T Function(Object? json) fromJsonT,
) =>
    ResultList<T>(
      code: (json['code'] as num).toInt(),
      data: (json['data'] as List<dynamic>?)
          ?.map((e) => _$nullableGenericFromJson(e, fromJsonT))
          .toList(),
      msg: json['msg'] as String?,
    );

Map<String, dynamic> _$ResultListToJson<T>(
  ResultList<T> instance,
  Object? Function(T value) toJsonT,
) =>
    <String, dynamic>{
      'code': instance.code,
      'data': instance.data
          ?.map((e) => _$nullableGenericToJson(e, toJsonT))
          .toList(),
      'msg': instance.msg,
    };

T? _$nullableGenericFromJson<T>(
  Object? input,
  T Function(Object? json) fromJson,
) =>
    input == null ? null : fromJson(input);

Object? _$nullableGenericToJson<T>(
  T? input,
  Object? Function(T value) toJson,
) =>
    input == null ? null : toJson(input);
