// ignore_for_file: depend_on_referenced_packages

import 'package:json_annotation/json_annotation.dart';

part 'request_body.g.dart';

@JsonSerializable()
class RequestBody {
  ///密码，密码登录必传
  @Json<PERSON>ey(name: "filters")
  final List<Filter>? filters;
  @JsonKey(name: "orders")
  final List<Order>? orders;

  ///页码，1...
  @JsonKey(name: "pageNum")
  final int? pageNum;

  ///每页数量
  @Json<PERSON>ey(name: "pageSize")
  final int? pageSize;

  ///是否分页，true分页，false分页
  @JsonKey(name: "pagination")
  final bool? pagination;

  RequestBody({
    this.filters,
    this.orders,
    this.pageNum = 1,
    this.pageSize = 10,
    this.pagination = true,
  });

  factory RequestBody.fromJson(Map<String, dynamic> json) =>
      _$RequestBodyFromJson(json);

  Map<String, dynamic> toJson() => _$RequestBodyToJson(this);
}

@JsonSerializable()
class Filter {
  ///筛选表达式，"="等于,"<>"不等于,"in"在数组中,"not_in"不在数组中,">"大于,">="大于等于,"<"小于,"<="小于等于,"like"模糊搜索
  @JsonKey(name: "expr")
  final String expr;

  ///筛选字段名
  @JsonKey(name: "name")
  final String name;

  ///筛选字段值，根据筛选的字段类型传值
  @JsonKey(name: "value")
  final dynamic value;

  Filter({
    required this.expr,
    required this.name,
    required this.value,
  });

  factory Filter.fromJson(Map<String, dynamic> json) => _$FilterFromJson(json);

  Map<String, dynamic> toJson() => _$FilterToJson(this);
}

@JsonSerializable()
class Order {
  ///排序方式，asc 顺序 desc倒序
  @JsonKey(name: "expr")
  final String expr;

  ///排序字段名
  @JsonKey(name: "name")
  final String name;

  Order({
    required this.expr,
    required this.name,
  });

  factory Order.fromJson(Map<String, dynamic> json) => _$OrderFromJson(json);

  Map<String, dynamic> toJson() => _$OrderToJson(this);
}
