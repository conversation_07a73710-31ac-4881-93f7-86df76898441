// ignore_for_file: depend_on_referenced_packages

import 'package:json_annotation/json_annotation.dart';
import '../api_exception.dart';
part 'result_list.g.dart';

@JsonSerializable(genericArgumentFactories: true)
class ResultList<T> {
  int code;
  List<T?>? data;
  String? msg;

  ResultList({
    required this.code,
    this.data,
    this.msg,
  });

  factory ResultList.fromJson(
    Map<String, dynamic> json,
    T Function(dynamic json) fromJsonT,
  ) =>
      _$ResultListFromJson(json, fromJsonT);

  Map<String, dynamic> toJson(Object? Function(T value) toJsonT) =>
      _$ResultListToJson(this, toJsonT);

  factory ResultList.fromMapJson(Map<String, dynamic> json) {
    return ResultList(
        code: json['code'] as int,
        data: json['data'] as List<T>,
        msg: json['msg'] as String);
  }

  ApiException toException({dynamic cause}) =>
      ApiException(code: code, message: msg, cause: cause);
}
