import 'package:dio/dio.dart';
import 'package:get/get.dart' as getx;
import 'package:getx_xiaopa/extension/widget_extension.dart';
import 'package:getx_xiaopa/network/http_client.dart';
import 'package:getx_xiaopa/routes/router.dart';
import 'package:getx_xiaopa/storage/storage.dart';
import 'package:getx_xiaopa/utils/utils.dart';

import 'error_interceptor.dart';
import 'middle_interceptor.dart';

typedef Server = String? Function();
typedef Token = String Function();

class DioUtil {
  static final DioUtil _instance = DioUtil._();

  /// 私有构造器
  DioUtil._();

  static DioUtil getInstance() => _instance;

  ///超时时间
  static const Duration connectTimeout = Duration(milliseconds: 60 * 1000);

  static const Duration receiveTimeout = Duration(milliseconds: 60 * 1000);

  Dio createDioInstance({required Server server, required Token token}) {
    BaseOptions options = BaseOptions(
      connectTimeout: connectTimeout,

      /// 响应流上前后两次接受到数据的间隔，单位为毫秒。
      receiveTimeout: receiveTimeout,
      baseUrl: server() ?? '',
      contentType: Headers.jsonContentType,
      responseType: ResponseType.json,
      receiveDataWhenStatusError: false,
    );
    Dio dio = Dio(options);
    dio.interceptors.add(MiddleInterceptor());
    dio.interceptors.add(ErrorInterceptor());

    return dio;
  }

  ///=================上传文件===============================
  Future<String> uploadFile(String filePath,
      {String access = 'brain-avatar', String filename = 'file.jpg'}) async {
    CToast.showLoading();
    final cDio = Dio(BaseOptions(headers: {
      "Content-Type":
          "multipart/form-data; boundary=--------------------------813473593856391253156942",
      "Authorization": UserStore.to.authorization
    }));
    FormData formData = FormData.fromMap({
      'fileName': await MultipartFile.fromFile(filePath, filename: filename),
      'access': access
    });
    Response response = await cDio.post(
        "$baseUrl/client/base/file/buffer",
        data: formData);
    logD("上传文件返回：$response");
    CToast.dismiss();
    if (response.data["code"] == 102) {
      ///维护或者被顶号
      CToast.showToast(response.data["msg"] ?? '');
      await UserStore.to.removeUserInfo();

      getx.Get.offAndToNamed(AppRoutes.LOGIN);
    }
    if (response.data != null) {
      return response.data["data"];
    }
    return response.data["msg"];
  }

  /// 获取图片字节
  Future<List<int>> getImageBytes(String imageUrl) async {
    final response = await Dio().get<List<int>>(
      imageUrl,
      options: Options(responseType: ResponseType.bytes),
    );
    return response.data ?? [];
  }
}
