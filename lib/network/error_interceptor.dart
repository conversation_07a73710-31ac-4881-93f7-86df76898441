// ignore_for_file: prefer_typing_uninitialized_variables

import 'package:dio/dio.dart';
import 'package:getx_xiaopa/extension/widget_extension.dart';
import 'package:getx_xiaopa/utils/utils.dart';

import 'api_exception.dart';
import 'app_exception.dart';

/// 错误处理拦截器
class ErrorInterceptor extends QueuedInterceptor {
  @override
  void onError(DioException err, ErrorInterceptorHandler handler) {
    var appException;

    /// error统一处理
    if (err is ApiException) {
      /// 自定义异常，如后台透传异常
      appException = ApiException(code: err.code, message: err.message);
    } else {
      /// 网络异常，socket异常等
      appException = AppException.create(err);
    }

    /// 错误提示
    logE(
      'DioError===: ${appException.toString()},method=${err.requestOptions.method},code=${err.response?.statusCode},uri=${err.requestOptions.uri}',
    );
    CToast.dismiss();
    CToast.showToast(appException.toString());
    err = appException;

    // ✅ 通知后续流程，这个错误已经处理完了
    handler.reject(err);
  }
}
