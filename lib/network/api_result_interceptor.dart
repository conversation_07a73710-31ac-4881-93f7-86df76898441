import 'dart:convert';
import 'package:dio/dio.dart';
import 'package:getx_xiaopa/utils/utils.dart';

import 'model/result.dart';

class ApiResultInterceptor extends Interceptor {
  @override
  Future<void> onResponse(
      Response response, ResponseInterceptorHandler handler) async {
    var decode = utf8.decode(response.data);
    response.data = json.decode(decode);
    String urlPath = response.requestOptions.path;
    logD('请求返回接口--->:$urlPath');
    logD('请求返回数据--->:${json.encode(response.data)}');

    /// http error错误处理
    if (response.statusCode != 200) {
      handler.reject(
          DioException(
              requestOptions: response.requestOptions, response: response),
          true);
      return;
    }
    final result =
        Result<dynamic>.fromMapJson(response.data as Map<String, dynamic>);

    if (result.code == 0) {
      /// 成功
      handler.next(response);
      return;
    } else {
      /// 失败
      handler.reject(
          result.toException()..requestOptions = response.requestOptions, true);
    }
    // handler.next(resp);
  }
}
