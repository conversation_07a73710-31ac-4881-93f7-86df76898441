import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:getx_xiaopa/pages/console/console_schedule/index.dart';
import 'package:getx_xiaopa/pages/diary/utils/diary_store.dart';
import 'package:getx_xiaopa/pages/period/index.dart';
import 'package:getx_xiaopa/storage/storage.dart';
import 'package:getx_xiaopa/utils/utils.dart';

///全局静态数据
class Global {
  ///初始化
  static Future init() async {
    setSystemUi();

    await Get.putAsync<SharedPreferencesUtil>(
        () => SharedPreferencesUtil().init());

    await Get.putAsync<FileStore>(() => FileStore().init());
    await Get.putAsync<ConfigStore>(() => ConfigStore().init());
    await Get.putAsync<UserStore>(() => UserStore().init());
    await Get.putAsync<CommonStore>(() => CommonStore().init());

    Get.put<ByteRtcUtil>(ByteRtcUtil());

    Get.put<ThemeColorController>(ThemeColorController());
    Get.put<ThemeImageController>(ThemeImageController());

    Get.put<DiaryStore>(DiaryStore());
    Get.put<ScheduleStore>(ScheduleStore());
    Get.put<PeriodStore>(PeriodStore());

    DownloadUtil.initialize();

    ///初始化微信SDK
    WxUtil.initWx(WxConfig.appKey, WxConfig.linkUrl);

    ///初始化小红书SDK
    XhsUtil.initXhs(
        Platform.isAndroid ? XhsConfig.androidAppKey : XhsConfig.iosAppKey);

    ///初始化极光
    // JPushUtil().initialize();
  }

  static void setSystemUi() async {
    SystemChrome.setEnabledSystemUIMode(SystemUiMode.edgeToEdge);

    await SystemChrome.setPreferredOrientations([DeviceOrientation.portraitUp]);
    if (GetPlatform.isAndroid) {
      SystemUiOverlayStyle systemUiOverlayStyle = const SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,
        statusBarBrightness: Brightness.dark,
        statusBarIconBrightness: Brightness.dark,
        systemNavigationBarDividerColor: Colors.transparent,
        systemNavigationBarColor: Colors.transparent,
        systemNavigationBarIconBrightness: Brightness.light,
      );
      SystemChrome.setSystemUIOverlayStyle(systemUiOverlayStyle);
    }
  }
}
