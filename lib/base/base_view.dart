import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get_state_manager/get_state_manager.dart';
import 'package:getx_xiaopa/extension/widget_extension.dart';

abstract class BaseView<T> extends GetView<T> {
  BaseView(
      {this.contentColor,
      this.navTitle,
      this.navTitleStyle,
      this.navColor,
      this.leftButton,
      this.leftWidth,
      this.rightActionList,
      this.isHiddenNav,
      this.customBar,
      this.floatingWidget,
      this.extendBodyBehindAppBar,
      this.customWidget = false,
      this.isResize,
      this.bottomWidget,
      this.systemUiOverlayStyle,
      this.navTitleCall,
      super.key});

  /// 状态栏高度
  final double statusBarH = ScreenUtil().statusBarHeight;

  /// 导航栏高度
  final double navBarH = AppBar().preferredSize.height;

  /// 安全区域高度
  final double safeBarH = ScreenUtil().bottomBarHeight;

  /// 设置背景颜色
  final Color? contentColor;

  /// 设置标题文字
  final String? navTitle;

  ///标题Action
  final VoidCallback? navTitleCall;

  final PreferredSizeWidget? customBar;

  /// 设置标题样式
  final TextStyle? navTitleStyle;

  /// 设置导航栏颜色
  final Color? navColor;

  /// 设置左边按钮
  final Widget? leftButton;

  /// 设置左边宽度
  final double? leftWidth;

  /// 设置右边按钮数组
  final List<Widget>? rightActionList;

  /// 是否隐藏导航栏
  final bool? isHiddenNav;

  ///悬浮按钮
  final Widget? floatingWidget;

  ///布局从导航栏开始
  final bool? extendBodyBehindAppBar;

  ///配置状态栏 默认透明黑色字体（
  final SystemUiOverlayStyle? systemUiOverlayStyle;

  ///完全自定义控件
  final bool customWidget;

  ///是否根据键盘弹起重新布局
  final bool? isResize;

  ///底部视图
  final Widget? bottomWidget;

  /// 设置主主视图内容(子类不实现会报错)
  Widget buildContent();

  @override
  Widget build(BuildContext context) {
    return customWidget
        ? AnnotatedRegion<SystemUiOverlayStyle>(
            value: systemUiOverlayStyle ??
                const SystemUiOverlayStyle(
                  statusBarColor: Colors.transparent,
                  statusBarIconBrightness: Brightness.dark,
                ),
            child: buildContent(),
          )
        : Scaffold(
            backgroundColor: contentColor ?? Colors.white,
            resizeToAvoidBottomInset: isResize ?? false,
            extendBodyBehindAppBar: extendBodyBehindAppBar ?? false,
            appBar: isHiddenNav == true
                ? null
                : customBar ??
                    AppBar(
                      systemOverlayStyle: systemUiOverlayStyle ??
                          const SystemUiOverlayStyle(
                            statusBarColor: Colors.transparent,
                            statusBarIconBrightness: Brightness.dark,
                          ),
                      backgroundColor: navColor ?? Colors.transparent,
                      elevation: 0,
                      scrolledUnderElevation: 0,
                      title: Text(
                        navTitle ?? '',
                        style: navTitleStyle,
                      ).inkWell(() => navTitleCall?.call()),
                      leading: leftButton,
                      leadingWidth: leftWidth,
                      actions: rightActionList,
                    ),
            floatingActionButton: floatingWidget,
            body: isHiddenNav == true
                ? AnnotatedRegion<SystemUiOverlayStyle>(
                    value: systemUiOverlayStyle ??
                        const SystemUiOverlayStyle(
                          statusBarColor: Colors.transparent,
                          statusBarIconBrightness: Brightness.dark,
                        ),
                    child: buildContent(),
                  )
                : buildContent(),
            bottomNavigationBar: bottomWidget,
          );
  }
}
