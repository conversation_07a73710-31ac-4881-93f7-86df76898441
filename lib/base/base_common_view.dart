import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:getx_xiaopa/widget/widget.dart';
import 'base_common_controller.dart';
import 'base_controller.dart';
import 'base_view.dart';

typedef InitBuilder = Widget Function();
typedef BodyBuilder = Widget Function(BaseCommonController baseState);

abstract class BaseCommonView<T> extends BaseView<T> {
  BaseCommonView(
      {super.contentColor,
      super.navTitle,
      super.navTitleStyle,
      super.navColor,
      super.leftButton,
      super.leftWidth,
      super.rightActionList,
      super.isHiddenNav,
      super.key});

  /// 创建空视图 (子视图实现的话 Widget就是子视图实现的)
  Widget createEmptyWidget() {
    return const EmptyStatusWidget(
      emptyType: EmptyStatusType.noMessage,
    );
  }

  /// 创建错误视图 (子视图实现的话 Widget就是子视图实现的)
  Widget createFailWidget(BaseCommonController controller) {
    return EmptyStatusWidget(
      emptyType: EmptyStatusType.fail,
      refreshTitle: 'base_reload'.tr,
      width: 1.sw,
      height: 1.sh,
      onTap: () {
        /// 重新请求数据
        controller.getNetWorkData(controller.configNetWorkParams());
      },
    );
  }

  /// 创建页面主视图
  Widget createCommonView(BaseCommonController controller, BodyBuilder builder,
      {InitBuilder? initBuilder}) {
    if (controller.netState == NetState.loadingState) {
      /// loading 不会有这个状态,只是写一个这样的判断吧(控制器里面已经封装好了单例了,防止在网络层直接操作控制不了loading的场景)
      return Container();
    } else if (controller.netState == NetState.emptyDataState) {
      /// 返回站位视图
      return createEmptyWidget();
    } else if (controller.netState == NetState.errorShowRefresh) {
      /// 返回站位刷新视图
      return createFailWidget(controller);
    } else if (controller.netState == NetState.dataSuccessState) {
      return builder(controller);
    } else if (controller.netState == NetState.initializeState) {
      return initBuilder == null ? const SizedBox() : initBuilder();
    } else {
      return Center(child: Text('base_unkonwn'.tr));
    }
  }
}
