// ignore_for_file: unused_element

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:loading_animation_widget/loading_animation_widget.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';

import 'base_controller.dart';
import 'base_list_controller.dart';
import 'base_view.dart';

typedef InitBuilder = Widget Function();
typedef BodyBuilder = Widget Function(BaseListController baseState);

abstract class BaseListView<T> extends BaseView<T> {
  BaseListView(
      {super.contentColor,
      super.navTitle,
      super.navTitleStyle,
      super.navColor,
      super.leftButton,
      super.leftWidth,
      super.rightActionList,
      super.isHiddenNav,
      super.customBar,
      super.key});

  /// 创建空视图 (子视图实现的话 Widget就是子视图实现的)
  Widget createEmptyWidget() {
    return Container();
  }

  /// 创建错误视图 (子视图实现的话 Widget就是子视图实现的)
  Widget createFailWidget(BaseListController controller) {
    return Container();
  }

  /// 创建列表视图
  Widget createRefresherListView(
      BaseListController controller, BodyBuilder builder,
      {InitBuilder? initBuilder, bool? enablePullUp, bool? enablePullDown}) {
    if (controller.netState == NetState.loadingState) {
      /// loading 不会有这个状态,只是写一个这样的判断吧(控制器里面已经封装好了单例了,防止在网络层直接操作控制不了loading的场景)
      return Container();
    } else if (controller.netState == NetState.emptyDataState) {
      /// 返回站位视图
      return createEmptyWidget();
    } else if (controller.netState == NetState.errorShowRefresh) {
      /// 返回站位刷新视图
      return createFailWidget(controller);
    } else if (controller.netState == NetState.dataSuccessState) {
      return SmartRefresher(
          controller: controller.refreshController,

          /// 是否显示下拉
          enablePullUp: enablePullUp ?? true,

          /// 是否显示上拉
          enablePullDown: enablePullDown ?? true,

          /// 刷新回调方法
          onRefresh: () async {
            controller.refreshData();
          },

          /// 加载下一页回调
          onLoading: () async {
            controller.loadMore();
          },
          footer: createFooter(),
          child: builder(controller));
    } else if (controller.netState == NetState.initializeState) {
      return initBuilder == null ? const SizedBox() : initBuilder();
    } else {
      return Center(child: Text('base_unkonwn'.tr));
    }
  }

  /// 修改头视图配置
  ClassicHeader createHeader() {
    return ClassicHeader(
      textStyle: TextStyle(fontSize: 12.sp, color: Colors.red),
      refreshingText: 'base_list_refreshing'.tr,
      releaseText: 'base_list_complete'.tr,
      completeText: 'base_list_complete'.tr,
      failedText: 'base_list_failed'.tr,
      idleText: 'base_list_idle'.tr,
      height: 40,
    );
  }

  /// 自定义头视图配置
  CustomHeader createCustomHeader() {
    Widget body = _prompt("base_list_down_refresh".tr);
    return CustomHeader(
      height: 60,
      refreshStyle: RefreshStyle.Front,
      builder: (BuildContext context, RefreshStatus? mode) {
        body =
            LoadingAnimationWidget.newtonCradle(color: Colors.blue, size: 64);
        return body;
      },
    );
  }

  /// 头视图样式
  Widget _header(String showText) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        const CupertinoActivityIndicator(
          radius: 10,
          color: Colors.black45,
        ),
        SizedBox(width: 8.w),
        Text(
          showText,
          style: TextStyle(
            color: const Color(0xFF979797),
            fontSize: 14.sp,
          ),
        )
      ],
    );
  }

  /// 修改尾视图配置
  CustomFooter createFooter() {
    Widget body;
    return CustomFooter(
      height: 60.h,
      builder: (context, mode) {
        body = Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // LoadingAnimationWidget.inkDrop(color: Colors.white, size: 16),
            SizedBox(height: 5.h),
            _prompt(mode),
          ],
        );
        return body;
      },
    );
  }

  Widget _prompt(mode) {
    String showText;
    if (mode == LoadStatus.idle) {
      showText = "base_list_load_more".tr;
    } else if (mode == LoadStatus.loading) {
      showText = "base_list_loading".tr;
    } else if (mode == LoadStatus.failed) {
      showText = "base_list_load_fail".tr;
    } else if (mode == LoadStatus.canLoading) {
      showText = "base_list_load_more_data".tr;
    } else {
      showText = "base_list_no_more_data".tr;
    }
    return Center(
      child: Text(
        showText,
        style: TextStyle(
            color: const Color(0xFF979797),
            fontSize: 12.sp,
            fontWeight: FontWeight.w400,
            fontFamily: "HanSans"),
      ),
    );
  }
}
