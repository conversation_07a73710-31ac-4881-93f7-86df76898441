import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:get/get.dart';
import 'package:getx_xiaopa/extension/widget_extension.dart';
import 'package:getx_xiaopa/network/http_client.dart';
import 'package:getx_xiaopa/network/model/request_body.dart';
import 'package:getx_xiaopa/network/model/result.dart';
import 'package:getx_xiaopa/pages/recharge/index.dart';
import 'package:getx_xiaopa/pages/recharge/widgets/recharge_explain_dialog.dart';
import 'package:getx_xiaopa/r.dart';
import 'package:getx_xiaopa/routes/router.dart';
import 'package:getx_xiaopa/storage/user_store.dart';
import 'package:getx_xiaopa/utils/utils.dart';
// ignore: depend_on_referenced_packages
import 'package:in_app_purchase_storekit/in_app_purchase_storekit.dart';

class RechargeController extends GetxController {
  RechargeController();

  StreamSubscription? wxResultSubscription;
  int cIndex = 0;
  int payType = 0;
  List<PackageModel> packageList = [];
  List<PaymentModel> paymentList = [];

  _initData() async {
    if (Platform.isIOS) {
      ///监听iOS内购状态
      bus.on("iosInAppPurchase", _buyResult);
    }

    wxResultSubscription = await WxUtil.onWxResultListener(
        onSuccess: (res) async {
          var result = jsonDecode(res);
          logD("微信SDK回调：$result");

          ///充值成功
          if (result['code'] == 0) {
            await UserStore.to.getAccountItems();
          }
          update(["recharge"]);
        },
        onError: (e) {});

    getPackageList();
    getPaymentList();
  }

  ///iOS内购回调
  _buyResult(res) async {
    CToast.showLoading();
    String receipt = await ReceiptHelper.getReceipt(true);
    logD("下单后：$receipt");
    if (receipt.isEmpty) {
      CToast.showToast("mine_recharge_tips".tr);
      return;
    }

    _orderAppleChenck(res, receipt);
  }

  @override
  void onReady() {
    super.onReady();
    _initData();
  }

  @override
  void onClose() {
    wxResultSubscription?.cancel();
    bus.off("iosInAppPurchase");
    super.onClose();
  }

  getPayImage(String str) {
    if (str == "wepay") {
      return R.recharge_wx_png;
    } else {
      return R.recharge_zfb_png;
    }
  }

  recordPage() {
    Get.toNamed(AppRoutes.RECHARGE_RECORD);
  }

  explanAction() {
    Get.dialog(const RechargeExplainDialog());
  }

  rechargePrice(int index) {
    cIndex = index;
    for (var i = 0; i < packageList.length; i++) {
      packageList[i].isSelect = false;
    }
    packageList[index].isSelect = true;

    update(["recharge"]);
  }

  rechaegeType(int type) {
    payType = type;
    update(["recharge"]);
  }

  payAction() async {
    if (packageList.isEmpty) {
      CToast.showToast("recharge_pay_error_one".tr);
      return;
    }
    if (paymentList.isEmpty) {
      CToast.showToast("recharge_pay_error_two".tr);
      return;
    }
    if (Platform.isAndroid) {
      rechargeOrder();
    } else {
      CToast.showLoading();
      String eRes =
          await BuyEngine().buyProduct(packageList[cIndex].name ?? "");
      if (eRes.isNotEmpty) {
        CToast.dismiss();
        CToast.showToast(eRes);
      }
    }
  }

  ///==================网络请求=======================
  ///充值套餐
  getPackageList() async {
    var map = RequestBody(filters: [
      Filter(expr: "=", name: "status", value: 1),
    ], orders: [
      Order(expr: "asrc", name: "price")
    ], pagination: false)
        .toJson();
    Result result = await http.accountPackageList(map);
    if (result.code == 0) {
      for (var i = 0; i < result.data.data.length; i++) {
        PackageModel model = result.data.data[i];
        if (i == 0) {
          model.isSelect = true;
        }
        packageList.add(model);
      }
    } else {
      CToast.showToast(result.msg!);
    }
    update(["recharge"]);
  }

  ///付款方式
  getPaymentList() async {
    var map = RequestBody(filters: [
      Filter(expr: "=", name: "status", value: 1),
      Filter(expr: "=", name: "is_ios", value: Platform.isIOS ? 1 : 2)
    ], pagination: false)
        .toJson();
    Result result = await http.accountPaymentList(map);
    if (result.code == 0) {
      for (var i = 0; i < result.data.data.length; i++) {
        PaymentModel model = result.data.data[i];
        paymentList.add(model);
      }
    } else {
      CToast.showToast(result.msg!);
    }
    update(["recharge"]);
  }

  ///充值下单
  rechargeOrder() async {
    var map = {
      "product_id": packageList[cIndex].id,
      "payment_id": paymentList[payType].id,
      "order_type": 11,
      "is_mobile": true,
      "is_app": true,
    };
    CToast.showLoading();
    Result result = await http.orderCreate(map);
    if (result.code == 0) {
      CreateOrder createOrder = result.data;
      if (createOrder.orderId != "") {
        ///微信支付
        if (paymentList[payType].code == "wepay") {
          WxUtil.launchWxPay(
              createOrder.app!.appid!,
              createOrder.app!.partnerid!,
              createOrder.app!.prepayid!,
              createOrder.app!.sign!,
              createOrder.app!.package!,
              createOrder.app!.noncestr!,
              createOrder.app!.timestamp!);
        }
      }
    } else {
      CToast.showToast(result.msg!);
    }
  }

  ///ios校验
  _orderAppleChenck(SK2PurchaseDetails sk, String receipt) async {
    var map = {
      "product_id": sk.productID,
      "transaction_id": sk.purchaseID,
      "receipt": receipt
    };
    Result result = await http.orderApple(map);
    if (result.code == 0) {
      BuyEngine().checkApplePayInfo(sk);

      ///重新获取账户余额
      await UserStore.to.getAccountItems();

      update(["recharge"]);
    }
  }
}
