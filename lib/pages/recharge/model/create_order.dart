///
/// Code generated by jsonToDartModel https://ashamp.github.io/jsonToDartModel/
///
class CreateOrderApp {
/*
{
  "appid": "wxbfeaf9ea3b3d297d",
  "partnerid": "1723011302",
  "prepayid": "wx231537388417477d2c9ea25baf52150000",
  "package": "Sign=WXPay",
  "noncestr": "zjGi89dKdY7u1pWvZAQ9Qp2ADPk2xOwr",
  "timestamp": 1753256258,
  "sign": "zm30SqPwU29NgE9Hpl7HBzKmLvqMOQLXnZOekPWb6pHRmarY29pNDkdVXjbQAuwyXOe/fZ5UIKAHU5+KyzMKpFvE8CoYAkTRRJsLCH0JIf2cBrkBOZY6/Q39/hINmb0LOjMptFiiqo1gPvZGOoKn9gCIuCI/fmYxyuBKFL8pzYdZr7nDcz8/iLavTTD6cam8Ouwjvop5wwl3QZXRum2WJYnqHafhhkzYndXBoXQeQ6o4qY8aZ1Xhes3py5VDNj6bsTmRvGL8uKgWOhg9ZDvA6LRLwIFIcoo17P5oG5VXkusmDukXwygsaA9M2qKRYJGjFH9zG1S1kzI1W5M0ugMMVw=="
} 
*/

  String? appid;
  String? partnerid;
  String? prepayid;
  String? package;
  String? noncestr;
  String? timestamp;
  String? sign;

  CreateOrderApp({
    this.appid,
    this.partnerid,
    this.prepayid,
    this.package,
    this.noncestr,
    this.timestamp,
    this.sign,
  });
  CreateOrderApp.fromJson(Map<String, dynamic> json) {
    appid = json['appid']?.toString();
    partnerid = json['partnerid']?.toString();
    prepayid = json['prepayid']?.toString();
    package = json['package']?.toString();
    noncestr = json['noncestr']?.toString();
    timestamp = json['timestamp']?.toString();
    sign = json['sign']?.toString();
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['appid'] = appid;
    data['partnerid'] = partnerid;
    data['prepayid'] = prepayid;
    data['package'] = package;
    data['noncestr'] = noncestr;
    data['timestamp'] = timestamp;
    data['sign'] = sign;
    return data;
  }
}

class CreateOrder {
/*
{
  "order_id": "4992495621505024",
  "qrcode": "",
  "pay_url": "",
  "prepay_id": "wx231537388417477d2c9ea25baf52150000",
  "app": {
    "appid": "wxbfeaf9ea3b3d297d",
    "partnerid": "1723011302",
    "prepayid": "wx231537388417477d2c9ea25baf52150000",
    "package": "Sign=WXPay",
    "noncestr": "zjGi89dKdY7u1pWvZAQ9Qp2ADPk2xOwr",
    "timestamp": 1753256258,
    "sign": "zm30SqPwU29NgE9Hpl7HBzKmLvqMOQLXnZOekPWb6pHRmarY29pNDkdVXjbQAuwyXOe/fZ5UIKAHU5+KyzMKpFvE8CoYAkTRRJsLCH0JIf2cBrkBOZY6/Q39/hINmb0LOjMptFiiqo1gPvZGOoKn9gCIuCI/fmYxyuBKFL8pzYdZr7nDcz8/iLavTTD6cam8Ouwjvop5wwl3QZXRum2WJYnqHafhhkzYndXBoXQeQ6o4qY8aZ1Xhes3py5VDNj6bsTmRvGL8uKgWOhg9ZDvA6LRLwIFIcoo17P5oG5VXkusmDukXwygsaA9M2qKRYJGjFH9zG1S1kzI1W5M0ugMMVw=="
  }
} 
*/

  String? orderId;
  String? qrcode;
  String? payUrl;
  String? prepayId;
  CreateOrderApp? app;

  CreateOrder({
    this.orderId,
    this.qrcode,
    this.payUrl,
    this.prepayId,
    this.app,
  });
  CreateOrder.fromJson(Map<String, dynamic> json) {
    orderId = json['order_id']?.toString();
    qrcode = json['qrcode']?.toString();
    payUrl = json['pay_url']?.toString();
    prepayId = json['prepay_id']?.toString();
    app = (json['app'] != null) ? CreateOrderApp.fromJson(json['app']) : null;
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['order_id'] = orderId;
    data['qrcode'] = qrcode;
    data['pay_url'] = payUrl;
    data['prepay_id'] = prepayId;
    if (app != null) {
      data['app'] = app!.toJson();
    }
    return data;
  }
}
