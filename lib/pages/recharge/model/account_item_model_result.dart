///
/// Code generated by jsonToDartModel https://ashamp.github.io/jsonToDartModel/
///
class AccoundItemModel {
/*
{
  "id": "4977832502493184",
  "created_at": "2025-07-18T14:14:23.29+08:00",
  "updated_at": "2025-07-18T14:14:23.29+08:00",
  "deleted_at": null,
  "name": "PaPa币",
  "code": "points",
  "currency": "PaPa币",
  "currency_abbr": "Pa",
  "currency_symbol": "¥",
  "currency_unit": "个",
  "exchange_rate": 0.01,
  "is_payable": 1,
  "is_recharge": 1,
  "is_cashout": 2,
  "is_transfer": 2,
  "is_minus": 2
} 
*/

  String? id;
  String? createdAt;
  String? updatedAt;
  String? deletedAt;
  String? name;
  String? code;
  String? currency;
  String? currencyAbbr;
  String? currencySymbol;
  String? currencyUnit;
  double? exchangeRate;
  int? isPayable;
  int? isRecharge;
  int? isCashout;
  int? isTransfer;
  int? isMinus;

  AccoundItemModel({
    this.id,
    this.createdAt,
    this.updatedAt,
    this.deletedAt,
    this.name,
    this.code,
    this.currency,
    this.currencyAbbr,
    this.currencySymbol,
    this.currencyUnit,
    this.exchangeRate,
    this.isPayable,
    this.isRecharge,
    this.isCashout,
    this.isTransfer,
    this.isMinus,
  });
  AccoundItemModel.fromJson(Map<String, dynamic> json) {
    id = json['id']?.toString();
    createdAt = json['created_at']?.toString();
    updatedAt = json['updated_at']?.toString();
    deletedAt = json['deleted_at']?.toString();
    name = json['name']?.toString();
    code = json['code']?.toString();
    currency = json['currency']?.toString();
    currencyAbbr = json['currency_abbr']?.toString();
    currencySymbol = json['currency_symbol']?.toString();
    currencyUnit = json['currency_unit']?.toString();
    exchangeRate = json['exchange_rate']?.toDouble();
    isPayable = json['is_payable']?.toInt();
    isRecharge = json['is_recharge']?.toInt();
    isCashout = json['is_cashout']?.toInt();
    isTransfer = json['is_transfer']?.toInt();
    isMinus = json['is_minus']?.toInt();
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['id'] = id;
    data['created_at'] = createdAt;
    data['updated_at'] = updatedAt;
    data['deleted_at'] = deletedAt;
    data['name'] = name;
    data['code'] = code;
    data['currency'] = currency;
    data['currency_abbr'] = currencyAbbr;
    data['currency_symbol'] = currencySymbol;
    data['currency_unit'] = currencyUnit;
    data['exchange_rate'] = exchangeRate;
    data['is_payable'] = isPayable;
    data['is_recharge'] = isRecharge;
    data['is_cashout'] = isCashout;
    data['is_transfer'] = isTransfer;
    data['is_minus'] = isMinus;
    return data;
  }
}

class AccoundItemModelResiltData {
/*
{
  "id": "4992817072963585",
  "created_at": "2025-07-23T18:17:18.765+08:00",
  "updated_at": "2025-07-23T18:17:18.765+08:00",
  "deleted_at": null,
  "status": 1,
  "balance": 120400,
  "salt": "",
  "category_code": "points",
  "category_id": "4977832502493184",
  "category": {
    "id": "4977832502493184",
    "created_at": "2025-07-18T14:14:23.29+08:00",
    "updated_at": "2025-07-18T14:14:23.29+08:00",
    "deleted_at": null,
    "name": "PaPa币",
    "code": "points",
    "currency": "PaPa币",
    "currency_abbr": "Pa",
    "currency_symbol": "¥",
    "currency_unit": "个",
    "exchange_rate": 0.01,
    "is_payable": 1,
    "is_recharge": 1,
    "is_cashout": 2,
    "is_transfer": 2,
    "is_minus": 2
  },
  "user_id": "4928810819518464",
  "user_mobile": "19866726670"
} 
*/

  String? id;
  String? createdAt;
  String? updatedAt;
  String? deletedAt;
  int? status;
  int? balance;
  String? salt;
  String? categoryCode;
  String? categoryId;
  AccoundItemModel? category;
  String? userId;
  String? userMobile;

  AccoundItemModelResiltData({
    this.id,
    this.createdAt,
    this.updatedAt,
    this.deletedAt,
    this.status,
    this.balance,
    this.salt,
    this.categoryCode,
    this.categoryId,
    this.category,
    this.userId,
    this.userMobile,
  });
  AccoundItemModelResiltData.fromJson(Map<String, dynamic> json) {
    id = json['id']?.toString();
    createdAt = json['created_at']?.toString();
    updatedAt = json['updated_at']?.toString();
    deletedAt = json['deleted_at']?.toString();
    status = json['status']?.toInt();
    balance = json['balance']?.toInt();
    salt = json['salt']?.toString();
    categoryCode = json['category_code']?.toString();
    categoryId = json['category_id']?.toString();
    category = (json['category'] != null)
        ? AccoundItemModel.fromJson(json['category'])
        : null;
    userId = json['user_id']?.toString();
    userMobile = json['user_mobile']?.toString();
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['id'] = id;
    data['created_at'] = createdAt;
    data['updated_at'] = updatedAt;
    data['deleted_at'] = deletedAt;
    data['status'] = status;
    data['balance'] = balance;
    data['salt'] = salt;
    data['category_code'] = categoryCode;
    data['category_id'] = categoryId;
    if (category != null) {
      data['category'] = category!.toJson();
    }
    data['user_id'] = userId;
    data['user_mobile'] = userMobile;
    return data;
  }
}

class AccoundItemModelResilt {
/*
{
  "count": 1,
  "pageSize": 10,
  "pageNum": 1,
  "data": [
    {
      "id": "4992817072963585",
      "created_at": "2025-07-23T18:17:18.765+08:00",
      "updated_at": "2025-07-23T18:17:18.765+08:00",
      "deleted_at": null,
      "status": 1,
      "balance": 120400,
      "salt": "",
      "category_code": "points",
      "category_id": "4977832502493184",
      "category": {
        "id": "4977832502493184",
        "created_at": "2025-07-18T14:14:23.29+08:00",
        "updated_at": "2025-07-18T14:14:23.29+08:00",
        "deleted_at": null,
        "name": "PaPa币",
        "code": "points",
        "currency": "PaPa币",
        "currency_abbr": "Pa",
        "currency_symbol": "¥",
        "currency_unit": "个",
        "exchange_rate": 0.01,
        "is_payable": 1,
        "is_recharge": 1,
        "is_cashout": 2,
        "is_transfer": 2,
        "is_minus": 2
      },
      "user_id": "4928810819518464",
      "user_mobile": "19866726670"
    }
  ]
} 
*/

  int? count;
  int? pageSize;
  int? pageNum;
  List<AccoundItemModelResiltData?>? data;

  AccoundItemModelResilt({
    this.count,
    this.pageSize,
    this.pageNum,
    this.data,
  });
  AccoundItemModelResilt.fromJson(Map<String, dynamic> json) {
    count = json['count']?.toInt();
    pageSize = json['pageSize']?.toInt();
    pageNum = json['pageNum']?.toInt();
    if (json['data'] != null) {
      final v = json['data'];
      final arr0 = <AccoundItemModelResiltData>[];
      v.forEach((v) {
        arr0.add(AccoundItemModelResiltData.fromJson(v));
      });
      data = arr0;
    }
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['count'] = count;
    data['pageSize'] = pageSize;
    data['pageNum'] = pageNum;
    if (this.data != null) {
      final v = this.data;
      final arr0 = [];
      for (var v in v!) {
        arr0.add(v!.toJson());
      }
      data['data'] = arr0;
    }
    return data;
  }
}
