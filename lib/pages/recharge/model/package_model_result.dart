///
/// Code generated by jsonToDartModel https://ashamp.github.io/jsonToDartModel/
///
class PackageModel {
/*
{
  "id": "4978120298856449",
  "created_at": "2025-07-18T16:37:20.305+08:00",
  "updated_at": "2025-07-18T16:37:20.305+08:00",
  "deleted_at": null,
  "name": "6000",
  "quota": 6000,
  "price": 6000,
  "category_code": "points",
  "category_id": "4977832502493184",
  "status": 1,
  "is_default": 1,
  "digest": "",
  "sort": 1
} 
*/

  String? id;
  String? createdAt;
  String? updatedAt;
  String? deletedAt;
  String? name;
  int? quota;
  int? price;
  String? categoryCode;
  String? categoryId;
  int? status;
  int? isDefault;
  String? digest;
  String? enDigest;
  int? sort;
  bool? isSelect; //自定义参数

  PackageModel(
      {this.id,
      this.createdAt,
      this.updatedAt,
      this.deletedAt,
      this.name,
      this.quota,
      this.price,
      this.categoryCode,
      this.categoryId,
      this.status,
      this.isDefault,
      this.digest,
      this.enDigest,
      this.sort,
      this.isSelect});

  PackageModel.fromJson(Map<String, dynamic> json) {
    id = json['id']?.toString();
    createdAt = json['created_at']?.toString();
    updatedAt = json['updated_at']?.toString();
    deletedAt = json['deleted_at']?.toString();
    name = json['name']?.toString();
    quota = json['quota']?.toInt();
    price = json['price']?.toInt();
    categoryCode = json['category_code']?.toString();
    categoryId = json['category_id']?.toString();
    status = json['status']?.toInt();
    isDefault = json['is_default']?.toInt();
    digest = json['digest']?.toString();
    enDigest = json['en_digest']?.toString();
    sort = json['sort']?.toInt();
    isSelect = json['isSelect']?.toBool() ?? false;
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['id'] = id;
    data['created_at'] = createdAt;
    data['updated_at'] = updatedAt;
    data['deleted_at'] = deletedAt;
    data['name'] = name;
    data['quota'] = quota;
    data['price'] = price;
    data['category_code'] = categoryCode;
    data['category_id'] = categoryId;
    data['status'] = status;
    data['is_default'] = isDefault;
    data['digest'] = digest;
    data['en_digest'] = enDigest;
    data['sort'] = sort;
    data['isSelect'] = isSelect;
    return data;
  }
}

class PackageModelResult {
/*
{
  "count": 6,
  "pageSize": 10,
  "data": [
    {
      "id": "4978120298856449",
      "created_at": "2025-07-18T16:37:20.305+08:00",
      "updated_at": "2025-07-18T16:37:20.305+08:00",
      "deleted_at": null,
      "name": "6000",
      "quota": 6000,
      "price": 6000,
      "category_code": "points",
      "category_id": "4977832502493184",
      "status": 1,
      "is_default": 1,
      "digest": "",
      "sort": 1
    }
  ]
} 
*/

  int? count;
  int? pageSize;
  List<PackageModel?>? data;

  PackageModelResult({
    this.count,
    this.pageSize,
    this.data,
  });
  PackageModelResult.fromJson(Map<String, dynamic> json) {
    count = json['count']?.toInt();
    pageSize = json['pageSize']?.toInt();
    if (json['data'] != null) {
      final v = json['data'];
      final arr0 = <PackageModel>[];
      v.forEach((v) {
        arr0.add(PackageModel.fromJson(v));
      });
      data = arr0;
    }
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['count'] = count;
    data['pageSize'] = pageSize;
    if (this.data != null) {
      final v = this.data;
      final arr0 = [];
      for (var v in v!) {
        arr0.add(v!.toJson());
      }
      data['data'] = arr0;
    }
    return data;
  }
}
