///
/// Code generated by jsonToDartModel https://ashamp.github.io/jsonToDartModel/
///
class PaymentModel {
/*
{
  "id": "4992056796643328",
  "created_at": "2025-07-23T11:59:40.796+08:00",
  "updated_at": "2025-07-23T12:02:03.172+08:00",
  "deleted_at": null,
  "name": "微信支付",
  "code": "wepay",
  "app_id": "wxbfeaf9ea3b3d297d",
  "app_key": "cE6dG5fQ9vD1tD1wS2uQ6dE7xJ4oQ5mX",
  "password": "",
  "app_public_key": "-----BEGIN PUBLIC KEY----- MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAmNHKgyfQ1AqdlWo0OL+L 6xGDRUKqHhl3TNWv1+kDaqQj3tXrV9Ukpn8wl1i1X+tSzeXAdTC3MGPJdRJXao0W VKzgQhBB5qczJZflv6Xi2lpN1JIC97ixhbncEM6oQPQUjx96pkwNKMzhzZ9HbMrm AeKgl6BAG8fSBEp0CB+HXk+GXuPkLHxlfzxB3DhHoaC3lS9su/gmAku3lfg/d9xX QsPwPVmU6P+qUTytLbvir984gEjqRdH+BdrhgFjsr4aU+aORmnk1N1wYnPdGB9uz w+tcHYwCAWPy3zWvJRL2TkjfiJo5lxkbEYGqbJwuV9Xd7th9KpR4vVdJc0Agk67K bwIDAQAB -----END PUBLIC KEY-----",
  "merchant_id": "**********",
  "merchant_serial_no": "20EBCCBC6870278C79D0FC4DEF39A05B8C37AEA5",
  "cert_file": "upload/account-payment/202507/8bbeac60592c67488b577339951b2024.pem",
  "is_default": 1,
  "status": 1,
  "is_ios": 0,
  "notify_url": "http://test.zerzhi.com/client/order/order/wepay"
} 
*/

  String? id;
  String? createdAt;
  String? updatedAt;
  String? deletedAt;
  String? name;
  String? enName;
  String? code;
  String? appId;
  String? appKey;
  String? password;
  String? appPublicKey;
  String? merchantId;
  String? merchantSerialNo;
  String? certFile;
  int? isDefault;
  int? status;
  int? isIos;
  String? notifyUrl;

  PaymentModel({
    this.id,
    this.createdAt,
    this.updatedAt,
    this.deletedAt,
    this.name,
    this.enName,
    this.code,
    this.appId,
    this.appKey,
    this.password,
    this.appPublicKey,
    this.merchantId,
    this.merchantSerialNo,
    this.certFile,
    this.isDefault,
    this.status,
    this.isIos,
    this.notifyUrl,
  });
  PaymentModel.fromJson(Map<String, dynamic> json) {
    id = json['id']?.toString();
    createdAt = json['created_at']?.toString();
    updatedAt = json['updated_at']?.toString();
    deletedAt = json['deleted_at']?.toString();
    name = json['name']?.toString();
    enName = json['en_name']?.toString();
    code = json['code']?.toString();
    appId = json['app_id']?.toString();
    appKey = json['app_key']?.toString();
    password = json['password']?.toString();
    appPublicKey = json['app_public_key']?.toString();
    merchantId = json['merchant_id']?.toString();
    merchantSerialNo = json['merchant_serial_no']?.toString();
    certFile = json['cert_file']?.toString();
    isDefault = json['is_default']?.toInt();
    status = json['status']?.toInt();
    isIos = json['is_ios']?.toInt();
    notifyUrl = json['notify_url']?.toString();
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['id'] = id;
    data['created_at'] = createdAt;
    data['updated_at'] = updatedAt;
    data['deleted_at'] = deletedAt;
    data['name'] = name;
    data['en_name'] = enName;
    data['code'] = code;
    data['app_id'] = appId;
    data['app_key'] = appKey;
    data['password'] = password;
    data['app_public_key'] = appPublicKey;
    data['merchant_id'] = merchantId;
    data['merchant_serial_no'] = merchantSerialNo;
    data['cert_file'] = certFile;
    data['is_default'] = isDefault;
    data['status'] = status;
    data['is_ios'] = isIos;
    data['notify_url'] = notifyUrl;
    return data;
  }
}

class PaymentModelResult {
/*
{
  "count": 1,
  "pageSize": 10,
  "data": [
    {
      "id": "4992056796643328",
      "created_at": "2025-07-23T11:59:40.796+08:00",
      "updated_at": "2025-07-23T12:02:03.172+08:00",
      "deleted_at": null,
      "name": "微信支付",
      "code": "wepay",
      "app_id": "wxbfeaf9ea3b3d297d",
      "app_key": "cE6dG5fQ9vD1tD1wS2uQ6dE7xJ4oQ5mX",
      "password": "",
      "app_public_key": "-----BEGIN PUBLIC KEY----- MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAmNHKgyfQ1AqdlWo0OL+L 6xGDRUKqHhl3TNWv1+kDaqQj3tXrV9Ukpn8wl1i1X+tSzeXAdTC3MGPJdRJXao0W VKzgQhBB5qczJZflv6Xi2lpN1JIC97ixhbncEM6oQPQUjx96pkwNKMzhzZ9HbMrm AeKgl6BAG8fSBEp0CB+HXk+GXuPkLHxlfzxB3DhHoaC3lS9su/gmAku3lfg/d9xX QsPwPVmU6P+qUTytLbvir984gEjqRdH+BdrhgFjsr4aU+aORmnk1N1wYnPdGB9uz w+tcHYwCAWPy3zWvJRL2TkjfiJo5lxkbEYGqbJwuV9Xd7th9KpR4vVdJc0Agk67K bwIDAQAB -----END PUBLIC KEY-----",
      "merchant_id": "**********",
      "merchant_serial_no": "20EBCCBC6870278C79D0FC4DEF39A05B8C37AEA5",
      "cert_file": "upload/account-payment/202507/8bbeac60592c67488b577339951b2024.pem",
      "is_default": 1,
      "status": 1,
      "is_ios": 0,
      "notify_url": "http://test.zerzhi.com/client/order/order/wepay"
    }
  ]
} 
*/

  int? count;
  int? pageSize;
  List<PaymentModel?>? data;

  PaymentModelResult({
    this.count,
    this.pageSize,
    this.data,
  });
  PaymentModelResult.fromJson(Map<String, dynamic> json) {
    count = json['count']?.toInt();
    pageSize = json['pageSize']?.toInt();
    if (json['data'] != null) {
      final v = json['data'];
      final arr0 = <PaymentModel>[];
      v.forEach((v) {
        arr0.add(PaymentModel.fromJson(v));
      });
      data = arr0;
    }
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['count'] = count;
    data['pageSize'] = pageSize;
    if (this.data != null) {
      final v = this.data;
      final arr0 = [];
      for (var v in v!) {
        arr0.add(v!.toJson());
      }
      data['data'] = arr0;
    }
    return data;
  }
}
