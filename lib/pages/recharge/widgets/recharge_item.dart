import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:getx_xiaopa/extension/images.dart';
import 'package:getx_xiaopa/extension/widget_extension.dart';
import 'package:getx_xiaopa/pages/recharge/model/package_model_result.dart';
import 'package:getx_xiaopa/r.dart';
import 'package:getx_xiaopa/storage/storage.dart';
import 'package:getx_xiaopa/utils/utils.dart';
import 'package:getx_xiaopa/values/values.dart';

class RechargeItem extends StatelessWidget {
  final int cIndex;
  final PackageModel packageModel;
  final Function(int) onTap;

  const RechargeItem(
      {super.key,
      required this.packageModel,
      required this.cIndex,
      required this.onTap});

  String _formatDouble(double value) {
    return value == value.truncateToDouble()
        ? value.toInt().toString()
        : value.toString();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 96.w,
      height: 120.h,
      padding: EdgeInsets.all(packageModel.isSelect ?? false ? 1.5.r : 0),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8.r),
        gradient: const LinearGradient(
          colors: [
            Color.fromRGBO(255, 96, 141, 1),
            Color.fromRGBO(255, 196, 47, 1)
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
      ),
      child: Stack(
        clipBehavior: Clip.none,
        children: [
          Container(
            width: 96.w,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(6.5.r),
              gradient: const LinearGradient(
                colors: [
                  Color.fromRGBO(255, 253, 247, 1),
                  Color.fromRGBO(255, 255, 255, 1)
                ],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              boxShadow: const [
                BoxShadow(
                  color: Color.fromRGBO(255, 183, 9, 0.10),
                  // 阴影颜色和透明度
                  spreadRadius: 0,
                  // 阴影扩散范围
                  blurRadius: 10,
                  // 阴影模糊程度
                  offset: Offset(0, 2),
                  // 阴影偏移量（水平，垂直）
                )
              ],
            ),
            child: Column(
              children: [
                SizedBox(height: 16.h),
                Images(
                  path: R.papa_image_png,
                  width: 28.r,
                  height: 28.r,
                ),
                SizedBox(height: 3.h),
                Text(
                  '${packageModel.quota}',
                  style: StyleConfig.otherStyle(
                      color: ColorConfig.searchTextColor,
                      fontWeight: FontWeight.w700),
                ),
                SizedBox(height: 10.h),
                Container(
                  margin: EdgeInsets.only(bottom: 5.h),
                  width: 76.w,
                  height: 27.h,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(6.r),
                    gradient: LinearGradient(
                      colors: [
                        Get.find<ThemeColorController>().gRechargeGradientOne,
                        Get.find<ThemeColorController>().gRechargeGradientTwo
                      ],
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                    ),
                    boxShadow: const [
                      BoxShadow(
                        color: Color.fromRGBO(255, 211, 9, 0.13),
                        // 阴影颜色和透明度
                        spreadRadius: 0,
                        // 阴影扩散范围
                        blurRadius: 3,
                        // 阴影模糊程度
                        offset: Offset(0, 1),
                        // 阴影偏移量（水平，垂直）
                      )
                    ],
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Container(
                        margin: EdgeInsets.only(top: 5.h),
                        child: Text(
                          "recharge_symbol".tr,
                          style: StyleConfig.otherStyle(
                              color: ColorConfig.searchTextColor,
                              fontWeight: FontWeight.w400,
                              fontSize: 10),
                        ),
                      ),
                      Text(
                        _formatDouble(packageModel.price! / 100),
                        style: StyleConfig.otherStyle(
                          color: ColorConfig.searchTextColor,
                          fontWeight: FontWeight.w700,
                        ),
                      ),
                    ],
                  ),
                )
              ],
            ),
          ),
          Positioned(
            top: -4.h,
            left: -2.h,
            child: packageModel.digest!.isEmpty
                ? const SizedBox()
                : Container(
                    padding:
                        EdgeInsets.only(bottom: 3.h, left: 2.w, right: 5.w),
                    height: 20.h,
                    decoration: const BoxDecoration(
                      image: DecorationImage(
                          image: AssetImage(R.rechage_sign_bg_png),
                          fit: BoxFit.fill),
                    ),
                    child: Center(
                      child: Text(
                        ConfigStore.to.getLocaleCode() == "zh"
                            ? packageModel.digest ?? ''
                            : packageModel.enDigest ?? '',
                        style: StyleConfig.otherStyle(
                            color: Colors.white,
                            fontWeight: FontWeight.w500,
                            fontSize: 10),
                      ),
                    ),
                  ),
          ),
        ],
      ),
    ).inkWell(() => onTap(cIndex));
  }
}
