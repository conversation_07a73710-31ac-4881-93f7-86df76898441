import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:getx_xiaopa/base/base.dart';
import 'package:getx_xiaopa/extension/images.dart';
import 'package:getx_xiaopa/extension/widget_extension.dart';
import 'package:getx_xiaopa/pages/recharge/widgets/recharge_item.dart';
import 'package:getx_xiaopa/r.dart';
import 'package:getx_xiaopa/storage/storage.dart';
import 'package:getx_xiaopa/utils/utils.dart';
import 'package:getx_xiaopa/values/values.dart';
import 'package:getx_xiaopa/widget/widget.dart';

import 'index.dart';

class RechargePage extends BaseCommonView<RechargeController> {
  RechargePage({super.key});

  @override
  String? get navTitle => "recharge_title".tr;

  @override
  TextStyle? get navTitleStyle =>
      StyleConfig.blackStyle(fontSize: 18, fontWeight: FontWeight.w600);

  @override
  Color? get navColor => Colors.transparent;

  @override
  double? get leftWidth => 35.w;

  @override
  Widget? get leftButton => Padding(
        padding: EdgeInsets.only(left: 15.w),
        child: Images(
          path: R.back_png,
          width: 20.w,
          height: 20.h,
          scale: 2.8,
        ).inkWell(() => Get.back()),
      );

  @override
  bool? get extendBodyBehindAppBar => true;

  @override
  Color? get contentColor => Colors.white;

  // 主视图
  Widget _buildView() {
    return Column(
      children: [
        Expanded(
          child: SingleChildScrollView(
            child: Column(
              children: [
                SizedBox(
                  width: 1.sw,
                  height: 537.h,
                  child: Stack(
                    children: [
                      Positioned(
                        width: 1.sw,
                        height: 327.h,
                        child: const ThemeImagePath(
                          fileName: 'recharge_bg.png',
                          fit: BoxFit.fill,
                        ),
                      ),
                      Positioned(
                        top: 122.h,
                        left: 28.w,
                        child: Row(
                          children: [
                            Images(
                                path: R.papa_image_png,
                                width: 24.r,
                                height: 24.r),
                            SizedBox(width: 7.w),
                            Text(
                              "recharge_title".tr,
                              style: StyleConfig.otherStyle(
                                  color: ColorConfig.searchTextColor,
                                  fontWeight: FontWeight.w500),
                            ),
                            SizedBox(width: 6.w),
                            Images(
                              path: R.mine_right_png,
                              width: 8.w,
                              height: 14.h,
                              color: const Color.fromRGBO(180, 142, 78, 1),
                            )
                          ],
                        ).inkWell(() => controller.recordPage()),
                      ),
                      Positioned(
                        top: 149.h,
                        left: 32.w,
                        child: Row(
                          children: [
                            Text(
                              "${UserStore.to.balance}",
                              style: StyleConfig.otherStyle(
                                  color: ColorConfig.searchTextColor,
                                  fontWeight: FontWeight.w700,
                                  fontSize: 30),
                            ),
                            SizedBox(width: 2.w),
                            Container(
                              margin: EdgeInsets.only(top: 5.h),
                              child: Text(
                                "recharge_unit".tr,
                                style: StyleConfig.otherStyle(
                                    color:
                                        const Color.fromRGBO(180, 142, 78, 1),
                                    fontSize: 12,
                                    fontWeight: FontWeight.w500),
                              ),
                            )
                          ],
                        ),
                      ),
                      Positioned(
                        top: 205.h,
                        left: 7.w,
                        right: 7.w,
                        height: 332.h,
                        child: ThemeContainerImage(
                          padding: EdgeInsets.only(
                              top: 22.h, left: 23.w, right: 23.w),
                          fileName: 'rechagre_bg_002.png',
                          fit: BoxFit.fill,
                          child: Column(
                            children: [
                              Row(
                                children: [
                                  Text(
                                    "recharge_tips".tr,
                                    style: StyleConfig.otherStyle(
                                        color: ColorConfig.searchTextColor,
                                        fontWeight: FontWeight.w600),
                                  ),
                                  SizedBox(width: 6.w),
                                  ThemeImagePath(
                                    fileName: 'console_sound_explan.png',
                                    imgWidget: 18.r,
                                    imgHeight: 18.r,
                                  ).inkWell(() => controller.explanAction())
                                ],
                              ),
                              SizedBox(height: 16.h),
                              Wrap(
                                spacing: 10.w,
                                runSpacing: 10.h,
                                children: List.generate(
                                  controller.packageList.length,
                                  (index) {
                                    return RechargeItem(
                                      cIndex: index,
                                      packageModel:
                                          controller.packageList[index],
                                      onTap: (cIndex) =>
                                          controller.rechargePrice(cIndex),
                                    );
                                  },
                                ),
                              )
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                Offstage(
                  offstage: Platform.isIOS,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Container(
                        margin: EdgeInsets.only(left: 15.w),
                        child: Text(
                          'recharge_pay_type'.tr,
                          style: StyleConfig.otherStyle(
                              color: ColorConfig.searchTextColor,
                              fontWeight: FontWeight.w600),
                        ),
                      ),
                      Container(
                        margin: EdgeInsets.only(top: 10.h),
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(10.r),
                          color: Colors.white,
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withValues(alpha: 0.06),
                              // 阴影颜色和透明度
                              spreadRadius: 0,
                              // 阴影扩散范围
                              blurRadius: 10,
                              // 阴影模糊程度
                              offset: const Offset(0, 2),
                              // 阴影偏移量（水平，垂直）
                            )
                          ],
                        ),
                        child: Column(
                          children: List.generate(controller.paymentList.length,
                              (index) {
                            return Column(
                              children: [
                                Container(
                                  padding:
                                      EdgeInsets.only(left: 17.w, right: 26.w),
                                  width: 345.w,
                                  height: 58.h,
                                  child: Row(
                                    children: [
                                      Images(
                                        path: controller.getPayImage(controller
                                            .paymentList[index].code!),
                                        width: 24.r,
                                        height: 24.r,
                                      ),
                                      SizedBox(width: 6.w),
                                      Text(
                                        ConfigStore.to.getLocaleCode() == "zh"
                                            ? controller
                                                    .paymentList[index].name ??
                                                ''
                                            : controller.paymentList[index]
                                                    .enName ??
                                                '',
                                        style: StyleConfig.otherStyle(
                                            color: ColorConfig.searchTextColor,
                                            fontWeight: FontWeight.w400),
                                      ),
                                      const Expanded(child: SizedBox()),
                                      CheckBoxRounded(
                                        isGroup: true,
                                        value: index,
                                        groupValue: controller.payType,
                                        checkedColor:
                                            Get.find<ThemeColorController>()
                                                .getColor('textColor'),
                                        checkedBgColor:
                                            ColorConfig.searchTextColor,
                                        uncheckedBgColor: Colors.white,
                                        borderColor: const Color.fromRGBO(
                                            215, 215, 215, 1),
                                        onGroupTap: (sign) =>
                                            controller.rechaegeType(sign!),
                                      )
                                    ],
                                  ),
                                ).inkWell(() => controller.rechaegeType(index)),
                                (index + 1) < controller.paymentList.length
                                    ? Container(
                                        width: 305.w,
                                        height: 1.h,
                                        color: const Color.fromRGBO(
                                            233, 232, 233, 1),
                                      )
                                    : const SizedBox()
                              ],
                            );
                          }),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
        Container(
          margin: EdgeInsets.only(bottom: ScreenUtil().statusBarHeight + 20.h),
          width: 283.w,
          height: 48.h,
          decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(24.r),
              color: ColorConfig.searchTextColor),
          child: Center(
            child: ThemeText(
              dataStr: "recharge_pay_btn".tr,
              keyName: 'textColor',
              fontSize: 16,
              fontWeight: FontWeight.w700,
            ),
          ),
        ).inkWell(() => controller.payAction()),
      ],
    );
  }

  @override
  Widget buildContent() {
    return GetBuilder<RechargeController>(
      init: RechargeController(),
      id: "recharge",
      builder: (_) {
        return _buildView();
      },
    );
  }
}
