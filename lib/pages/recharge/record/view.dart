import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:getx_xiaopa/base/base_list_view.dart';
import 'package:getx_xiaopa/extension/images.dart';
import 'package:getx_xiaopa/extension/widget_extension.dart';
import 'package:getx_xiaopa/pages/recharge/record/widgets/record_item.dart';
import 'package:getx_xiaopa/r.dart';
import 'package:getx_xiaopa/storage/storage.dart';
import 'package:getx_xiaopa/values/values.dart';

import 'index.dart';

class RecordPage extends BaseListView<RecordController> {
  RecordPage({super.key});

  @override
  String? get navTitle => "recharge_record".tr;

  @override
  TextStyle? get navTitleStyle =>
      StyleConfig.blackStyle(fontSize: 18, fontWeight: FontWeight.w600);

  @override
  Color? get navColor => Colors.white;

  @override
  double? get leftWidth => 35.w;

  @override
  Widget? get leftButton => Padding(
        padding: EdgeInsets.only(left: 15.w),
        child: Images(
          path: R.back_png,
          width: 20.w,
          height: 20.h,
          scale: 2.8,
        ).inkWell(() => Get.back()),
      );

  @override
  Color? get contentColor => const Color.fromRGBO(249, 249, 249, 1);

  // 主视图
  Widget _buildView() {
    return Column(
      children: [
        Container(
          margin: EdgeInsets.only(left: 16.w, top: 20.h, bottom: 20.h),
          child: Row(
            children: [
              Images(
                path: R.papa_image_png,
                width: 24.r,
                height: 24.r,
              ),
              SizedBox(width: 6.w),
              Text(
                '${"recharge_record_one".tr}${UserStore.to.balance}',
                style: StyleConfig.otherStyle(
                    color: ColorConfig.searchTextColor,
                    fontWeight: FontWeight.w700),
              )
            ],
          ),
        ),
        Expanded(
          child: Container(
            margin:
                EdgeInsets.only(bottom: ScreenUtil().bottomBarHeight + 20.h),
            color: Colors.white,
            child: createRefresherListView(
              controller,
              (_) {
                return ListView.builder(
                  itemCount: controller.flowItems.length,
                  itemBuilder: (_, index) {
                    return RecordItem(model: controller.flowItems[index]);
                  },
                );
              },
            ),
          ),
        )
      ],
    );
  }

  @override
  Widget buildContent() {
    return GetBuilder<RecordController>(
      init: RecordController(),
      id: "record",
      builder: (_) {
        return _buildView();
      },
    );
  }
}
