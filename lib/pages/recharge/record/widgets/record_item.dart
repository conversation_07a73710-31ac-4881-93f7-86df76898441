import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:getx_xiaopa/pages/recharge/record/model/account_flow_items_model.dart';
import 'package:getx_xiaopa/utils/utils.dart';
import 'package:getx_xiaopa/values/values.dart';
import 'package:getx_xiaopa/widget/theme_image.dart';

class RecordItem extends StatelessWidget {
  final AccoundFlowItemsModel model;

  const RecordItem({super.key, required this.model});

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.only(top: 15.h, left: 15.w, right: 15.w),
      width: 345.w,
      height: 57.h,
      child: Column(
        children: [
          Row(
            children: [
              ThemeImagePath(
                fileName: 'recharge_record_icon.png',
                imgWidget: 40.r,
                imgHeight: 40.r,
              ),
              SizedBox(width: 10.w),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    model.name ?? "",
                    style: StyleConfig.otherStyle(
                        color: ColorConfig.searchTextColor,
                        fontWeight: FontWeight.w500),
                  ),
                  SizedBox(height: 2.w),
                  Text(
                    TimeUtil.getSimpleDate(model.createdAt),
                    style: StyleConfig.otherStyle(
                        color: ColorConfig.shopDetailTextColor,
                        fontWeight: FontWeight.w400,
                        fontSize: 12),
                  ),
                ],
              ),
              const Expanded(child: SizedBox()),
              Text(
                model.type == 1 ? '+${model.amount}' : '-${model.amount}',
                style: StyleConfig.otherStyle(
                    color: model.type == 1
                        ? const Color.fromRGBO(253, 105, 82, 1)
                        : ColorConfig.searchTextColor,
                    fontWeight: FontWeight.w600),
              ),
              SizedBox(width: 5.w),
            ],
          ),
          const Expanded(child: SizedBox()),
          Container(
            width: 345.w,
            height: 1.h,
            color: const Color.fromRGBO(232, 232, 232, 1),
          )
        ],
      ),
    );
  }
}
