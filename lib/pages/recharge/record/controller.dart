import 'package:getx_xiaopa/base/base.dart';
import 'package:getx_xiaopa/extension/widget_extension.dart';
import 'package:getx_xiaopa/network/http_client.dart';
import 'package:getx_xiaopa/network/model/request_body.dart';
import 'package:getx_xiaopa/network/model/result.dart';
import 'package:getx_xiaopa/pages/recharge/record/model/account_flow_items_model.dart';

class RecordController extends BaseListController {
  RecordController();

  List<AccoundFlowItemsModel> flowItems = [];

  @override
  void initData() {
    CToast.showLoading();
    _getFlowItems();
  }

  @override
  void onHidden() {}

  ///=======================网络请求=================
  @override
  void refreshData() {
    super.refreshData();
    page = 1;
    _getFlowItems();
  }

  @override
  void loadMore() {
    super.loadMore();
    page++;
    _getFlowItems();
  }

  _getFlowItems() async {
    var map = RequestBody(pageNum: page).toJson();
    Result result = await http.accountFlowList(map);
    if (result.code == 0) {
      if (page == 1) {
        flowItems.clear();
        for (int i = 0; i < result.data.data.length; i++) {
          AccoundFlowItemsModel model = result.data.data[i];
          flowItems.add(model);
        }
        refreshController.refreshCompleted();
        refreshController.loadComplete();
      } else {
        for (int i = 0; i < result.data.data.length; i++) {
          AccoundFlowItemsModel model = result.data.data[i];
          flowItems.add(model);
        }
        refreshController.loadComplete();
      }

      if (result.data.data.length < 10 && flowItems.isNotEmpty) {
        refreshController.loadNoData();
      }
    } else {
      CToast.showToast(result.msg!);
    }
    netState = NetState.dataSuccessState;
    update(["record"]);
  }
}
