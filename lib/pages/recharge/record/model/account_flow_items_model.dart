///
/// Code generated by jsonToDartModel https://ashamp.github.io/jsonToDartModel/
///
class AccoundFlowItemsModel {
/*
{
  "id": "****************",
  "created_at": "2025-07-25T17:25:08.323+08:00",
  "updated_at": "2025-07-25T17:25:08.323+08:00",
  "deleted_at": null,
  "name": "P28",
  "order_id": "****************",
  "account_id": "****************",
  "category_id": "****************",
  "payment_id": "",
  "type": 1,
  "trade_type": 1,
  "amount": 2800,
  "balance": 204400,
  "salt": "660d9566ec0155848747cb41ad85e0ab",
  "user_id": "****************",
  "user_mobile": "***********"
} 
*/

  String? id;
  String? createdAt;
  String? updatedAt;
  String? deletedAt;
  String? name;
  String? orderId;
  String? accountId;
  String? categoryId;
  String? paymentId;
  int? type;
  int? tradeType;
  int? amount;
  int? balance;
  String? salt;
  String? userId;
  String? userMobile;

  AccoundFlowItemsModel({
    this.id,
    this.createdAt,
    this.updatedAt,
    this.deletedAt,
    this.name,
    this.orderId,
    this.accountId,
    this.categoryId,
    this.paymentId,
    this.type,
    this.tradeType,
    this.amount,
    this.balance,
    this.salt,
    this.userId,
    this.userMobile,
  });
  AccoundFlowItemsModel.fromJson(Map<String, dynamic> json) {
    id = json['id']?.toString();
    createdAt = json['created_at']?.toString();
    updatedAt = json['updated_at']?.toString();
    deletedAt = json['deleted_at']?.toString();
    name = json['name']?.toString();
    orderId = json['order_id']?.toString();
    accountId = json['account_id']?.toString();
    categoryId = json['category_id']?.toString();
    paymentId = json['payment_id']?.toString();
    type = json['type']?.toInt();
    tradeType = json['trade_type']?.toInt();
    amount = json['amount']?.toInt();
    balance = json['balance']?.toInt();
    salt = json['salt']?.toString();
    userId = json['user_id']?.toString();
    userMobile = json['user_mobile']?.toString();
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['id'] = id;
    data['created_at'] = createdAt;
    data['updated_at'] = updatedAt;
    data['deleted_at'] = deletedAt;
    data['name'] = name;
    data['order_id'] = orderId;
    data['account_id'] = accountId;
    data['category_id'] = categoryId;
    data['payment_id'] = paymentId;
    data['type'] = type;
    data['trade_type'] = tradeType;
    data['amount'] = amount;
    data['balance'] = balance;
    data['salt'] = salt;
    data['user_id'] = userId;
    data['user_mobile'] = userMobile;
    return data;
  }
}

class AccoundFlowItemsModelResult {
/*
{
  "count": 73,
  "pageSize": 10,
  "pageNum": 1,
  "data": [
    {
      "id": "****************",
      "created_at": "2025-07-25T17:25:08.323+08:00",
      "updated_at": "2025-07-25T17:25:08.323+08:00",
      "deleted_at": null,
      "name": "P28",
      "order_id": "****************",
      "account_id": "****************",
      "category_id": "****************",
      "payment_id": "",
      "type": 1,
      "trade_type": 1,
      "amount": 2800,
      "balance": 204400,
      "salt": "660d9566ec0155848747cb41ad85e0ab",
      "user_id": "****************",
      "user_mobile": "***********"
    }
  ]
} 
*/

  int? count;
  int? pageSize;
  int? pageNum;
  List<AccoundFlowItemsModel?>? data;

  AccoundFlowItemsModelResult({
    this.count,
    this.pageSize,
    this.pageNum,
    this.data,
  });
  AccoundFlowItemsModelResult.fromJson(Map<String, dynamic> json) {
    count = json['count']?.toInt();
    pageSize = json['pageSize']?.toInt();
    pageNum = json['pageNum']?.toInt();
    if (json['data'] != null) {
      final v = json['data'];
      final arr0 = <AccoundFlowItemsModel>[];
      v.forEach((v) {
        arr0.add(AccoundFlowItemsModel.fromJson(v));
      });
      data = arr0;
    }
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['count'] = count;
    data['pageSize'] = pageSize;
    data['pageNum'] = pageNum;
    if (this.data != null) {
      final v = this.data;
      final arr0 = [];
      for (var v in v!) {
        arr0.add(v!.toJson());
      }
      data['data'] = arr0;
    }
    return data;
  }
}
