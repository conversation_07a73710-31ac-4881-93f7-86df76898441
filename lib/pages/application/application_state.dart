import 'package:flutter/material.dart';
import 'package:getx_xiaopa/pages/ai_chat/ai_chat_view.dart';
import 'package:getx_xiaopa/pages/console/console_view.dart';
import 'package:getx_xiaopa/pages/diary/diary_view.dart';
import 'package:getx_xiaopa/pages/mine/mine_view.dart';

class ApplicationState {
  List<Widget> tabPageList = [];
  TabController? tController;
  PageController? pController = PageController();
  int tabIndex = 0;

  DateTime? lastPopTime;

  List<BottomNavigationBarItem> bottomNavigationBarItemList = [];

  final ConsolePage console = ConsolePage();
  AiChatPage? aiChat;
  final DiaryPage diary = DiaryPage();

  final MinePage mine = MinePage();

  ApplicationState() {
    ///Initialize variables
  }
}
