import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:getx_xiaopa/base/base.dart';
import 'package:getx_xiaopa/values/values.dart';
import 'package:getx_xiaopa/widget/load_status_widget.dart';

import 'application_logic.dart';

class ApplicationPage extends BaseCommonView {
  ApplicationPage({super.key});

  final logic = Get.put(ApplicationLogic());
  final state = Get.find<ApplicationLogic>().state;

  @override
  bool? get isHiddenNav => true;

  @override
  Widget buildContent() {
    return GetBuilder<ApplicationLogic>(
      id: "application",
      builder: (_) => createCommonView(
        logic,
        (_) {
          return AnnotatedRegion<SystemUiOverlayStyle>(
            value: const SystemUiOverlayStyle(
              statusBarColor: Colors.transparent,
              statusBarIconBrightness: Brightness.light,
            ),
            child: PopScope(
              canPop: false,
              onPopInvokedWithResult: (bool didPop, result) async {
                if (didPop) return;
                var flag = logic.onWillPop();
                if (flag) {
                  // 退出app
                  // exit(0);
                  SystemNavigator.pop();
                }
              },
              child: DefaultTabController(
                length: state.tabPageList.length,
                child: Scaffold(
                  bottomNavigationBar: Container(
                    height: ScreenUtil().bottomBarHeight + 60.h,
                    decoration: BoxDecoration(
                        borderRadius: BorderRadius.only(
                            topLeft: Radius.circular(30.r),
                            topRight: Radius.circular(30.r)),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withValues(alpha: 0.14),
                            // 阴影颜色和透明度
                            spreadRadius: 0,
                            // 阴影扩散范围
                            blurRadius: 5,
                            // 阴影模糊程度
                            offset: const Offset(0, 2),
                            // 阴影偏移量（水平，垂直）
                          )
                        ]),
                    // clipBehavior: Clip.antiAlias,
                    child: BottomNavigationBar(
                        items: state.bottomNavigationBarItemList,
                        currentIndex: state.tabIndex,
                        selectedItemColor: ColorConfig.searchTextColor,
                        unselectedItemColor: ColorConfig.shopDetailTextColor,
                        type: BottomNavigationBarType.fixed,
                        backgroundColor: Colors.white,
                        selectedLabelStyle: StyleConfig.otherStyle(
                            color: ColorConfig.searchTextColor, fontSize: 11),
                        unselectedLabelStyle: StyleConfig.otherStyle(
                            color: ColorConfig.shopDetailTextColor,
                            fontSize: 10),
                        elevation: 0,
                        onTap: (int index) => logic.tChange(index)),
                  ),
                  body: PageView(
                    physics: const NeverScrollableScrollPhysics(),
                    controller: state.pController,
                    children: state.tabPageList,
                    onPageChanged: (index) => logic.pChange(index),
                  ),
                ),
              ),
            ),
          );
        },
        initBuilder: () => const LoadStatusWidget(),
      ),
    );
  }
}
