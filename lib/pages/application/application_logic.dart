import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:getx_xiaopa/base/base.dart';
import 'package:getx_xiaopa/extension/images.dart';
import 'package:getx_xiaopa/extension/widget_extension.dart';
import 'package:getx_xiaopa/pages/ai_chat/ai_chat_logic.dart';
import 'package:getx_xiaopa/pages/ai_chat/ai_chat_view.dart';
import 'package:getx_xiaopa/pages/console/console_logic.dart';
import 'package:getx_xiaopa/r.dart';
import 'package:getx_xiaopa/storage/storage.dart';
import 'package:getx_xiaopa/widget/widget.dart';

import 'application_state.dart';

class ApplicationLogic extends BaseCommonController
    with GetTickerProviderStateMixin {
  @override
  final ApplicationState state = ApplicationState();

  @override
  void initData() {
    _initData();
  }

  @override
  void onHidden() {}

  @override
  void onResumed() async {
    super.onResumed();
    if (CommonStore.to.showAichat == 1) {
      if (state.tabIndex == 1) {
        if (!Get.isRegistered<AiChatLogic>()) {
          Get.put(AiChatLogic());
        }
        var aiLogic = Get.find<AiChatLogic>();
        aiLogic.startPlay();
      }
    }

    if (UserStore.to.deviceList.isNotEmpty) {
      Get.find<ConsoleLogic>().deviceDetail();
    }
  }

  @override
  void onPaused() async {
    super.onPaused();
    if (CommonStore.to.showAichat == 1) {
      if (state.tabIndex == 1) {
        if (!Get.isRegistered<AiChatLogic>()) {
          Get.put(AiChatLogic());
        }
        var aiLogic = Get.find<AiChatLogic>();
        aiLogic.stopPlay(isLeaveRoom: true);
      }
    }
  }

  _initData() {
    if (UserStore.to.deviceList.isEmpty) {
      if (state.aiChat != null) {
        Get.find<AiChatLogic>().resetBrain();
      }
      state.bottomNavigationBarItemList = [
        BottomNavigationBarItem(
          icon: Images(
            path: R.u_console_png,
            width: 24.r,
            height: 24.r,
          ),
          activeIcon: ThemeImagePath(
            fileName: "s_console.png",
            imgWidget: 24.r,
            imgHeight: 24.r,
          ),
          label: "console".tr,
        ),
        BottomNavigationBarItem(
          icon: Images(
            path: R.u_diary_png,
            width: 24.r,
            height: 24.r,
          ),
          activeIcon: ThemeImagePath(
            fileName: "s_diary.png",
            imgWidget: 24.r,
            imgHeight: 24.r,
          ),
          label: "diary".tr,
        ),
        BottomNavigationBarItem(
            icon: Images(
              path: R.u_mine_png,
              width: 24.r,
              height: 24.r,
            ),
            activeIcon: ThemeImagePath(
              fileName: "s_mine.png",
              imgWidget: 24.r,
              imgHeight: 24.r,
            ),
            label: "mine".tr)
      ];
      state.tabPageList = [
        KeepAlivePage(state.console),
        KeepAlivePage(state.diary),
        KeepAlivePage(state.mine)
      ];
    } else {
      if (CommonStore.to.showAichat == 1) {
        state.aiChat ??= AiChatPage();
        state.bottomNavigationBarItemList = [
          BottomNavigationBarItem(
            icon: Images(
              path: R.u_console_png,
              width: 24.r,
              height: 24.r,
            ),
            activeIcon: ThemeImagePath(
              fileName: "s_console.png",
              imgWidget: 24.r,
              imgHeight: 24.r,
            ),
            label: "console".tr,
          ),
          BottomNavigationBarItem(
            icon: Images(
              path: R.u_brain_png,
              width: 24.r,
              height: 24.r,
            ),
            activeIcon: ThemeImagePath(
              fileName: "s_brain.png",
              imgWidget: 24.r,
              imgHeight: 24.r,
            ),
            label: "chat".tr,
          ),
          BottomNavigationBarItem(
            icon: Images(
              path: R.u_diary_png,
              width: 24.r,
              height: 24.r,
            ),
            activeIcon: ThemeImagePath(
              fileName: "s_diary.png",
              imgWidget: 24.r,
              imgHeight: 24.r,
            ),
            label: "diary".tr,
          ),
          BottomNavigationBarItem(
              icon: Images(
                path: R.u_mine_png,
                width: 24.r,
                height: 24.r,
              ),
              activeIcon: ThemeImagePath(
                fileName: "s_mine.png",
                imgWidget: 24.r,
                imgHeight: 24.r,
              ),
              label: "mine".tr)
        ];
        state.tabPageList = [
          KeepAlivePage(state.console),
          KeepAlivePage(state.aiChat!),
          KeepAlivePage(state.diary),
          KeepAlivePage(state.mine)
        ];
      } else {
        state.bottomNavigationBarItemList = [
          BottomNavigationBarItem(
            icon: Images(
              path: R.u_console_png,
              width: 24.r,
              height: 24.r,
            ),
            activeIcon: ThemeImagePath(
              fileName: "s_console.png",
              imgWidget: 24.r,
              imgHeight: 24.r,
            ),
            label: "console".tr,
          ),
          BottomNavigationBarItem(
            icon: Images(
              path: R.u_diary_png,
              width: 24.r,
              height: 24.r,
            ),
            activeIcon: ThemeImagePath(
              fileName: "s_diary.png",
              imgWidget: 24.r,
              imgHeight: 24.r,
            ),
            label: "diary".tr,
          ),
          BottomNavigationBarItem(
              icon: Images(
                path: R.u_mine_png,
                width: 24.r,
                height: 24.r,
              ),
              activeIcon: ThemeImagePath(
                fileName: "s_mine.png",
                imgWidget: 24.r,
                imgHeight: 24.r,
              ),
              label: "mine".tr)
        ];
        state.tabPageList = [
          KeepAlivePage(state.console),
          KeepAlivePage(state.diary),
          KeepAlivePage(state.mine)
        ];
      }
    }
    netState = NetState.dataSuccessState;
    state.tController =
        TabController(length: state.tabPageList.length, vsync: this);
    update(["application"]);
  }

  tChange(index, {bool isShow = false}) async {
    state.tabIndex = index;
    if (state.pController!.hasClients) {
      state.pController?.safeJumpToPage(index);
    }

    if (UserStore.to.deviceList.isNotEmpty) {
      ///如果是ios或者已发布
      if (CommonStore.to.showAichat == 1) {
        if (!Get.isRegistered<AiChatLogic>()) {
          Get.put(AiChatLogic());
        }
        if (index == 1) {
          var aiLogic = Get.find<AiChatLogic>();
          aiLogic.startPlay();
        } else {
          var aiLogic = Get.find<AiChatLogic>();
          aiLogic.stopPlay(isLeaveRoom: true);
        }
      }
    }

    ///心情页面点击查看，如果没有绑定设备，则弹出绑定设备提示
    if (isShow) {
      Future.delayed(const Duration(milliseconds: 100), () {
        Get.find<ConsoleLogic>().toPairingPage();
      });
      update(["application"]);
    }
  }

  pChange(index) {
    state.tabIndex = index;
    state.tController?.animateTo(index);
    update(["application"]);
  }

  ///绑定设备或者解绑设备后更新UI
  updateBottomNavigationBarItemList() {
    state.tController?.dispose();
    _initData();
  }

  bool onWillPop() {
    if (state.lastPopTime == null ||
        DateTime.now().difference(state.lastPopTime!) >
            const Duration(seconds: 2)) {
      state.lastPopTime = DateTime.now();
      CToast.showToast("exit_app".tr);
      return false;
    } else {
      state.lastPopTime = DateTime.now();
      // 退出app
      return true;
    }
  }

  @override
  void onClose() {
    state.tController?.dispose();
    state.pController?.dispose();
    super.onClose();
  }
}
