import 'dart:async';

import 'package:get/get.dart';

class ConsolePairingState {
  List<String> oneTips = [
    "console_pairing_one_tip_one".tr,
    "console_pairing_one_tip_two".tr,
    "console_pairing_one_tip_three".tr,
    "console_pairing_one_tip_four".tr
  ];
  List<String> oneErrorTips = [
    "console_pairing_one_error_tip_one".tr,
    "console_pairing_one_error_tip_two".tr,
    "console_pairing_one_error_tip_three".tr,
    "console_pairing_one_error_tip_four".tr
  ];
  List<String> twoTips = [
    "console_pairing_two_tip_one".tr,
    "console_pairing_two_tip_two".tr,
    "console_pairing_two_tip_three".tr,
    "console_pairing_two_tip_four".tr
  ];
  List<String> errorTips = [
    "console_pairing_error_tip_one".tr,
    "console_pairing_error_tip_two".tr,
    "console_pairing_error_tip_three".tr,
    "console_pairing_error_tip_four".tr
  ];
  List<String> tips = [
    "console_pairing_tip_one".tr,
    "console_pairing_tip_two".tr,
    "console_pairing_tip_three".tr,
    "console_pairing_tip_four".tr
  ];

  List<Status> statusList = [];

  /// 代表执行到哪一步
  int step = 0;

  StreamSubscription? espBlufiResultSubscription;

  String deviceMac = "";

  /// 是否正在扫描设备
  bool isScanDevice = false;

  ///是否已经连接过设备，如果连接过，取消的时候要断开，不然下次会连接不上
  bool isConnected = false;

  String wifiName = '';
  String wifiPwd = '';

  List<DeviceStatus> scanDevice = [];

  ConsolePairingState() {
    ///Initialize variables
  }
}

///status 0=未绑定 1=已绑定(同用户)  2=已绑定(不同用户)  3=未激活
class DeviceStatus {
  String mac;
  int status;
  DeviceStatus({this.mac = '', this.status = 0});
}

/// 状态结构体
class Status {
  String oneTips;
  String oneErrorTips;
  String twoTips;
  String errorTips;
  String tips;
  bool isStart;
  bool isFail;
  String status;

  Status(
      {this.oneTips = '',
      this.oneErrorTips = '',
      this.twoTips = '',
      this.errorTips = '',
      this.tips = '',
      this.isStart = false,
      this.isFail = false,
      this.status = 'idea'});
}
