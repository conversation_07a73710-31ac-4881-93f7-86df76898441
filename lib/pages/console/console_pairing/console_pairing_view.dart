import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:getx_xiaopa/base/base.dart';
import 'package:getx_xiaopa/extension/images.dart';
import 'package:getx_xiaopa/extension/widget_extension.dart';
import 'package:getx_xiaopa/pages/console/console_pairing/widget/fail_bubble.dart';
import 'package:getx_xiaopa/r.dart';
import 'package:getx_xiaopa/utils/utils.dart';
import 'package:getx_xiaopa/values/values.dart';
import 'package:getx_xiaopa/widget/widget.dart';
import 'package:loading_animation_widget/loading_animation_widget.dart';

import 'console_pairing_logic.dart';
import 'console_pairing_state.dart';

class ConsolePairingPage extends BaseCommonView {
  ConsolePairingPage({super.key});

  final ConsolePairingLogic logic = Get.put(ConsolePairingLogic());
  final ConsolePairingState state = Get.find<ConsolePairingLogic>().state;

  @override
  bool? get isHiddenNav => true;

  @override
  Widget buildContent() {
    return GetBuilder<ConsolePairingLogic>(
      id: "console_pairing_view",
      builder: (_) {
        return PopScope(
            canPop: false,
            onPopInvokedWithResult: (didPop, result) {
              if (!didPop) {
                logic.backAction();
              }
            },
            child: createCommonView(logic, (_) {
              return Container(
                color: Colors.white,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Container(
                      margin: EdgeInsets.only(
                          left: 15.w,
                          top: ScreenUtil().statusBarHeight + 17.h,
                          right: 15.w),
                      child: Row(
                        children: [
                          Images(
                            path: R.back_png,
                            width: 20.r,
                            height: 20.r,
                            color: ColorConfig.shopDetailTextColor,
                          ).inkWell(() {
                            if (state.isConnected) {
                              state.isConnected = false;
                              EspBlufiUtil.cloneConnect();
                            }
                            logic.backAction();
                          }),
                          const Expanded(child: SizedBox()),
                          state.isScanDevice
                              ? LoadingAnimationWidget.fourRotatingDots(
                                  color: const Color.fromRGBO(185, 185, 185, 1),
                                  size: 20.sp)
                              : Images(
                                  path: R.verify_refresh_png,
                                  width: 20.r,
                                  height: 20.r,
                                ).inkWell(() => logic.resetAction()),
                        ],
                      ),
                    ),
                    Container(
                      margin: EdgeInsets.only(top: 33.h, left: 40.w),
                      child: Text(
                        state.statusList[state.step].oneTips,
                        style: StyleConfig.otherStyle(
                            color: ColorConfig.searchTextColor,
                            fontSize: 26,
                            fontWeight: FontWeight.w500),
                      ),
                    ),
                    state.statusList[state.step].isFail
                        ? Container(
                            margin: EdgeInsets.only(left: 40.w, right: 42.w),
                            child: FailBubble(
                                child: Text(
                              state.statusList[state.step].errorTips,
                              style: StyleConfig.otherStyle(
                                  color: const Color.fromRGBO(249, 56, 56, 1),
                                  fontSize: 14),
                            )),
                          )
                        : Container(
                            margin: EdgeInsets.only(top: 10.h, left: 41.w),
                            child: Text(
                              state.statusList[state.step].twoTips,
                              style: StyleConfig.otherStyle(
                                  color: ColorConfig.shopDetailTextColor,
                                  fontSize: 12),
                            ),
                          ),
                    Container(
                      margin: EdgeInsets.only(
                          top:
                              state.statusList[state.step].isFail ? 25.h : 70.h,
                          left: 40.w),
                      child: Column(
                        children:
                            List.generate(state.statusList.length, (index) {
                          return Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Row(
                                children: [
                                  Container(
                                      width: 26.r,
                                      height: 26.r,
                                      decoration: BoxDecoration(
                                          borderRadius:
                                              BorderRadius.circular(13.r),
                                          border: Border.all(
                                              color: const Color.fromRGBO(
                                                  215, 215, 215, 1),
                                              width: 1.r),
                                          color: state.statusList[index].isFail
                                              ? const Color.fromRGBO(
                                                  255, 80, 111, 1)
                                              : state.statusList[index].isStart
                                                  ? Colors.black
                                                  : Colors.transparent),
                                      child: Center(
                                        child: state.statusList[index].status ==
                                                "loading"
                                            ? AnimatedRotateWidget(
                                                child: ThemeImage(
                                                  imgPath:
                                                      R.console_loading_png,
                                                  keyName: 'iconColor',
                                                  imgWidget: 14.r,
                                                  imgHeight: 14.r,
                                                ),
                                              )
                                            : state.statusList[index].status ==
                                                    "finish"
                                                ? ThemeImage(
                                                    imgPath:
                                                        R.console_success_png,
                                                    keyName: 'iconColor',
                                                    imgWidget: 14.r,
                                                    imgHeight: 11.r,
                                                  )
                                                : state.statusList[index]
                                                            .status ==
                                                        "fail"
                                                    ? Images(
                                                        path:
                                                            R.console_fail_png,
                                                        width: 12.r,
                                                        height: 12.r,
                                                      )
                                                    : const SizedBox(),
                                      )),
                                  Container(
                                    margin: EdgeInsets.only(left: 10.w),
                                    child: Text(
                                      state.statusList[index].tips,
                                      style: StyleConfig.otherStyle(
                                          color: ColorConfig.searchTextColor),
                                    ),
                                  )
                                ],
                              ),
                              index == state.statusList.length - 1
                                  ? const SizedBox()
                                  : Container(
                                      margin: EdgeInsets.only(left: 13.r),
                                      child: Images(
                                          path: R.line_png,
                                          width: 1,
                                          height: 27.h),
                                    ),
                            ],
                          );
                        }),
                      ),
                    ),
                    const Expanded(child: SizedBox()),
                    Container(
                      margin: EdgeInsets.only(
                          bottom: ScreenUtil().bottomBarHeight + 20.h),
                      alignment: Alignment.center,
                      child: Images(
                        path: R.console_image_04_png,
                        width: 190.w,
                        height: 190.h,
                      ),
                    )
                  ],
                ),
              );
            }));
      },
    );
  }
}
