import 'dart:convert';
import 'dart:io';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:getx_xiaopa/base/base.dart';
import 'package:getx_xiaopa/network/http_client.dart';
import 'package:getx_xiaopa/network/model/result.dart';
import 'package:getx_xiaopa/pages/console/console_pairing/widget/console_device_item.dart';
import 'package:getx_xiaopa/pages/console/model/device_items_result.dart';
import 'package:getx_xiaopa/routes/router.dart';
import 'package:getx_xiaopa/storage/storage.dart';
import 'package:getx_xiaopa/utils/utils.dart';
import 'package:getx_xiaopa/widget/common_dialog.dart';

import 'console_pairing_state.dart';

class ConsolePairingLogic extends BaseCommonController
    with GetSingleTickerProviderStateMixin {
  @override
  final ConsolePairingState state = ConsolePairingState();

  @override
  void initData() {
    ///初始化ESP-BLUFI
    EspBlufiUtil.initEspBlufi();

    WidgetsBinding.instance.addPostFrameCallback((_) async {
      state.espBlufiResultSubscription =
          await EspBlufiUtil.onEspBlufiResultListener(onSuccess: (res) async {
        var result = jsonDecode(res);

        logD("设备配对结果：$result");

        if (result["tag"] == "scan") {
          ///扫描设备
          if (result["status"] == "success") {
            if (result["data"].toString().isEmpty) return;
            String tempDevice =
                result["data"].toString().split("=")[1].split("_")[1];
            DeviceStatus dStatus = DeviceStatus();
            dStatus.mac = tempDevice;
            state.scanDevice.add(dStatus);
          } else if (result["status"] == "fail") {
            _statusFailAction(state.step);
          } else if (result["status"] == "stop") {
            ///停止扫描，弹出设备选择，如果只有一个设备，直接下一步
            ///去重操作
            Set<String> seenMac = {};
            state.scanDevice = state.scanDevice.where((item) {
              if (seenMac.contains(item.mac)) return false;
              seenMac.add(item.mac);
              return true;
            }).toList();

            if (state.scanDevice.isNotEmpty) {
              _statusFinishAction(state.step);
              state.step = 1;
              _statusStartAction(state.step);
              for (var i = 0; i < state.scanDevice.length; i++) {
                await checkDeviceAction(
                    devMac: state.scanDevice[i].mac, index: i);
              }

              if (state.scanDevice.length == 1 &&
                  state.scanDevice[0].status == 1) {
                ///只有一个设备，并且是自己绑定的，直接下一步
                state.deviceMac = state.scanDevice[0].mac;

                _statusFinishAction(state.step);
                state.step = 2;
                _statusStartAction(state.step);

                Future.delayed(const Duration(seconds: 1), () {
                  EspBlufiUtil.connectDevice("小耙AI_${state.scanDevice[0].mac}");
                });
              } else {
                _scanDeviceBottomSheet();
              }
            } else {
              _statusFailAction(state.step);
            }
          }
        } else if (result["tag"] == "connect") {
          ///连接设备
          if (result["status"] == "success") {
            state.isConnected = true;
            _statusFinishAction(state.step);
            state.step = 2;
            _statusStartAction(state.step);

            Future.delayed(const Duration(milliseconds: 500), () {
              _statusFinishAction(state.step);
              state.step = 3;
              _statusStartAction(state.step);
              Get.toNamed(AppRoutes.CONSOLE_WIFI)?.then((res) {
                if (res != null) {
                  if (res["status"] == "connect") {
                    state.wifiName = res["wifiName"];
                    state.wifiPwd = res["wifiPwd"];
                    EspBlufiUtil.configureWifi(state.wifiName, state.wifiPwd);
                  }
                } else {
                  _statusFailAction(state.step);
                }
              });
            });
          } else if (result["status"] == "fail") {
            _statusFailAction(state.step);
          }
        }

        /// 配置wifi情况
        if (result["tag"] == "configureWifi") {
          if (result["status"] == "success") {
            // 配置wifi成功重新请求状态
            Future.delayed(const Duration(seconds: 1), () {
              EspBlufiUtil.statusDevice();
            });
          } else if (result["status"] == "fail") {}
        }

        /// 连接wifi情况
        if (result["tag"] == "connectWifi") {
          if (result["status"] == "success") {
            Map<String, String> map = {};
            map[state.wifiName] = state.wifiPwd;
            ConfigStore.to.setWifiConfig(map);

            ///wifi配置成功才绑定设备
            bindDeviceAction();
          } else if (result["status"] == "fail") {
            EspBlufiUtil.cloneConnect();
            _statusFailAction(state.step);
          } else if (result["status"] == "retry") {
            /// 设备状态还在连接wifi，2秒后重新请求
            Future.delayed(const Duration(seconds: 2), () {
              EspBlufiUtil.statusDevice();
            });
          }
        }
      }, onError: (error) {
        // CToast.showToast("${error.message}");
      });

      ///初始化数据
      _init();

      await PermissionUtil.bluetooth(Get.context!, action: () {});

      /// 安卓9+以上权限不一样
      if (Platform.isAndroid) {
        DeviceInfoPlugin deviceInfoPlugin = DeviceInfoPlugin();
        AndroidDeviceInfo androidDeviceInfo =
            await deviceInfoPlugin.androidInfo;
        if (int.parse(androidDeviceInfo.version.release) >= 12) {
          await PermissionUtil.bluetoothScan(Get.context!, action: () {});
          await PermissionUtil.bluetoothConnect(Get.context!, action: () {});
        }
      }
      await PermissionUtil.location(Get.context!, action: () {
        Future.delayed(const Duration(seconds: 1), () {
          EspBlufiUtil.scanEspBlufi();
        });
      });
    });
  }

  @override
  void onHidden() {}

  _init() {
    state.statusList.clear();
    for (var i = 0; i < state.oneTips.length; i++) {
      Status status = Status();
      status.oneTips = state.oneTips[i];
      status.oneErrorTips = state.oneErrorTips[i];
      status.twoTips = state.twoTips[i];
      status.errorTips = state.errorTips[i];
      status.tips = state.tips[i];
      status.isStart = false;
      status.isFail = false;
      status.status = 'idea';
      state.statusList.add(status);
    }

    state.step = 0;
    _statusStartAction(state.step);

    netState = NetState.dataSuccessState;
    update(["console_pairing_view"]);
  }

  _scanDeviceBottomSheet() {
    Get.bottomSheet(
      isDismissible: false,
      enableDrag: false,
      ConsoleDeviceItem(
        deviceList: state.scanDevice,
        onTap: (status, mac) {
          if (mac.isEmpty) return;
          if (status == 3) {
            Get.dialog(
                CommonDialog(
                  isPop: false,
                  mTitle: "console_device_active_title".tr,
                  titleFontWeight: FontWeight.w700,
                  mContent: "console_device_active_tip".tr,
                  mContextSize: 12,
                  mContextAlign: TextAlign.center,
                  isCustonWidget: true,
                  mConfirm: "console_device_active_btn".tr,
                  confirmFontWeight: FontWeight.w600,
                  exPadding: EdgeInsets.only(left: 38.w, right: 35.w),
                  confirmAction: () {
                    _connectDeviceStep(mac);
                  },
                  cancelAction: () {
                    Get.back();
                    _statusFailAction(state.step);
                  },
                ),
                barrierDismissible: false);
          } else {
            _connectDeviceStep(mac);
          }
        },
      ),
    ).then((result) {
      if (result == "close") {
        _statusFailAction(state.step);
      }
    });
  }

  _connectDeviceStep(String mac) {
    state.deviceMac = mac;

    _statusFinishAction(state.step);
    state.step = 1;
    _statusStartAction(state.step);

    Future.delayed(const Duration(seconds: 1), () {
      EspBlufiUtil.connectDevice("小耙AI_$mac");
    });
  }

  resetAction() async {
    if (state.isConnected) {
      state.isConnected = false;
      await EspBlufiUtil.cloneConnect();
    }

    state.scanDevice.clear();
    state.isScanDevice = true;

    _init();

    update(["console_pairing_view"]);

    Future.delayed(const Duration(seconds: 1), () {
      state.isScanDevice = false;
      update(["console_pairing_view"]);
      EspBlufiUtil.scanEspBlufi();
    });
  }

  /// 修改每一步开始状态
  _statusStartAction(int cIndex) {
    state.statusList[cIndex].isStart = true;
    state.statusList[cIndex].status = "loading";
    update(["console_pairing_view"]);
  }

  /// 修改每一步完成状态
  _statusFinishAction(int cIndex) async {
    state.statusList[cIndex].status = "finish";

    ///绑定成功
    if (cIndex == 3) {
      await UserStore.to.deviceItems();
      await CommonStore.to.getBrainDetail(
          UserStore.to.deviceList[UserStore.to.selectDevice].botId ?? '');
      for (int i = 0; i < UserStore.to.deviceList.length; i++) {
        DeviceItemsModel model = UserStore.to.deviceList[i];
        if (model.mac == state.deviceMac) {
          UserStore.to.setSelectDevice(i);
        }
      }
      try {
        await SocketUtil.getInstance()?.disconnect();
      } catch (e) {
        logD("关闭socket出错");
      }
      Future.delayed(const Duration(milliseconds: 800), () {
        Get.back(result: "success");
      });
    }
    update(["console_pairing_view"]);
  }

  /// 修改每一步失败状态
  _statusFailAction(int cIndex) {
    state.statusList[state.step].isFail = true;
    state.statusList[cIndex].status = "fail";
    update(["console_pairing_view"]);
  }

  backAction() async {
    await UserStore.to.deviceItems();
    EventBus().emit("refreshDeviceItems", UserStore.to.deviceList);
    Get.back();
  }

  @override
  void onClose() {
    state.espBlufiResultSubscription?.cancel();
    super.onClose();
  }

  ///======================网络请求=========================
  checkDeviceAction({String devMac = '', int index = 0}) async {
    if (devMac.isEmpty) return;
    var map = {"mac": devMac};
    Result result = await http.checkDevice(map);
    DeviceStatus tempStatus = state.scanDevice[index];
    if (result.code == 0) {
      if (result.data != null) {
        ///is_in_use为true=已激活
        if (result.data["is_in_use"]) {
          ///设备绑定的用户id和登录的用户id一样
          if (result.data["user_id"] == UserStore.to.userInfo.id) {
            tempStatus.status = 1;
          } else if (result.data["user_id"] != "") {
            tempStatus.status = 2;
          } else {
            tempStatus.status = 0;
          }
        } else {
          tempStatus.status = 3;
        }
      }
    } else {
      tempStatus.status = 2;
    }
  }

  bindDeviceAction() async {
    var map = {"mac": state.deviceMac};
    Result result = await http.bindDevice(map);
    if (result.code == 0) {
      _statusFinishAction(state.step);
    } else {
      _statusFailAction(state.step);
    }
  }
}
