import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:getx_xiaopa/extension/images.dart';
import 'package:getx_xiaopa/extension/widget_extension.dart';
import 'package:getx_xiaopa/pages/console/console_pairing/console_pairing_state.dart';
import 'package:getx_xiaopa/r.dart';
import 'package:getx_xiaopa/values/values.dart';
import 'package:getx_xiaopa/widget/theme_text.dart';

class ConsoleDeviceItem extends StatelessWidget {
  final List<DeviceStatus> deviceList;
  final Function(int, String) onTap;
  const ConsoleDeviceItem(
      {super.key, required this.deviceList, required this.onTap});

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 1.sw,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.only(
            topLeft: Radius.circular(20.r), topRight: Radius.circular(20.r)),
        color: Colors.white,
      ),
      child: Column(
        children: [
          Row(
            children: [
              Container(
                margin: EdgeInsets.only(top: 20.h, left: 30.w),
                child: Text(
                  "console_devoce_item_tip".tr,
                  style: StyleConfig.otherStyle(
                      color: ColorConfig.searchTextColor,
                      fontSize: 18,
                      fontWeight: FontWeight.w600),
                ),
              ),
              const Expanded(child: SizedBox()),
              Container(
                margin: EdgeInsets.only(top: 21.h, right: 30.w),
                child: Images(
                  path: R.device_close_png,
                  width: 20.r,
                  height: 20.r,
                ),
              ).inkWell(() => Get.back(result: "close"))
            ],
          ),
          Container(
            margin: EdgeInsets.only(top: 20.h),
            width: 1.sw,
            height: 1.r,
            decoration: const BoxDecoration(
              color: Color.fromRGBO(233, 232, 233, 1),
            ),
          ),
          SizedBox(height: 10.h),
          Expanded(
            child: ListView.builder(
              itemCount: deviceList.length,
              itemBuilder: (context, index) {
                return Container(
                  margin: EdgeInsets.only(top: 10.h, left: 20.w, right: 20.w),
                  width: 335.w,
                  height: 68.h,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(10.r),
                    color: const Color.fromRGBO(245, 245, 245, 1),
                  ),
                  child: Row(
                    children: [
                      SizedBox(width: 10.w),
                      Images(
                        path: R.console_device_item_png,
                        width: 48.r,
                        height: 48.r,
                      ),
                      SizedBox(width: 10.w),
                      Expanded(
                        child: Text(
                          deviceList[index].mac,
                          style: StyleConfig.otherStyle(
                              color: ColorConfig.searchTextColor,
                              fontWeight: FontWeight.w500),
                        ),
                      ),
                      Container(
                        margin: EdgeInsets.only(right: 10.w),
                        padding: EdgeInsets.only(left: 15.w, right: 15.w),
                        // width: 67.w,
                        height: 29.h,
                        decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(15.r),
                            color: deviceList[index].status != 2
                                ? const Color.fromRGBO(40, 39, 46, 1)
                                : const Color.fromRGBO(215, 215, 215, 1)),
                        child: Center(
                          child: ThemeText(
                              dataStr: deviceList[index].status == 2
                                  ? "console_device_item_btn_two".tr
                                  : deviceList[index].status == 3
                                      ? "console_device_item_btn_one".tr
                                      : "console_device_item_btn".tr,
                              keyName: 'textColor',
                              fontSize: 12,
                              flag: deviceList[index].status != 2,
                              subColor: ColorConfig.authenticationTextColor,
                              fontWeight: FontWeight.w500),
                        ),
                      ).inkWell(() {
                        if (deviceList[index].status == 2) return;
                        Get.back();
                        onTap.call(
                            deviceList[index].status, deviceList[index].mac);
                      })
                    ],
                  ),
                );
              },
            ),
          )
        ],
      ),
    );
  }
}
