import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class FailBubble extends StatelessWidget {
  final Widget child;
  final Color color;

  const FailBubble({
    super.key,
    required this.child,
    this.color = const Color(0xFFFFF0ED), // 淡粉色
  });

  @override
  Widget build(BuildContext context) {
    return CustomPaint(
      painter: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(color: color),
      child: Padding(
        padding: EdgeInsets.only(
          top: 20.h,
          left: 20.w,
          right: 12.w,
          bottom: 11.h,
        ),
        child: child,
      ),
    );
  }
}

class BubblePainter extends CustomPainter {
  final Color color;

  BubblePainter({required this.color});

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()..color = color;

    final path = Path();

    double radius = 10.r;
    double pointerHeight = 10.w;

    path.moveTo(radius, pointerHeight);
    path.quadraticBezierTo(0, pointerHeight, 0, pointerHeight + radius);
    path.lineTo(0, size.height - radius);
    path.quadraticBezierTo(0, size.height, radius, size.height);
    path.lineTo(size.width - radius, size.height);
    path.quadraticBezierTo(
        size.width, size.height, size.width, size.height - radius);
    path.lineTo(size.width, pointerHeight + radius);
    path.quadraticBezierTo(
        size.width, pointerHeight, size.width - radius, pointerHeight);
    // 画尖角
    path.lineTo(30.w, pointerHeight);
    path.lineTo(40.w, 0);
    path.lineTo(50.w, pointerHeight);
    path.lineTo(radius, pointerHeight);

    path.close();
    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => false;
}
