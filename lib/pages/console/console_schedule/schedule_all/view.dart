import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:getx_xiaopa/base/base_list_view.dart';
import 'package:getx_xiaopa/extension/images.dart';
import 'package:getx_xiaopa/extension/widget_extension.dart';
import 'package:getx_xiaopa/pages/console/console_schedule/schedule_all/widgets/schedule_all_item.dart';
import 'package:getx_xiaopa/pages/console/console_schedule/schedule_all/widgets/schedule_calendar_widget.dart';
import 'package:getx_xiaopa/r.dart';
import 'package:getx_xiaopa/storage/storage.dart';
import 'package:getx_xiaopa/utils/utils.dart';
import 'package:getx_xiaopa/values/values.dart';
import 'package:getx_xiaopa/widget/widget.dart';
import 'package:scrollable_positioned_list/scrollable_positioned_list.dart';

import 'index.dart';

class ScheduleAllPage extends BaseListView<ScheduleAllController> {
  ScheduleAllPage({super.key});

  @override
  String? get navTitle => "schedule_all_title".tr;

  @override
  TextStyle? get navTitleStyle =>
      StyleConfig.blackStyle(fontSize: 18, fontWeight: FontWeight.w600);

  @override
  bool get customWidget => true;

  // 主视图
  Widget _buildView() {
    return Column(
      children: [
        Container(
          padding: EdgeInsets.all(5.r),
          margin: EdgeInsets.only(left: 15.w, right: 15.w),
          clipBehavior: Clip.hardEdge,
          decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(10.r),
              color: Colors.white,
              boxShadow: const [
                BoxShadow(
                  color: Color.fromRGBO(68, 78, 87, 0.06),
                  // 阴影颜色和透明度
                  spreadRadius: 0,
                  // 阴影扩散范围
                  blurRadius: 9,
                  // 阴影模糊程度
                  offset: Offset(0, 2),
                  // 阴影偏移量（水平，垂直）
                )
              ]),
          child: Column(
            children: [
              GestureDetector(
                onVerticalDragUpdate: controller.onDragUpdate,
                onVerticalDragEnd: controller.onDragEnd,
                child: ScheduleCalendarWidget(
                  focusedDay: controller.signDate,
                  controller: controller.scheduleCalendarController,
                  onTap: (date) => controller.selectDateAction(date),
                  onPageChanged: (p0) => controller.pageChangeDateAction(p0),
                ),
              ),
              Images(
                path: controller.imgStr,
                width: 18.r,
                height: 18.r,
              ).inkWell(() => controller.clickAction()),
              SizedBox(height: 5.h)
            ],
          ),
        ),
        Expanded(
          child: controller.scheduleMap.keys.isEmpty
              ? EmptyDataWidget(
                  title: "schedule_temp_data".tr,
                  imagePath: R.no_data_schedule_png,
                )
              : ScrollablePositionedList.builder(
                  itemScrollController: controller.itemScrollController,
                  itemCount: controller.scheduleMap.keys.length,
                  itemBuilder: (_, index) {
                    String dateKey =
                        controller.scheduleMap.keys.toList()[index];
                    return ScheduleAllItem(
                      dateKey: dateKey,
                      model: controller.scheduleMap[dateKey]!,
                      isManager: controller.isManager,
                      isAllSelect: controller.isAllSelect,
                      managerAction: (p0, p1) {
                        controller.selectAction(p0, p1);
                      },
                    );
                  },
                ),
        ),
        SizedBox(height: 10.h),
        controller.isManager
            ? Container(
                padding: EdgeInsets.only(
                    top: 10.h, bottom: ScreenUtil().statusBarHeight + 10.h),
                width: 375.w,
                color: Colors.white,
                child: Row(
                  children: [
                    SizedBox(width: 15.w),
                    CheckBoxRounded(
                      size: 22.r,
                      isChecked: false,
                      checkedColor: Get.find<ThemeColorController>()
                          .getColor('textColor'),
                      checkedBgColor: ColorConfig.searchTextColor,
                      uncheckedBgColor: Colors.white,
                      borderColor: const Color.fromRGBO(215, 215, 215, 1),
                      onTap: (flag) => controller.allSelectAction(flag!),
                    ),
                    SizedBox(width: 8.w),
                    Text(
                      'schedule_all_select'.tr,
                      style: StyleConfig.otherStyle(
                          color: const Color.fromRGBO(253, 105, 82, 1),
                          fontWeight: FontWeight.w500),
                    ),
                    SizedBox(
                        width: ConfigStore.to.getLocaleCode() == "zh"
                            ? 25.w
                            : 15.w),
                    Container(
                      width: ConfigStore.to.getLocaleCode() == "zh"
                          ? 245.w
                          : 225.w,
                      height: 48.h,
                      decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(24.r),
                          color: const Color.fromRGBO(40, 39, 46, 1)),
                      child: Center(
                        child: ThemeText(
                          dataStr: "schedule_all_del".tr,
                          keyName: 'textColor',
                          fontWeight: FontWeight.w800,
                        ),
                      ),
                    ).inkWell(() => controller.delAction())
                  ],
                ),
              )
            : Container(
                padding: EdgeInsets.only(top: 10.h),
                width: 375.w,
                color: Colors.white,
                child: Container(
                  margin:
                      EdgeInsets.only(left: 46.w, right: 46.w, bottom: 44.h),
                  width: 283.w,
                  height: 48.h,
                  decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(24.r),
                      color: const Color.fromRGBO(40, 39, 46, 1)),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      ThemeImagePath(
                        fileName: 'schedule_today_btn.png',
                        imgWidget: 20.r,
                        imgHeight: 20.r,
                      ),
                      SizedBox(width: 7.w),
                      ThemeText(
                        dataStr: "schedule_today_btn".tr,
                        keyName: 'textColor',
                        fontWeight: FontWeight.w800,
                      )
                    ],
                  ),
                ).inkWell(() => controller.toAddSchedulePage()),
              ),
      ],
    );
  }

  @override
  Widget buildContent() {
    return GetBuilder<ScheduleAllController>(
      init: ScheduleAllController(),
      id: "schedule_all",
      builder: (_) {
        return Scaffold(
          backgroundColor: const Color.fromRGBO(249, 249, 249, 1),
          extendBodyBehindAppBar: true,
          appBar: AppBar(
            elevation: 0,
            scrolledUnderElevation: 0,
            backgroundColor: const Color.fromRGBO(249, 249, 249, 1),
            centerTitle: true,
            title: Text(
              "schedule_all_title".tr,
              style: StyleConfig.blackStyle(
                  fontSize: 18, fontWeight: FontWeight.w500),
            ),
            leadingWidth: 35.w,
            leading: Padding(
              padding: EdgeInsets.only(left: 15.w),
              child: Images(
                path: R.back_png,
                width: 20.w,
                height: 20.h,
                scale: 2.8,
              ).inkWell(() => Get.back()),
            ),
            actions: [
              Padding(
                padding: EdgeInsets.only(right: 15.w),
                child: Text(
                  controller.managerTip,
                  style: StyleConfig.otherStyle(
                      color: ColorConfig.authenticationTextColor, fontSize: 16),
                ),
              ).inkWell(() => controller.managerAction())
            ],
          ),
          body: _buildView(),
        );
      },
    );
  }
}
