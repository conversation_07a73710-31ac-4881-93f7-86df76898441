import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:getx_xiaopa/extension/widget_extension.dart';
import 'package:getx_xiaopa/network/http_client.dart';
import 'package:getx_xiaopa/network/model/result.dart';
import 'package:getx_xiaopa/pages/console/console_schedule/index.dart';
import 'package:getx_xiaopa/pages/console/console_schedule/model/schedule_model_result.dart';
import 'package:getx_xiaopa/pages/console/console_schedule/schedule_all/widgets/schedule_calendar_widget.dart';
import 'package:getx_xiaopa/r.dart';
import 'package:getx_xiaopa/routes/router.dart';
import 'package:scrollable_positioned_list/scrollable_positioned_list.dart';

class ScheduleAllController extends GetxController
    with GetSingleTickerProviderStateMixin {
  ScheduleAllController();

  String managerTip = "schedule_all_clear".tr;

  bool isManager = false;
  bool isAllSelect = false;
  bool isExpandedCalendar = false;
  String imgStr = R.schedule_all_down_png;
  final double minHeight = 200;
  final double maxHeight = 400;
  double currentHeight = 100;

  bool get isExpanded =>
      currentHeight > (minHeight + (maxHeight - minHeight) / 2);

  late AnimationController _controller;
  late Animation<double> _animation;

  final ScheduleCalendarController<bool> scheduleCalendarController =
      ScheduleCalendarController();

  DateTime signDate = DateTime.now();

  Map<String, List<ScheduleModel>> scheduleMap = {};

  List<String> deleteId = [];

  ItemScrollController itemScrollController = ItemScrollController();

  @override
  void onInit() {
    _controller = AnimationController(
        vsync: this, duration: const Duration(milliseconds: 300));

    scheduleMap = ScheduleStore.to.scheduleAllMap;

    Future.delayed(const Duration(milliseconds: 500), () {
      _scrollToDate(signDate);
    });

    update(["schedule_all"]);
    super.onInit();
  }

  managerAction() {
    isManager = !isManager;
    if (isManager) {
      managerTip = "schedule_all_completed".tr;
    } else {
      managerTip = "schedule_all_clear".tr;
    }
    update(["schedule_all"]);
  }

  selectAction(String sId, bool value) {
    if (value) {
      deleteId.add(sId);
    } else {
      deleteId.remove(sId);
    }
  }

  allSelectAction(bool value) {
    isAllSelect = value;
    if (value) {
      deleteId = scheduleMap.values
          .expand((list) => list)
          .map((item) => item.id)
          .whereType<String>()
          .toList();
    } else {
      deleteId.clear();
    }
    update(["schedule_all"]);
  }

  delAction() {
    if (deleteId.isEmpty) return;
    for (int i = 0; i < deleteId.length; i++) {
      if (i == deleteId.length - 1) {
        _removeSchedule(scheduleId: deleteId[i]);
      } else {
        _removeSchedule(scheduleId: deleteId[i]);
      }
    }
  }

  void onDragUpdate(DragUpdateDetails details) {
    currentHeight += details.delta.dy;
    currentHeight = currentHeight.clamp(minHeight, maxHeight);
  }

  void onDragEnd(DragEndDetails details) {
    final shouldExpand = isExpanded;
    _animateTo(shouldExpand ? maxHeight : minHeight);

    scheduleCalendarController.onDragEnd(shouldExpand);
    isExpandedCalendar = shouldExpand;
    _setArrowImg();
  }

  void clickAction() {
    isExpandedCalendar = !isExpandedCalendar;
    scheduleCalendarController.onDragEnd(isExpandedCalendar);
    _setArrowImg();
  }

  _setArrowImg() {
    if (isExpandedCalendar) {
      imgStr = R.schedule_all_up_png;
    } else {
      imgStr = R.schedule_all_down_png;
    }
    update(["schedule_all"]);
  }

  void _animateTo(double targetHeight) {
    _animation = Tween<double>(begin: currentHeight, end: targetHeight).animate(
      CurvedAnimation(parent: _controller, curve: Curves.easeOut),
    )..addListener(() {
        currentHeight = _animation.value;
        update(["schedule_all"]);
      });
    _controller.forward(from: 0);
  }

  selectDateAction(DateTime date) {
    scheduleCalendarController.onDragEnd(false);
    isExpandedCalendar = false;
    _setArrowImg();
    if (_isSameWeekSundayStart(signDate, date)) {
      ///选择的日期在同一周里，直接跳转
      _getRepeatSchedule(isJump: true, jumpTime: date);
    } else {
      ///不在同一周则请求再跳转
      _getRepeatSchedule(jumpTime: date);
    }
    signDate = date;
  }

  bool _isSameWeekSundayStart(DateTime date1, DateTime date2) {
    DateTime weekStart1 = date1.subtract(Duration(days: date1.weekday % 7));
    DateTime weekStart2 = date2.subtract(Duration(days: date2.weekday % 7));

    return weekStart1.year == weekStart2.year &&
        weekStart1.month == weekStart2.month &&
        weekStart1.day == weekStart2.day;
  }

  pageChangeDateAction(DateTime date) {
    if (isExpandedCalendar) return;
    signDate = date;
    _getRepeatSchedule(jumpTime: signDate);
  }

  toAddSchedulePage() {
    Get.toNamed(AppRoutes.SCHEDULE_ADD)?.then((value) async {
      if (value == "success") {
        _getRepeatSchedule(jumpTime: signDate);
      }
    });
  }

  void _scrollToDate(DateTime jumpTime) {
    String eTime = jumpTime.toString().split(" ")[0];

    int index = scheduleMap.keys.toList().indexWhere((item) => item == eTime);

    if (index != -1) {
      itemScrollController.scrollTo(
          index: index,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeInOut);
    }
  }

  ///==========================网络请求===========================
  ///isJump=true直接跳转，否则请求再跳转
  _getRepeatSchedule({bool isJump = false, DateTime? jumpTime}) async {
    if (isJump) {
      _scrollToDate(jumpTime!);
    } else {
      scheduleMap.clear();
      update(["schedule_all"]);

      await ScheduleStore.to.getAllSchedule(signDate: signDate);
      scheduleMap = ScheduleStore.to.scheduleAllMap;

      update(["schedule_all"]);

      Future.delayed(const Duration(milliseconds: 500), () {
        _scrollToDate(jumpTime!);
      });
    }
  }

  _removeSchedule({required String scheduleId, isRefresh = true}) async {
    if (isRefresh) {
      CToast.showLoading();
    }
    var map = {"id": scheduleId};
    Result result = await http.rmoveSchedule(map);
    if (result.code == 0) {
      isManager = false;
      _getRepeatSchedule(jumpTime: signDate);
    } else {
      CToast.showToast(result.msg!);
    }
  }
}
