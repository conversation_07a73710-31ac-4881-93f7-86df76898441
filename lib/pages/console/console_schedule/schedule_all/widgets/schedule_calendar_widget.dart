import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:getx_xiaopa/storage/storage.dart';
import 'package:getx_xiaopa/values/values.dart';
import 'package:getx_xiaopa/widget/theme_text.dart';
import 'package:table_calendar/table_calendar.dart';

class ScheduleCalendarController<T> {
  ValueChanged<T>? _onDragEndCallback;

  void _bind({required ValueChanged<T> onDragEnd}) {
    _onDragEndCallback = onDragEnd;
  }

  void onDragEnd(value) {
    _onDragEndCallback?.call(value);
  }
}

class ScheduleCalendarWidget extends StatefulWidget {
  final DateTime focusedDay;
  final ScheduleCalendarController controller;
  final Function(DateTime)? onTap;
  final Function(DateTime)? onPageChanged;

  const ScheduleCalendarWidget(
      {super.key,
      required this.focusedDay,
      required this.controller,
      this.onTap,
      this.onPageChanged});

  @override
  State<ScheduleCalendarWidget> createState() => _ScheduleCalendarWidgetState();
}

class _ScheduleCalendarWidgetState extends State<ScheduleCalendarWidget> {
  CalendarFormat calendarFormat = CalendarFormat.week;
  DateTime? _monthDay;
  DateTime? _selectedDay; // 新增选中的日期变量
  //防抖
  Timer? _debounce;

  @override
  void initState() {
    super.initState();
    _monthDay = widget.focusedDay;
    _selectedDay = DateTime.now(); // 第一次默认选中今天
    widget.controller._bind(onDragEnd: _onDragEnd);
  }

  void _onDragEnd(value) {
    setState(() {
      calendarFormat = value ? CalendarFormat.month : CalendarFormat.week;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.only(top: ScreenUtil().statusBarHeight + 64.h),
      width: 345.w,
      child: TableCalendar(
        availableGestures: AvailableGestures.none,
        firstDay: DateTime.utc(2020, 1, 1),
        lastDay: DateTime.utc(2030, 12, 31),
        focusedDay: _monthDay!,
        calendarFormat: calendarFormat,
        rowHeight: 40.h,
        daysOfWeekHeight: 30.h,
        locale: ConfigStore.to.locale.toString(),
        onDaySelected: (selectedDay, focusedDay) {
          setState(() {
            _selectedDay = selectedDay; // 更新选中日期
            _monthDay = focusedDay;
          });
          widget.onTap?.call(selectedDay);
        },
        selectedDayPredicate: (day) =>
            _isSameDay(day, _selectedDay ?? DateTime.now()),
        headerStyle: HeaderStyle(
            formatButtonVisible: false,
            titleCentered: true,
            leftChevronIcon: Container(
              width: 20.r,
              height: 20.r,
              decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(20.r),
                  color: const Color.fromRGBO(242, 241, 245, 1)),
              child: Center(
                child: Icon(
                  size: 20.r,
                  Icons.chevron_left,
                  color: const Color.fromRGBO(185, 185, 185, 1),
                ),
              ),
            ),
            rightChevronIcon: Container(
              width: 20.r,
              height: 20.r,
              decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(20.r),
                  color: const Color.fromRGBO(242, 241, 245, 1)),
              child: Center(
                child: Icon(
                  size: 20.r,
                  Icons.chevron_right,
                  color: const Color.fromRGBO(185, 185, 185, 1),
                ),
              ),
            ),
            headerPadding: EdgeInsets.zero,
            titleTextStyle: StyleConfig.otherStyle(
                color: ColorConfig.searchTextColor,
                fontWeight: FontWeight.w400,
                fontSize: 18)),
        onPageChanged: (focusedDay) {
          if (calendarFormat == CalendarFormat.week) {
            _debounce?.cancel();
            _debounce = Timer(const Duration(milliseconds: 300), () {
              setState(() {
                _selectedDay = focusedDay; // 更新选中日期
                _monthDay = focusedDay;
              });
              // 这里就是最后一次触发
              widget.onPageChanged?.call(focusedDay);
            });
          }
        },
        calendarStyle: const CalendarStyle(
          outsideDaysVisible: false,
          isTodayHighlighted: false,
        ),
        calendarBuilders: CalendarBuilders(
          dowBuilder: (context, day) {
            final text = [
              'diary_week_seven'.tr,
              'diary_week_one'.tr,
              'diary_week_two'.tr,
              'diary_week_three'.tr,
              'diary_week_four'.tr,
              'diary_week_five'.tr,
              'diary_week_six'.tr
            ][day.weekday % 7];
            return Center(
              child: Text(
                text,
                style: StyleConfig.otherStyle(
                    color: ColorConfig.authenticationTextColor, fontSize: 14),
              ),
            );
          },

          ///设置不是本月的样式
          outsideBuilder: (context, date, _) => _buildDayCell(date),
          defaultBuilder: (context, date, _) => _buildDayCell(date),
          todayBuilder: (context, date, _) => _buildDayCell(date),
          selectedBuilder: (context, date, _) => _buildDayCell(date),
        ),
      ),
    );
  }

  bool _isSameDay(DateTime a, DateTime b) {
    return a.year == b.year && a.month == b.month && a.day == b.day;
  }

  Widget _buildDayCell(DateTime date) {
    final bool isSelected =
        _selectedDay != null && _isSameDay(date, _selectedDay!);

    return Column(
      children: [
        Container(
          margin: EdgeInsets.only(top: 3.h, bottom: 3.h),
          padding: EdgeInsets.all(3.r),
          decoration: isSelected
              ? const BoxDecoration(
                  color: Color.fromRGBO(40, 39, 46, 1), shape: BoxShape.circle)
              : const BoxDecoration(
                  color: Color.fromRGBO(249, 249, 249, 1),
                  shape: BoxShape.circle),
          child: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                ThemeText(
                  dataStr: '${date.day}',
                  keyName: 'textColor',
                  flag: isSelected,
                  subColor: ColorConfig.shopDetailTextColor,
                  fontWeight: isSelected ? FontWeight.w500 : FontWeight.w400,
                  fontSize: 12,
                )
              ],
            ),
          ),
        ),
      ],
    );
  }
}
