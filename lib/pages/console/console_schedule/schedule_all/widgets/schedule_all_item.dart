import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:getx_xiaopa/pages/console/console_schedule/model/schedule_model_result.dart';
import 'package:getx_xiaopa/utils/utils.dart';
import 'package:getx_xiaopa/values/values.dart';
import 'package:getx_xiaopa/widget/widget.dart';

class ScheduleAllItem extends StatelessWidget {
  final bool isManager;
  final bool isAllSelect;
  final String dateKey;
  final List<ScheduleModel> model;
  final Function(String, bool)? managerAction;

  const ScheduleAllItem(
      {super.key,
      this.isManager = false,
      this.isAllSelect = false,
      required this.managerAction,
      required this.dateKey,
      required this.model});

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.only(top: 10.h, left: 15.w, right: 15.w),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          isManager
              ? Container(
                  margin: EdgeInsets.only(top: 20.h),
                  child: CheckBoxRounded(
                    size: 22.r,
                    isChecked: isAllSelect,
                    checkedColor:
                        Get.find<ThemeColorController>().getColor('textColor'),
                    checkedBgColor: ColorConfig.searchTextColor,
                    uncheckedBgColor: Colors.white,
                    borderColor: const Color.fromRGBO(215, 215, 215, 1),
                    onTap: (flag) {
                      for (ScheduleModel m in model) {
                        managerAction?.call(m.id ?? '', flag!);
                      }
                    },
                  ),
                )
              : const SizedBox(),
          Expanded(
            child: Container(
              margin: EdgeInsets.only(top: 10.h, left: isManager ? 8.w : 0),
              // width: isManager ? 315.w : 345.w,
              padding: EdgeInsets.all(15.r),
              decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(10.r),
                  color: Colors.white),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  ThemeText(
                    dataStr: dateKey.replaceAll("-", "."),
                    keyName: 'mineOnline',
                    fontSize: 18,
                    fontWeight: FontWeight.w700,
                  ),
                  ...List.generate(model.length, (index) {
                    bool isPass = TimeUtil.isTimePassed(TimeUtil.getSimpleDate(
                        model[index].nextActiveAt,
                        splitStr: "+"));
                    return ThemeContainer(
                      keyName: "scheduleItemColor",
                      tMargin: EdgeInsets.only(top: 10.h),
                      tPandding:
                          EdgeInsets.only(left: 15.w, top: 10.h, bottom: 10.h),
                      tWidget: 315.w,
                      // tHeight: 79.h,
                      flag: isPass,
                      subColor: const Color.fromRGBO(249, 249, 249, 1),
                      radius: 10.r,
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            TimeUtil.getFormatDateHM(model[index].nextActiveAt),
                            style: StyleConfig.otherStyle(
                                color: ColorConfig.searchTextColor,
                                fontWeight: FontWeight.w700),
                          ),
                          SizedBox(height: 6.h),
                          Text(
                            model[index].content ?? '',
                            style: StyleConfig.otherStyle(
                                color: ColorConfig.authenticationTextColor,
                                fontWeight: FontWeight.w500),
                          ),
                        ],
                      ),
                    );
                  })
                ],
              ),
            ),
          )
        ],
      ),
    );
  }
}
