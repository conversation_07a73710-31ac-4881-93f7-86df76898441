import 'package:get/get.dart';
import 'package:getx_xiaopa/extension/widget_extension.dart';
import 'package:getx_xiaopa/pages/console/console_schedule/index.dart';
import 'package:getx_xiaopa/routes/router.dart';

class ConsoleScheduleController extends GetxController {
  ConsoleScheduleController();

  String dateDay = '1';

  @override
  void onReady() {
    super.onReady();

    dateDay = _getDateDay();

    _requestAllData();

    update(["console_schedule"]);
  }

  String _getDateDay() {
    DateTime dateTime = DateTime.now();
    return '${dateTime.day}';
  }

  toScheduleTodayPage() {
    Get.toNamed(AppRoutes.SCHEDULE_TODAY)?.then((value) {
      _requestAllData();
    });
  }

  toSchedulePlanPage() {
    Get.toNamed(AppRoutes.SCHEDULE_PLAN)?.then((value) {
      _requestAllData();
    });
  }

  toScheduleALLPage() {
    Get.toNamed(AppRoutes.SCHEDULE_ALL)?.then((value) {
      _requestAllData();
    });
  }

  toAddSchedulePage() {
    Get.toNamed(AppRoutes.SCHEDULE_ADD)?.then((value) async {
      if (value == "success") {
        _requestAllData();
      }
    });
  }

  _requestAllData() async {
    await ScheduleStore.to.init();
    CToast.showLoading();
    Future.delayed(const Duration(milliseconds: 800), () {
      CToast.dismiss();
      update(["console_schedule"]);
    });
  }
}
