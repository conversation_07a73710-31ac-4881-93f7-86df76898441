///
/// Code generated by jsonToDartModel https://ashamp.github.io/jsonToDartModel/
///
class ScheduleModel {
/*
{
  "id": 34,
  "time": "2025-08-21 11:25",
  "repeat_rule": "0000000",
  "content": "下班了",
  "device_id": "4934524703080448",
  "user_id": "19976060665857",
  "status": 2,
  "next_active_at": "2025-08-21T11:25:00+08:00",
  "is_active": false,
  "remark": "",
  "created_at": "2025-08-21T11:20:38.253+08:00",
  "updated_at": "2025-08-21T11:25:00.172+08:00"
} 
*/

  String? id;
  String? time;
  String? repeatRule;
  String? content;
  String? deviceId;
  String? userId;
  int? status;
  String? nextActiveAt;
  bool? isActive;
  String? remark;
  String? createdAt;
  String? updatedAt;

  ScheduleModel({
    this.id,
    this.time,
    this.repeatRule,
    this.content,
    this.deviceId,
    this.userId,
    this.status,
    this.nextActiveAt,
    this.isActive,
    this.remark,
    this.createdAt,
    this.updatedAt,
  });
  ScheduleModel.fromJson(Map<String, dynamic> json) {
    id = json['id']?.toString();
    time = json['time']?.toString();
    repeatRule = json['repeat_rule']?.toString();
    content = json['content']?.toString();
    deviceId = json['device_id']?.toString();
    userId = json['user_id']?.toString();
    status = json['status']?.toInt();
    nextActiveAt = json['next_active_at']?.toString();
    isActive = json['is_active'];
    remark = json['remark']?.toString();
    createdAt = json['created_at']?.toString();
    updatedAt = json['updated_at']?.toString();
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['id'] = id;
    data['time'] = time;
    data['repeat_rule'] = repeatRule;
    data['content'] = content;
    data['device_id'] = deviceId;
    data['user_id'] = userId;
    data['status'] = status;
    data['next_active_at'] = nextActiveAt;
    data['is_active'] = isActive;
    data['remark'] = remark;
    data['created_at'] = createdAt;
    data['updated_at'] = updatedAt;
    return data;
  }
}

class ScheduleModelResult {
/*
{
  "count": 7,
  "pageSize": 10,
  "pageNum": 1,
  "data": [
    {
      "id": 34,
      "time": "2025-08-21 11:25",
      "repeat_rule": "0000000",
      "content": "下班了",
      "device_id": "4934524703080448",
      "user_id": "19976060665857",
      "status": 2,
      "next_active_at": "2025-08-21T11:25:00+08:00",
      "is_active": false,
      "remark": "",
      "created_at": "2025-08-21T11:20:38.253+08:00",
      "updated_at": "2025-08-21T11:25:00.172+08:00"
    }
  ]
} 
*/

  int? count;
  int? pageSize;
  int? pageNum;
  List<ScheduleModel?>? data;

  ScheduleModelResult({
    this.count,
    this.pageSize,
    this.pageNum,
    this.data,
  });
  ScheduleModelResult.fromJson(Map<String, dynamic> json) {
    count = json['count']?.toInt();
    pageSize = json['pageSize']?.toInt();
    pageNum = json['pageNum']?.toInt();
    if (json['data'] != null) {
      final v = json['data'];
      final arr0 = <ScheduleModel>[];
      v.forEach((v) {
        arr0.add(ScheduleModel.fromJson(v));
      });
      data = arr0;
    }
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['count'] = count;
    data['pageSize'] = pageSize;
    data['pageNum'] = pageNum;
    if (this.data != null) {
      final v = this.data;
      final arr0 = [];
      for (var v in v!) {
        arr0.add(v!.toJson());
      }
      data['data'] = arr0;
    }
    return data;
  }
}
