import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:getx_xiaopa/base/base.dart';
import 'package:getx_xiaopa/extension/images.dart';
import 'package:getx_xiaopa/extension/widget_extension.dart';
import 'package:getx_xiaopa/pages/console/console_schedule/schedule_add/widgets/date_widget.dart';
import 'package:getx_xiaopa/r.dart';
import 'package:getx_xiaopa/values/values.dart';
import 'package:getx_xiaopa/widget/widget.dart';

import 'index.dart';

class ScheduleAddPage extends BaseCommonView<ScheduleAddController> {
  ScheduleAddPage({super.key});

  @override
  String? get navTitle => "schedule_add_title".tr;

  @override
  TextStyle? get navTitleStyle =>
      StyleConfig.blackStyle(fontSize: 18, fontWeight: FontWeight.w600);

  @override
  Color? get navColor => Colors.transparent;

  @override
  double? get leftWidth => 35.w;

  @override
  Widget? get leftButton => Padding(
        padding: EdgeInsets.only(left: 15.w),
        child: Images(
          path: R.back_png,
          width: 20.w,
          height: 20.h,
          scale: 2.8,
        ).inkWell(() => Get.back()),
      );

  @override
  Color? get contentColor => const Color.fromRGBO(249, 249, 249, 1);

  // 主视图
  Widget _buildView() {
    return SizedBox(
      width: 1.sw,
      height: 1.sh,
      child: Column(
        children: [
          Expanded(
            child: SingleChildScrollView(
              child: Column(
                children: [
                  Container(
                      margin:
                          EdgeInsets.only(top: 10.h, left: 15.w, right: 15.w),
                      width: 345.w,
                      // height: 158.h,
                      decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(10.r),
                          color: Colors.white,
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withValues(alpha: 0.06),
                              // 阴影颜色和透明度
                              spreadRadius: 0,
                              // 阴影扩散范围
                              blurRadius: 10,
                              // 阴影模糊程度
                              offset: const Offset(0, 2),
                              // 阴影偏移量（水平，垂直）
                            )
                          ]),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.end,
                        children: [
                          SizedBox(
                            width: 345.w,
                            child: AppTextField(
                              controller: controller.cTextEditingController,
                              style: StyleConfig.otherStyle(
                                  color: ColorConfig.searchTextColor),
                              keyboardType: TextInputType.text,
                              maxLines: null,
                              maxLength: controller.maxLenght,
                              onChanged: (value) =>
                                  controller.changeText(value),
                              decoration: InputDecoration(
                                border: InputBorder.none,
                                enabledBorder: InputBorder.none,
                                focusedBorder: InputBorder.none,
                                contentPadding: EdgeInsets.symmetric(
                                    horizontal: 18.w, vertical: 18.h),
                                hintText: 'schedule_add_hint_text_one'.tr,
                                hintStyle: StyleConfig.otherStyle(
                                    color: ColorConfig.shopDetailTextColor),
                              ),
                            ),
                          ),
                          Container(
                            margin: EdgeInsets.only(right: 15.w, bottom: 5.h),
                            child: Text(
                              '${controller.textLenght}/${controller.maxLenght}',
                              style: StyleConfig.otherStyle(
                                  color: ColorConfig.authenticationTextColor,
                                  fontSize: 14),
                            ),
                          )
                        ],
                      )),
                  Container(
                    margin: EdgeInsets.only(top: 10.h),
                    padding: EdgeInsets.only(left: 20.w, right: 20.w),
                    width: 345.w,
                    height: 58.h,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.only(
                          topLeft: Radius.circular(10.r),
                          topRight: Radius.circular(10.r),
                          bottomLeft:
                              Radius.circular(controller.showTime ? 0 : 10.r),
                          bottomRight:
                              Radius.circular(controller.showTime ? 0 : 10.r)),
                      color: Colors.white,
                    ),
                    child: Row(
                      children: [
                        Text(
                          'schedule_add_item_two'.tr,
                          style: StyleConfig.otherStyle(
                              color: ColorConfig.searchTextColor),
                        ),
                        const Expanded(child: SizedBox()),
                        CupertinoSwitch(
                            value: controller.showTime,
                            activeTrackColor: ColorConfig.searchTextColor,
                            onChanged: (value) => controller.changeTime(value))
                      ],
                    ),
                  ),
                  Offstage(
                      offstage: !controller.showTime,
                      child: Column(
                        children: [
                          Container(
                            padding: EdgeInsets.only(
                                left: 17.w, right: 17.w, bottom: 10.h),
                            width: 345.w,
                            decoration: BoxDecoration(
                                borderRadius: BorderRadius.only(
                                    bottomLeft: Radius.circular(10.r),
                                    bottomRight: Radius.circular(10.r)),
                                color: Colors.white,
                                boxShadow: const [
                                  BoxShadow(
                                    color: Color.fromRGBO(68, 78, 87, 0.06),
                                    // 阴影颜色和透明度
                                    spreadRadius: 0,
                                    // 阴影扩散范围
                                    blurRadius: 9,
                                    // 阴影模糊程度
                                    offset: Offset(0, 2),
                                    // 阴影偏移量（水平，垂直）
                                  )
                                ]),
                            child: Column(
                              children: [
                                Container(
                                  width: 305.w,
                                  height: 1.h,
                                  color: const Color.fromRGBO(233, 232, 233, 1),
                                ),
                                SizedBox(
                                  width: 345.w,
                                  height: 185.h,
                                  child: CupertinoDatePicker(
                                    mode: CupertinoDatePickerMode.time,
                                    itemExtent: 45.h,
                                    backgroundColor: Colors.white,
                                    use24hFormat: false,
                                    onDateTimeChanged: (value) =>
                                        controller.selectTimeAction(value),
                                  ),
                                )
                              ],
                            ),
                          ),
                          Container(
                            margin: EdgeInsets.only(left: 25.w, top: 10.h),
                            child: Row(
                              children: [
                                Images(
                                  path: R.console_tips_png,
                                  width: 12.r,
                                  height: 12.r,
                                ),
                                SizedBox(width: 6.w),
                                ThemeText(
                                  dataStr: 'schedule_repeat_five'.tr,
                                  keyName: 'mineOnline',
                                  fontSize: 12,
                                )
                              ],
                            ),
                          )
                        ],
                      )),
                  Container(
                    margin: EdgeInsets.only(top: 10.h),
                    padding: EdgeInsets.only(left: 20.w, right: 20.w),
                    width: 345.w,
                    height: 58.h,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(10.r),
                      color: Colors.white,
                    ),
                    child: Row(
                      children: [
                        Text(
                          'schedule_add_item_three'.tr,
                          style: StyleConfig.otherStyle(
                              color: ColorConfig.searchTextColor),
                        ),
                        const Expanded(child: SizedBox()),
                        Text(
                          controller.repeat.name,
                          style: StyleConfig.otherStyle(
                              color: ColorConfig.authenticationTextColor,
                              fontSize: 14),
                        ),
                        SizedBox(width: 10.w),
                        Images(
                          path: R.mine_right_png,
                          width: 11.w,
                          height: 20.h,
                        ),
                      ],
                    ),
                  ).inkWell(() => controller.changeRepeat()),
                  Offstage(
                    offstage: controller.isShowDate,
                    child: Container(
                      margin: EdgeInsets.only(top: 8.h),
                      padding: EdgeInsets.only(left: 20.w, right: 20.w),
                      width: 345.w,
                      height: 58.h,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.only(
                            topLeft: Radius.circular(10.r),
                            topRight: Radius.circular(10.r),
                            bottomLeft:
                                Radius.circular(controller.showDate ? 0 : 10.r),
                            bottomRight: Radius.circular(
                                controller.showDate ? 0 : 10.r)),
                        color: Colors.white,
                      ),
                      child: Row(
                        children: [
                          Text(
                            'schedule_add_item_one'.tr,
                            style: StyleConfig.otherStyle(
                                color: ColorConfig.searchTextColor),
                          ),
                          Offstage(
                            offstage: !controller.showDate,
                            child: Text(
                              controller.today,
                              style: StyleConfig.otherStyle(
                                  color: const Color.fromRGBO(253, 105, 82, 1),
                                  fontSize: 12,
                                  fontWeight: FontWeight.w600),
                            ),
                          ),
                          const Expanded(child: SizedBox()),
                          CupertinoSwitch(
                              value: controller.showDate,
                              activeTrackColor: ColorConfig.searchTextColor,
                              onChanged: (value) =>
                                  controller.changeDate(value))
                        ],
                      ),
                    ),
                  ),
                  Offstage(
                    offstage: !controller.showDate,
                    child: Container(
                      padding: EdgeInsets.only(
                          left: 17.w, right: 17.w, bottom: 10.h),
                      width: 345.w,
                      decoration: BoxDecoration(
                          borderRadius: BorderRadius.only(
                              bottomLeft: Radius.circular(10.r),
                              bottomRight: Radius.circular(10.r)),
                          color: Colors.white,
                          boxShadow: const [
                            BoxShadow(
                              color: Color.fromRGBO(68, 78, 87, 0.06),
                              // 阴影颜色和透明度
                              spreadRadius: 0,
                              // 阴影扩散范围
                              blurRadius: 9,
                              // 阴影模糊程度
                              offset: Offset(0, 2),
                              // 阴影偏移量（水平，垂直）
                            )
                          ]),
                      child: Column(
                        children: [
                          Container(
                            width: 305.w,
                            height: 1.h,
                            color: const Color.fromRGBO(233, 232, 233, 1),
                          ),
                          SizedBox(height: 10.h),
                          DateWidget(
                            dayAction: (dateDay) =>
                                controller.selectDateAction(dateDay),
                            monthAction: (dateMonth) {},
                          )
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
          Container(
            margin: EdgeInsets.only(
                top: 10.h, bottom: ScreenUtil().statusBarHeight + 10.h),
            width: 283.w,
            height: 48.h,
            decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(24.r),
                color: const Color.fromRGBO(40, 39, 46, 1)),
            child: Center(
              child: ThemeText(
                dataStr: "schedule_add_item_btn".tr,
                keyName: 'textColor',
                fontWeight: FontWeight.w800,
              ),
            ),
          ).inkWell(() => controller.saveAction())
        ],
      ),
    );
  }

  @override
  Widget buildContent() {
    return GetBuilder<ScheduleAddController>(
      init: ScheduleAddController(),
      id: "schedule_add",
      builder: (_) {
        return _buildView();
      },
    );
  }
}
