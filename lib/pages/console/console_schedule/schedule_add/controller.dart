import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:getx_xiaopa/extension/widget_extension.dart';
import 'package:getx_xiaopa/network/http_client.dart';
import 'package:getx_xiaopa/network/model/result.dart';
import 'package:getx_xiaopa/pages/console/console_schedule/schedule_add/widgets/repeat_bottom_sheet.dart';
import 'package:getx_xiaopa/storage/storage.dart';
import 'package:getx_xiaopa/utils/utils.dart';

class ScheduleAddController extends GetxController {
  ScheduleAddController();

  bool showTime = false;
  DateTime _selectTime = DateTime.now();

  RepeatStruct repeat =
      RepeatStruct(id: 1, name: "schedule_repeat_one".tr, rule: "0000000");

  ///只有重复选择永不才能选择日期
  bool isShowDate = false;
  bool showDate = false;
  String today = '';
  final DateTime _nowDay = DateTime.now();
  DateTime _selectDay = DateTime.now();

  TextEditingController cTextEditingController = TextEditingController();
  int textLenght = 0;
  int maxLenght = ConfigStore.to.getLocaleCode() == "zh" ? 30 : 40;
  @override
  void onReady() {
    super.onReady();

    update(["schedule_add"]);
  }

  changeText(String value) {
    textLenght = value.length;
    update(["schedule_add"]);
  }

  changeDate(value) {
    showDate = value;
    bool flag = _isSameDay(_selectDay);
    if (flag) {
      today = 'console_schedule_today'.tr;
    } else {
      today = '';
    }
    update(["schedule_add"]);
  }

  changeTime(value) {
    showTime = value;
    update(["schedule_add"]);
  }

  changeRepeat() {
    Get.bottomSheet(RepeatBottomSheet(
      repeatStruct: repeat,
      onSave: (p0) {
        repeat = p0;
        if (repeat.id != 1) {
          isShowDate = true;
        } else {
          isShowDate = false;
        }
        update(["schedule_add"]);
      },
    ));
  }

  selectDateAction(DateTime date) {
    _selectDay = date;
    bool flag = _isSameDay(date);
    if (flag) {
      today = 'console_schedule_today'.tr;
    } else {
      today = '';
    }
    update(["schedule_add"]);
  }

  bool _isSameDay(DateTime b) {
    return _nowDay.year == b.year &&
        _nowDay.month == b.month &&
        _nowDay.day == b.day;
  }

  selectTimeAction(DateTime time) {
    _selectTime = time;
  }

  saveAction() {
    if (cTextEditingController.text.isEmpty) {
      CToast.showToast("schedule_repeat_tips_three".tr);
      return;
    }
    if (!showTime) {
      CToast.showToast("schedule_repeat_tips_two".tr);
      return;
    }
    if (repeat.id == 1) {
      if (!showDate) {
        CToast.showToast("schedule_repeat_tips_one".tr);
        return;
      }
    }

    if (_isAllSymbols(cTextEditingController.text)) {
      CToast.showToast("schedule_repeat_tips_five".tr);
      return;
    }

    String timeStr = '';

    if (isShowDate) {
      timeStr =
          '${_selectTime.hour.toString().padLeft(2, "0")}:${_selectTime.minute.toString().padLeft(2, "0")}';
    } else {
      timeStr =
          '${_selectDay.year}-${_selectDay.month.toString().padLeft(2, "0")}-${_selectDay.day.toString().padLeft(2, "0")} ${_selectTime.hour.toString().padLeft(2, "0")}:${_selectTime.minute.toString().padLeft(2, "0")}';

      ///日程提醒时间不能早于当前时间3分钟
      if (!TimeUtil.isAfterNowPlusMinutes(timeStr, outTime: 3)) {
        CToast.showToast("schedule_repeat_tips_four".tr);
        return;
      }
    }

    _addSchedule(time: timeStr);
  }

  bool _isAllSymbols(String input) {
    if (input.isEmpty) return false;

    // 匹配字母、数字、汉字
    final hasLetterOrDigitOrChinese =
        RegExp(r'[A-Za-z0-9\u4e00-\u9fa5]').hasMatch(input);

    // 如果没有这些，就说明全是符号
    return !hasLetterOrDigitOrChinese;
  }

  ///=====================网络请求========================
  _addSchedule({String time = ''}) async {
    CToast.showLoading();
    var map = {
      "device_id": UserStore.to.deviceList[UserStore.to.selectDevice].id,
      "time": time,
      "content": cTextEditingController.text,
      "repeat_rule": repeat.rule
    };
    Result result = await http.createSchedule(map);
    if (result.code == 0) {
      Get.back(result: "success");
    }
    CToast.showToast(result.msg!);
  }
}
