import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:getx_xiaopa/extension/widget_extension.dart';
import 'package:getx_xiaopa/utils/utils.dart';
import 'package:getx_xiaopa/values/values.dart';
import 'package:getx_xiaopa/widget/widget.dart';

class RepeatBottomSheet extends StatefulWidget {
  final RepeatStruct repeatStruct;
  final Function(RepeatStruct) onSave;
  const RepeatBottomSheet(
      {super.key, required this.repeatStruct, required this.onSave});

  @override
  State<RepeatBottomSheet> createState() => _RepeatBottomSheetState();
}

class _RepeatBottomSheetState extends State<RepeatBottomSheet> {
  ///rule:从第一位开始分别代表周一到周日对应。需要执行的位数是1，不需要执行的就是0
  final List<RepeatStruct> _list = [
    RepeatStruct(id: 1, name: "schedule_repeat_one".tr, rule: "0000000"),
    RepeatStruct(id: 2, name: "schedule_repeat_two".tr, rule: "1111111"),
    RepeatStruct(id: 3, name: "schedule_repeat_three".tr, rule: "1111100"),
    RepeatStruct(id: 4, name: "schedule_repeat_four".tr, rule: "0000011")
  ];

  int _index = 0;

  @override
  void initState() {
    super.initState();
    for (var i = 0; i < _list.length; i++) {
      if (_list[i].id == widget.repeatStruct.id) {
        _index = i;
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 1.sw,
      height: 407.h,
      decoration: BoxDecoration(
          borderRadius: BorderRadius.only(
              topLeft: Radius.circular(20.r), topRight: Radius.circular(20.r)),
          color: Colors.white),
      child: Column(
        children: [
          SizedBox(height: 20.h),
          Text(
            'schedule_add_item_three'.tr,
            style: StyleConfig.otherStyle(
                color: ColorConfig.searchTextColor,
                fontSize: 20,
                fontWeight: FontWeight.w700),
          ),
          SizedBox(height: 20.h),
          ...List.generate(_list.length, (index) {
            bool flag = _index == index;
            return Container(
              margin: EdgeInsets.only(left: 15.w, right: 15.w),
              width: 1.sw,
              height: 58.h,
              child: Column(
                children: [
                  Expanded(
                    child: Row(
                      children: [
                        SizedBox(width: 15.w),
                        Text(
                          _list[index].name,
                          style: StyleConfig.otherStyle(
                              color: ColorConfig.searchTextColor),
                        ),
                        const Expanded(child: SizedBox()),
                        CheckBoxRounded(
                          isChecked: flag,
                          isGroup: true,
                          value: index,
                          groupValue: _index,
                          uncheckedBgColor: Colors.white,
                          borderColor: const Color.fromRGBO(215, 215, 215, 1),
                          checkedColor: Get.find<ThemeColorController>()
                              .getColor('textColor'),
                          checkedBgColor: ColorConfig.searchTextColor,
                          onGroupTap: (value) {
                            setState(() {
                              _index = value!;
                            });
                          },
                        ),
                        SizedBox(width: 15.w),
                      ],
                    ),
                  ),
                  Container(
                    width: 345.w,
                    height: 1.h,
                    color: const Color.fromRGBO(233, 232, 233, 1),
                  )
                ],
              ),
            );
          }),
          SizedBox(height: 31.h),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              Container(
                width: 163.w,
                height: 40.h,
                decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(20.r),
                    border: Border.all(
                        width: 1.r,
                        color: const Color.fromRGBO(120, 120, 120, 1))),
                child: Center(
                  child: Text(
                    'cancel'.tr,
                    style: StyleConfig.otherStyle(
                        color: ColorConfig.authenticationTextColor),
                  ),
                ),
              ).inkWell(() => Get.back()),
              Container(
                width: 163.w,
                height: 40.h,
                decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(20.r),
                    color: ColorConfig.searchTextColor),
                child: Center(
                  child: ThemeText(
                    dataStr: 'schedule_repeat_save'.tr,
                    keyName: 'textColor',
                    fontWeight: FontWeight.w700,
                  ),
                ),
              ).inkWell(() {
                widget.onSave(_list[_index]);
                Get.back();
              })
            ],
          )
        ],
      ),
    );
  }
}

class RepeatStruct {
  int id;
  String name;
  String rule;
  RepeatStruct({this.id = 0, this.name = '', this.rule = ''});
}
