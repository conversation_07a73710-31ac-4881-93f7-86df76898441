import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:getx_xiaopa/extension/images.dart';
import 'package:getx_xiaopa/r.dart';
import 'package:getx_xiaopa/storage/storage.dart';
import 'package:getx_xiaopa/values/values.dart';
import 'package:getx_xiaopa/widget/widget.dart';
import 'package:table_calendar/table_calendar.dart';

class DateWidget extends StatefulWidget {
  final Function(DateTime) dayAction;
  final Function(DateTime) monthAction;
  const DateWidget(
      {super.key, required this.monthAction, required this.dayAction});

  @override
  State<DateWidget> createState() => _DateWidgetState();
}

class _DateWidgetState extends State<DateWidget> {
  DateTime _monthDay = DateTime.now();
  DateTime? _selectedDay; // 新增选中的日期变量

  @override
  void initState() {
    super.initState();
    _selectedDay = DateTime.now(); // 第一次默认选中今天
  }

  @override
  Widget build(BuildContext context) {
    return TableCalendar(
      availableGestures: AvailableGestures.horizontalSwipe,
      firstDay: DateTime.utc(2020, 1, 1),
      lastDay: DateTime.utc(2099, 12, 31),
      focusedDay: _monthDay,
      rowHeight: 44.h,
      daysOfWeekHeight: 44.h,
      locale: ConfigStore.to.locale.toString(),
      calendarFormat: CalendarFormat.month,
      onDaySelected: (selectedDay, focusedDay) {
        if (_isBefore(selectedDay)) return;

        setState(() {
          _selectedDay = selectedDay; // 更新选中日期
          _monthDay = focusedDay;
        });
        widget.dayAction(selectedDay);
      },
      selectedDayPredicate: (day) =>
          _isSameDay(day, _selectedDay ?? DateTime.now()),
      headerStyle: HeaderStyle(
          formatButtonVisible: false,
          titleCentered: true,
          headerMargin: EdgeInsets.symmetric(vertical: 2.h, horizontal: 10.w),
          leftChevronIcon: Container(
            width: 20.r,
            height: 20.r,
            decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(20.r),
                color: const Color.fromRGBO(242, 241, 245, 1)),
            child: Center(
              child: Icon(
                size: 20.r,
                Icons.chevron_left,
                color: const Color.fromRGBO(185, 185, 185, 1),
              ),
            ),
          ),
          rightChevronIcon: Container(
            width: 20.r,
            height: 20.r,
            decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(20.r),
                color: const Color.fromRGBO(242, 241, 245, 1)),
            child: Center(
              child: Icon(
                size: 20.r,
                Icons.chevron_right,
                color: const Color.fromRGBO(185, 185, 185, 1),
              ),
            ),
          ),
          headerPadding: EdgeInsets.zero,
          titleTextStyle: StyleConfig.otherStyle(
              color: ColorConfig.searchTextColor,
              fontWeight: FontWeight.w500,
              fontSize: 18)),
      calendarStyle: const CalendarStyle(
        outsideDaysVisible: false,
        isTodayHighlighted: false,
      ),
      calendarBuilders: CalendarBuilders(
        dowBuilder: (context, day) {
          final text = [
            'diary_week_seven'.tr,
            'diary_week_one'.tr,
            'diary_week_two'.tr,
            'diary_week_three'.tr,
            'diary_week_four'.tr,
            'diary_week_five'.tr,
            'diary_week_six'.tr
          ][day.weekday % 7];
          return Center(
            child: Text(
              text,
              style: StyleConfig.otherStyle(
                  color: ColorConfig.authenticationTextColor, fontSize: 14),
            ),
          );
        },
        headerTitleBuilder: (context, date) {
          String weekRange = _getWeekRange(date);
          return Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Images(path: R.diary_item_image_png, width: 15.r, height: 15.r),
              SizedBox(width: 5.w),
              Text(
                weekRange,
                style:
                    StyleConfig.otherStyle(color: ColorConfig.searchTextColor),
              )
            ],
          );
        },

        ///设置不是本月的样式
        outsideBuilder: (context, date, _) => _buildDayCell(date),
        defaultBuilder: (context, date, _) => _buildDayCell(date),
        todayBuilder: (context, date, _) => _buildDayCell(date),
        selectedBuilder: (context, date, _) => _buildDayCell(date),
      ),
      onPageChanged: (focusedDay) {
        setState(() {
          _monthDay = focusedDay;
          widget.monthAction.call(_monthDay);
        });
      },
    );
  }

  // 获取一周的日期范围
  String _getWeekRange(DateTime date) {
    String temp = '${date.year}.${date.month.toString().padLeft(2, "0")}';
    return temp;
  }

  bool _isSameDay(DateTime a, DateTime b) {
    return a.year == b.year && a.month == b.month && a.day == b.day;
  }

  bool _isBefore(DateTime a) {
    return a.isBefore(DateTime(
        DateTime.now().year, DateTime.now().month, DateTime.now().day));
  }

  Widget _buildDayCell(DateTime date) {
    final bool isSelected =
        _selectedDay != null && _isSameDay(date, _selectedDay!);

    final isPast = _isBefore(date);

    Color bgColor;
    Color textColor;
    FontWeight fontWeight;

    if (isPast) {
      bgColor = const Color.fromRGBO(215, 215, 215, 1); // 灰色背景
      textColor = ColorConfig.authenticationTextColor;
      fontWeight = FontWeight.w400;
    } else if (isSelected) {
      bgColor = const Color.fromRGBO(40, 39, 46, 1);
      textColor = ColorConfig.shopDetailTextColor;
      fontWeight = FontWeight.w600;
    } else {
      bgColor = const Color.fromRGBO(249, 249, 249, 1);
      textColor = ColorConfig.shopDetailTextColor;
      fontWeight = FontWeight.w400;
    }

    return Container(
      margin: EdgeInsets.symmetric(vertical: 8.w, horizontal: 8.h),
      decoration: BoxDecoration(color: bgColor, shape: BoxShape.circle),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            ThemeText(
              dataStr: '${date.day}',
              keyName: 'textColor',
              flag: isSelected,
              subColor: textColor,
              fontWeight: fontWeight,
              fontSize: 10,
            )
          ],
        ),
      ),
    );
  }
}
