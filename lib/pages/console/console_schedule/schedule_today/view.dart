import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:getx_xiaopa/base/base.dart';
import 'package:getx_xiaopa/extension/images.dart';
import 'package:getx_xiaopa/extension/widget_extension.dart';
import 'package:getx_xiaopa/pages/console/console_schedule/schedule_today/widgets/schedule_today_item.dart';
import 'package:getx_xiaopa/r.dart';
import 'package:getx_xiaopa/values/values.dart';
import 'package:getx_xiaopa/widget/widget.dart';

import 'index.dart';

class ScheduleTodayPage extends BaseCommonView<ScheduleTodayController> {
  ScheduleTodayPage({super.key});

  @override
  String? get navTitle => "schedule_today_title".tr;

  @override
  TextStyle? get navTitleStyle =>
      StyleConfig.blackStyle(fontSize: 18, fontWeight: FontWeight.w600);

  @override
  Color? get navColor => Colors.transparent;

  @override
  double? get leftWidth => 35.w;

  @override
  Widget? get leftButton => Padding(
        padding: EdgeInsets.only(left: 15.w),
        child: Images(
          path: R.back_png,
          width: 20.w,
          height: 20.h,
          scale: 2.8,
        ).inkWell(() => Get.back()),
      );

  @override
  Color? get contentColor => Colors.white;

  // 主视图
  Widget _buildView() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Expanded(
          child: controller.scheduleList.isEmpty
              ? EmptyDataWidget(
                  title: "schedule_temp_data".tr,
                  imagePath: R.no_data_schedule_png,
                )
              : ListView.builder(
                  itemCount: controller.scheduleList.length,
                  itemBuilder: (_, index) {
                    return ScheduleTodayItem(
                      index: index,
                      model: controller.scheduleList[index],
                      removeAction: (index) => controller.removeSchedule(index),
                    );
                  },
                ),
        ),
        Container(
          margin:
              EdgeInsets.only(top: 10.h, left: 46.w, right: 46.w, bottom: 44.h),
          width: 283.w,
          height: 48.h,
          decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(24.r),
              color: const Color.fromRGBO(40, 39, 46, 1)),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              ThemeImagePath(
                fileName: 'schedule_today_btn.png',
                imgWidget: 20.r,
                imgHeight: 20.r,
              ),
              SizedBox(width: 7.w),
              ThemeText(
                dataStr: "schedule_today_btn".tr,
                keyName: 'textColor',
                fontWeight: FontWeight.w800,
              )
            ],
          ),
        ).inkWell(() => controller.toAddSchedulePage())
      ],
    );
  }

  @override
  Widget buildContent() {
    return GetBuilder<ScheduleTodayController>(
      init: ScheduleTodayController(),
      id: "schedule_today",
      builder: (_) {
        return _buildView();
      },
    );
  }
}
