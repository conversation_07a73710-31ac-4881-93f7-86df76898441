import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_slidable/flutter_slidable.dart';
import 'package:get/get.dart';
import 'package:getx_xiaopa/pages/console/console_schedule/model/schedule_model_result.dart';
import 'package:getx_xiaopa/storage/storage.dart';
import 'package:getx_xiaopa/utils/utils.dart';
import 'package:getx_xiaopa/values/values.dart';
import 'package:getx_xiaopa/widget/widget.dart';

class ScheduleTodayItem extends StatelessWidget {
  final int index;
  final ScheduleModel model;
  final Function(int) removeAction;
  const ScheduleTodayItem(
      {super.key,
      required this.index,
      required this.model,
      required this.removeAction});

  @override
  Widget build(BuildContext context) {
    bool isPass =
        TimeUtil.isTimePassed(TimeUtil.getFormatDateHM(model.nextActiveAt));

    return Container(
      margin: EdgeInsets.only(left: 23.w, right: 29.w),
      child: IntrinsicHeight(
        child: Row(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Column(
              children: [
                Container(
                  width: 14.r,
                  height: 14.r,
                  decoration: BoxDecoration(
                      border: Border.all(
                          width: isPass ? 0 : 1.r,
                          color: isPass
                              ? Colors.transparent
                              : const Color.fromRGBO(215, 215, 215, 1)),
                      color: isPass
                          ? Get.find<ThemeColorController>().gIconColor
                          : Colors.transparent,
                      shape: BoxShape.circle),
                ),
                Expanded(
                  child: Container(
                    width: 1.w,
                    color: const Color.fromRGBO(249, 249, 249, 1),
                  ),
                )
              ],
            ),
            SizedBox(width: 20.w),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  ThemeText(
                    dataStr: TimeUtil.getFormatDateHM(model.nextActiveAt),
                    keyName: "mineOnline",
                    flag: isPass,
                    subColor: ColorConfig.searchTextColor,
                    height: 1,
                    fontWeight: FontWeight.w700,
                  ),
                  Container(
                    margin: EdgeInsets.only(top: 8.h, bottom: 10.h),
                    child: Slidable(
                      key: ValueKey(index),
                      endActionPane: ActionPane(
                        extentRatio: 0.3,
                        motion: const BehindMotion(),
                        children: [
                          SlidableAction(
                            borderRadius: BorderRadius.horizontal(
                                right: Radius.circular(10.r)),
                            onPressed: (context) {
                              removeAction.call(index);
                            },
                            backgroundColor:
                                const Color.fromRGBO(255, 80, 110, 1),
                            foregroundColor: Colors.white,
                            icon: Icons.delete,
                            label: ConfigStore.to.getLocaleCode() == "zh"
                                ? "schedule_today_delete".tr
                                : null,
                          )
                        ],
                      ),
                      child: ThemeContainer(
                        keyName: "scheduleItemColor",
                        flag: isPass,
                        subColor: const Color.fromRGBO(249, 249, 249, 1),
                        tWidget: double.infinity, // 动态宽度
                        tPandding: EdgeInsets.all(16.r),
                        radius: 10.r,
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              model.content ?? '',
                              style: StyleConfig.otherStyle(
                                  color: const Color.fromRGBO(69, 47, 10, 1),
                                  fontWeight: FontWeight.w600),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            )
          ],
        ),
      ),
    );
  }
}
