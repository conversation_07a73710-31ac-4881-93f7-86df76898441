import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_slidable/flutter_slidable.dart';
import 'package:get/get.dart';
import 'package:getx_xiaopa/pages/console/console_schedule/model/schedule_model_result.dart';
import 'package:getx_xiaopa/storage/storage.dart';
import 'package:getx_xiaopa/utils/utils.dart';
import 'package:getx_xiaopa/values/values.dart';
import 'package:getx_xiaopa/widget/widget.dart';

class SchedulePlanItem extends StatelessWidget {
  final String dateKey;
  final List<ScheduleModel> model;
  final Function(String) removeAction;
  const SchedulePlanItem(
      {super.key,
      required this.model,
      required this.dateKey,
      required this.removeAction});

  @override
  Widget build(BuildContext context) {
    return IntrinsicHeight(
      child: Container(
        margin: EdgeInsets.only(top: 10.h, left: 15.w, right: 15.w),
        padding: EdgeInsets.all(15.r),
        width: 345.w,
        decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(10.r), color: Colors.white),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            ThemeText(
              dataStr: dateKey.replaceAll("-", "."),
              keyName: 'mineOnline',
              fontSize: 18,
              fontWeight: FontWeight.w700,
            ),
            ...List.generate(model.length, (index) {
              return Container(
                margin: EdgeInsets.only(top: 10.h),
                child: Slidable(
                  key: ValueKey(index),
                  endActionPane: ActionPane(
                    extentRatio: 0.3,
                    motion: const BehindMotion(),
                    children: [
                      SlidableAction(
                        borderRadius: BorderRadius.horizontal(
                            right: Radius.circular(10.r)),
                        onPressed: (context) {
                          removeAction.call(model[index].id.toString());
                        },
                        backgroundColor: const Color.fromRGBO(255, 80, 110, 1),
                        foregroundColor: Colors.white,
                        icon: Icons.delete,
                        label: ConfigStore.to.getLocaleCode() == "zh"
                            ? "schedule_today_delete".tr
                            : null,
                      )
                    ],
                  ),
                  child: Container(
                    padding: EdgeInsets.only(left: 15.w),
                    width: 315.w,
                    height: 79.h,
                    decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(10.r),
                        color: const Color.fromRGBO(249, 249, 249, 1)),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          TimeUtil.getFormatDateHM(model[index].nextActiveAt),
                          style: StyleConfig.otherStyle(
                              color: ColorConfig.searchTextColor,
                              fontWeight: FontWeight.w700),
                        ),
                        SizedBox(height: 6.h),
                        Text(
                          model[index].content ?? '',
                          style: StyleConfig.otherStyle(
                              color: ColorConfig.authenticationTextColor,
                              fontWeight: FontWeight.w500),
                        ),
                      ],
                    ),
                  ),
                ),
              );
            })
          ],
        ),
      ),
    );
  }
}
