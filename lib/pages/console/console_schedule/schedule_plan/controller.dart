import 'package:get/get.dart';
import 'package:getx_xiaopa/extension/widget_extension.dart';
import 'package:getx_xiaopa/network/http_client.dart';
import 'package:getx_xiaopa/network/model/result.dart';
import 'package:getx_xiaopa/pages/console/console_schedule/index.dart';
import 'package:getx_xiaopa/pages/console/console_schedule/model/schedule_model_result.dart';
import 'package:getx_xiaopa/routes/router.dart';

class SchedulePlanController extends GetxController {
  SchedulePlanController();

  Map<String, List<ScheduleModel>> scheduleMap = {};

  @override
  void onReady() {
    super.onReady();
    scheduleMap = ScheduleStore.to.schedulePlanMap;

    update(["schedule_plan"]);
  }

  toAddSchedulePage() {
    Get.toNamed(AppRoutes.SCHEDULE_ADD)?.then((value) async {
      if (value == "success") {
        _requestPlanDate();
      }
    });
  }

  removeSchedule(String sId) {
    _removeSchedule(scheduleId: sId);
  }

  ///=====================网络请求===============================
  _requestPlanDate() async {
    await ScheduleStore.to.getNoRepeatSchedule();
    scheduleMap = ScheduleStore.to.schedulePlanMap;

    update(["schedule_plan"]);
  }

  _removeSchedule({required String scheduleId}) async {
    CToast.showLoading();
    var map = {"id": scheduleId};
    Result result = await http.rmoveSchedule(map);
    if (result.code == 0) {
      _requestPlanDate();
    } else {
      CToast.showToast(result.msg!);
    }
  }
}
