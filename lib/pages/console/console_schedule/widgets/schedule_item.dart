import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:getx_xiaopa/extension/widget_extension.dart';
import 'package:getx_xiaopa/values/values.dart';

class ScheduleItem extends StatelessWidget {
  final Widget? icon;
  final String title;
  final Widget? subWidget;
  final double? mHeight;
  final Function() onClick;

  const ScheduleItem({
    super.key,
    this.icon,
    required this.title,
    this.subWidget,
    this.mHeight = 72,
    required this.onClick,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.only(left: 10.w, right: 10.w),
      width: 345.w,
      height: mHeight?.h,
      decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(10.r),
          color: Colors.white,
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.06),
              // 阴影颜色和透明度
              spreadRadius: 0,
              // 阴影扩散范围
              blurRadius: 10,
              // 阴影模糊程度
              offset: const Offset(0, 2),
              // 阴影偏移量（水平，垂直）
            )
          ]),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              icon ?? const SizedBox(),
              SizedBox(width: 4.w),
              Text(
                title,
                style: StyleConfig.otherStyle(
                    color: ColorConfig.searchTextColor, fontSize: 16),
              ),
              const Expanded(child: SizedBox()),
              Container(
                margin: EdgeInsets.only(right: 21.w),
                child: subWidget,
              ),
            ],
          ),
        ],
      ),
    ).inkWell(() => onClick());
  }
}
