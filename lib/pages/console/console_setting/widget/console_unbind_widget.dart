import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:getx_xiaopa/extension/images.dart';
import 'package:getx_xiaopa/extension/widget_extension.dart';
import 'package:getx_xiaopa/r.dart';
import 'package:getx_xiaopa/routes/router.dart';
import 'package:getx_xiaopa/utils/utils.dart';
import 'package:getx_xiaopa/values/values.dart';

class ConsoleUnbindWidget extends StatelessWidget {
  const ConsoleUnbindWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      onPopInvokedWithResult: (canPop, result) {},
      child: Container(
        color: Colors.white,
        child: Column(
          children: [
            SizedBox(height: 237.h),
            Images(
              path: R.console_bind_image_04_png,
              width: 150.w,
              height: 150.h,
            ),
            SizedBox(height: 22.h),
            Text(
              "console_unbind_success".tr,
              style: StyleConfig.otherStyle(
                  color: ColorConfig.searchTextColor,
                  fontSize: 14,
                  fontWeight: FontWeight.w500),
            ),
            SizedBox(height: 6.h),
            Text(
              "console_unbind_success_tips".tr,
              textAlign: TextAlign.center,
              style: StyleConfig.otherStyle(
                  color: ColorConfig.shopDetailTextColor, fontSize: 14),
            ),
            SizedBox(height: 22.h),
            Container(
              width: 200.w,
              height: 48.h,
              decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(24.r),
                  color: const Color.fromRGBO(40, 39, 46, 1)),
              child: Center(
                child: Text(
                  "console_unbind_back".tr,
                  style: StyleConfig.otherStyle(
                      color: Get.find<ThemeColorController>().gTextColor,
                      fontWeight: FontWeight.w500),
                ),
              ),
            ).inkWell(() async {
              Get.until((route) => Get.currentRoute == AppRoutes.APPLICATION);
            })
          ],
        ),
      ),
    );
  }
}
