///
/// Code generated by jsonToDartModel https://ashamp.github.io/jsonToDartModel/
///
class VersionDeviceModel {
/*
{
  "id": "4693409399832576",
  "created_at": "2025-04-11T11:39:54.33+08:00",
  "updated_at": "2025-04-11T11:39:54.33+08:00",
  "deleted_at": null,
  "name": "v1.0.1",
  "client": 2,
  "type": 3,
  "code": "1.0.1",
  "number": 2,
  "logs": "",
  "url": "",
  "redirect": ""
} 
*/

  String? id;
  String? createdAt;
  String? updatedAt;
  String? deletedAt;
  String? name;
  int? client;
  int? type;
  String? code;
  int? number;
  String? logs;
  String? url;
  String? redirect;

  VersionDeviceModel({
    this.id,
    this.createdAt,
    this.updatedAt,
    this.deletedAt,
    this.name,
    this.client,
    this.type,
    this.code,
    this.number,
    this.logs,
    this.url,
    this.redirect,
  });
  VersionDeviceModel.fromJson(Map<String, dynamic> json) {
    id = json['id']?.toString();
    createdAt = json['created_at']?.toString();
    updatedAt = json['updated_at']?.toString();
    deletedAt = json['deleted_at']?.toString();
    name = json['name']?.toString();
    client = json['client']?.toInt();
    type = json['type']?.toInt();
    code = json['code']?.toString();
    number = json['number']?.toInt();
    logs = json['logs']?.toString();
    url = json['url']?.toString();
    redirect = json['redirect']?.toString();
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['id'] = id;
    data['created_at'] = createdAt;
    data['updated_at'] = updatedAt;
    data['deleted_at'] = deletedAt;
    data['name'] = name;
    data['client'] = client;
    data['type'] = type;
    data['code'] = code;
    data['number'] = number;
    data['logs'] = logs;
    data['url'] = url;
    data['redirect'] = redirect;
    return data;
  }
}
