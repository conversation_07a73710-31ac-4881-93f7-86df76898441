import 'package:get/get.dart';
import 'package:getx_xiaopa/base/base.dart';
import 'package:getx_xiaopa/extension/widget_extension.dart';
import 'package:getx_xiaopa/network/http_client.dart';
import 'package:getx_xiaopa/network/model/request_body.dart';
import 'package:getx_xiaopa/network/model/result.dart';
import 'package:getx_xiaopa/pages/console/model/device_items_result.dart';
import 'package:getx_xiaopa/storage/storage.dart';

import 'console_update_state.dart';

class ConsoleUpdateLogic extends BaseCommonController {
  @override
  final ConsoleUpdateState state = ConsoleUpdateState();

  @override
  void initData() {
    versionDevice(isInit: false);
    if (UserStore.to.deviceList.isEmpty) return;
    state.model = UserStore.to.deviceList[UserStore.to.selectDevice];
  }

  ///每次进来请求一次
  reloadVersion() {
    versionDevice();
  }

  @override
  void onHidden() {}

  autoModel(flag) {
    state.isManualChange = true;

    ///5秒后自动恢复
    Future.delayed(const Duration(seconds: 5), () {
      state.isManualChange = false;
    });

    state.isAuto = flag;
    int isUpgrade = flag ? 1 : 2;

    sendDeviceConfigAction(type: 2, command: "is_upgrade", value: isUpgrade);
  }

  /// 立即更新
  updateNow() {
    if (state.model.isOnline == 2) {
      CToast.showToast("console_offline_tips".tr);
      return;
    }
    sendDeviceConfigAction(type: 3, command: "", value: "");
  }

  ///更新后socket更新版本号则更新显示ui
  updateStatus(DeviceItemsModel model) {
    state.model = model;
    if (state.versionDeviceModel.code == state.model.firmwareVersion) {
      state.isUpdate = true;
    } else {
      state.isUpdate = false;
    }

    if (!state.isManualChange) {
      state.isAuto = state.model.isUpgrade == 1 ? true : false;
    }

    state.currentVersion = state.model.firmwareVersion ?? "";

    update(["console_update_view"]);
  }

  ///================网络请求==================
  ///isinit 初始化的时候不做版本号对比
  versionDevice({bool isInit = true}) async {
    var map =
        RequestBody(filters: [Filter(expr: "=", name: "type", value: "3")])
            .toJson();
    Result result = await http.versionDevice(map);
    if (result.code == 0) {
      state.versionDeviceModel = result.data;
      if (isInit) {
        if (state.model.firmwareVersion == state.versionDeviceModel.code) {
          state.versionTis = "console_update_new_version_now".tr;
          state.isUpdate = true;
        } else {
          state.versionTis = "console_update_version_now".tr;
          state.isUpdate = false;
        }
      }
    } else {
      CToast.showToast(result.msg!);
    }

    netState = NetState.dataSuccessState;
    update(["console_update_view"]);
  }

  ///========================网络请求===============================
  /// 发送修改设备配置
  /// type 1：重启 2：查询电量音量 3：升级 4：设置音量 5：查询版本号 6：设置麦克风 7：设置提醒
  sendDeviceConfigAction(
      {int? type, String command = "", dynamic value = ""}) async {
    CToast.showLoading();
    var map = {
      "device_id": UserStore.to.deviceList[UserStore.to.selectDevice].id,
      "command": command,
      "value": value,
      "type": type
    };
    Result result = await http.setUpDevice(map);
    if (result.code == 0) {
      update(["console_update_view"]);
    } else {
      CToast.showToast(result.msg!);
    }
  }
}
