import 'package:get/get.dart';
import 'package:getx_xiaopa/pages/console/console_setting/console_update/model/version_device.dart';
import 'package:getx_xiaopa/pages/console/model/device_items_result.dart';

class ConsoleUpdateState {
  /// 是否自动升级，默认false
  bool isAuto = false;

  /// 是否显示立即更新按钮
  bool isUpdate = false;

  VersionDeviceModel versionDeviceModel = VersionDeviceModel();

  DeviceItemsModel model = DeviceItemsModel();

  String currentVersion = "";

  String versionTis = "console_update_current_version".tr;

  ///是否正在修改自动更新模式
  bool isManualChange = false;

  ConsoleUpdateState() {
    ///Initialize variables
  }
}
