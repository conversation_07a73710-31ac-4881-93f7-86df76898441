import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:getx_xiaopa/base/base.dart';
import 'package:getx_xiaopa/extension/widget_extension.dart';
import 'package:getx_xiaopa/network/http_client.dart';
import 'package:getx_xiaopa/network/model/result.dart';
import 'package:getx_xiaopa/pages/console/model/device_items_result.dart';
import 'package:getx_xiaopa/widget/widget.dart';

class ConsoleCompiledController extends BaseCommonController {
  List<String> typeStr = [];
  int type = 0;

  ///活跃频率
  int activeFrequency = 0;
  int type_1 = 0;
  int type_2 = 0;

  bool isDisturb = false;

  DateTime startTimeDate = DateTime.now();
  DateTime endTimeDate = DateTime.now();

  DateTime tempDate = DateTime.now();

  String startTime = "23:00";
  String endTime = "07:00";

  String startTips = "console_compiled_today".tr;
  String endTips = "console_compiled_tomorrow".tr;

  late DeviceItemsModel model;

  @override
  void initData() {}

  @override
  void onHidden() {}

  ///每次进来请求一次
  reloadData() {
    type = model.personality! - 1;
    activeFrequency = model.activeFrequency ?? 0;
    if (activeFrequency == 3) {
      type_2 = 2;
    } else if (activeFrequency == 10) {
      type_2 = 1;
    } else if (activeFrequency == 15) {
      type_2 = 0;
    } else if (activeFrequency == 25) {
      type_1 = 1;
    } else if (activeFrequency == 30) {
      type_1 = 0;
    } else {
      type_1 = 0;
      type_2 = 0;
    }

    isDisturb = model.isMute ?? false;

    if (model.muteAt != null && model.muteAt!.isNotEmpty) {
      String tempStr = model.muteAt!.split("-")[0];
      String tempEnd = model.muteAt!.split("-")[1];
      DateTime tempDate = DateTime.now();
      String startTemp =
          "${tempDate.year}-${tempDate.month.toString().padLeft(2, '0')}-${tempDate.day.toString().padLeft(2, '0')} $tempStr:00";
      String endTemp =
          "${tempDate.year}-${tempDate.month.toString().padLeft(2, '0')}-${tempDate.day.toString().padLeft(2, '0')} $tempEnd:00";
      startTimeDate = DateTime.parse(startTemp);
      endTimeDate = DateTime.parse(endTemp);
      startTime = _getModelTime(startTimeDate);
      endTime = _getModelTime(endTimeDate);
      _getTips(startTimeDate, endTimeDate);
    }

    typeStr = [
      "console_compiled_type_one".tr,
      "console_compiled_type_two".tr,
      "console_compiled_type_three".tr
    ];

    netState = NetState.dataSuccessState;
    update(["console_compiled_view"]);
  }

  updateStatus(DeviceItemsModel vModel) {
    model = vModel;
    update(["console_compiled_view"]);
  }

  typeAction(int index) {
    type = index;
    update(["console_compiled_view"]);
  }

  type21Action(int index) {
    type_1 = index;
    update(["console_compiled_view"]);
  }

  type22Action(int index) {
    type_2 = index;
    update(["console_compiled_view"]);
  }

  isDisturbAction(bool value) {
    isDisturb = value;
    update(["console_compiled_view"]);
  }

  startTimeAction() {
    _showStartTimePicker();
  }

  endTimeAction() {
    _showEndTimePicker();
  }

  _showStartTimePicker() {
    tempDate = startTimeDate;
    showModalBottomSheet(
      context: Get.context!,
      isDismissible: false,
      enableDrag: true,
      builder: (context) => Container(
        width: 1.sw,
        height: 300.h,
        color: Colors.white,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.end,
          children: [
            Row(
              children: [
                Container(
                  margin: EdgeInsets.only(left: 15.w, top: 15.h),
                  width: 50.w,
                  height: 28.h,
                  decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(24.r),
                      color: const Color.fromRGBO(40, 39, 46, 1)),
                  child: Center(
                    child: ThemeText(
                      dataStr: "console_compiled_close".tr,
                      keyName: 'textColor',
                      fontWeight: FontWeight.w400,
                      fontSize: 12,
                    ),
                  ),
                ).inkWell(() => Get.back()),
                const Expanded(child: SizedBox()),
                Container(
                  margin: EdgeInsets.only(right: 15.w, top: 15.h),
                  width: 50.w,
                  height: 28.h,
                  decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(24.r),
                      color: const Color.fromRGBO(40, 39, 46, 1)),
                  child: Center(
                    child: ThemeText(
                      dataStr: "console_compiled_confirm".tr,
                      keyName: 'textColor',
                      fontWeight: FontWeight.w400,
                      fontSize: 12,
                    ),
                  ),
                ).inkWell(() {
                  startTimeDate = tempDate;
                  startTime = _getModelTime(startTimeDate);
                  _getTips(startTimeDate, endTimeDate);
                  Get.back();
                }),
              ],
            ),
            Expanded(
              child: CupertinoDatePicker(
                mode: CupertinoDatePickerMode.time,
                itemExtent: 50.h,
                backgroundColor: Colors.white,
                use24hFormat: true,
                initialDateTime: startTimeDate,
                onDateTimeChanged: (value) {
                  tempDate = value;
                },
              ),
            )
          ],
        ),
      ),
    );
  }

  _showEndTimePicker() {
    tempDate = endTimeDate;
    showModalBottomSheet(
      context: Get.context!,
      isDismissible: false,
      enableDrag: true,
      builder: (context) => Container(
        width: 1.sw,
        height: 300.h,
        color: Colors.white,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.end,
          children: [
            Row(
              children: [
                Container(
                  margin: EdgeInsets.only(left: 15.w, top: 15.h),
                  width: 50.w,
                  height: 28.h,
                  decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(24.r),
                      color: const Color.fromRGBO(40, 39, 46, 1)),
                  child: Center(
                    child: ThemeText(
                      dataStr: "console_compiled_close".tr,
                      keyName: 'textColor',
                      fontWeight: FontWeight.w400,
                      fontSize: 12,
                    ),
                  ),
                ).inkWell(() => Get.back()),
                const Expanded(child: SizedBox()),
                Container(
                  margin: EdgeInsets.only(right: 15.w, top: 15.h),
                  width: 50.w,
                  height: 28.h,
                  decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(24.r),
                      color: const Color.fromRGBO(40, 39, 46, 1)),
                  child: Center(
                    child: ThemeText(
                      dataStr: "console_compiled_confirm".tr,
                      keyName: 'textColor',
                      fontWeight: FontWeight.w400,
                      fontSize: 12,
                    ),
                  ),
                ).inkWell(() {
                  endTimeDate = tempDate;
                  endTime = _getModelTime(endTimeDate);
                  _getTips(startTimeDate, endTimeDate);
                  Get.back();
                }),
              ],
            ),
            Expanded(
              child: CupertinoDatePicker(
                mode: CupertinoDatePickerMode.time,
                itemExtent: 50.h,
                backgroundColor: Colors.white,
                use24hFormat: true,
                initialDateTime: endTimeDate,
                onDateTimeChanged: (value) {
                  tempDate = value;
                },
              ),
            )
          ],
        ),
      ),
    );
  }

  _getModelTime(DateTime date) {
    return "${date.hour.toString().padLeft(2, '0')}:${date.minute.toString().padLeft(2, '0')}";
  }

  _getTips(DateTime sDate, DateTime eDate) {
    if (sDate.isBefore(eDate)) {
      startTips = "console_compiled_today".tr;
      endTips = "console_compiled_today".tr;
    } else {
      startTips = "console_compiled_today".tr;
      endTips = "console_compiled_tomorrow".tr;
    }
    update(["console_compiled_view"]);
  }

  int _getFrequency(int type) {
    if (type == 0) {
      return 30;
    } else if (type == 1) {
      return 25;
    } else if (type == 2) {
      return 15;
    } else if (type == 3) {
      return 10;
    } else if (type == 4) {
      return 3;
    } else {
      return 1;
    }
  }

  saveAction() {
    int tempType = 0;
    if (type == 1) {
      tempType = type_1;
    } else if (type == 2) {
      tempType = type_2 + 2;
    }

    activeFrequency = _getFrequency(tempType);
    if (type == 0) {
      activeFrequency = 0;
    }

    _saveDevicePreference();
  }

  ///====================互动偏好====================
  _saveDevicePreference() async {
    CToast.showLoading();
    var map = {
      "id": model.id,
      "personality": type + 1,
      "active_frequency": activeFrequency,
      "is_mute": isDisturb,
      "mute_at": "$startTime-$endTime"
    };
    Result result = await http.updateDevicePreference(map);
    CToast.showToast(result.msg!);
  }
}
