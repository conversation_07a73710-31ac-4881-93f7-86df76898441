import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:getx_xiaopa/base/base.dart';
import 'package:getx_xiaopa/extension/images.dart';
import 'package:getx_xiaopa/extension/widget_extension.dart';
import 'package:getx_xiaopa/r.dart';
import 'package:getx_xiaopa/utils/utils.dart';
import 'package:getx_xiaopa/values/values.dart';
import 'package:getx_xiaopa/widget/widget.dart';

import 'index.dart';

class ConsoleCompiledPage extends BaseCommonView {
  ConsoleCompiledPage({super.key});

  final ConsoleCompiledController logic = Get.put(ConsoleCompiledController());

  @override
  String? get navTitle => "console_compiled_title".tr;

  @override
  TextStyle? get navTitleStyle =>
      StyleConfig.blackStyle(fontSize: 18, fontWeight: FontWeight.w600);

  @override
  Color? get navColor => Colors.transparent;

  @override
  double? get leftWidth => 35.w;

  @override
  Widget? get leftButton => Padding(
        padding: EdgeInsets.only(left: 15.w),
        child: Images(
          path: R.back_png,
          width: 20.w,
          height: 20.h,
          scale: 2.8,
        ).inkWell(() => Get.back()),
      );

  @override
  Color? get contentColor => const Color.fromRGBO(249, 249, 249, 1);

  // 主视图
  Widget _buildView() {
    return Column(
      children: [
        _firstWidget(),
        Offstage(
          offstage: logic.type == 0,
          child: logic.type == 1 ? _secondWidget() : _second2Widget(),
        ),
        Offstage(
          offstage: logic.type == 0,
          child: _thirdWidget(),
        ),
      ],
    );
  }

  ///第一个控件
  Widget _firstWidget() {
    return Container(
      padding: EdgeInsets.only(top: 16.h, left: 10.w, right: 10.w),
      width: 345.w,
      height: 224.h,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(10.r),
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.06),
            // 阴影颜色和透明度
            spreadRadius: 0,
            // 阴影扩散范围
            blurRadius: 10,
            // 阴影模糊程度
            offset: const Offset(0, 2),
            // 阴影偏移量（水平，垂直）
          )
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            margin: EdgeInsets.only(left: 4.w),
            child: Text(
              "console_compiled_first_title".tr,
              style: StyleConfig.otherStyle(
                color: ColorConfig.searchTextColor,
                fontWeight: FontWeight.w700,
              ),
            ),
          ),
          Container(
            margin: EdgeInsets.only(top: 4.h, left: 4.w),
            child: Text(
              "console_compiled_first_tips".tr,
              style: StyleConfig.otherStyle(
                  color: ColorConfig.shopDetailTextColor, fontSize: 12),
            ),
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: List.generate(logic.typeStr.length, (index) {
              return Container(
                margin: EdgeInsets.only(top: 12.h),
                width: 101.w,
                height: 137.h,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(16.r),
                  color: const Color.fromRGBO(249, 249, 249, 1),
                  border: Border.all(
                    color: logic.type == index
                        ? Get.find<ThemeColorController>().getColor('iconColor')
                        : Colors.transparent,
                    width: 1,
                  ),
                ),
                child: Column(
                  children: [
                    ThemeImagePath(
                      fileName: 'compiled_image_0${index + 1}.png',
                      imgWidget: 101.w,
                      imgHeight: 98.h,
                    ),
                    Container(
                      width: 93.w,
                      height: 33.h,
                      decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(13.r),
                          color: logic.type == index
                              ? Get.find<ThemeColorController>()
                                  .getColor('textColor')
                              : Colors.white),
                      child: Center(
                        child: Text(
                          logic.typeStr[index],
                          style: StyleConfig.otherStyle(
                              color: ColorConfig.searchTextColor,
                              fontSize: 14,
                              fontWeight: logic.type == index
                                  ? FontWeight.w600
                                  : FontWeight.w400),
                        ),
                      ),
                    )
                  ],
                ),
              ).inkWell(() => logic.typeAction(index));
            }),
          )
        ],
      ),
    );
  }

  ///第二个控件
  Widget _secondWidget() {
    return Container(
      margin: EdgeInsets.only(top: 10.h),
      padding: EdgeInsets.only(left: 14.w, right: 14.w, top: 16.h),
      width: 345.w,
      height: 219.h,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(10.r),
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.06),
            // 阴影颜色和透明度
            spreadRadius: 0,
            // 阴影扩散范围
            blurRadius: 10,
            // 阴影模糊程度
            offset: const Offset(0, 2),
            // 阴影偏移量（水平，垂直）
          )
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            "console_compiled_second_title".tr,
            style: StyleConfig.otherStyle(
              color: ColorConfig.searchTextColor,
              fontWeight: FontWeight.w700,
            ),
          ),
          SizedBox(height: 4.h),
          SizedBox(
            width: 270.w,
            child: Text(
              "console_compiled_second_tips".tr,
              style: StyleConfig.otherStyle(
                  color: ColorConfig.shopDetailTextColor,
                  fontWeight: FontWeight.w400,
                  fontSize: 12),
            ),
          ),
          SizedBox(height: 12.h),
          Container(
            padding: EdgeInsets.only(left: 10.w, right: 10.w),
            width: 315.w,
            height: 61.h,
            decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(6.r),
                color: const Color.fromRGBO(249, 249, 249, 1)),
            child: Row(
              children: [
                Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      "console_compiled_second_item_one_title".tr,
                      style: StyleConfig.otherStyle(
                        color: ColorConfig.searchTextColor,
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    SizedBox(height: 6.h),
                    SizedBox(
                      width: 270.w,
                      child: Text(
                        "console_compiled_second_item_one_tips".tr,
                        maxLines: 2,
                        style: StyleConfig.otherStyle(
                          color: ColorConfig.shopDetailTextColor,
                          fontSize: 12,
                          fontWeight: FontWeight.w400,
                        ),
                      ),
                    )
                  ],
                ),
                const Expanded(child: SizedBox()),
                CheckBoxRounded(
                  isGroup: true,
                  value: 0,
                  groupValue: logic.type_1,
                  checkedColor:
                      Get.find<ThemeColorController>().getColor('textColor'),
                  checkedBgColor: ColorConfig.searchTextColor,
                  uncheckedBgColor: Colors.white,
                  borderColor: const Color.fromRGBO(215, 215, 215, 1),
                  onGroupTap: (sign) => logic.type21Action(sign!),
                )
              ],
            ),
          ).inkWell(() => logic.type21Action(0)),
          SizedBox(height: 12.h),
          Container(
            padding: EdgeInsets.only(left: 10.w, right: 10.w),
            width: 315.w,
            height: 61.h,
            decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(6.r),
                color: const Color.fromRGBO(249, 249, 249, 1)),
            child: Row(
              children: [
                Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      "console_compiled_second_item_two_title".tr,
                      style: StyleConfig.otherStyle(
                        color: ColorConfig.searchTextColor,
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    SizedBox(height: 6.h),
                    SizedBox(
                      width: 270.w,
                      child: Text(
                        "console_compiled_second_item_two_tips".tr,
                        style: StyleConfig.otherStyle(
                          color: ColorConfig.shopDetailTextColor,
                          fontSize: 12,
                          fontWeight: FontWeight.w400,
                        ),
                      ),
                    ),
                  ],
                ),
                const Expanded(child: SizedBox()),
                CheckBoxRounded(
                  isGroup: true,
                  value: 1,
                  groupValue: logic.type_1,
                  checkedColor:
                      Get.find<ThemeColorController>().getColor('textColor'),
                  checkedBgColor: ColorConfig.searchTextColor,
                  uncheckedBgColor: Colors.white,
                  borderColor: const Color.fromRGBO(215, 215, 215, 1),
                  onGroupTap: (sign) => logic.type21Action(sign!),
                )
              ],
            ),
          ).inkWell(() => logic.type21Action(1))
        ],
      ),
    );
  }

  ///第二个控件2
  Widget _second2Widget() {
    return Container(
      margin: EdgeInsets.only(top: 10.h),
      padding: EdgeInsets.only(left: 14.w, right: 14.w, top: 16.h),
      width: 345.w,
      height: 290.h,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(10.r),
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.06),
            // 阴影颜色和透明度
            spreadRadius: 0,
            // 阴影扩散范围
            blurRadius: 10,
            // 阴影模糊程度
            offset: const Offset(0, 2),
            // 阴影偏移量（水平，垂直）
          )
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            "console_compiled_second_title".tr,
            style: StyleConfig.otherStyle(
              color: ColorConfig.searchTextColor,
              fontWeight: FontWeight.w700,
            ),
          ),
          SizedBox(height: 4.h),
          SizedBox(
            width: 270.w,
            child: Text(
              "console_compiled_second_tips".tr,
              style: StyleConfig.otherStyle(
                  color: ColorConfig.shopDetailTextColor,
                  fontWeight: FontWeight.w400,
                  fontSize: 12),
            ),
          ),
          SizedBox(height: 12.h),
          Container(
            padding: EdgeInsets.only(left: 10.w, right: 10.w),
            width: 315.w,
            height: 61.h,
            decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(6.r),
                color: const Color.fromRGBO(249, 249, 249, 1)),
            child: Row(
              children: [
                Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      "console_compiled_second_item_three_title".tr,
                      style: StyleConfig.otherStyle(
                        color: ColorConfig.searchTextColor,
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    SizedBox(height: 6.h),
                    SizedBox(
                      width: 270.w,
                      child: Text(
                        "console_compiled_second_item_three_tips".tr,
                        style: StyleConfig.otherStyle(
                          color: ColorConfig.shopDetailTextColor,
                          fontSize: 12,
                          fontWeight: FontWeight.w400,
                        ),
                      ),
                    ),
                  ],
                ),
                const Expanded(child: SizedBox()),
                CheckBoxRounded(
                  isGroup: true,
                  value: 0,
                  groupValue: logic.type_2,
                  checkedColor:
                      Get.find<ThemeColorController>().getColor('textColor'),
                  checkedBgColor: ColorConfig.searchTextColor,
                  uncheckedBgColor: Colors.white,
                  borderColor: const Color.fromRGBO(215, 215, 215, 1),
                  onGroupTap: (sign) => logic.type22Action(sign!),
                )
              ],
            ),
          ).inkWell(() => logic.type22Action(0)),
          SizedBox(height: 12.h),
          Container(
            padding: EdgeInsets.only(left: 10.w, right: 10.w),
            width: 315.w,
            height: 61.h,
            decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(6.r),
                color: const Color.fromRGBO(249, 249, 249, 1)),
            child: Row(
              children: [
                Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      "console_compiled_second_item_four_title".tr,
                      style: StyleConfig.otherStyle(
                        color: ColorConfig.searchTextColor,
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    SizedBox(height: 6.h),
                    SizedBox(
                      width: 270.w,
                      child: Text(
                        "console_compiled_second_item_four_tips".tr,
                        style: StyleConfig.otherStyle(
                          color: ColorConfig.shopDetailTextColor,
                          fontSize: 12,
                          fontWeight: FontWeight.w400,
                        ),
                      ),
                    ),
                  ],
                ),
                const Expanded(child: SizedBox()),
                CheckBoxRounded(
                  isGroup: true,
                  value: 1,
                  groupValue: logic.type_2,
                  checkedColor:
                      Get.find<ThemeColorController>().getColor('textColor'),
                  checkedBgColor: ColorConfig.searchTextColor,
                  uncheckedBgColor: Colors.white,
                  borderColor: const Color.fromRGBO(215, 215, 215, 1),
                  onGroupTap: (sign) => logic.type22Action(sign!),
                )
              ],
            ),
          ).inkWell(() => logic.type22Action(1)),
          SizedBox(height: 12.h),
          Container(
            padding: EdgeInsets.only(left: 10.w, right: 10.w),
            width: 315.w,
            height: 61.h,
            decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(6.r),
                color: const Color.fromRGBO(249, 249, 249, 1)),
            child: Row(
              children: [
                Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      "console_compiled_second_item_five_title".tr,
                      style: StyleConfig.otherStyle(
                        color: ColorConfig.searchTextColor,
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    SizedBox(height: 6.h),
                    SizedBox(
                      width: 270.w,
                      child: Text(
                        "console_compiled_second_item_five_tips".tr,
                        style: StyleConfig.otherStyle(
                          color: ColorConfig.shopDetailTextColor,
                          fontSize: 12,
                          fontWeight: FontWeight.w400,
                        ),
                      ),
                    ),
                  ],
                ),
                const Expanded(child: SizedBox()),
                CheckBoxRounded(
                  isGroup: true,
                  value: 2,
                  groupValue: logic.type_2,
                  checkedColor:
                      Get.find<ThemeColorController>().getColor('textColor'),
                  checkedBgColor: ColorConfig.searchTextColor,
                  uncheckedBgColor: Colors.white,
                  borderColor: const Color.fromRGBO(215, 215, 215, 1),
                  onGroupTap: (sign) => logic.type22Action(sign!),
                )
              ],
            ),
          ).inkWell(() => logic.type22Action(2)),
        ],
      ),
    );
  }

  ///第三个控件
  Widget _thirdWidget() {
    return Column(
      children: [
        Container(
          margin: EdgeInsets.only(top: 10.h),
          padding:
              EdgeInsets.only(left: 14.w, right: 14.w, top: 12.h, bottom: 14.h),
          width: 345.w,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(10.r),
            color: Colors.white,
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.06),
                // 阴影颜色和透明度
                spreadRadius: 0,
                // 阴影扩散范围
                blurRadius: 10,
                // 阴影模糊程度
                offset: const Offset(0, 2),
                // 阴影偏移量（水平，垂直）
              )
            ],
          ),
          child: Column(
            children: [
              Row(
                children: [
                  Text(
                    "console_compiled_third_title".tr,
                    style: StyleConfig.otherStyle(
                        color: ColorConfig.searchTextColor,
                        fontWeight: FontWeight.w600),
                  ),
                  const Expanded(child: SizedBox()),
                  CupertinoSwitch(
                      activeTrackColor: ColorConfig.searchTextColor,
                      value: logic.isDisturb,
                      onChanged: (value) => logic.isDisturbAction(value)),
                ],
              ),
              Offstage(
                  offstage: !logic.isDisturb,
                  child: Container(
                    margin: EdgeInsets.only(top: 12.h),
                    child: Row(
                      children: [
                        Container(
                          padding: EdgeInsets.only(left: 20.w),
                          width: 110.w,
                          height: 67.h,
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(7.r),
                            color: const Color.fromRGBO(249, 249, 249, 1),
                          ),
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                logic.startTime,
                                style: StyleConfig.otherStyle(
                                    color: ColorConfig.searchTextColor,
                                    fontSize: 14,
                                    fontWeight: FontWeight.w400),
                              ),
                              SizedBox(height: 2.h),
                              Text(
                                logic.startTips,
                                style: StyleConfig.otherStyle(
                                    color: ColorConfig.shopDetailTextColor,
                                    fontSize: 12,
                                    fontWeight: FontWeight.w400),
                              ),
                            ],
                          ),
                        ).inkWell(() => logic.startTimeAction()),
                        SizedBox(width: 12.w),
                        Text(
                          "~",
                          style: StyleConfig.otherStyle(
                              color: ColorConfig.authenticationTextColor,
                              fontSize: 12,
                              fontWeight: FontWeight.w400),
                        ),
                        SizedBox(width: 12.w),
                        Container(
                          padding: EdgeInsets.only(left: 20.w),
                          width: 110.w,
                          height: 67.h,
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(7.r),
                            color: const Color.fromRGBO(249, 249, 249, 1),
                          ),
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                logic.endTime,
                                style: StyleConfig.otherStyle(
                                    color: ColorConfig.searchTextColor,
                                    fontSize: 14,
                                    fontWeight: FontWeight.w400),
                              ),
                              SizedBox(height: 2.h),
                              Text(
                                logic.endTips,
                                style: StyleConfig.otherStyle(
                                    color: ColorConfig.shopDetailTextColor,
                                    fontSize: 12,
                                    fontWeight: FontWeight.w400),
                              ),
                            ],
                          ),
                        ).inkWell(() => logic.endTimeAction()),
                      ],
                    ),
                  ))
            ],
          ),
        ),
        SizedBox(height: 10.h),
        SizedBox(
          width: 345.w,
          child: Text(
            "console_compiled_third_tips".tr,
            style: StyleConfig.otherStyle(
                color: ColorConfig.shopDetailTextColor,
                fontWeight: FontWeight.w400,
                fontSize: 12),
          ),
        ),
      ],
    );
  }

  @override
  Widget buildContent() {
    return GetBuilder<ConsoleCompiledController>(
      id: "console_compiled_view",
      builder: (_) {
        return Container(
          margin: EdgeInsets.only(
              top: 10.h,
              left: 15.w,
              right: 15.w,
              bottom: ScreenUtil().bottomBarHeight + 20.h),
          width: 1.sw,
          height: 1.sh,
          child: Column(
            children: [
              Expanded(
                child: SingleChildScrollView(
                  child: _buildView(),
                ),
              ),
              Container(
                margin: EdgeInsets.only(top: 20.h),
                width: 283.w,
                height: 48.h,
                decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(24.r),
                    color: const Color.fromRGBO(40, 39, 46, 1)),
                child: Center(
                  child: ThemeText(
                    dataStr: "console_compiled_save".tr,
                    keyName: 'textColor',
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ).inkWell(() => logic.saveAction())
            ],
          ),
        );
      },
    );
  }
}
