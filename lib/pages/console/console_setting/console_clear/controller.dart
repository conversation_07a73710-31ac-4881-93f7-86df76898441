import 'package:get/get.dart';
import 'package:getx_xiaopa/extension/widget_extension.dart';
import 'package:getx_xiaopa/network/http_client.dart';
import 'package:getx_xiaopa/network/model/result.dart';
import 'package:getx_xiaopa/pages/console/model/device_items_result.dart';
import 'package:getx_xiaopa/routes/router.dart';
import 'package:getx_xiaopa/storage/storage.dart';

class ConsoleClearController extends GetxController {
  ConsoleClearController();

  DeviceItemsModel model = DeviceItemsModel();

  ///每次进来前执行一次
  reloadData() {
    update(["console_clear"]);
  }

  updateStatus(DeviceItemsModel vModel) {
    model = vModel;
    update(["console_clear"]);
  }

  clearMemoryAction() {
    _clearDeviceConfigAction();
  }

  ///===================网络请求===========================
  _clearDeviceConfigAction() async {
    if (model.id!.isEmpty) {
      CToast.showToast("console_language_two".tr);
      return;
    }
    CToast.showLoading();
    var map = {
      "device_id": model.id,
      "user_id": UserStore.to.userInfo.id,
    };
    Result result = await http.clearDeviceMemory(map);
    if (result.code == 0) {
      sendDeviceConfigAction(type: 1);
    }
  }

  /// 发送修改设备配置
  /// type 1：重启 2：查询电量音量 3：升级 4：设置音量 5：查询版本号 6：设置麦克风 7：设置提醒
  sendDeviceConfigAction(
      {int? type, String command = "", dynamic value = ""}) async {
    if (model.isOnline == 2) {
      return;
    }
    var map = {
      "device_id": model.id,
      "command": command,
      "value": value,
      "type": type
    };
    Result result = await http.setUpDevice(map);
    if (result.code != 0) {
      CToast.showToast(result.msg!);
      Get.until((route) => route.settings.name == AppRoutes.APPLICATION);
    }
  }
}
