import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:getx_xiaopa/base/base.dart';
import 'package:getx_xiaopa/extension/images.dart';
import 'package:getx_xiaopa/extension/widget_extension.dart';
import 'package:getx_xiaopa/r.dart';
import 'package:getx_xiaopa/values/values.dart';
import 'package:getx_xiaopa/widget/widget.dart';

import 'index.dart';

class ConsoleClearPage extends BaseCommonView<ConsoleClearController> {
  ConsoleClearPage({super.key});

  @override
  bool? get extendBodyBehindAppBar => true;

  @override
  Color? get navColor => Colors.transparent;

  @override
  String? get navTitle => "console_clear_title".tr;

  @override
  TextStyle? get navTitleStyle =>
      StyleConfig.blackStyle(fontSize: 18, fontWeight: FontWeight.w600);

  @override
  double? get leftWidth => 35.w;

  @override
  Color? get contentColor => const Color.fromRGBO(249, 249, 249, 1);

  @override
  Widget? get leftButton => Padding(
        padding: EdgeInsets.only(left: 15.w),
        child: Images(
          path: R.back_png,
          width: 20.w,
          height: 20.h,
          scale: 2.8,
        ).inkWell(() => Get.back()),
      );

  // 主视图
  Widget _buildView() {
    return ThemeContainerImage(
      padding: EdgeInsets.only(left: 28.w),
      conWidget: 1.sw,
      conHeight: 1.sh,
      fit: BoxFit.fill,
      fileName: "console_clear_bg.png",
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(height: 139.h),
          SizedBox(
            width: 319.w,
            child: Text(
              'console_clear_one'.tr,
              style: StyleConfig.otherStyle(
                  color: ColorConfig.searchTextColor,
                  fontSize: 18,
                  fontWeight: FontWeight.w900),
            ),
          ),
          SizedBox(height: 14.h),
          SizedBox(
            width: 319.w,
            child: Text(
              'console_clear_two'.tr,
              style: StyleConfig.otherStyle(
                  color: const Color.fromRGBO(180, 142, 78, 1),
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  height: 2),
            ),
          ),
          SizedBox(height: 14.h),
          SizedBox(
            width: 319.w,
            child: Text(
              'console_clear_three'.tr,
              style: StyleConfig.otherStyle(
                  color: ColorConfig.searchTextColor,
                  fontSize: 16,
                  fontWeight: FontWeight.w600),
            ),
          ),
          Container(
            margin: EdgeInsets.only(top: 81.h, left: 20.w),
            width: 283.w,
            height: 48.h,
            decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(24.r),
                color: ColorConfig.searchTextColor),
            child: Center(
              child: ThemeText(
                dataStr: 'console_clear_btn'.tr,
                keyName: 'textColor',
                fontWeight: FontWeight.w600,
              ),
            ),
          ).inkWell(() => controller.clearMemoryAction())
        ],
      ),
    );
  }

  @override
  Widget buildContent() {
    return GetBuilder<ConsoleClearController>(
      init: ConsoleClearController(),
      id: "console_clear",
      builder: (_) {
        return _buildView();
      },
    );
  }
}
