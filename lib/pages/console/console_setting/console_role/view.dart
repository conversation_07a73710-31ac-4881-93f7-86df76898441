import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:getx_xiaopa/base/base.dart';
import 'package:getx_xiaopa/extension/images.dart';
import 'package:getx_xiaopa/extension/widget_extension.dart';
import 'package:getx_xiaopa/r.dart';
import 'package:getx_xiaopa/storage/storage.dart';
import 'package:getx_xiaopa/utils/utils.dart';
import 'package:getx_xiaopa/values/values.dart';
import 'package:getx_xiaopa/widget/widget.dart';

import 'index.dart';

class ConsoleRolePage extends BaseCommonView<ConsoleRoleController> {
  ConsoleRolePage({super.key});

  @override
  bool? get extendBodyBehindAppBar => true;

  @override
  Color? get navColor => Colors.transparent;

  @override
  String? get navTitle => "console_role".tr;

  @override
  TextStyle? get navTitleStyle =>
      StyleConfig.blackStyle(fontSize: 18, fontWeight: FontWeight.w600);

  @override
  double? get leftWidth => 35.w;

  @override
  Color? get contentColor => const Color.fromRGBO(249, 249, 249, 1);

  @override
  Widget? get leftButton => Padding(
        padding: EdgeInsets.only(left: 15.w),
        child: Images(
          path: R.back_png,
          width: 20.w,
          height: 20.h,
          scale: 2.8,
        ).inkWell(() => Get.back()),
      );

  @override
  bool get isResize => true;

  // 主视图
  Widget _buildView() {
    return SingleChildScrollView(
      child: ThemeContainerImage(
        fileName: 'console_role_bg_${ConfigStore.to.getLocaleCode()}.png',
        padding: EdgeInsets.only(bottom: 34.h),
        conWidget: 1.sw,
        conHeight: 1.sh,
        fit: BoxFit.fill,
        child: Column(
          children: [
            Container(
              margin: EdgeInsets.only(top: 475.h, left: 29.w, right: 29.w),
              width: 317.sw,
              height: 227.h,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(10.w),
                color: const Color.fromRGBO(252, 252, 252, 1),
                border: Border.all(
                  color: const Color.fromRGBO(239, 239, 239, 1),
                  width: 1,
                ),
              ),
              child: Column(
                children: [
                  Expanded(
                    child: AppTextField(
                      style: StyleConfig.otherStyle(
                          color: ColorConfig.searchTextColor,
                          fontSize: 14,
                          fontWeight: FontWeight.w400,
                          overflow: TextOverflow.visible),
                      controller: controller.textEditingController,
                      maxLines: null,
                      maxLength: controller.textMaxLength,
                      keyboardType: TextInputType.text,
                      onChanged: (value) => controller.textMaxLenght(value),
                      decoration: InputDecoration(
                        border: InputBorder.none,
                        enabledBorder: InputBorder.none,
                        focusedBorder: InputBorder.none,
                        counterText: '',
                        contentPadding: EdgeInsets.symmetric(
                            horizontal: 10.w, vertical: 10.h),
                        hintText: 'console_role_hint_text'.tr,
                        hintStyle: StyleConfig.otherStyle(
                          color: ColorConfig.shopDetailTextColor,
                          fontSize: 14,
                          fontWeight: FontWeight.w400,
                        ),
                      ),
                    ),
                  ),
                  Container(
                    margin: EdgeInsets.only(right: 10.w, bottom: 10.h),
                    width: 1.sw,
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: [
                        Text(
                          "${controller.textLength}",
                          style: StyleConfig.otherStyle(
                            fontSize: 12,
                            color: ColorConfig.authenticationTextColor,
                          ),
                        ),
                        Text(
                          "/${controller.textMaxLength}",
                          style: StyleConfig.otherStyle(
                            fontSize: 12,
                            color: ColorConfig.searchTextColor,
                          ),
                        ),
                      ],
                    ),
                  )
                ],
              ),
            ),
            Container(
              margin: EdgeInsets.only(top: 28.h, left: 46.w, right: 46.w),
              width: 283.w,
              height: 48.h,
              decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(24.r),
                  color: ColorConfig.searchTextColor),
              child: Center(
                child: Text(
                  "console_role_save".tr,
                  style: StyleConfig.otherStyle(
                    fontSize: 16.sp,
                    fontWeight: FontWeight.w600,
                    color: Get.find<ThemeColorController>().gTextColor,
                  ),
                ),
              ),
            ).inkWell(() => controller.saveAction())
          ],
        ),
      ),
    );
  }

  @override
  Widget buildContent() {
    return GetBuilder<ConsoleRoleController>(
      init: ConsoleRoleController(),
      id: "console_role",
      builder: (_) {
        return _buildView();
      },
    );
  }
}
