import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:getx_xiaopa/extension/widget_extension.dart';
import 'package:getx_xiaopa/network/http_client.dart';
import 'package:getx_xiaopa/network/model/result.dart';
import 'package:getx_xiaopa/pages/console/model/device_items_result.dart';
import 'package:getx_xiaopa/storage/storage.dart';

class ConsoleRoleController extends GetxController {
  ConsoleRoleController();

  TextEditingController textEditingController = TextEditingController();
  int textLength = 0;
  int textMaxLength = ConfigStore.to.getLocaleCode() == "zh" ? 200 : 300;

  DeviceItemsModel model = DeviceItemsModel();

  bool _isUpdate = false;

  reloadData() {
    if (!_isUpdate) {
      textEditingController.text = model.extraPrompt ?? '';
      update(["console_role"]);
    }
  }

  updateStatus(DeviceItemsModel vModel) {
    _isUpdate = false;
    model = vModel;
    update(["console_role"]);
  }

  textMaxLenght(String value) {
    textLength = value.length;
    update(["console_role"]);
  }

  saveAction() {
    if (textEditingController.text.isEmpty) {
      CToast.showToast("console_role_empty".tr);
      return;
    }

    _sendUpdateDevice();
  }

  ///=================请求网络=====================
  _sendUpdateDevice() async {
    CToast.showLoading();
    var map = {
      "id": model.id,
      "extra_prompt": textEditingController.text,
    };
    Result result = await http.updateDevicePreference(map);
    if (result.code == 0) {
      _isUpdate = true;
      sendDeviceConfigAction(type: 1);
    }
    CToast.showToast(result.msg!);
  }

  /// 发送修改设备配置
  /// type 1：重启 2：查询电量音量 3：升级 4：设置音量 5：查询版本号 6：设置麦克风 7：设置提醒
  sendDeviceConfigAction(
      {int? type, String command = "", dynamic value = ""}) async {
    if (model.isOnline == 2) {
      return;
    }
    var map = {
      "device_id": model.id,
      "command": command,
      "value": value,
      "type": type
    };
    Result result = await http.setUpDevice(map);
    if (result.code != 0) {
      CToast.showToast(result.msg!);
    }
  }
}
