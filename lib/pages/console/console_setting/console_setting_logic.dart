import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:getx_xiaopa/base/base.dart';
import 'package:getx_xiaopa/extension/widget_extension.dart';
import 'package:getx_xiaopa/network/http_client.dart';
import 'package:getx_xiaopa/network/model/result.dart';
import 'package:getx_xiaopa/pages/console/console_setting/console_clear/controller.dart';
import 'package:getx_xiaopa/pages/console/console_setting/console_compiled/controller.dart';
import 'package:getx_xiaopa/pages/console/console_setting/console_language/controller.dart';
import 'package:getx_xiaopa/pages/console/console_setting/console_role/controller.dart';
import 'package:getx_xiaopa/pages/console/console_setting/console_sound/index.dart';
import 'package:getx_xiaopa/pages/console/console_setting/console_update/console_update_logic.dart';
import 'package:getx_xiaopa/pages/console/console_setting/widget/console_unbind_widget.dart';
import 'package:getx_xiaopa/pages/console/model/device_items_result.dart';
import 'package:getx_xiaopa/routes/router.dart';
import 'package:getx_xiaopa/storage/storage.dart';
import 'package:getx_xiaopa/utils/event_bus.dart';
import 'package:getx_xiaopa/utils/utils.dart';
import 'package:getx_xiaopa/widget/widget.dart';

import 'console_setting_state.dart';

class ConsoleSettingLogic extends BaseCommonController {
  @override
  final ConsoleSettingState state = ConsoleSettingState();

  @override
  void initData() {}

  updateStatus(DeviceItemsModel model) {
    state.model = model;
    netState = NetState.dataSuccessState;
    update(["console_setting_view"]);
  }

  @override
  void onHidden() {}

  ///复制ID
  copyAction() async {
    await Clipboard.setData(ClipboardData(text: state.model?.id ?? ""));
    CToast.showToast("ID${"console_setting_copy".tr}");
  }

  toClearPage() {
    Get.find<ConsoleClearController>().reloadData();
    Get.toNamed(AppRoutes.CONSOLE_CLEAR);
  }

  toUpdatePage() {
    Get.find<ConsoleUpdateLogic>().reloadVersion();
    Get.toNamed(AppRoutes.CONSOLE_UPDATE);
  }

  toRoleConfigPage() {
    Get.find<ConsoleRoleController>().reloadData();
    Get.toNamed(AppRoutes.CONSOLE_ROLE);
  }

  toLanguagePage() {
    Get.find<ConsoleLanguageController>().reloadData();
    Get.toNamed(AppRoutes.CONSOLE_LANGUAGE);
  }

  toCompiledPage() {
    Get.find<ConsoleCompiledController>().reloadData();
    Get.toNamed(AppRoutes.CONSOLE_COMPILED);
  }

  toSoundPage() {
    Get.find<ConsoleSoundController>().reloadData();
    Get.toNamed(AppRoutes.CONSOLE_SOUND);
  }

  unBindAction() {
    Get.dialog(
      CommonDialog(
        mTitle: "console_unbind_title".tr,
        mContent: "console_unbind_tips".tr,
        mContextAlign: TextAlign.center,
        mContextSize: 12,
        mConfirm: "console_unbind_confirm".tr,
        confirmAction: () {
          unBindDevice();
        },
      ),
    );
  }

  ///================ 网络请求 ======================
  /// 设备解绑
  void unBindDevice() async {
    if (state.model?.id == null) return;
    CToast.showLoading();
    var map = {"device_id": state.model?.id};
    Result result = await http.unbindDevice(map);
    if (result.code == 0) {
      await UserStore.to.deviceItems(isInit: true, isChangeTheme: true);
      EventBus().emit("refreshDeviceItems", UserStore.to.deviceList);
      if (UserStore.to.deviceList.isEmpty) {
        Get.to(() => const ConsoleUnbindWidget());
      } else {
        Get.back();
      }
    }
  }
}
