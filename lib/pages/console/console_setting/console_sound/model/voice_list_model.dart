///
/// Code generated by jsonToDartModel https://ashamp.github.io/jsonToDartModel/
///
class VoiceListModel {
/*
{
  "id": "4670274793373696",
  "created_at": "2025-04-03T12:08:49.039+08:00",
  "updated_at": "2025-04-07T11:23:31.442+08:00",
  "deleted_at": null,
  "name": "小耙",
  "audio": "upload/aigc-voice/202503/991dcdfec9b7d7d02c69d53f5c147eed.mp3",
  "text": "  ",
  "language": 0,
  "type": 1,
  "user_id": "19976060665856",
  "user_mobile": "13340084850",
  "status": 2,
  "train_num": 0,
  "speaker_id": "S_P51LZ4Wn1",
  "is_default": 0
} 
*/

  String? id;
  String? createdAt;
  String? updatedAt;
  String? deletedAt;
  String? expiredAt;
  String? name;
  String? enName;
  String? audio;
  String? text;
  int? language;
  int? type;
  String? userId;
  String? userMobile;
  int? status;
  int? trainNum;
  String? speakerId;
  int? isDefault;

  ///自定义参数
  bool? isPlay;
  bool? isUseDefault;

  VoiceListModel(
      {this.id,
      this.createdAt,
      this.updatedAt,
      this.deletedAt,
      this.name,
      this.enName,
      this.audio,
      this.text,
      this.language,
      this.type,
      this.userId,
      this.userMobile,
      this.status,
      this.trainNum,
      this.speakerId,
      this.isDefault,
      this.isPlay,
      this.isUseDefault,
      this.expiredAt});

  VoiceListModel.fromJson(Map<String, dynamic> json) {
    id = json['id']?.toString();
    createdAt = json['created_at']?.toString();
    updatedAt = json['updated_at']?.toString();
    deletedAt = json['deleted_at']?.toString();
    expiredAt = json['expired_at']?.toString();
    name = json['name']?.toString();
    enName = json['en_name']?.toString();
    audio = json['audio']?.toString();
    text = json['text']?.toString();
    language = json['language']?.toInt();
    type = json['type']?.toInt();
    userId = json['user_id']?.toString();
    userMobile = json['user_mobile']?.toString();
    status = json['status']?.toInt();
    trainNum = json['train_num']?.toInt();
    speakerId = json['speaker_id']?.toString();
    isDefault = json['is_default']?.toInt();
    isPlay = json['is_play']?.toBool() ?? false;
    isUseDefault = json['is_use_default']?.toBool() ?? false;
  }

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['id'] = id;
    data['created_at'] = createdAt;
    data['updated_at'] = updatedAt;
    data['deleted_at'] = deletedAt;
    data['expired_at'] = expiredAt;
    data['name'] = name;
    data['en_name'] = enName;
    data['audio'] = audio;
    data['text'] = text;
    data['language'] = language;
    data['type'] = type;
    data['user_id'] = userId;
    data['user_mobile'] = userMobile;
    data['status'] = status;
    data['train_num'] = trainNum;
    data['speaker_id'] = speakerId;
    data['is_default'] = isDefault;
    data['is_play'] = isPlay;
    data['is_use_default'] = isUseDefault;
    return data;
  }
}

class VoiceListModelResult {
/*
{
  "count": 1,
  "pageSize": 10,
  "pageNum": 1,
  "data": [
    {
      "id": "4670274793373696",
      "created_at": "2025-04-03T12:08:49.039+08:00",
      "updated_at": "2025-04-07T11:23:31.442+08:00",
      "deleted_at": null,
      "name": "小耙",
      "audio": "upload/aigc-voice/202503/991dcdfec9b7d7d02c69d53f5c147eed.mp3",
      "text": "  ",
      "language": 0,
      "type": 1,
      "user_id": "19976060665856",
      "user_mobile": "13340084850",
      "status": 2,
      "train_num": 0,
      "speaker_id": "S_P51LZ4Wn1",
      "is_default": 0
    }
  ]
} 
*/

  int? count;
  int? pageSize;
  int? pageNum;
  List<VoiceListModel?>? data;

  VoiceListModelResult({
    this.count,
    this.pageSize,
    this.pageNum,
    this.data,
  });
  VoiceListModelResult.fromJson(Map<String, dynamic> json) {
    count = json['count']?.toInt();
    pageSize = json['pageSize']?.toInt();
    pageNum = json['pageNum']?.toInt();
    if (json['data'] != null) {
      final v = json['data'];
      final arr0 = <VoiceListModel>[];
      v.forEach((v) {
        arr0.add(VoiceListModel.fromJson(v));
      });
      data = arr0;
    }
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['count'] = count;
    data['pageSize'] = pageSize;
    data['pageNum'] = pageNum;
    if (this.data != null) {
      final v = this.data;
      final arr0 = [];
      for (var v in v!) {
        arr0.add(v!.toJson());
      }
      data['data'] = arr0;
    }
    return data;
  }
}
