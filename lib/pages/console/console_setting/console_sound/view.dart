import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:getx_xiaopa/base/base.dart';
import 'package:getx_xiaopa/extension/images.dart';
import 'package:getx_xiaopa/extension/widget_extension.dart';
import 'package:getx_xiaopa/pages/console/console_setting/console_sound/widgets/sound_item.dart';
import 'package:getx_xiaopa/r.dart';
import 'package:getx_xiaopa/storage/storage.dart';
import 'package:getx_xiaopa/utils/utils.dart';
import 'package:getx_xiaopa/values/values.dart';
import 'package:getx_xiaopa/widget/widget.dart';

import 'index.dart';

class ConsoleSoundPage extends BaseCommonView<ConsoleSoundController> {
  ConsoleSoundPage({super.key});

  @override
  String? get navTitle => "console_sound_title".tr;

  @override
  TextStyle? get navTitleStyle =>
      StyleConfig.blackStyle(fontSize: 18, fontWeight: FontWeight.w600);

  @override
  Color? get navColor => Colors.transparent;

  @override
  double? get leftWidth => 35.w;

  @override
  Widget? get leftButton => Padding(
        padding: EdgeInsets.only(left: 15.w),
        child: Images(
          path: R.back_png,
          width: 20.w,
          height: 20.h,
          scale: 2.8,
        ).inkWell(() => Get.back()),
      );

  @override
  List<Widget>? get rightActionList => [
        Padding(
          padding: EdgeInsets.only(right: 15.w),
          child: Text(
            'console_sound_exchange'.tr,
            style: StyleConfig.otherStyle(
              color: ColorConfig.authenticationTextColor,
            ),
          ),
        ).inkWell(() => controller.exchangePage())
      ];

  @override
  Color? get contentColor => const Color.fromRGBO(249, 249, 249, 1);

  // 主视图
  Widget _buildView() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(height: 20.h),
        Container(
          margin: EdgeInsets.only(left: 20.w, right: 20.w),
          child: Row(
            children: [
              Text(
                "console_sound_package".tr,
                style: StyleConfig.otherStyle(
                    color: ColorConfig.searchTextColor,
                    fontWeight: FontWeight.w600),
              ),
              SizedBox(width: 5.w),
              ThemeImagePath(
                fileName: 'console_sound_explan.png',
                imgWidget: 18.r,
                imgHeight: 18.r,
              ).inkWell(() => controller.voiceExplnAction())
            ],
          ),
        ),
        SizedBox(height: 14.h),
        Expanded(
          child: ListView.builder(
            itemCount: controller.soundList.length,
            itemBuilder: (_, index) {
              return SoundItem(
                cIndex: index,
                model: controller.soundList[index],
                onPlay: () => controller.playAction(index),
                onStop: () => controller.playAction(index),
                onUse: () => controller.useAction(index),
                isClone: () => controller.cloneAction(index),
                onRename: () => controller.renameAction(index),
              );
              // return Slidable(
              //   key: ValueKey(index),
              //   endActionPane: ActionPane(
              //     extentRatio: 0.3,
              //     motion: const DrawerMotion(),
              //     children: [
              //       SlidableAction(
              //         borderRadius:
              //             BorderRadius.horizontal(right: Radius.circular(10.r)),
              //         onPressed: (context) => controller.delAction(index),
              //         backgroundColor: Colors.red,
              //         foregroundColor: Colors.white,
              //         icon: Icons.delete,
              //         label: "删除",
              //       )
              //     ],
              //   ),
              //   child: SoundItem(
              //     cIndex: index,
              //     model: controller.soundList[index],
              //     onPlay: () => controller.playAction(index),
              //     onStop: () => controller.playAction(index),
              //     onUse: () => controller.useAction(index),
              //     isClone: () => controller.cloneAction(index),
              //     onRename: () => controller.renameAction(index),
              //   ),
              // );
            },
          ),
        ),
        Container(
          margin: EdgeInsets.only(
              top: 0.h, bottom: ScreenUtil().bottomBarHeight + 10.h),
          width: 375.w,
          height: 210.h,
          decoration: BoxDecoration(
            image: DecorationImage(
              image: AssetImage(ConfigStore.to.getLocaleCode() == 'zh'
                  ? Get.find<ThemeImageController>().soundBuyBtnBg
                  : Get.find<ThemeImageController>().soundBuyBtnBgEn),
              fit: BoxFit.fill,
            ),
          ),
          child: Stack(
            children: [
              Positioned(
                top: 60.h,
                left: ConfigStore.to.getLocaleCode() == "zh" ? 125.w : 140.w,
                child: ThemeImagePath(
                  fileName: 'console_sound_explan.png',
                  imgWidget: 18.r,
                  imgHeight: 18.r,
                ).inkWell(() => controller.explanAction()),
              ),
              Positioned(
                right: 30.w,
                bottom: 26.h,
                child: Container(
                  width: 89.w,
                  height: 28.h,
                  decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(14.r),
                      color: ColorConfig.searchTextColor),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Images(
                        path: R.papa_image_png,
                        width: 18.r,
                        height: 18.r,
                      ),
                      SizedBox(width: 4.w),
                      ThemeText(
                        dataStr: "${CommonStore.to.voicePrice}",
                        keyName: "textColor",
                        fontSize: 14,
                        fontWeight: FontWeight.w700,
                      )
                    ],
                  ),
                ),
              )
            ],
          ),
        ).inkWell(() => controller.exchangeAction()),
      ],
    );
  }

  @override
  Widget buildContent() {
    return GetBuilder<ConsoleSoundController>(
      init: ConsoleSoundController(),
      id: "console_sound",
      builder: (_) {
        return _buildView();
      },
    );
  }
}
