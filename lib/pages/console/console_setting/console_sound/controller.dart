import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:getx_xiaopa/extension/widget_extension.dart';
import 'package:getx_xiaopa/network/http_client.dart';
import 'package:getx_xiaopa/network/model/request_body.dart';
import 'package:getx_xiaopa/network/model/result.dart';
import 'package:getx_xiaopa/pages/console/console_setting/console_sound/model/voice_list_model.dart';
import 'package:getx_xiaopa/pages/console/console_setting/console_sound/sound_exchange/model/voice_buy_model.dart';
import 'package:getx_xiaopa/pages/console/console_setting/console_sound/widgets/exchange_result.dart';
import 'package:getx_xiaopa/pages/console/console_setting/console_sound/widgets/sound_explain_dialog.dart';
import 'package:getx_xiaopa/pages/console/console_setting/console_sound/widgets/sound_item_name.dart';
import 'package:getx_xiaopa/pages/console/console_setting/console_sound/widgets/voice_explain_dialog.dart';
import 'package:getx_xiaopa/pages/console/model/device_items_result.dart';
import 'package:getx_xiaopa/pages/mine/mine_logic.dart';
import 'package:getx_xiaopa/routes/router.dart';
import 'package:getx_xiaopa/storage/storage.dart';
import 'package:getx_xiaopa/utils/utils.dart';
import 'package:getx_xiaopa/values/values.dart';
import 'package:getx_xiaopa/widget/widget.dart';

class ConsoleSoundController extends GetxController {
  ConsoleSoundController();

  final SoundEffectPlayer soundEffectPlayer =
      SoundEffectPlayer(volume: 1, maxPlayers: 1);
  List<VoiceListModel> soundList = [];

  DeviceItemsModel deviceModel = DeviceItemsModel();

  ///保存正在播放的音频路径，防止用户点击不同的播放音频做处理
  String tempPath = '';

  int trainNum = 10;
  int trained = 0;

  @override
  void onClose() {
    super.onClose();
    soundEffectPlayer.dispose();
  }

  ///每次进来请求一次
  reloadData() {
    _getVoicePublicList();
  }

  updateStatus(DeviceItemsModel model) {
    deviceModel = model;
  }

  exchangePage() {
    Get.toNamed(AppRoutes.SOUND_EXCHANGE)?.then((result) {
      _getVoicePublicList();
    });
  }

  voiceExplnAction() {
    Get.dialog(const VoiceExplainDialog());
  }

  explanAction() {
    Get.dialog(const SoundExplainDialog());
  }

  cloneAction(int index) {
    var map = {"voiceId": soundList[index].id!};
    Get.toNamed(AppRoutes.SOUND_RECORD, arguments: map)?.then((result) {
      if (result == "success") {
        _getVoicePublicList();
      }
    });
  }

  playAction(int index) async {
    if (soundList[index].isPlay!) {
      soundEffectPlayer.stop();
      tempPath = "";
      soundList[index].isPlay = false;
      update(["console_sound"]);
    } else {
      String path = soundList[index].audio ?? "";
      if (path.isEmpty) {
        CToast.showToast("console_sound_tips".tr);
        return;
      }
      CToast.showLoading();
      if (tempPath != path) {
        soundEffectPlayer.stop();
        for (VoiceListModel model in soundList) {
          model.isPlay = false;
        }
        update(["console_sound"]);
      }

      soundEffectPlayer.playNetAudio(
        path,
        onStartPlay: () {
          CToast.dismiss();
          tempPath = soundList[index].audio!;
          soundList[index].isPlay = true;
          update(["console_sound"]);
        },
        onComplete: () {
          tempPath = "";
          soundList[index].isPlay = false;
          update(["console_sound"]);
        },
      );
    }
  }

  useAction(int index) {
    int status = soundList[index].status ?? 1;
    if (status == 1) {
      CToast.showToast("console_sound_tips".tr);
      return;
    }
    if (soundList[index].type == 1) {
      ///公共音色，调用接口清空默认
      _resetVoice();
    } else {
      ///私有音色，调用接口使用
      _useVoice(soundList[index].id!);
    }
  }

  renameAction(int index) {
    Get.dialog(SoundItemName(onTap: (eName) {
      if (eName.isNotEmpty) {
        _useVoice(soundList[index].id!, name: eName);
      }
    }), barrierDismissible: false);
  }

  delAction(int index) {
    int type = soundList[index].type ?? 1;
    if (type == 1) {
      CToast.showToast("console_sound_error_tips".tr);
      return;
    }
  }

  exchangeAction() {
    if (CommonStore.to.voicePrice == 0) {
      CToast.showToast("console_sound_price_error_tips".tr);
      return;
    }

    Get.dialog(
      CommonDialog(
        mContent: "console_sound_exchange_tips"
            .trArgs(["${CommonStore.to.voicePrice}"]),
        mHeight: 170,
        mTitle: "",
        mContextSize: 16,
        mContextTextColor: ColorConfig.searchTextColor,
        contextFontWeight: FontWeight.w700,
        confirmFontWeight: FontWeight.w600,
        mConfirm: "console_sound_confirm".tr,
        confirmAction: () {
          _exchangeVoice();
        },
      ),
    );
  }

  ///===================网络请求===========================
  _getVoicePublicList() async {
    var map = RequestBody(pagination: false).toJson();
    Result result = await http.voicePublicList(map);
    if (result.code == 0) {
      soundList.clear();
      update(["console_sound"]);

      for (VoiceListModel model in result.data.data) {
        soundList.add(model);
      }
      _getVoiceMyItems();
    } else {
      CToast.showToast(result.msg!);
    }
  }

  _getVoiceMyItems() async {
    var map = RequestBody(
        pagination: false,
        orders: [Order(expr: "asrc", name: "created_at")]).toJson();
    Result result = await http.voiceMyItems(map);
    if (result.code == 0) {
      VoiceListModelResult mResult = result.data;
      if (mResult.data!.isNotEmpty) {
        for (var i = 0; i < mResult.data!.length; i++) {
          VoiceListModel model = mResult.data![i]!;
          if (model.isDefault == 1) {
            model.isUseDefault = true;
          }
          trained = model.trainNum ?? 0;
          soundList.add(model);
        }
        bool flag = soundList.any((e) => e.isDefault == 1);
        if (!flag) {
          soundList[0].isUseDefault = true;
        }
      } else {
        soundList[0].isUseDefault = true;
      }
    } else {
      CToast.showToast(result.msg!);
    }
    update(["console_sound"]);
  }

  _exchangeVoice() async {
    CToast.showLoading();
    Result result = await http.voiceBuy({});
    if (result.code == 0) {
      VoiceBuyModel model = result.data;

      ///消费成功刷新账户余额
      Get.find<MineLogic>().refreshAccountItems();

      Get.to(() => const ExchangeResult())?.then((result) {
        if (result == "next") {
          var map = {"voiceId": model.id!};
          Get.toNamed(AppRoutes.SOUND_RECORD, arguments: map)?.then((result) {
            _getVoicePublicList();
          });
        } else {
          _getVoicePublicList();
        }
      });
    } else {
      CToast.showToast(result.msg!);
    }
  }

  _useVoice(String voiceId, {int isDefault = 1, String? name}) async {
    CToast.showLoading();
    var map = {
      "id": voiceId,
      "is_default": isDefault,
      ConfigStore.to.getLocaleCode() == 'zh' ? "name" : 'en_name': name,
    };
    Result result = await http.voiceUpdate(map);
    if (result.code == 0) {
      sendDeviceConfigAction(type: 1);
      _getVoicePublicList();
    } else {
      CToast.showToast(result.msg!);
    }
  }

  _resetVoice() async {
    CToast.showLoading();

    Result result = await http.voiceReset({});
    if (result.code == 0) {
      sendDeviceConfigAction(type: 1);
      _getVoicePublicList();
    } else {
      CToast.showToast(result.msg!);
    }
  }

  /// 发送修改设备配置
  /// type 1：重启 2：查询电量音量 3：升级 4：设置音量 5：查询版本号 6：设置麦克风 7：设置提醒
  sendDeviceConfigAction(
      {int? type, String command = "", dynamic value = ""}) async {
    if (deviceModel.isOnline == 2) {
      return;
    }
    var map = {
      "device_id": deviceModel.id,
      "command": command,
      "value": value,
      "type": type
    };
    Result result = await http.setUpDevice(map);
    if (result.code != 0) {
      CToast.showToast(result.msg!);
    }
  }
}
