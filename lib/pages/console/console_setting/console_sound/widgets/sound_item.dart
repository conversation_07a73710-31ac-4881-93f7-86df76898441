import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:getx_xiaopa/extension/images.dart';
import 'package:getx_xiaopa/extension/widget_extension.dart';
import 'package:getx_xiaopa/pages/console/console_setting/console_sound/model/voice_list_model.dart';
import 'package:getx_xiaopa/r.dart';
import 'package:getx_xiaopa/storage/storage.dart';
import 'package:getx_xiaopa/utils/utils.dart';
import 'package:getx_xiaopa/values/values.dart';
import 'package:getx_xiaopa/widget/widget.dart';

class SoundItem extends StatelessWidget {
  final int cIndex;
  final VoiceListModel model;
  final Function() onPlay;
  final Function() onStop;
  final Function() onUse;
  final Function() isClone;
  final Function() onRename;

  const SoundItem(
      {super.key,
      required this.model,
      required this.onPlay,
      required this.onStop,
      required this.onUse,
      required this.cIndex,
      required this.isClone,
      required this.onRename});

  @override
  Widget build(BuildContext context) {
    return Container(
      margin:
          EdgeInsets.only(top: cIndex > 0 ? 10.w : 0, left: 20.w, right: 20.w),
      padding: EdgeInsets.only(left: 10.w, right: 10.w),
      width: 335.w,
      height: 100.h,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(10.r),
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.06),
            // 阴影颜色和透明度
            spreadRadius: 0,
            // 阴影扩散范围
            blurRadius: 10,
            // 阴影模糊程度
            offset: const Offset(0, 2),
            // 阴影偏移量（水平，垂直）
          )
        ],
      ),
      child: Row(
        children: [
          SizedBox(
            width: 84.w,
            height: 80.h,
            child: Stack(
              children: [
                Positioned.fill(
                  child: Images(
                    path: model.type == 1
                        ? R.console_sound_image_png
                        : R.console_sound_image_02_png,
                    boxFit: BoxFit.fill,
                  ),
                ),
                // Positioned(
                //   right: 5.w,
                //   bottom: 5.h,
                //   width: 20.r,
                //   height: 20.r,
                //   child: model.isPlay!
                //       ? Container(
                //           padding: EdgeInsets.only(left: 5.w, right: 5.w),
                //           width: 20.r,
                //           height: 20.r,
                //           decoration: const BoxDecoration(
                //             image: DecorationImage(
                //                 image: AssetImage(R.sound_item_status_png),
                //                 fit: BoxFit.fill),
                //           ),
                //           child: BarPulseLoading(
                //             width: 0.5.w,
                //             height: 2.5.h,
                //           ),
                //         ).inkWell(() => onStop())
                //       : Images(path: R.console_sound_play_png)
                //           .inkWell(() => onPlay()),
                // )
              ],
            ),
          ),
          SizedBox(width: 10.w),
          Column(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Container(
                    constraints: BoxConstraints(minWidth: 0.w, maxWidth: 120.w),
                    child: Text(
                      ConfigStore.to.getLocaleCode() == "zh"
                          ? model.name ?? ''
                          : model.enName ?? model.name ?? '',
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                      style: StyleConfig.otherStyle(
                          color: ColorConfig.searchTextColor,
                          fontSize: 16,
                          fontWeight: FontWeight.w700),
                    ),
                  ),
                  model.type == 1
                      ? Container(
                          margin: EdgeInsets.only(left: 4.w),
                          padding: EdgeInsets.only(left: 3.w, right: 3.w),
                          // width: 36.w,
                          height: 14.h,
                          decoration: BoxDecoration(
                            color: Get.find<ThemeColorController>()
                                .gConsoleSoundUseBtn,
                            borderRadius: BorderRadius.circular(13.r),
                          ),
                          child: Center(
                            child: ThemeText(
                              dataStr: "console_sound_item_sign".tr,
                              keyName: "consoleDefalutColor",
                              fontSize: 10,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        )
                      : Container(
                          margin: EdgeInsets.only(left: 4.w),
                          child: ThemeImagePath(
                            fileName: "console_sound_item_e_name.png",
                            imgWidget: 18.r,
                            imgHeight: 18.r,
                          ),
                        ).inkWell(() => onRename()),
                ],
              ),
              SizedBox(height: 6.h),
              Text(
                model.type == 1
                    ? 'console_sound_item_permanent_date'.tr
                    : 'console_sound_item_date'.trArgs([
                        TimeUtil.getSimpleDate(model.expiredAt ?? '',
                            isHMS: false)
                      ]),
                style: StyleConfig.otherStyle(
                    color: ColorConfig.shopDetailTextColor,
                    fontSize: 12,
                    fontWeight: FontWeight.w500),
              ),
              if (model.type != 1) ...{
                SizedBox(height: 6.h),
                Row(
                  children: [
                    Text(
                      'console_sound_item_tips'.tr,
                      style: StyleConfig.otherStyle(
                          color: ColorConfig.shopDetailTextColor,
                          fontSize: 12,
                          fontWeight: FontWeight.w500),
                    ),
                    Text(
                      'console_sound_item_tips_count'
                          .trArgs(['${10 - model.trainNum!}']),
                      style: StyleConfig.otherStyle(
                          color: const Color.fromRGBO(253, 105, 82, 1),
                          fontSize: 12,
                          fontWeight: FontWeight.w600),
                    ),
                  ],
                ),
              }
            ],
          ),
          const Expanded(child: SizedBox()),
          model.type == 1
              ? model.isUseDefault!
                  ? Container(
                      width: 72.w,
                      height: 30.h,
                      decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(18.r),
                          color: Get.find<ThemeColorController>()
                              .gConsoleSoundUseBtn),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Container(
                            width: 5.r,
                            height: 5.r,
                            decoration: BoxDecoration(
                                shape: BoxShape.circle,
                                color: Get.find<ThemeColorController>()
                                    .gIconColor),
                          ),
                          SizedBox(width: 4.w),
                          Text(
                            'console_sound_item_tips_using'.tr,
                            style: StyleConfig.otherStyle(
                                color: ColorConfig.searchTextColor,
                                fontSize: 12,
                                fontWeight: FontWeight.w700),
                          )
                        ],
                      ),
                    ).inkWell(() => onUse())
                  : Container(
                      width: 72.w,
                      height: 30.h,
                      decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(18.r),
                          color: ColorConfig.searchTextColor),
                      child: Center(
                        child: ThemeText(
                          dataStr: "console_sound_item_tips_use".tr,
                          keyName: 'textColor',
                          fontSize: 12,
                        ),
                      ),
                    ).inkWell(() => onUse())
              : Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    model.isUseDefault!
                        ? Container(
                            width: 72.w,
                            height: 30.h,
                            decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(18.r),
                                color: Get.find<ThemeColorController>()
                                    .gConsoleSoundUseBtn),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Container(
                                  width: 5.r,
                                  height: 5.r,
                                  decoration: BoxDecoration(
                                      shape: BoxShape.circle,
                                      color: Get.find<ThemeColorController>()
                                          .gIconColor),
                                ),
                                SizedBox(width: 4.w),
                                Text(
                                  'console_sound_item_tips_using'.tr,
                                  style: StyleConfig.otherStyle(
                                      color: ColorConfig.searchTextColor,
                                      fontSize: 12,
                                      fontWeight: FontWeight.w700),
                                )
                              ],
                            ),
                          ).inkWell(() => onUse())
                        : Container(
                            width: 72.w,
                            height: 30.h,
                            decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(18.r),
                                color: Colors.white,
                                border: Border.all(
                                    width: 1.r,
                                    color: Get.find<ThemeColorController>()
                                        .gMineOnline)),
                            child: Center(
                              child: ThemeText(
                                dataStr: "console_sound_item_tips_use".tr,
                                keyName: 'mineOnline',
                                fontSize: 12,
                              ),
                            ),
                          ).inkWell(() => onUse()),
                    SizedBox(height: 10.h),
                    Container(
                      width: 72.w,
                      height: 30.h,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(18.r),
                        color: ColorConfig.searchTextColor,
                      ),
                      child: Center(
                        child: ThemeText(
                          dataStr: "console_sound_item_clone".tr,
                          keyName: 'textColor',
                          fontSize: 12,
                        ),
                      ),
                    ).inkWell(() => isClone()),
                  ],
                ),
        ],
      ),
    );
  }
}
