import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:getx_xiaopa/extension/images.dart';
import 'package:getx_xiaopa/extension/widget_extension.dart';
import 'package:getx_xiaopa/r.dart';
import 'package:getx_xiaopa/storage/storage.dart';
import 'package:getx_xiaopa/values/values.dart';

class SoundExplainDialog extends StatelessWidget {
  const SoundExplainDialog({super.key});

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            padding: EdgeInsets.only(
                top: 30.h, bottom: 34.h, left: 15.w, right: 19.w),
            width: 296.w,
            decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(10.r), color: Colors.white),
            child: Column(
              children: [
                Text(
                  "console_sound_explan_title".tr,
                  style: StyleConfig.otherStyle(
                      color: ColorConfig.searchTextColor,
                      fontWeight: FontWeight.w700,
                      fontSize: 20),
                ),
                SizedBox(height: 20.h),
                Row(
                  children: [
                    Images(
                      path: R.console_sound_explan_image_png,
                      width: 18.r,
                      height: 11.h,
                    ),
                    SizedBox(width: 3.w),
                    SizedBox(
                      width: 230.w,
                      child: Text(
                        "console_sound_explan_title_one".tr,
                        style: StyleConfig.otherStyle(
                            color: ColorConfig.searchTextColor,
                            fontWeight: FontWeight.w600,
                            fontSize: 14),
                      ),
                    )
                  ],
                ),
                Container(
                  margin: EdgeInsets.only(top: 2.h, left: 20.w),
                  width: 230.w,
                  child: Text(
                    "console_sound_explan_tips_one".tr,
                    style: StyleConfig.otherStyle(
                        color: ColorConfig.authenticationTextColor,
                        fontSize: 12),
                  ),
                ),
                SizedBox(height: 8.h),
                Row(
                  children: [
                    Images(
                      path: R.console_sound_explan_image_png,
                      width: 18.r,
                      height: 11.h,
                    ),
                    SizedBox(width: 3.w),
                    Text(
                      "console_sound_explan_title_two".tr,
                      style: StyleConfig.otherStyle(
                          color: ColorConfig.searchTextColor,
                          fontWeight: FontWeight.w600,
                          fontSize: 14),
                    )
                  ],
                ),
                Container(
                  margin: EdgeInsets.only(top: 2.h, left: 20.w),
                  width: 1.sw,
                  child: Text(
                    "console_sound_explan_tips_two".tr,
                    style: StyleConfig.otherStyle(
                        color: ColorConfig.authenticationTextColor,
                        fontSize: 12),
                  ),
                ),
                SizedBox(height: 8.h),
                Row(
                  children: [
                    Images(
                      path: R.console_sound_explan_image_png,
                      width: 18.r,
                      height: 11.h,
                    ),
                    SizedBox(width: 3.w),
                    Text(
                      "console_sound_explan_title_three".tr,
                      style: StyleConfig.otherStyle(
                          color: ColorConfig.searchTextColor,
                          fontWeight: FontWeight.w600,
                          fontSize: 14),
                    )
                  ],
                ),
                Container(
                  margin: EdgeInsets.only(top: 2.h, left: 20.w),
                  width: 1.sw,
                  child: Text(
                    "console_sound_explan_tips_three"
                        .trArgs(["${CommonStore.to.voicePrice}"]),
                    style: StyleConfig.otherStyle(
                        color: ColorConfig.authenticationTextColor,
                        fontSize: 12),
                  ),
                ),
                SizedBox(height: 8.h),
                Row(
                  children: [
                    Images(
                      path: R.console_sound_explan_image_png,
                      width: 18.r,
                      height: 11.h,
                    ),
                    SizedBox(width: 3.w),
                    Text(
                      "console_sound_explan_title_four".tr,
                      style: StyleConfig.otherStyle(
                          color: ColorConfig.searchTextColor,
                          fontWeight: FontWeight.w600,
                          fontSize: 14),
                    )
                  ],
                ),
                Container(
                  margin: EdgeInsets.only(top: 2.h, left: 20.w),
                  width: 1.sw,
                  child: Text(
                    "console_sound_explan_tips_four".tr,
                    style: StyleConfig.otherStyle(
                        color: ColorConfig.authenticationTextColor,
                        fontSize: 12),
                  ),
                ),
              ],
            ),
          ),
          SizedBox(height: 30.h),
          Images(
            path: R.task_close_png,
            width: 34.r,
            height: 34.r,
          ).inkWell(() => Get.back())
        ],
      ),
    );
  }
}
