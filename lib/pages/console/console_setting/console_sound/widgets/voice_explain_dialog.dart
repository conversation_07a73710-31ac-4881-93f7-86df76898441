import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:getx_xiaopa/extension/images.dart';
import 'package:getx_xiaopa/extension/widget_extension.dart';
import 'package:getx_xiaopa/r.dart';
import 'package:getx_xiaopa/values/values.dart';

class VoiceExplainDialog extends StatelessWidget {
  const VoiceExplainDialog({super.key});

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            padding: EdgeInsets.only(
                top: 30.h, bottom: 34.h, left: 15.w, right: 15.w),
            width: 296.w,
            decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(10.r), color: Colors.white),
            child: Column(
              children: [
                Text(
                  "console_sound_voice_title".tr,
                  style: StyleConfig.otherStyle(
                      color: ColorConfig.searchTextColor,
                      fontWeight: FontWeight.w700,
                      fontSize: 20),
                ),
                SizedBox(height: 20.h),
                Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Container(
                      margin: EdgeInsets.only(top: 5.h),
                      child: Images(
                        path: R.console_sound_explan_image_png,
                        width: 18.r,
                        height: 11.h,
                      ),
                    ),
                    SizedBox(width: 3.w),
                    SizedBox(
                      width: 241.w,
                      child: Text(
                        "console_sound_voice_title_two".tr,
                        style: StyleConfig.otherStyle(
                            color: ColorConfig.searchTextColor,
                            fontWeight: FontWeight.w600,
                            fontSize: 14),
                      ),
                    )
                  ],
                ),
                Container(
                  margin: EdgeInsets.only(top: 7.h, left: 12.w, right: 13.w),
                  width: 1.sw,
                  child: Text(
                    "console_sound_voice_tips_two".tr,
                    style: StyleConfig.otherStyle(
                        color: ColorConfig.authenticationTextColor,
                        fontWeight: FontWeight.w500,
                        fontSize: 12),
                  ),
                ),
              ],
            ),
          ),
          SizedBox(height: 30.h),
          Images(
            path: R.task_close_png,
            width: 34.r,
            height: 34.r,
          ).inkWell(() => Get.back())
        ],
      ),
    );
  }
}
