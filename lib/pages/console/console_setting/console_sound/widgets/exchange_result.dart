import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:getx_xiaopa/extension/images.dart';
import 'package:getx_xiaopa/extension/widget_extension.dart';
import 'package:getx_xiaopa/r.dart';
import 'package:getx_xiaopa/values/values.dart';
import 'package:getx_xiaopa/widget/widget.dart';

class ExchangeResult extends StatelessWidget {
  const ExchangeResult({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      color: const Color.fromRGBO(249, 249, 249, 1),
      child: Column(
        children: [
          SizedBox(height: 61.h),
          Row(
            children: [
              SizedBox(width: 15.w),
              Images(
                path: R.back_png,
                width: 20.w,
                height: 20.h,
              ).inkWell(() => Get.back())
            ],
          ),
          Sized<PERSON>ox(height: 58.h),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Images(
                path: R.mine_agree_png,
                width: 20.r,
                height: 20.r,
              ),
              SizedBox(width: 6.w),
              Text(
                "console_sound_exchange_success".tr,
                style: StyleConfig.otherStyle(
                    color: ColorConfig.searchTextColor,
                    fontWeight: FontWeight.w700,
                    fontSize: 24),
              )
            ],
          ),
          SizedBox(height: 49.h),
          Images(
            path: R.console_sound_exchange_image_png,
            width: 242.w,
            height: 135.h,
          ),
          SizedBox(height: 31.h),
          Container(
            padding: EdgeInsets.all(10.r),
            width: 335.w,
            height: 90.h,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(10.r),
              color: Colors.white,
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.06),
                  // 阴影颜色和透明度
                  spreadRadius: 0,
                  // 阴影扩散范围
                  blurRadius: 10,
                  // 阴影模糊程度
                  offset: const Offset(0, 2),
                  // 阴影偏移量（水平，垂直）
                )
              ],
            ),
            child: Row(
              children: [
                Images(
                  path: R.console_sound_exchange_result_image_png,
                  width: 94.w,
                  height: 70.h,
                ),
                SizedBox(width: 15.w),
                Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      "console_sound_exchange_tips_one".tr,
                      style: StyleConfig.otherStyle(
                          color: ColorConfig.searchTextColor,
                          fontWeight: FontWeight.w700),
                    ),
                    SizedBox(height: 6.h),
                    Text(
                      "console_sound_exchange_tips_date".tr,
                      style: StyleConfig.otherStyle(
                          color: ColorConfig.shopDetailTextColor,
                          fontWeight: FontWeight.w400),
                    ),
                  ],
                )
              ],
            ),
          ),
          const Expanded(child: SizedBox()),
          Container(
            margin:
                EdgeInsets.only(bottom: ScreenUtil().statusBarHeight + 30.h),
            width: 283.w,
            height: 48.h,
            decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(24.r),
                color: ColorConfig.searchTextColor),
            child: Center(
              child: ThemeText(
                dataStr: "console_sound_exchange_btn".tr,
                keyName: 'textColor',
                fontSize: 16,
                fontWeight: FontWeight.w700,
              ),
            ),
          ).inkWell(() => Get.back(result: "next"))
        ],
      ),
    );
  }
}
