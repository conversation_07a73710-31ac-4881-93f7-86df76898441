///
/// Code generated by jsonToDartModel https://ashamp.github.io/jsonToDartModel/
///
class VoiceBuyModel {
/*
{
  "id": "5006313605038081",
  "created_at": "2025-07-28T10:01:06.928+08:00",
  "updated_at": "2025-07-28T10:01:06.928+08:00",
  "deleted_at": null,
  "name": "19866726670的声音1",
  "audio": "",
  "text": "",
  "language": 0,
  "type": 2,
  "user_id": "",
  "user_mobile": "",
  "status": 1,
  "train_num": 0,
  "speaker_id": "S_R9Ji4Wft1",
  "is_default": 0
} 
*/

  String? id;
  String? createdAt;
  String? updatedAt;
  String? deletedAt;
  String? name;
  String? audio;
  String? text;
  int? language;
  int? type;
  String? userId;
  String? userMobile;
  int? status;
  int? trainNum;
  String? speakerId;
  int? isDefault;

  VoiceBuyModel({
    this.id,
    this.createdAt,
    this.updatedAt,
    this.deletedAt,
    this.name,
    this.audio,
    this.text,
    this.language,
    this.type,
    this.userId,
    this.userMobile,
    this.status,
    this.trainNum,
    this.speakerId,
    this.isDefault,
  });
  VoiceBuyModel.fromJson(Map<String, dynamic> json) {
    id = json['id']?.toString();
    createdAt = json['created_at']?.toString();
    updatedAt = json['updated_at']?.toString();
    deletedAt = json['deleted_at']?.toString();
    name = json['name']?.toString();
    audio = json['audio']?.toString();
    text = json['text']?.toString();
    language = json['language']?.toInt();
    type = json['type']?.toInt();
    userId = json['user_id']?.toString();
    userMobile = json['user_mobile']?.toString();
    status = json['status']?.toInt();
    trainNum = json['train_num']?.toInt();
    speakerId = json['speaker_id']?.toString();
    isDefault = json['is_default']?.toInt();
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['id'] = id;
    data['created_at'] = createdAt;
    data['updated_at'] = updatedAt;
    data['deleted_at'] = deletedAt;
    data['name'] = name;
    data['audio'] = audio;
    data['text'] = text;
    data['language'] = language;
    data['type'] = type;
    data['user_id'] = userId;
    data['user_mobile'] = userMobile;
    data['status'] = status;
    data['train_num'] = trainNum;
    data['speaker_id'] = speakerId;
    data['is_default'] = isDefault;
    return data;
  }
}
