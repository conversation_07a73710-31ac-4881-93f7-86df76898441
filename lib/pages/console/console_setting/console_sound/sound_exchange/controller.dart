import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:getx_xiaopa/extension/widget_extension.dart';
import 'package:getx_xiaopa/network/http_client.dart';
import 'package:getx_xiaopa/network/model/result.dart';
import 'package:getx_xiaopa/pages/console/console_setting/console_sound/sound_exchange/model/voice_buy_model.dart';
import 'package:getx_xiaopa/pages/console/console_setting/console_sound/sound_exchange/widgets/exchange_result.dart';
import 'package:getx_xiaopa/pages/console/console_setting/console_sound/widgets/exchange_result.dart';
import 'package:getx_xiaopa/routes/router.dart';

class SoundExchangeController extends GetxController {
  SoundExchangeController();

  TextEditingController ticketController = TextEditingController();

  @override
  void onReady() {
    super.onReady();
    update(["sound_exchange"]);
  }

  exchangeAction() {
    if (ticketController.text.isEmpty) {
      CToast.showToast("console_sound_exchange_view_empty_tips".tr);
      return;
    }
    if (ticketController.text.length != 17) {
      CToast.showToast("console_sound_exchange_view_length_tips".tr);
      return;
    }

    _exchangeVoice();
  }

  ///====================网络请求===================
  _exchangeVoice() async {
    CToast.showLoading();
    var map = {"code": ticketController.text};
    Result result = await http.botSnActivate(map);
    if (result.code == 0) {
      VoiceBuyModel model = result.data;

      Get.to(() => const ExchangeResult())?.then((result) {
        if (result == "next") {
          var map = {"voiceId": model.id!};
          Get.toNamed(AppRoutes.SOUND_RECORD, arguments: map)?.then((result) {
            if (result == "success") {
              Get.back(result: "success");
            }
          });
        }
      });
    } else {
      Get.dialog(ExchangeTicketResult(
          tips: result.msg ?? "console_sound_exchange_view_no_tips".tr));
    }
  }
}
