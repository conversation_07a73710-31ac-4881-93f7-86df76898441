import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:getx_xiaopa/extension/images.dart';
import 'package:getx_xiaopa/extension/widget_extension.dart';
import 'package:getx_xiaopa/utils/utils.dart';
import 'package:getx_xiaopa/values/values.dart';
import 'package:getx_xiaopa/widget/widget.dart';

class ExchangeTicketResult extends StatelessWidget {
  final String tips;
  const ExchangeTicketResult({super.key, required this.tips});

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            width: 295.w,
            height: 226.h,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(10.r),
              gradient: const LinearGradient(
                colors: [
                  Color.fromRGBO(255, 253, 247, 1),
                  Color.fromRGBO(255, 255, 255, 1)
                ],
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
              ),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.06),
                  // 阴影颜色和透明度
                  spreadRadius: 0,
                  // 阴影扩散范围
                  blurRadius: 10,
                  // 阴影模糊程度
                  offset: const Offset(0, 2),
                  // 阴影偏移量（水平，垂直）
                )
              ],
            ),
            child: Stack(
              alignment: Alignment.topCenter,
              clipBehavior: Clip.none,
              children: [
                Positioned(
                  top: -20.h,
                  width: 50.w,
                  height: 56.h,
                  child: Images(
                    path: Get.find<ThemeImageController>().exchangeResultImage,
                    boxFit: BoxFit.fill,
                  ),
                ),
                Positioned(
                  top: 56.h,
                  child: Text(
                    "console_sound_exchange_fail_title".tr,
                    style: StyleConfig.otherStyle(
                        color: ColorConfig.searchTextColor,
                        fontSize: 18,
                        fontWeight: FontWeight.w700),
                  ),
                ),
                Positioned(
                  top: 95.h,
                  child: Text(
                    tips,
                    style: StyleConfig.otherStyle(
                        color: ColorConfig.shopDetailTextColor,
                        fontWeight: FontWeight.w500),
                  ),
                ),
                Positioned(
                  top: 138.h,
                  child: Container(
                    margin: EdgeInsets.only(
                        left: 46.w,
                        right: 46.w,
                        bottom: ScreenUtil().statusBarHeight + 30.h),
                    width: 200.w,
                    height: 48.h,
                    decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(24.r),
                        color: ColorConfig.searchTextColor),
                    child: Center(
                      child: ThemeText(
                        dataStr: "confirm".tr,
                        keyName: 'textColor',
                        fontSize: 16,
                        fontWeight: FontWeight.w700,
                      ),
                    ),
                  ).inkWell(() => Get.back()),
                )
              ],
            ),
          )
        ],
      ),
    );
  }
}
