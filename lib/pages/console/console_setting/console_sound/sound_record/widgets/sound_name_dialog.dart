import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:getx_xiaopa/extension/widget_extension.dart';
import 'package:getx_xiaopa/storage/storage.dart';
import 'package:getx_xiaopa/values/values.dart';
import 'package:getx_xiaopa/widget/widget.dart';

class SoundNameDialog extends StatefulWidget {
  final Function(String) onTap;
  const SoundNameDialog({super.key, required this.onTap});

  @override
  State<SoundNameDialog> createState() => _SoundNameDialogState();
}

class _SoundNameDialogState extends State<SoundNameDialog> {
  final TextEditingController _controller = TextEditingController();
  final RegExp _chineseEnglishRegex = RegExp(r'^[\u4e00-\u9fa5a-zA-Z]+$');
  int _lenght = 0;

  int _maxLenght = 8;

  @override
  void initState() {
    super.initState();
    _maxLenght = ConfigStore.to.getLocaleCode() == "zh" ? 8 : 20;
  }

  _textChange(String str) {
    setState(() {
      _lenght = str.length;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            margin: EdgeInsets.only(bottom: 100.h),
            width: 295.w,
            // height: 269.h,
            decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(10.r), color: Colors.white),
            child: Column(
              children: [
                SizedBox(height: 26.h),
                Text(
                  "console_sound_name_dialog_title".tr,
                  style: StyleConfig.otherStyle(
                      color: ColorConfig.searchTextColor,
                      fontSize: 18,
                      fontWeight: FontWeight.w600),
                ),
                SizedBox(height: 10.h),
                Container(
                  margin: EdgeInsets.only(left: 41.w, right: 30.w),
                  child: Text(
                    "console_sound_name_dialog_tips".tr,
                    textAlign: TextAlign.center,
                    style: StyleConfig.otherStyle(
                        color: ColorConfig.shopDetailTextColor,
                        fontSize: 12,
                        fontWeight: FontWeight.w500),
                  ),
                ),
                Container(
                  margin: EdgeInsets.only(top: 15.h),
                  width: 245.w,
                  height: 40.h,
                  decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(6.r),
                      color: const Color.fromRGBO(243, 243, 243, 1)),
                  child: AppTextField(
                    controller: _controller,
                    style: StyleConfig.otherStyle(
                        color: ColorConfig.searchTextColor,
                        height: 1,
                        fontSize: 14),
                    keyboardType: TextInputType.text,
                    maxLength: _maxLenght,
                    onChanged: _textChange,
                    decoration: InputDecoration(
                      border: InputBorder.none,
                      enabledBorder: InputBorder.none,
                      focusedBorder: InputBorder.none,
                      counterText: '',
                      contentPadding: EdgeInsets.symmetric(
                          horizontal: 10.w, vertical: 16.h),
                      hintText: 'console_sound_item_name_hint_text'.tr,
                      hintStyle: StyleConfig.otherStyle(
                          color: ColorConfig.shopDetailTextColor,
                          height: 1,
                          fontSize: 14),
                    ),
                  ),
                ),
                Container(
                  margin: EdgeInsets.only(left: 33.w, top: 8.h, right: 24.w),
                  child: Row(
                    children: [
                      SizedBox(
                        width: 200.w,
                        child: Text(
                          "console_sound_item_name_hlep_text"
                              .trArgs(['$_maxLenght']),
                          style: StyleConfig.otherStyle(
                              color: ColorConfig.shopDetailTextColor,
                              fontSize: 12),
                        ),
                      ),
                      const Expanded(child: SizedBox()),
                      Text(
                        "$_lenght/$_maxLenght",
                        style: StyleConfig.otherStyle(
                            color: ColorConfig.shopDetailTextColor,
                            fontSize: 12),
                      ),
                    ],
                  ),
                ),
                Container(
                  margin: EdgeInsets.only(top: 30.h, bottom: 25.h),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Container(
                        width: 105.w,
                        height: 48.h,
                        decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(24.r),
                            color: const Color.fromRGBO(215, 215, 215, 1)),
                        child: Center(
                          child: Text(
                            "cancel".tr,
                            style: StyleConfig.otherStyle(
                              color: ColorConfig.authenticationTextColor,
                            ),
                          ),
                        ),
                      ).inkWell(() {
                        Get.back();
                      }),
                      SizedBox(width: 16.w),
                      Container(
                        width: 135.w,
                        height: 48.h,
                        decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(24.r),
                            color: const Color.fromRGBO(40, 39, 46, 1)),
                        child: Center(
                          child: ThemeText(
                            dataStr: "confirm".tr,
                            keyName: 'textColor',
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ).inkWell(() {
                        if (_controller.text.isNotEmpty) {
                          if (!_chineseEnglishRegex
                              .hasMatch(_controller.text)) {
                            CToast.showToast(
                                "console_sound_item_name_hlep_text_one".tr);
                            return;
                          }
                          Get.back();
                          widget.onTap(_controller.text);
                        } else {
                          CToast.showToast(
                              "console_sound_item_name_hlep_text_two".tr);
                        }
                      })
                    ],
                  ),
                )
              ],
            ),
          )
        ],
      ),
    );
  }
}
