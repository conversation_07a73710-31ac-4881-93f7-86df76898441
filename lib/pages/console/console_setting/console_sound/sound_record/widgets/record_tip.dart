import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:getx_xiaopa/extension/images.dart';
import 'package:getx_xiaopa/extension/widget_extension.dart';
import 'package:getx_xiaopa/pages/console/console_setting/console_sound/sound_record/controller.dart';
import 'package:getx_xiaopa/r.dart';
import 'package:getx_xiaopa/values/values.dart';
import 'package:getx_xiaopa/widget/widget.dart';

class RecordTip extends GetView<SoundRecordController> {
  const RecordTip({super.key});

  // @override
  // String? get navTitle => "录制注意事项";

  // @override
  // TextStyle? get navTitleStyle =>
  //     StyleConfig.blackStyle(fontSize: 18, fontWeight: FontWeight.w600);

  // @override
  // Color? get navColor => Colors.transparent;

  // @override
  // double? get leftWidth => 35.w;

  // @override
  // Widget? get leftButton => Padding(
  //       padding: EdgeInsets.only(left: 15.w),
  //       child: Images(
  //         path: R.back_png,
  //         width: 20.w,
  //         height: 20.h,
  //         scale: 2.8,
  //       ).inkWell(() => Get.back()),
  //     );

  // @override
  // Color? get contentColor => const Color.fromRGBO(249, 249, 249, 1);

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Container(
          margin: EdgeInsets.only(top: 10.h, left: 15.w, right: 15.w),
          padding: EdgeInsets.only(top: 20.h, left: 10.w, bottom: 20.h),
          width: 345.w,
          // height: 441.h,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(10.r),
            color: Colors.white,
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.06),
                // 阴影颜色和透明度
                spreadRadius: 0,
                // 阴影扩散范围
                blurRadius: 10,
                // 阴影模糊程度
                offset: const Offset(0, 2),
                // 阴影偏移量（水平，垂直）
              )
            ],
          ),
          child: Column(
            children: [
              Row(
                children: [
                  Images(
                    path: R.sound_record_image_001_png,
                    width: 50.r,
                    height: 50.r,
                  ),
                  SizedBox(width: 14.w),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        "console_sound_tips_title_one".tr,
                        style: StyleConfig.otherStyle(
                            color: ColorConfig.searchTextColor,
                            fontWeight: FontWeight.w700),
                      ),
                      SizedBox(height: 6.h),
                      SizedBox(
                        width: 256.w,
                        child: Text(
                          "console_sound_tips_tips_one".tr,
                          style: StyleConfig.otherStyle(
                              fontSize: 12,
                              color: ColorConfig.authenticationTextColor,
                              fontWeight: FontWeight.w400),
                        ),
                      ),
                    ],
                  )
                ],
              ),
              SizedBox(height: 17.h),
              Row(
                children: [
                  Images(
                    path: R.sound_record_image_002_png,
                    width: 50.r,
                    height: 50.r,
                  ),
                  SizedBox(width: 14.w),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        "console_sound_tips_title_two".tr,
                        style: StyleConfig.otherStyle(
                            color: ColorConfig.searchTextColor,
                            fontWeight: FontWeight.w700),
                      ),
                      SizedBox(height: 6.h),
                      SizedBox(
                        width: 256.w,
                        child: Text(
                          "console_sound_tips_tips_two".tr,
                          style: StyleConfig.otherStyle(
                              fontSize: 12,
                              color: ColorConfig.authenticationTextColor,
                              fontWeight: FontWeight.w400),
                        ),
                      ),
                    ],
                  )
                ],
              ),
              SizedBox(height: 17.h),
              Row(
                children: [
                  Images(
                    path: R.sound_record_image_003_png,
                    width: 50.r,
                    height: 50.r,
                  ),
                  SizedBox(width: 14.w),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        "console_sound_tips_title_three".tr,
                        style: StyleConfig.otherStyle(
                            color: ColorConfig.searchTextColor,
                            fontWeight: FontWeight.w700),
                      ),
                      SizedBox(height: 6.h),
                      SizedBox(
                        width: 256.w,
                        child: Text(
                          "console_sound_tips_tips_three".tr,
                          style: StyleConfig.otherStyle(
                              fontSize: 12,
                              color: ColorConfig.authenticationTextColor,
                              fontWeight: FontWeight.w400),
                        ),
                      ),
                    ],
                  )
                ],
              ),
              SizedBox(height: 17.h),
              Row(
                children: [
                  Images(
                    path: R.sound_record_image_004_png,
                    width: 50.r,
                    height: 50.r,
                  ),
                  SizedBox(width: 14.w),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        "console_sound_tips_title_four".tr,
                        style: StyleConfig.otherStyle(
                            color: ColorConfig.searchTextColor,
                            fontWeight: FontWeight.w700),
                      ),
                      SizedBox(height: 6.h),
                      SizedBox(
                        width: 256.w,
                        child: Text(
                          "console_sound_tips_tips_four".tr,
                          style: StyleConfig.otherStyle(
                              fontSize: 12,
                              color: ColorConfig.authenticationTextColor,
                              fontWeight: FontWeight.w400),
                        ),
                      ),
                    ],
                  )
                ],
              ),
              SizedBox(height: 17.h),
              Row(
                children: [
                  Images(
                    path: R.sound_record_image_005_png,
                    width: 50.r,
                    height: 50.r,
                  ),
                  SizedBox(width: 14.w),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        "console_sound_tips_title_five".tr,
                        style: StyleConfig.otherStyle(
                            color: ColorConfig.searchTextColor,
                            fontWeight: FontWeight.w700),
                      ),
                      SizedBox(height: 6.h),
                      SizedBox(
                        width: 256.w,
                        child: Text(
                          "console_sound_tips_tips_five".tr,
                          style: StyleConfig.otherStyle(
                              fontSize: 12,
                              color: ColorConfig.authenticationTextColor,
                              fontWeight: FontWeight.w400),
                        ),
                      ),
                    ],
                  )
                ],
              ),
            ],
          ),
        ),
        const Expanded(child: SizedBox()),
        Container(
          margin: EdgeInsets.only(bottom: ScreenUtil().statusBarHeight + 30.h),
          width: 283.w,
          height: 48.h,
          decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(24.r),
              color: ColorConfig.searchTextColor),
          child: Center(
            child: ThemeText(
              dataStr: "console_sound_exchange_btn".tr,
              keyName: 'textColor',
              fontSize: 16,
              fontWeight: FontWeight.w700,
            ),
          ),
        ).inkWell(() {
          controller.readTipsComplete();
        })
      ],
    );
  }
}
