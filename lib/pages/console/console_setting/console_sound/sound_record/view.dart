import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:getx_xiaopa/base/base.dart';
import 'package:getx_xiaopa/extension/images.dart';
import 'package:getx_xiaopa/extension/widget_extension.dart';
import 'package:getx_xiaopa/pages/console/console_setting/console_sound/sound_record/widgets/record_tip.dart';
import 'package:getx_xiaopa/r.dart';
import 'package:getx_xiaopa/utils/utils.dart';
import 'package:getx_xiaopa/values/values.dart';
import 'package:getx_xiaopa/widget/bar/bar_pulse_loading.dart';
import 'package:getx_xiaopa/widget/widget.dart';

import 'index.dart';

class SoundRecordPage extends BaseCommonView<SoundRecordController> {
  SoundRecordPage({super.key});

  @override
  String? get navTitle => 'console_sound_record'.tr;

  @override
  TextStyle? get navTitleStyle =>
      StyleConfig.blackStyle(fontSize: 18, fontWeight: FontWeight.w600);

  @override
  Color? get navColor => Colors.transparent;

  @override
  double? get leftWidth => 35.w;

  @override
  Widget? get leftButton => Padding(
        padding: EdgeInsets.only(left: 15.w),
        child: Images(
          path: R.back_png,
          width: 20.w,
          height: 20.h,
          scale: 2.8,
        ).inkWell(() => Get.back()),
      );

  @override
  Color? get contentColor => const Color.fromRGBO(249, 249, 249, 1);

  // 主视图
  Widget _buildView() {
    return Column(
      children: [
        Container(
          margin: EdgeInsets.only(top: 17.h, left: 20.w, right: 20.w),
          width: 335.w,
          height: 481.h,
          decoration: const BoxDecoration(
            image: DecorationImage(
                image: AssetImage(R.sound_record_bg_png), fit: BoxFit.fill),
          ),
          child: Column(
            children: [
              SizedBox(height: 94.h),
              Text(
                "console_sound_record_title".tr,
                style: StyleConfig.otherStyle(
                    color: ColorConfig.searchTextColor,
                    fontWeight: FontWeight.w700,
                    fontSize: 22),
              ),
              SizedBox(height: 65.h),
              SizedBox(
                width: 257.h,
                child: Text(
                  "console_sound_record_content".tr,
                  textAlign: TextAlign.center,
                  style: StyleConfig.otherStyle(
                      color: ColorConfig.searchTextColor, fontSize: 18),
                ),
              ),
              Offstage(
                offstage: !controller.isFail,
                child: Container(
                  margin: EdgeInsets.only(top: 23.h),
                  alignment: Alignment.topCenter,
                  child: IntrinsicWidth(
                    child: ThemeContainerImage(
                      fileName: 'sound_record_tip_bg.png',
                      fit: BoxFit.fill,
                      padding: EdgeInsets.only(
                          top: 10.h, left: 25.w, right: 25.w, bottom: 21.h),
                      conHeight: 51.h,
                      alignment: Alignment.center,
                      child: Text(
                        "console_sound_record_error_tips".tr,
                        style: StyleConfig.otherStyle(
                            color: const Color.fromRGBO(249, 56, 56, 1),
                            fontWeight: FontWeight.w400,
                            fontSize: 14),
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
        SizedBox(height: 70.h),
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Offstage(
              offstage: !controller.isCompleted,
              child: Container(
                margin: EdgeInsets.only(right: 32.w),
                child: Column(
                  children: [
                    controller.isPlay
                        ? Container(
                            padding: EdgeInsets.only(left: 25.w, right: 25.w),
                            width: 90.r,
                            height: 90.r,
                            decoration: const BoxDecoration(
                              image: DecorationImage(
                                  image: AssetImage(R.sound_record_btn_bg_png),
                                  fit: BoxFit.fill),
                            ),
                            child: BarPulseLoading(
                              width: 1.w,
                              height: 3.h,
                              color:
                                  Get.find<ThemeColorController>().gIconColor,
                            ),
                          ).inkWell(() => controller.stopRecordAction())
                        : ThemeImagePath(
                            fileName: 'sound_record_play.png',
                            imgWidget: 90.r,
                            imgHeight: 90.r,
                          ),
                    SizedBox(height: 2.h),
                    Text(
                      controller.isPlay
                          ? "console_sound_record_stop".tr
                          : "console_sound_record_listen".tr,
                      style: StyleConfig.otherStyle(
                          color: ColorConfig.authenticationTextColor,
                          fontWeight: FontWeight.w500,
                          fontSize: 14),
                    )
                  ],
                ),
              ).inkWell(() => controller.playRecordAction()),
            ),
            Column(
              children: [
                SizedBox(height: 8.h),
                ThemeImagePath(
                  fileName: controller.isRecording
                      ? 'sound_record_stop.png'
                      : 'sound_record_icon.png',
                  imgWidget: 70.r,
                  imgHeight: 70.r,
                ),
                SizedBox(height: 14.h),
                Text(
                  controller.tips,
                  style: StyleConfig.otherStyle(
                      color: ColorConfig.authenticationTextColor,
                      fontWeight: FontWeight.w500,
                      fontSize: 14),
                )
              ],
            ).inkWell(() => controller.starRecordAction()),
            Offstage(
              offstage: !controller.isCompleted,
              child: Container(
                margin: EdgeInsets.only(left: 32.w),
                child: Column(
                  children: [
                    ThemeImagePath(
                      fileName: 'sound_record_completed.png',
                      imgWidget: 90.r,
                      imgHeight: 90.r,
                    ),
                    Text(
                      "console_sound_record_compose".tr,
                      style: StyleConfig.otherStyle(
                          color: ColorConfig.authenticationTextColor,
                          fontWeight: FontWeight.w500,
                          fontSize: 14),
                    )
                  ],
                ),
              ).inkWell(() => controller.completedAction()),
            )
          ],
        )
      ],
    );
  }

  @override
  Widget buildContent() {
    return GetBuilder<SoundRecordController>(
      init: SoundRecordController(),
      id: "sound_record",
      builder: (_) {
        return controller.isTip ? const RecordTip() : _buildView();
      },
    );
  }
}
