import 'dart:io';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:getx_xiaopa/extension/widget_extension.dart';
import 'package:getx_xiaopa/network/dio_util.dart';
import 'package:getx_xiaopa/network/http_client.dart';
import 'package:getx_xiaopa/network/model/result.dart';
import 'package:getx_xiaopa/pages/console/console_setting/console_sound/sound_record/widgets/sound_name_dialog.dart';
import 'package:getx_xiaopa/utils/utils.dart';
import 'package:getx_xiaopa/widget/widget.dart';
import 'package:path_provider/path_provider.dart';
import 'package:record/record.dart';

class SoundRecordController extends GetxController {
  bool isTip = true;

  final _recorder = AudioRecorder();
  late Directory dir;
  var path = '';
  final SoundEffectPlayer soundEffectPlayer =
      SoundEffectPlayer(volume: 1, maxPlayers: 1);

  String voiceName = '';

  String voiceId = '';

  ///是否正在录制
  bool isRecording = false;

  String tips = "console_sound_record_start_record".tr;

  ///是否完成
  bool isCompleted = false;

  ///录制失败
  bool isFail = false;

  ///播放试听
  bool isPlay = false;

  @override
  void onReady() async {
    super.onReady();
    voiceId = Get.arguments["voiceId"];
    dir = await getTemporaryDirectory();
    path = '${dir.path}/audio_${DateTime.now().millisecondsSinceEpoch}.wav';
    update(["sound_record"]);
  }

  readTipsComplete() {
    isTip = false;
    update(["sound_record"]);
  }

  starRecordAction() async {
    if (path.isEmpty) {
      path = '${dir.path}/audio_${DateTime.now().millisecondsSinceEpoch}.wav';
    }
    if (isRecording) {
      _recorder.stop();
      isRecording = await _recorder.isRecording();
      isCompleted = true;
      tips = "console_sound_record_reset_record".tr;
      update(["sound_record"]);
    } else {
      FileStore.to.deleteFile(path);
      PermissionUtil.microPhone(Get.context!, action: () async {
        _recorder.start(const RecordConfig(encoder: AudioEncoder.wav),
            path: path);
        isRecording = await _recorder.isRecording();
        tips = "console_sound_record_stop_record".tr;
        isCompleted = false;
        update(["sound_record"]);
      });
    }
  }

  playRecordAction() async {
    soundEffectPlayer.play(path, isCall: true, onStartPlay: () {
      isPlay = true;
      update(["sound_record"]);
    }, onComplete: () {
      isPlay = false;
      update(["sound_record"]);
    });
  }

  stopRecordAction() async {
    soundEffectPlayer.stop();
    isPlay = false;
    update(["sound_record"]);
  }

  completedAction() {
    soundEffectPlayer.stop();
    Get.dialog(SoundNameDialog(
      onTap: (str) {
        if (str.isNotEmpty) {
          voiceName = str;
          _completedActionTips();
        }
      },
    ), barrierDismissible: false);
  }

  _completedActionTips() {
    Get.dialog(
      CommonDialog(
        mContent: "console_sound_record_dialog_tips".tr,
        mContextAlign: TextAlign.center,
        mContextSize: 12,
        mTitle: "console_sound_record_dialog_title".tr,
        titleFontWeight: FontWeight.w600,
        mConfirm: "console_sound_record_dialog_confirm".tr,
        confirmFontWeight: FontWeight.w600,
        confirmAction: () {
          _voiceAudioUpdate();
        },
      ),
    );
  }

  @override
  void onClose() {
    _recorder.dispose();
    super.onClose();
  }

  ///===================网络请求==========================
  _voiceAudioUpdate() async {
    if (voiceId.isEmpty) {
      CToast.showToast("console_sound_record_error_one".tr);
      return;
    }
    final result = await DioUtil.getInstance()
        .uploadFile(path, access: "bot-voice", filename: "audio.wav");
    if (result.isNotEmpty) {
      FileStore.to.deleteFile(path);
      _voiceUpdate(result);
    } else {
      CToast.showToast("console_sound_record_error_two".tr);
    }
  }

  _voiceUpdate(String audioPath) async {
    CToast.showLoading();
    var map = {"id": voiceId, "audio": audioPath, "name": voiceName};
    Result result = await http.voiceUpdate(map);
    if (result.code == 0) {
      _voiceTrain();
    } else {
      CToast.showToast(result.msg!);
    }
  }

  _voiceTrain() async {
    CToast.showLoading();
    var map = {"id": voiceId};
    Result result = await http.voiceTrain(map);
    if (result.code == 0) {
      Get.back(result: "success");
    } else {
      CToast.showToast(result.msg!);
    }
  }
}
