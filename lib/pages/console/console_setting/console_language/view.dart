import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:getx_xiaopa/base/base.dart';
import 'package:getx_xiaopa/extension/images.dart';
import 'package:getx_xiaopa/extension/widget_extension.dart';
import 'package:getx_xiaopa/pages/console/console_setting/console_language/widgets/console_language_item.dart';
import 'package:getx_xiaopa/r.dart';
import 'package:getx_xiaopa/storage/storage.dart';
import 'package:getx_xiaopa/values/values.dart';

import 'index.dart';

class ConsoleLanguagePage extends BaseCommonView<ConsoleLanguageController> {
  ConsoleLanguagePage({super.key});

  @override
  Color? get navColor => Colors.transparent;

  @override
  String? get navTitle => "console_language_title".tr;

  @override
  TextStyle? get navTitleStyle =>
      StyleConfig.blackStyle(fontSize: 18, fontWeight: FontWeight.w600);

  @override
  double? get leftWidth => 35.w;

  @override
  Color? get contentColor => const Color.fromRGBO(249, 249, 249, 1);

  @override
  Widget? get leftButton => Padding(
        padding: EdgeInsets.only(left: 15.w),
        child: Images(
          path: R.back_png,
          width: 20.w,
          height: 20.h,
          scale: 2.8,
        ).inkWell(() => Get.back()),
      );

  // 主视图
  Widget _buildView() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          margin: EdgeInsets.only(top: 20.h, left: 24.w),
          width: 275.w,
          child: Text(
            'console_language_tips'.tr,
            textAlign: TextAlign.left,
            style: StyleConfig.otherStyle(
                color: const Color.fromRGBO(180, 142, 78, 1),
                fontSize: ConfigStore.to.getLocaleCode() == "zh" ? 18 : 16,
                fontWeight: FontWeight.w700),
          ),
        ),
        Expanded(
          child: Stack(
            clipBehavior: Clip.none,
            children: [
              Container(
                margin: EdgeInsets.only(top: 10.h, left: 15.w, right: 15.w),
                width: 375.w,
                height: 125.h,
                constraints: BoxConstraints(minHeight: 125.h),
                clipBehavior: Clip.hardEdge,
                decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(10.r),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withValues(alpha: 0.06),
                        // 阴影颜色和透明度
                        spreadRadius: 0,
                        // 阴影扩散范围
                        blurRadius: 10,
                        // 阴影模糊程度
                        offset: const Offset(0, 2),
                        // 阴影偏移量（水平，垂直）
                      )
                    ]),
                child: controller.languages.isEmpty
                    ? const SizedBox()
                    : ConsoleLanguageItem(
                        cIndex: controller.cIndex,
                        structList: controller.languages,
                        onTap: (cIndex) => controller.changeLanguage(cIndex),
                      ),
              ),
              Positioned(
                top: -25.h,
                right: 9.w,
                width: 58.w,
                height: 40.h,
                child: Images(path: R.console_language_image_png),
              ),
            ],
          ),
        ),
        Images(
          path: R.console_language_bg_png,
          width: 375.w,
          height: 346.h,
          boxFit: BoxFit.fill,
        )
      ],
    );
  }

  @override
  Widget buildContent() {
    return GetBuilder<ConsoleLanguageController>(
      init: ConsoleLanguageController(),
      id: "console_language",
      builder: (_) {
        return _buildView();
      },
    );
  }
}
