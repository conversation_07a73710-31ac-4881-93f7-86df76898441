import 'package:get/get.dart';
import 'package:getx_xiaopa/extension/widget_extension.dart';
import 'package:getx_xiaopa/network/http_client.dart';
import 'package:getx_xiaopa/network/model/result.dart';
import 'package:getx_xiaopa/pages/ai_chat/ai_chat_logic.dart';
import 'package:getx_xiaopa/pages/console/console_setting/console_language/widgets/console_language_item.dart';
import 'package:getx_xiaopa/pages/console/model/device_items_result.dart';
import 'package:getx_xiaopa/storage/storage.dart';
import 'package:getx_xiaopa/widget/widget.dart';

class ConsoleLanguageController extends GetxController {
  ConsoleLanguageController();

  DeviceItemsModel model = DeviceItemsModel();

  ///当前使用的语言
  int cIndex = 0;

  ///选择修改的语言
  int _cIndex = 0;

  final List _languages = [
    {"name": "简体中文", "code": "zh", "country": "CN"},
    {"name": "English", "code": "en", "country": "US"},
  ];

  List<ConsoleLanguageStruct> languages = [];

  reloadData() {
    languages.clear();
    for (var i = 0; i < _languages.length; i++) {
      ConsoleLanguageStruct struct = ConsoleLanguageStruct();
      struct.name = _languages[i]["name"];
      struct.code = _languages[i]["code"];
      struct.country = _languages[i]["country"];
      if (model.lang == struct.code) {
        cIndex = i;
      }
      languages.add(struct);
    }
    update(["console_language"]);
  }

  updateStatus(DeviceItemsModel vModel) {
    model = vModel;
    update(["console_language"]);
  }

  changeLanguage(int index) {
    if (index == cIndex) return;
    _cIndex = index;
    Get.dialog(
      CommonDialog(
        mContent: "console_language_one".tr,
        confirmAction: () async {
          _sendDeviceConfigAction();
        },
      ),
    );
  }

  ///===================网络请求===========================
  _sendDeviceConfigAction() async {
    if (model.id!.isEmpty) {
      CToast.showToast("console_language_two".tr);
      return;
    }
    CToast.showLoading();
    var map = {
      "id": model.id,
      "lang": _languages[_cIndex]["code"],
    };
    Result result = await http.updateDevicePreference(map);
    if (result.code == 0) {
      cIndex = _cIndex;

      ///切换数字人数据
      if (CommonStore.to.showAichat == 1) {
        Get.find<AiChatLogic>().changeBrain();
      }

      sendDeviceConfigAction(type: 1);
    }
    CToast.showToast(result.msg!);
  }

  /// 发送修改设备配置
  /// type 1：重启 2：查询电量音量 3：升级 4：设置音量 5：查询版本号 6：设置麦克风 7：设置提醒
  sendDeviceConfigAction(
      {int? type, String command = "", dynamic value = ""}) async {
    if (model.isOnline == 2) {
      return;
    }
    var map = {
      "device_id": model.id,
      "command": command,
      "value": value,
      "type": type
    };
    Result result = await http.setUpDevice(map);
    if (result.code != 0) {
      CToast.showToast(result.msg!);
    }
  }
}
