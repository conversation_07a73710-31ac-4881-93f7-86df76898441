import 'package:auto_size_text/auto_size_text.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:getx_xiaopa/base/base.dart';
import 'package:getx_xiaopa/extension/images.dart';
import 'package:getx_xiaopa/extension/widget_extension.dart';
import 'package:getx_xiaopa/network/http_client.dart';
import 'package:getx_xiaopa/pages/console/console_schedule/utils/schedule_store.dart';
import 'package:getx_xiaopa/pages/console/widget/floating_text_overlay.dart';
import 'package:getx_xiaopa/pages/task/widget/water_fill_widget_v3.dart';
import 'package:getx_xiaopa/r.dart';
import 'package:getx_xiaopa/storage/storage.dart';
import 'package:getx_xiaopa/utils/utils.dart';
import 'package:getx_xiaopa/values/values.dart';
import 'package:getx_xiaopa/widget/widget.dart';
import 'package:loading_animation_widget/loading_animation_widget.dart';

import 'console_logic.dart';
import 'console_state.dart';

class ConsolePage extends BaseCommonView {
  ConsolePage({super.key});

  final ConsoleLogic logic = Get.put(ConsoleLogic());
  final ConsoleState state = Get.find<ConsoleLogic>().state;

  @override
  bool get customWidget => true;

  @override
  Widget buildContent() {
    return FloatingTextOverlay(
      child: GetBuilder<ConsoleLogic>(
        id: "console_view",
        builder: (_) => createCommonView(logic, (_) {
          return _bodyWidget();
        }, initBuilder: () => const LoadStatusWidget()),
      ),
    );
  }

  Widget _bodyWidget() {
    Widget temp = const SizedBox();
    if (state.isBind) {
      temp = Scaffold(
        extendBodyBehindAppBar: true,
        appBar: PreferredSize(
          preferredSize: Size.fromHeight(65.h),
          child: AppBar(
            systemOverlayStyle: const SystemUiOverlayStyle(
              statusBarColor: Colors.transparent,
              statusBarIconBrightness: Brightness.dark,
            ),
            automaticallyImplyLeading: false,
            backgroundColor: state.navColor,
            elevation: 0,
            scrolledUnderElevation: 0,
            clipBehavior: Clip.none,
            flexibleSpace: Stack(
              children: [
                Positioned(
                  left: 30.w,
                  bottom: 14.h,
                  child: ThemeImagePath(
                    fileName: "console_hi_icon.png",
                    imgWidget: 92.w,
                    imgHeight: 30.h,
                  ),
                ),
                Positioned(
                  left: 90.w,
                  bottom: 15.h,
                  width: 158.w,
                  child: AutoSizeText(
                    "${UserStore.to.userInfo.nickname}",
                    maxLines: 1,
                    style: StyleConfig.otherStyle(
                        color: const Color.fromRGBO(69, 67, 74, 1),
                        fontSize: 20,
                        fontWeight: FontWeight.w600),
                  ),
                ),
                Positioned(
                  right: 20.w,
                  bottom: 18.h,
                  child: Row(
                    children: [
                      state.isRefresh
                          ? LoadingAnimationWidget.fourRotatingDots(
                              color: const Color.fromRGBO(185, 185, 185, 1),
                              size: 20.sp)
                          : Images(
                              path: R.console_refresh_png,
                              width: 20.r,
                              height: 20.r,
                            ).inkWell(
                              () => logic.onRefreshData(isAnimation: true)),
                      SizedBox(width: 16.w),
                      Images(
                        path: R.console_bind_add_png,
                        width: 20.r,
                        height: 20.r,
                      ).inkWell(() => logic.toPairingPage()),
                      SizedBox(width: 16.w),
                      Images(
                        path: R.console_bind_setting_png,
                        width: 20.r,
                        height: 20.r,
                      ).inkWell(() => logic.toSettingPage()),
                    ],
                  ),
                )
              ],
            ),
          ),
        ),
        body: ThemeContainerImage(
          conWidget: 1.sw,
          conHeight: 1.sh,
          fileName: 'console_bind_bg.png',
          fit: BoxFit.fill,
          child: SingleChildScrollView(
            physics: const ClampingScrollPhysics(),
            controller: state.sController,
            child: Column(
              children: [
                Container(
                  margin: EdgeInsets.only(top: 99.h),
                  width: 1.sw,
                  height: 274.h,
                  child: Stack(
                    children: [
                      Positioned(
                        top: 26.h,
                        left: 90.w,
                        width: 216.w,
                        height: 259.h,
                        child: AnimatedPress(
                          isAuto: false,
                          seconds: 100,
                          scaleEnd: 0.95,
                          child: ThemeImagePath(
                            key: state.floatingTextKey,
                            fileName: 'console_bind_image_03.png',
                            imgWidget: 206.w,
                            imgHeight: 249.h,
                          ).inkWell(() => logic.vibratianAction()),
                        ),
                      ),
                      Positioned(
                        top: 5.h,
                        left: 32.w,
                        child: Row(
                          children: [
                            Text(
                              UserStore.to.deviceList.length >= 2
                                  ? "${"console_name".tr}_${state.socketModel.mac?.substring(state.socketModel.mac!.length - 4, state.socketModel.mac?.length).toUpperCase()}"
                                  : "console_name".tr,
                              style: StyleConfig.otherStyle(
                                  color: ColorConfig.searchTextColor,
                                  fontSize: 18,
                                  fontWeight: FontWeight.w600),
                            ),
                            SizedBox(width: 2.w),
                            Container(
                              margin: EdgeInsets.only(top: 11.h),
                              child: Images(
                                path: R.console_bind_image_05_png,
                                width: 7.w,
                                height: 6.h,
                              ),
                            )
                          ],
                        ).inkWell(() => logic.selectDeviceAction()),
                      ),
                      Positioned(
                        top: 40.h,
                        left: 34.w,
                        child: Row(
                          children: [
                            Images(
                              path: state.isOnline
                                  ? R.console_bind_wifi_on_png
                                  : R.console_bind_wifi_off_png,
                              width: 22.w,
                              height: 22.h,
                              color: state.isOnline
                                  ? null
                                  : const Color.fromRGBO(253, 105, 82, 1),
                            ),
                            SizedBox(width: 3.w),
                            Text(
                              state.onlineTips,
                              style: StyleConfig.otherStyle(
                                  color: ColorConfig.shopDetailTextColor,
                                  fontSize: 10),
                            )
                          ],
                        ),
                      ),
                      Positioned(
                        top: 65.h,
                        left: 34.w,
                        child: Row(
                          children: [
                            SizedBox(
                              width: 22.w,
                              height: 22.h,
                              child: Stack(
                                children: [
                                  Positioned.fill(
                                      child: Images(
                                    path: R.power_bg_png,
                                    color: state.isOnline
                                        ? null
                                        : const Color.fromRGBO(
                                            215, 215, 215, 1),
                                  )),
                                  Positioned(
                                    top: 8.h,
                                    left: 3.w,
                                    width: 13.w,
                                    height: 6.h,
                                    child: LinearProgressIndicator(
                                      value: state.isCharge
                                          ? null
                                          : state.powerValue,
                                      backgroundColor: Colors.white,
                                      valueColor: AlwaysStoppedAnimation(state
                                              .isOnline
                                          ? const Color.fromRGBO(56, 197, 96, 1)
                                          : const Color.fromRGBO(
                                              215, 215, 215, 1)),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            SizedBox(width: 3.w),
                            Text(
                              "${(state.powerValue * 100).toInt()}%",
                              style: StyleConfig.otherStyle(
                                  color: ColorConfig.shopDetailTextColor,
                                  fontSize: 10),
                            )
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
                Container(
                  padding: EdgeInsets.only(top: 20.h, bottom: 20.h),
                  width: 345.w,
                  // height: 223.h,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(10.r),
                    color: Colors.white,
                    boxShadow: [
                      BoxShadow(
                        color:
                            Get.find<ThemeColorController>().gConBoxShadowColor,
                        // 阴影颜色和透明度
                        spreadRadius: 0,
                        // 阴影扩散范围
                        blurRadius: 18,
                        // 阴影模糊程度
                        offset: const Offset(0, 2),
                        // 阴影偏移量（水平，垂直）
                      )
                    ],
                  ),
                  child: Column(
                    children: [
                      ThemeContainer(
                        tMargin: EdgeInsets.only(left: 10.w, right: 10.w),
                        tHeight: 40.h,
                        flag: state.isOnline,
                        keyName: 'conBgColor',
                        subColor: const Color.fromRGBO(238, 238, 238, 1),
                        radius: 8.r,
                        boxShadow: [
                          BoxShadow(
                            color: Colors.white.withValues(alpha: 0.5),
                            // 阴影颜色和透明度
                            spreadRadius: 0,
                            // 阴影扩散范围
                            blurRadius: 3,
                            // 阴影模糊程度
                            offset: const Offset(0, 1),
                            // 阴影偏移量（水平，垂直）
                          )
                        ],
                        child: CustomSlider(
                          controller: state.sliderController,
                          iconPosition: IconPosition.inside,
                          startIcon: ThemeImagePath(
                            fileName: state.isOnline
                                ? state.volumeValue == 0
                                    ? "console_bind_audio_off.png"
                                    : "console_bind_audio_on.png"
                                : 'console_bind_audio_off.png',
                            imgWidget: 30.r,
                            imgHeight: 30.r,
                            imgColor: state.isOnline
                                ? null
                                : const Color.fromRGBO(215, 215, 215, 1),
                          ),
                          enabled: state.isOnline ? true : false,
                          initialProgress:
                              state.isOnline ? state.volumeValue : 0,
                          min: 0,
                          max: 1,
                          unfocusedHeight: 40.h,
                          focusedHeight: 40.h,
                          padding: EdgeInsets.zero,
                          iconGap: 12.w,
                          backgroundColor: Colors.transparent,
                          foregroundColor: const Color.fromRGBO(40, 39, 46, 1),
                          shapeBorder: ContinuousRectangleBorder(
                            borderRadius:
                                BorderRadius.all(Radius.circular(20.r)),
                          ),
                          isPaddingAnimated: false,
                          onFocused: (value) => logic.volumeFocused(value),
                          onChanged: (value) => logic.volumeChange(value),
                          onProgressUpdated: (value) =>
                              logic.volumeAction(value),
                        ),
                      ),
                      Container(
                        margin:
                            EdgeInsets.only(top: 20.h, left: 15.w, right: 15.w),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                ThemeImagePath(
                                  fileName: state.isOnline
                                      ? 'console_bind_mode.png'
                                      : 'console_bind_mode_no_online.png',
                                  imgWidget: 30.r,
                                  imgHeight: 30.r,
                                ),
                                SizedBox(width: 7.w),
                                Text(
                                  "console_interrupt_mode".tr,
                                  style: StyleConfig.otherStyle(
                                      color:
                                          const Color.fromRGBO(69, 67, 74, 1),
                                      fontWeight: FontWeight.w600),
                                ),
                                const Expanded(child: SizedBox()),
                                CupertinoSwitch(
                                    activeTrackColor: state.isOnline
                                        ? ColorConfig.searchTextColor
                                        : const Color.fromRGBO(
                                            215, 215, 215, 1),
                                    value: state.isInterrupt,
                                    onChanged: (value) =>
                                        logic.interruptMode(value)),
                                SizedBox(width: 10.w)
                              ],
                            ),
                            Container(
                              margin: EdgeInsets.only(top: 0.h, left: 1.w),
                              child: Text(
                                "console_interrupt_mode_tip".tr,
                                style: StyleConfig.otherStyle(
                                    color: ColorConfig.shopDetailTextColor,
                                    fontSize: 12),
                              ),
                            ),
                          ],
                        ),
                      ),
                      Container(
                        margin:
                            EdgeInsets.only(top: 18.h, left: 15.w, right: 15.w),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                ThemeImagePath(
                                  fileName: state.isOnline
                                      ? 'console_jingyin.png'
                                      : 'console_jingyin_no_online.png',
                                  imgWidget: 30.r,
                                  imgHeight: 30.r,
                                ),
                                SizedBox(width: 7.w),
                                Text(
                                  "console_silence_mode".tr,
                                  style: StyleConfig.otherStyle(
                                      color:
                                          const Color.fromRGBO(69, 67, 74, 1),
                                      fontWeight: FontWeight.w600),
                                ),
                                const Expanded(child: SizedBox()),
                                CupertinoSwitch(
                                    activeTrackColor: state.isOnline
                                        ? ColorConfig.searchTextColor
                                        : const Color.fromRGBO(
                                            215, 215, 215, 1),
                                    value: state.isSilence,
                                    onChanged: (value) =>
                                        logic.silenceMode(value)),
                                SizedBox(width: 10.w)
                              ],
                            ),
                            Container(
                              margin: EdgeInsets.only(top: 0.h, left: 1),
                              child: Text(
                                "console_silence_mode_tip".tr,
                                style: StyleConfig.otherStyle(
                                    color: ColorConfig.shopDetailTextColor,
                                    fontSize: 12),
                              ),
                            )
                          ],
                        ),
                      )
                    ],
                  ),
                ),
                Container(
                  margin: EdgeInsets.only(top: 10.h, left: 15.w, right: 15.w),
                  child: Row(
                    children: [
                      Container(
                        decoration: const BoxDecoration(
                          boxShadow: [
                            BoxShadow(
                              color: Color.fromRGBO(226, 226, 226, 0.5),
                              // 阴影颜色和透明度
                              spreadRadius: 0,
                              // 阴影扩散范围
                              blurRadius: 16,
                              // 阴影模糊程度
                              offset: Offset(0, 0),
                              // 阴影偏移量（水平，垂直）
                            )
                          ],
                        ),
                        child: ThemeContainerImage(
                          padding: EdgeInsets.only(top: 10.h),
                          conWidget: 164.w,
                          conHeight: 174.h,
                          fileName: 'drink_bg.png',
                          fit: BoxFit.fill,
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Container(
                                margin: EdgeInsets.only(
                                    left: ConfigStore.to.getLocaleCode() == "zh"
                                        ? 15.w
                                        : 5.w),
                                child: Text(
                                  'console_drink_record'.tr,
                                  style: StyleConfig.otherStyle(
                                    color: ColorConfig.searchTextColor,
                                    fontSize: 18,
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                              ),
                              Container(
                                margin: EdgeInsets.only(
                                    top: 4.h,
                                    left: ConfigStore.to.getLocaleCode() == "zh"
                                        ? 15.w
                                        : 5.w),
                                child: Row(
                                  children: [
                                    ThemeText(
                                      dataStr: 'console_dirk_tips_one'.trArgs(
                                          [state.cupSliderSchedule.toString()]),
                                      keyName: 'cupSchedule',
                                      fontSize: 14,
                                      fontWeight: FontWeight.w600,
                                    ),
                                    SizedBox(width: 7.w),
                                    Container(
                                      margin: EdgeInsets.only(top: 2.h),
                                      child: Text(
                                        "console_dirk_tips_two".trArgs(
                                            [state.maxCupSchedule.toString()]),
                                        style: StyleConfig.otherStyle(
                                            color:
                                                ColorConfig.shopDetailTextColor,
                                            fontSize: ConfigStore.to
                                                        .getLocaleCode() ==
                                                    "zh"
                                                ? 12
                                                : 10,
                                            wordSpacing: ConfigStore.to
                                                        .getLocaleCode() ==
                                                    "zh"
                                                ? 0
                                                : -1),
                                      ),
                                    )
                                  ],
                                ),
                              ),
                              Container(
                                child: state.isDrinkSuccess
                                    ? Container(
                                        margin: EdgeInsets.only(left: 8.w),
                                        child: Images(
                                          path:
                                              fileUrl(UserStore.to.skinPicture),
                                          width: 137.w,
                                          height: 110.h,
                                        ),
                                      )
                                    : Container(
                                        margin: EdgeInsets.only(
                                            top: 8.h, left: 42.w),
                                        width: 75.w,
                                        height: 94.h,
                                        child: WaterFillWidgetV3(
                                          isOut: true,
                                          currentCapacity: state
                                              .cupSliderSchedule
                                              .toDouble(),
                                          maxCapacity:
                                              state.maxCupSchedule.toDouble(),
                                          imagePath: UserStore.to.contour,
                                        ),
                                      ),
                              )
                            ],
                          ),
                        ).inkWell(() => logic.toTaskPage()),
                      ),
                      SizedBox(width: 12.w),
                      Container(
                        decoration: const BoxDecoration(
                          boxShadow: [
                            BoxShadow(
                              color: Color.fromRGBO(226, 226, 226, 0.5),
                              // 阴影颜色和透明度
                              spreadRadius: 0,
                              // 阴影扩散范围
                              blurRadius: 16,
                              // 阴影模糊程度
                              offset: Offset(0, 0),
                              // 阴影偏移量（水平，垂直）
                            )
                          ],
                        ),
                        child: ThemeContainerImage(
                          padding: EdgeInsets.only(top: 10.h),
                          conWidget: 169.w,
                          conHeight: 174.h,
                          fileName: 'schedule_bg.png',
                          fit: BoxFit.fill,
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Container(
                                margin: EdgeInsets.only(left: 15.w),
                                child: Text(
                                  'console_schedule_title'.tr,
                                  style: StyleConfig.otherStyle(
                                    color: ColorConfig.searchTextColor,
                                    fontSize: 18,
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                              ),
                              Container(
                                margin: EdgeInsets.only(top: 4.h, left: 15.w),
                                child: ThemeText(
                                  dataStr: 'console_schedule_tips_one'.trArgs([
                                    "${ScheduleStore.to.scheduleTodayList.length}"
                                  ]),
                                  keyName: 'mineOnline',
                                  fontSize: 14,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                              Container(
                                margin: EdgeInsets.only(
                                    top: 68.h,
                                    left:
                                        DateTime.now().day.toString().length ==
                                                2
                                            ? 40.w
                                            : 45.w),
                                child: ThemeText(
                                  dataStr: '${DateTime.now().day}',
                                  keyName: 'mineOnline',
                                  fontSize: 18,
                                  fontWeight: FontWeight.w600,
                                  skewX: -0.1,
                                ),
                              )
                            ],
                          ),
                        ).inkWell(() => logic.toSchedulePage()),
                      )
                    ],
                  ),
                ),
                SizedBox(height: 10.h)
              ],
            ),
          ),
        ),
      );
    } else {
      temp = AnnotatedRegion<SystemUiOverlayStyle>(
        value: SystemUiOverlayStyle.dark,
        child: Container(
          width: 1.sw,
          height: 1.sh,
          decoration: const BoxDecoration(
              image: DecorationImage(
                  image: AssetImage(R.console_bg_png), fit: BoxFit.cover)),
          child: Column(
            children: [
              const Expanded(child: SizedBox()),
              Container(
                margin: EdgeInsets.only(bottom: 21.h),
                child: Text(
                  'console_tips'.tr,
                  style: StyleConfig.otherStyle(
                      color: const Color.fromRGBO(69, 47, 10, 1),
                      fontWeight: FontWeight.w500),
                ),
              ),
              Container(
                margin: EdgeInsets.only(bottom: 62.h),
                width: 200.w,
                height: 48.h,
                decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(24.r),
                    color: const Color.fromRGBO(40, 39, 46, 1)),
                child: Center(
                  child: ThemeText(
                    dataStr: "console_add".tr,
                    keyName: 'textColor',
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ).inkWell(() => logic.toPairingPage())
            ],
          ),
        ),
      );
    }

    return temp;
  }
}
