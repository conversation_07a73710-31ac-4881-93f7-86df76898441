import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:getx_xiaopa/extension/images.dart';
import 'package:getx_xiaopa/extension/widget_extension.dart';
import 'package:getx_xiaopa/r.dart';
import 'package:getx_xiaopa/values/values.dart';
import 'package:getx_xiaopa/widget/widget.dart';

class OneWidget extends StatefulWidget {
  final Function? sAction;

  const OneWidget({super.key, this.sAction});

  @override
  State<OneWidget> createState() => _OneWidgetState();
}

class _OneWidgetState extends State<OneWidget> {
  bool _isShow = true;
  late Timer? _timer;
  @override
  void initState() {
    super.initState();
    _timer = Timer.periodic(const Duration(milliseconds: 1500), (timer) {
      setState(() {
        _isShow = !_isShow;
      });
    });
  }

  @override
  void dispose() {
    super.dispose();
    _timer?.cancel();
  }

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Container(
        padding: EdgeInsets.only(left: 15.w, right: 15.w),
        width: 295.w,
        height: 425.h,
        decoration: BoxDecoration(
            color: Colors.white, borderRadius: BorderRadius.circular(10.r)),
        child: Column(
          children: [
            Container(
              margin: EdgeInsets.only(top: 15.h, right: 15.w),
              alignment: Alignment.centerRight,
              child: Images(
                path: R.close_png,
                width: 24.r,
                height: 24.r,
              ),
            ).inkWell(() => Get.back()),
            Container(
              margin: EdgeInsets.only(top: 10.h),
              width: 160.w,
              height: 160.h,
              decoration: const BoxDecoration(
                  image: DecorationImage(
                      image: AssetImage(R.console_image_02_png),
                      fit: BoxFit.fill)),
              child: Stack(
                children: [
                  Positioned(
                    top: 60.h,
                    left: 62.w,
                    width: 8.w,
                    height: 8.h,
                    child: Offstage(
                      offstage: _isShow,
                      child: Images(path: R.nose_png),
                    ),
                  ),
                  Positioned(
                    top: 83.h,
                    left: 40.w,
                    width: 10.w,
                    height: 6.h,
                    child: Offstage(
                      offstage: !_isShow,
                      child: Images(path: R.nose_x_png),
                    ),
                  ),
                ],
              ),
            ),
            SizedBox(height: 17.h),
            Text(
              "console_pairing_step_one".tr,
              style: StyleConfig.otherStyle(
                  color: ColorConfig.searchTextColor,
                  fontSize: 14,
                  fontWeight: FontWeight.w600),
            ),
            SizedBox(height: 6.h),
            Text(
              "console_pairing_step_two".tr,
              textAlign: TextAlign.center,
              style: StyleConfig.otherStyle(
                  color: ColorConfig.searchTextColor,
                  fontSize: 14,
                  fontWeight: FontWeight.w600),
            ),
            SizedBox(height: 6.h),
            Text(
              "console_pairing_before".tr,
              textAlign: TextAlign.center,
              style: StyleConfig.otherStyle(
                  color: ColorConfig.shopDetailTextColor, fontSize: 12),
            ),
            SizedBox(height: 20.h),
            Container(
              width: 200.w,
              height: 48.h,
              decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(24.r),
                  color: const Color.fromRGBO(40, 39, 46, 1)),
              child: Center(
                child: ThemeText(
                  dataStr: "console_pairing_start".tr,
                  keyName: 'textColor',
                  fontWeight: FontWeight.w500,
                  textAlign: TextAlign.center,
                ),
              ),
            ).inkWell(() {
              Get.back();
              widget.sAction?.call();
            })
          ],
        ),
      ),
    );
  }
}
