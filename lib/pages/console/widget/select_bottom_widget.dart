import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:getx_xiaopa/extension/widget_extension.dart';
import 'package:getx_xiaopa/pages/console/model/device_items_result.dart';
import 'package:getx_xiaopa/utils/utils.dart';
import 'package:getx_xiaopa/values/values.dart';
import 'package:getx_xiaopa/widget/widget.dart';

class SelectBottomWidget extends StatefulWidget {
  final int cIndex;
  final List<DeviceItemsModel> deviceItems;
  final Function(int index) onTap;

  const SelectBottomWidget(
      {super.key,
      required this.deviceItems,
      required this.cIndex,
      required this.onTap});

  @override
  State<SelectBottomWidget> createState() => _SelectBottomWidgetState();
}

class _SelectBottomWidgetState extends State<SelectBottomWidget> {
  int _cIndex = 0;

  bool flag = false;

  @override
  void initState() {
    super.initState();
    _cIndex = widget.cIndex;
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 1.sw,
      constraints: BoxConstraints(minHeight: 100.h, maxHeight: 400.h),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.only(
            topLeft: Radius.circular(20.r), topRight: Radius.circular(20.r)),
      ),
      child: Column(
        children: [
          SizedBox(height: 20.h),
          Text(
            "console_select".tr,
            style: StyleConfig.otherStyle(
                color: ColorConfig.searchTextColor,
                fontWeight: FontWeight.w700,
                fontSize: 20),
          ),
          SizedBox(height: 20.h),
          Expanded(
            child: ListView.builder(
                itemCount: widget.deviceItems.length,
                itemBuilder: (context, index) {
                  flag = index == _cIndex;
                  return Column(
                    children: [
                      SizedBox(
                        height: 57.h,
                        child: Row(
                          children: [
                            SizedBox(width: 20.w),
                            Text(
                              "${"console_name".tr}_${widget.deviceItems[index].mac!.toUpperCase()}",
                              style: StyleConfig.otherStyle(
                                  color: ColorConfig.searchTextColor),
                            ),
                            const Expanded(child: SizedBox()),
                            CheckBoxRounded(
                              isChecked: flag,
                              isGroup: true,
                              value: index,
                              groupValue: _cIndex,
                              checkedColor: Get.find<ThemeColorController>()
                                  .getColor('textColor'),
                              checkedBgColor: ColorConfig.searchTextColor,
                              uncheckedBgColor: Colors.white,
                              borderColor:
                                  const Color.fromRGBO(215, 215, 215, 1),
                              onGroupTap: (index) {
                                setState(() {
                                  _cIndex = index!;
                                });
                                widget.onTap(_cIndex);
                              },
                            ),
                            SizedBox(width: 20.w),
                          ],
                        ),
                      ),
                      Container(
                        width: 345.w,
                        height: 1.h,
                        color: const Color.fromRGBO(233, 232, 233, 1),
                      ),
                    ],
                  ).inkWell(() {
                    setState(() {
                      _cIndex = index;
                    });
                    widget.onTap(_cIndex);
                  });
                }),
          ),
          SizedBox(height: 20.h)
        ],
      ),
    );
  }
}
