import 'dart:math';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:getx_xiaopa/utils/utils.dart';

class FloatingText extends StatefulWidget {
  final String text;
  final Offset centerOffset;
  final VoidCallback onFinish;

  const FloatingText({
    super.key,
    required this.text,
    required this.centerOffset,
    required this.onFinish,
  });

  @override
  State<FloatingText> createState() => _FloatingTextState();
}

class _FloatingTextState extends State<FloatingText>
    with SingleTickerProviderStateMixin {
  late final AnimationController _controller;
  late final Animation<double> _opacityAnimation;
  late final Animation<double> _progressAnimation;
  late final Offset _randomOffset;

  final Random _random = Random();

  @override
  void initState() {
    super.initState();

    _randomOffset = Offset(
      (_random.nextDouble() - 0.5) * 40,
      (_random.nextDouble() - 0.5) * 20,
    );

    _controller = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 1200),
    );

    _opacityAnimation = Tween<double>(begin: 1, end: 0).animate(_controller);
    _progressAnimation = CurvedAnimation(
      parent: _controller,
      curve: Curves.easeOut,
    );

    _controller.forward().whenComplete(() {
      if (mounted) {
        widget.onFinish();
      }
    });
  }

  @override
  void dispose() {
    _controller.dispose(); // 正确销毁 ticker
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final base = widget.centerOffset + _randomOffset;

    return AnimatedBuilder(
      animation: _controller,
      builder: (_, __) {
        final progress = _progressAnimation.value;
        final dy = -60 * progress;
        final dx = sin(progress * pi * 4) * 5;
        final pos = base + Offset(dx, dy);
        // 计算缩放：0.5 -> 1.2 -> 0.0
        final scale = TweenSequence([
          TweenSequenceItem(
              tween: Tween(begin: 0.5, end: 1.2)
                  .chain(CurveTween(curve: Curves.easeOut)),
              weight: 50),
          TweenSequenceItem(
              tween: Tween(begin: 1.2, end: 0.0)
                  .chain(CurveTween(curve: Curves.easeIn)),
              weight: 50),
        ]).evaluate(_controller);

        return Positioned(
          left: pos.dx,
          top: pos.dy,
          child: Opacity(
            opacity: _opacityAnimation.value,
            child: Transform.scale(
              scale: scale,
              child: Text(
                widget.text,
                style: TextStyle(
                  fontSize: 14.sp,
                  fontWeight: FontWeight.bold,
                  color: Get.find<ThemeColorController>().gFloatingTextColor,
                  shadows: const [Shadow(color: Colors.black26, blurRadius: 2)],
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}
