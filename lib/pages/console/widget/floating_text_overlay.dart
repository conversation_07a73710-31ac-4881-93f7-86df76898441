import 'package:flutter/material.dart';
import 'package:getx_xiaopa/pages/console/widget/floating_text.dart';

class FloatingTextOverlay extends StatefulWidget {
  final Widget child;

  const FloatingTextOverlay({super.key, required this.child});

  // ignore: library_private_types_in_public_api
  static _FloatingTextOverlayState of(BuildContext context) {
    final state = context.findAncestorStateOfType<_FloatingTextOverlayState>();
    if (state == null) {
      throw Exception('FloatingTextOverlay not found in widget tree');
    }
    return state;
  }

  @override
  State<FloatingTextOverlay> createState() => _FloatingTextOverlayState();
}

class _FloatingTextOverlayState extends State<FloatingTextOverlay> {
  final List<_FloatingTextEntry> _entries = [];

  void show(String text, {required GlobalKey fromKey}) {
    final RenderBox? fromBox =
        fromKey.currentContext?.findRenderObject() as RenderBox?;
    final RenderBox? overlayBox = context.findRenderObject() as RenderBox?;

    if (fromBox == null || overlayBox == null) return;

    final fromPosition = fromBox.localToGlobal(Offset.zero);
    final local = overlayBox.globalToLocal(fromPosition);
    final size = fromBox.size;
    final center = local + Offset(size.width / 2 - 20, 0);

    final id = UniqueKey();
    final entry = _FloatingTextEntry(
      key: id,
      child: FloatingText(
        key: id,
        text: text,
        centerOffset: center,
        onFinish: () {
          setState(() {
            _entries.removeWhere((e) => e.key == id);
          });
        },
      ),
    );

    setState(() {
      _entries.add(entry);
    });
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        widget.child,
        ..._entries,
      ],
    );
  }
}

class _FloatingTextEntry extends StatelessWidget {
  final Widget child;
  const _FloatingTextEntry({required Key key, required this.child})
      : super(key: key);

  @override
  Widget build(BuildContext context) => child;
}
