///
/// Code generated by jsonToDartModel https://ashamp.github.io/jsonToDartModel/
///
///
/// Code generated by jsonToDartModel https://ashamp.github.io/jsonToDartModel/
///
class DeviceItemsModel {
/*
{
  "id": "4859157690712110",
  "created_at": "2025-06-07T15:47:58.237+08:00",
  "updated_at": "2025-07-11T11:02:05.424+08:00",
  "deleted_at": null,
  "bot_id": "4163364096835584",
  "mac": "30eda0301170",
  "user_id": "4928810819518464",
  "user_mobile": "19866726670",
  "power": 40,
  "volume": 100,
  "is_microphone": 2,
  "firmware_version": "1.0.22",
  "is_online": 1,
  "interrupt_mode": 0,
  "is_upgrade": 1,
  "theme": 2,
  "is_test": false,
  "bind_time": "2025-07-11T11:01:46.702+08:00",
  "is_bind": true,
  "is_active": true,
  "license_url": "",
  "license_expire_date": "2026-07-10T00:00:00+08:00",
  "renew_at": "2025-07-10 15:26:58",
  "personality": 1,
  "active_frequency": 0,
  "is_mute": false,
  "mute_at": ""
} 
*/

  String? id;
  String? createdAt;
  String? updatedAt;
  String? deletedAt;
  String? botId;
  String? mac;
  String? userId;
  String? lang;
  String? prompt;
  String? extraPrompt;
  String? userMobile;
  int? power;
  int? volume;
  int? isMicrophone;
  String? firmwareVersion;
  int? isOnline;
  int? interruptMode;
  int? isUpgrade;
  int? theme;
  bool? isTest;
  String? bindTime;
  bool? isBind;
  bool? isActive;
  String? licenseUrl;
  String? licenseExpireDate;
  String? renewAt;
  int? personality;
  int? activeFrequency;
  bool? isMute;
  String? muteAt;
  String? registrationId;

  DeviceItemsModel({
    this.id,
    this.createdAt,
    this.updatedAt,
    this.deletedAt,
    this.botId,
    this.mac,
    this.userId,
    this.lang,
    this.prompt,
    this.extraPrompt,
    this.userMobile,
    this.power,
    this.volume,
    this.isMicrophone,
    this.firmwareVersion,
    this.isOnline,
    this.interruptMode,
    this.isUpgrade,
    this.theme,
    this.isTest,
    this.bindTime,
    this.isBind,
    this.isActive,
    this.licenseUrl,
    this.licenseExpireDate,
    this.renewAt,
    this.personality,
    this.activeFrequency,
    this.isMute,
    this.muteAt,
    this.registrationId,
  });
  DeviceItemsModel.fromJson(Map<String, dynamic> json) {
    id = json['id']?.toString();
    createdAt = json['created_at']?.toString();
    updatedAt = json['updated_at']?.toString();
    deletedAt = json['deleted_at']?.toString();
    botId = json['bot_id']?.toString();
    mac = json['mac']?.toString();
    userId = json['user_id']?.toString();
    lang = json['lang']?.toString();
    prompt = json['prompt']?.toString();
    extraPrompt = json['extra_prompt']?.toString();
    userMobile = json['user_mobile']?.toString();
    power = json['power']?.toInt();
    volume = json['volume']?.toInt();
    isMicrophone = json['is_microphone']?.toInt();
    firmwareVersion = json['firmware_version']?.toString();
    isOnline = json['is_online']?.toInt();
    interruptMode = json['interrupt_mode']?.toInt();
    isUpgrade = json['is_upgrade']?.toInt();
    theme = json['theme']?.toInt();
    isTest = json['is_test'];
    bindTime = json['bind_time']?.toString();
    isBind = json['is_bind'];
    isActive = json['is_active'];
    licenseUrl = json['license_url']?.toString();
    licenseExpireDate = json['license_expire_date']?.toString();
    renewAt = json['renew_at']?.toString();
    personality = json['personality']?.toInt();
    activeFrequency = json['active_frequency']?.toInt();
    isMute = json['is_mute'];
    muteAt = json['mute_at']?.toString();
    registrationId = json['registration_id'].toString();
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['id'] = id;
    data['created_at'] = createdAt;
    data['updated_at'] = updatedAt;
    data['deleted_at'] = deletedAt;
    data['bot_id'] = botId;
    data['mac'] = mac;
    data['user_id'] = userId;
    data['lang'] = lang;
    data['prompt'] = prompt;
    data['extra_prompt'] = extraPrompt;
    data['user_mobile'] = userMobile;
    data['power'] = power;
    data['volume'] = volume;
    data['is_microphone'] = isMicrophone;
    data['firmware_version'] = firmwareVersion;
    data['is_online'] = isOnline;
    data['interrupt_mode'] = interruptMode;
    data['is_upgrade'] = isUpgrade;
    data['theme'] = theme;
    data['is_test'] = isTest;
    data['bind_time'] = bindTime;
    data['is_bind'] = isBind;
    data['is_active'] = isActive;
    data['license_url'] = licenseUrl;
    data['license_expire_date'] = licenseExpireDate;
    data['renew_at'] = renewAt;
    data['personality'] = personality;
    data['active_frequency'] = activeFrequency;
    data['is_mute'] = isMute;
    data['mute_at'] = muteAt;
    data['registration_id'] = registrationId;
    return data;
  }
}

class DeviceItemsResult {
/*
{
  "count": 1,
  "pageSize": 10,
  "pageNum": 1,
  "data": [
    {
      "id": "4163364096835585",
      "created_at": "0001-01-01T00:00:00Z",
      "updated_at": "2025-05-07T14:10:28.208+08:00",
      "deleted_at": null,
      "bot_id": "4163364096835584",
      "mac": "FC:01:2C:D1:56:B2",
      "user_id": "4470670919467008",
      "user_mobile": "",
      "power": 0,
      "volume": 50,
      "firmware_version": 0,
      "is_online": 2,
      "volume_gain": 0,
      "interrupt_mode": 1,
      "asr_end_window_size": 0,
      "asr_silence_time": 0,
      "asr_silence_threshold": 0,
      "asr_force_to_speechtime": 0,
      "speaker_id": "",
      "tts_speechrate": 0
    }
  ]
} 
*/

  int? count;
  int? pageSize;
  int? pageNum;
  List<DeviceItemsModel?>? data;

  DeviceItemsResult({
    this.count,
    this.pageSize,
    this.pageNum,
    this.data,
  });
  DeviceItemsResult.fromJson(Map<String, dynamic> json) {
    count = json['count']?.toInt();
    pageSize = json['pageSize']?.toInt();
    pageNum = json['pageNum']?.toInt();
    if (json['data'] != null) {
      final v = json['data'];
      final arr0 = <DeviceItemsModel>[];
      v.forEach((v) {
        arr0.add(DeviceItemsModel.fromJson(v));
      });
      data = arr0;
    }
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['count'] = count;
    data['pageSize'] = pageSize;
    data['pageNum'] = pageNum;
    if (this.data != null) {
      final v = this.data;
      final arr0 = [];
      for (var v in v!) {
        arr0.add(v!.toJson());
      }
      data['data'] = arr0;
    }
    return data;
  }
}
