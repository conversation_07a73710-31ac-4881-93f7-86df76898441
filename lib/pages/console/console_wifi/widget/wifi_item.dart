import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:getx_xiaopa/extension/images.dart';
import 'package:getx_xiaopa/extension/widget_extension.dart';
import 'package:getx_xiaopa/r.dart';
import 'package:getx_xiaopa/values/values.dart';

class WifiItem extends StatelessWidget {
  final int cIndex;
  final String wifiName;
  final bool isShowLine;
  final Function(int) action;

  const WifiItem(
      {super.key,
      required this.cIndex,
        required this.wifiName,
      this.isShowLine = true,
      required this.action});

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 58.h,
      child: Column(
        children: [
          Expanded(
            child: Container(
              margin: EdgeInsets.only(left: 31.w, right: 26.w),
              child: Row(
                children: [
                  Text(
                    wifiName,
                    style: StyleConfig.otherStyle(
                        color: ColorConfig.searchTextColor, fontSize: 14),
                  ),
                  const Expanded(child: SizedBox()),
                  Images(
                    path: R.console_wifi_png,
                    width: 20.r,
                    height: 20.r,
                  )
                ],
              ),
            ),
          ),
          Offstage(
            offstage: !isShowLine,
            child: Container(
              width: 295.w,
              height: 1.h,
              color: const Color.fromRGBO(233, 232, 233, 1),
            ),
          )
        ],
      ),
    ).inkWell(() => action(cIndex));
  }
}
