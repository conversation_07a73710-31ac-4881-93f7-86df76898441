import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:getx_xiaopa/extension/images.dart';
import 'package:getx_xiaopa/extension/widget_extension.dart';
import 'package:getx_xiaopa/pages/console/console_wifi/console_wifi_logic.dart';
import 'package:getx_xiaopa/r.dart';
import 'package:getx_xiaopa/values/values.dart';
import 'package:getx_xiaopa/widget/widget.dart';

class ConsoleWifiInput extends GetView<ConsoleWifiLogic> {
  const ConsoleWifiInput({super.key});

  @override
  Widget build(BuildContext context) {
    return GetBuilder<ConsoleWifiLogic>(
      id: "console_wifi_input",
      builder: (_) {
        return Scaffold(
          body: SingleChildScrollView(
            child: Container(
              color: Colors.white,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Container(
                    margin: EdgeInsets.only(
                        left: 15.w,
                        top: ScreenUtil().statusBarHeight + 17.h,
                        right: 15.w),
                    child: Images(
                      path: R.back_png,
                      width: 20.r,
                      height: 20.r,
                      color: ColorConfig.shopDetailTextColor,
                    ).inkWell(() => Get.back()),
                  ),
                  Container(
                    margin: EdgeInsets.only(top: 33.h, left: 40.w),
                    child: Text(
                      'console_pairing_wifi_title'.tr,
                      style: StyleConfig.otherStyle(
                          color: ColorConfig.searchTextColor,
                          fontSize: 26,
                          fontWeight: FontWeight.w500),
                    ),
                  ),
                  Container(
                    margin: EdgeInsets.only(top: 16.h, left: 95.w),
                    child: Images(
                        path: R.console_image_05_png,
                        width: 185.w,
                        height: 185.h),
                  ),
                  Container(
                    margin: EdgeInsets.only(left: 20.w, top: 86.h, right: 20.w),
                    height: 117.h,
                    decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(10.r),
                        color: const Color.fromRGBO(249, 249, 249, 1)),
                    child: Column(
                      children: [
                        SizedBox(
                          height: 58.h,
                          child: Row(
                            children: [
                              SizedBox(width: 30.w),
                              Text(
                                'console_paring_wifi_name'.tr,
                                style: StyleConfig.otherStyle(
                                    color: ColorConfig.searchTextColor,
                                    fontSize: 14),
                              ),
                              SizedBox(width: 20.w),
                              Expanded(
                                child: AppTextField(
                                  controller: controller.state.wifiName,
                                  style: StyleConfig.otherStyle(
                                      color: ColorConfig.searchTextColor,
                                      fontSize: 16,
                                      fontWeight: FontWeight.w500),
                                  keyboardType: TextInputType.text,
                                  decoration: InputDecoration(
                                    border: InputBorder.none,
                                    enabledBorder: InputBorder.none,
                                    focusedBorder: InputBorder.none,
                                    counterText: '',
                                    hintText:
                                        'console_paring_wifi_name_hint_text'.tr,
                                    hintStyle: StyleConfig.otherStyle(
                                        color: ColorConfig.shopDetailTextColor,
                                        fontSize: 14),
                                  ),
                                ),
                              )
                            ],
                          ),
                        ),
                        Container(
                          width: 295.w,
                          height: 1.h,
                          color: const Color.fromRGBO(233, 232, 233, 1),
                        ),
                        SizedBox(
                          height: 58.h,
                          child: Row(
                            children: [
                              SizedBox(width: 30.w),
                              Text(
                                'console_paring_wifi_pwd'.tr,
                                style: StyleConfig.otherStyle(
                                    color: ColorConfig.searchTextColor,
                                    fontSize: 14),
                              ),
                              SizedBox(width: 20.w),
                              Expanded(
                                child: AppTextField(
                                  controller: controller.state.wifiPwd,
                                  style: StyleConfig.otherStyle(
                                      color: ColorConfig.searchTextColor,
                                      fontSize: 16,
                                      fontWeight: FontWeight.w500),
                                  keyboardType: TextInputType.text,
                                  obscureText: controller.state.isObscureText,
                                  decoration: InputDecoration(
                                    border: InputBorder.none,
                                    enabledBorder: InputBorder.none,
                                    focusedBorder: InputBorder.none,
                                    counterText: '',
                                    hintText:
                                        'console_paring_wifi_pwd_hint_text'.tr,
                                    hintStyle: StyleConfig.otherStyle(
                                        color: ColorConfig.shopDetailTextColor,
                                        fontSize: 14),
                                    contentPadding:
                                        EdgeInsets.symmetric(vertical: 12.h),
                                    suffixIcon: Padding(
                                      padding: EdgeInsets.only(right: 20.w),
                                      child: Images(
                                        path: controller.state.iconStr,
                                        scale: 2.5,
                                      ),
                                    ).inkWell(
                                        () => controller.obscureTextAction()),
                                  ),
                                ),
                              )
                            ],
                          ),
                        )
                      ],
                    ),
                  ),
                  Container(
                    margin:
                        EdgeInsets.only(left: 46.w, top: 145.h, right: 46.w),
                    width: 283.w,
                    height: 48.h,
                    decoration: BoxDecoration(
                        color: controller.state.isNext
                            ? const Color.fromRGBO(40, 39, 46, 1)
                            : const Color.fromRGBO(215, 215, 215, 1),
                        borderRadius: BorderRadius.circular(24.r)),
                    child: Center(
                      child: Text(
                        "console_paring_wifi_next".tr,
                        style: StyleConfig.otherStyle(
                            color: controller.state.isNext
                                ? const Color.fromRGBO(255, 232, 174, 1)
                                : const Color.fromRGBO(102, 102, 102, 1),
                            fontWeight: FontWeight.w500),
                      ),
                    ),
                  ).inkWell(() => controller.wifiNextAction())
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}
