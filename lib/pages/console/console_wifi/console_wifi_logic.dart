import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:getx_xiaopa/extension/widget_extension.dart';
import 'package:getx_xiaopa/pages/console/console_wifi/widget/console_wifi_input.dart';
import 'package:getx_xiaopa/r.dart';
import 'package:getx_xiaopa/storage/storage.dart';
import 'package:getx_xiaopa/utils/utils.dart';
import 'package:getx_xiaopa/widget/widget.dart';

import 'console_wifi_state.dart';

class ConsoleWifiLogic extends GetxController
    with GetSingleTickerProviderStateMixin {
  final ConsoleWifiState state = ConsoleWifiState();

  @override
  void onInit() async {
    super.onInit();
    state.wifiName.addListener(_listener);
    state.wifiPwd.addListener(_listener);

    for (var wifiConfig in ConfigStore.to.wifiConfig) {
      Map temp = jsonDecode(wifiConfig);
      state.wifiConnectedList.add(temp.keys.first);
    }

    state.sController.addListener(() {
      ///监听滚动位置设置导航栏颜色
      double opacity;
      opacity = state.sController.offset / 100;

      if (opacity > 0.1) opacity = 0.9;

      state.navColor =
          state.sController.offset > 5.h ? Colors.white : Colors.transparent;
      update(["console_wifi_view"]);
    });

    state.espBlufiResultSubscription =
        await EspBlufiUtil.onEspBlufiResultListener(onSuccess: (res) {
      var result = jsonDecode(res);

      logD("设备配对wifi结果：$result");

      if (result["tag"] == "wifi") {
        ///扫描wifi
        if (result["status"] == "success") {
          for (String wifiName in result["data"]) {
            state.wifiList.add(wifiName);
          }
          state.wifiList = state.wifiList.toSet().toList();
          state.isScanWifi = false;
          state.isScanWifiFail = false;
          update(["console_wifi_view"]);
        } else if (result["status"] == "fail") {
          state.isScanWifi = false;
          state.isScanWifiFail = true;
          CToast.showToast(result["data"]);
          update(["console_wifi_view"]);
        }
      }
    }, onError: (error) {
      // CToast.showToast("${error.message}");
    });

    await PermissionUtil.location(Get.context!, action: () {
      Future.delayed(const Duration(seconds: 1), () {
        EspBlufiUtil.scanWifi();

        /// 5s后不管有没有数据，都停止动画
        Future.delayed(const Duration(seconds: 5), () {
          state.isScanWifi = false;
          update(["console_wifi_view"]);
        });
      });
    });
  }

  _listener() {
    if (state.wifiName.text.isNotEmpty && state.wifiPwd.text.isNotEmpty) {
      state.isNext = true;
    } else {
      state.isNext = false;
    }
    update(["console_wifi_input"]);
  }

  toInputPage() {
    Get.to(() => const ConsoleWifiInput());
  }

  obscureTextAction() {
    state.isObscureText = !state.isObscureText;
    if (state.isObscureText) {
      state.iconStr = R.eye_on_png;
    } else {
      state.iconStr = R.eye_off_png;
    }
    update(["console_wifi_input"]);
  }

  refreshWifi() {
    state.isScanWifi = true;
    state.wifiList.clear();
    update(["console_wifi_view"]);

    Future.delayed(const Duration(seconds: 1), () {
      EspBlufiUtil.scanWifi();

      /// 5s后不管有没有数据，都停止动画
      Future.delayed(const Duration(seconds: 5), () {
        state.isScanWifi = false;
        update(["console_wifi_view"]);
      });
    });
  }

  wifiNextAction() {
    if (!state.isNext) return;
    Get.back();

    Map arguments = {
      "status": "connect",
      "wifiName": state.wifiName.text,
      "wifiPwd": state.wifiPwd.text
    };

    Get.back(result: arguments);
  }

  wifiConnectedSelectAction(int index) {
    String tempName = state.wifiConnectedList[index];
    String tempPwd = '';
    for (var wifiConfig in ConfigStore.to.wifiConfig) {
      Map temp = jsonDecode(wifiConfig);
      if (temp.keys.first.toString() == tempName) {
        tempPwd = temp[tempName];
      }
    }

    Map arguments = {
      "status": "connect",
      "wifiName": tempName,
      "wifiPwd": tempPwd
    };

    Get.back(result: arguments);
  }

  wifiConnectedDetele(int index) {
    ConfigStore.to.deleteWifiConfig(state.wifiConnectedList[index]);
    state.wifiConnectedList.removeAt(index);
    update(["console_wifi_view"]);
  }

  wifiSelectAction(int index) {
    state.wifiSelectPwd.text = "";
    Get.dialog(
      barrierDismissible: false,
      CommonInputDialog(
        title: "“${state.wifiList[index]}”",
        isInput: true,
        mHeight: 176,
        isShowTwoBtn: true,
        mCancel: "cancel".tr,
        mConfirm: "console_paring_wifi_start_connect".tr,
        pwdController: state.wifiSelectPwd,
        confirmAction: () {
          Map arguments = {
            "status": "connect",
            "wifiName": state.wifiList[index],
            "wifiPwd": state.wifiSelectPwd.text
          };
          Get.back(result: arguments);
        },
      ),
    );
  }

  @override
  void onClose() {
    state.espBlufiResultSubscription?.cancel();
    super.onClose();
  }
}
