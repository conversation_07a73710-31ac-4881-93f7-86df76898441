import 'dart:async';

import 'package:flutter/material.dart';
import 'package:getx_xiaopa/r.dart';

class ConsoleWifiState {
  List<String> wifiList = [];

  ///保存的wifi
  List<String> wifiConnectedList = [];

  TextEditingController wifiSelectPwd = TextEditingController();

  TextEditingController wifiName = TextEditingController();
  TextEditingController wifiPwd = TextEditingController();
  bool isObscureText = true;
  String iconStr = R.eye_on_png;

  /// 输入账号密码是否允许下一步
  bool isNext = false;

  /// 是否正在扫描wifi
  bool isScanWifi = true;

  /// 扫描wifi是否出错
  bool isScanWifiFail = false;

  StreamSubscription? espBlufiResultSubscription;

  Color navColor = Colors.transparent;
  ScrollController sController = ScrollController();

  ConsoleWifiState() {
    ///Initialize variables
  }
}
