import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_slidable/flutter_slidable.dart';
import 'package:get/get.dart';
import 'package:getx_xiaopa/base/base.dart';
import 'package:getx_xiaopa/extension/images.dart';
import 'package:getx_xiaopa/extension/widget_extension.dart';
import 'package:getx_xiaopa/pages/console/console_pairing/widget/fail_bubble.dart';
import 'package:getx_xiaopa/pages/console/console_wifi/widget/wifi_item.dart';
import 'package:getx_xiaopa/r.dart';
import 'package:getx_xiaopa/values/values.dart';
import 'package:loading_animation_widget/loading_animation_widget.dart';

import 'console_wifi_logic.dart';
import 'console_wifi_state.dart';

class ConsoleWifiPage extends BaseCommonView {
  ConsoleWifiPage({super.key});

  final ConsoleWifiLogic logic = Get.put(ConsoleWifiLogic());
  final ConsoleWifiState state = Get.find<ConsoleWifiLogic>().state;

  @override
  bool get customWidget => true;

  @override
  Widget buildContent() {
    return GetBuilder<ConsoleWifiLogic>(
      id: "console_wifi_view",
      builder: (_) {
        return Scaffold(
          backgroundColor: const Color.fromRGBO(249, 249, 249, 1),
          appBar: AppBar(
            backgroundColor: state.navColor,
            centerTitle: true,
            systemOverlayStyle: const SystemUiOverlayStyle(
              statusBarColor: Colors.transparent,
              statusBarIconBrightness: Brightness.dark,
            ),
            elevation: 0,
            scrolledUnderElevation: 0,
            leadingWidth: 35.w,
            leading: Padding(
              padding: EdgeInsets.only(left: 15.w),
              child: Images(
                path: R.back_png,
                width: 20.w,
                height: 20.h,
                scale: 2.8,
              ).inkWell(() => Get.back()),
            ),
          ),
          body: SingleChildScrollView(
            controller: state.sController,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(
                  margin: EdgeInsets.only(top: 15.h, left: 40.w),
                  child: Text(
                    'console_pairing_wifi_title'.tr,
                    style: StyleConfig.otherStyle(
                        color: ColorConfig.searchTextColor,
                        fontSize: 26,
                        fontWeight: FontWeight.w500),
                  ),
                ),
                Container(
                  margin: EdgeInsets.only(top: 16.h, left: 95.w),
                  child: Images(
                    path: R.console_image_05_png,
                    width: 185.w,
                    height: 185.h,
                  ),
                ),
                Container(
                  alignment: Alignment.center,
                  margin: EdgeInsets.only(
                      top: 0.h, bottom: 20.h, left: 60.w, right: 60.w),
                  child: SizedBox(
                    width: 253.w,
                    child: Text(
                      Platform.isIOS
                          ? 'console_paring_wifi_tips_ios'.tr
                          : "console_paring_wifi_tips_android".tr,
                      style: StyleConfig.otherStyle(
                          color: const Color.fromRGBO(180, 142, 78, 1),
                          fontSize: 12),
                    ),
                  ),
                ),
                Container(
                  margin: EdgeInsets.only(top: 10.h, left: 40.w, right: 40.w),
                  child: Text(
                    "console_paring_wifi_kown".tr,
                    style: StyleConfig.otherStyle(
                        color: ColorConfig.searchTextColor,
                        fontSize: 18,
                        fontWeight: FontWeight.w500),
                  ),
                ),
                Container(
                  margin: EdgeInsets.only(left: 20.w, right: 20.w, top: 10.h),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(10.r),
                  ),
                  clipBehavior: Clip.hardEdge,
                  child: Column(
                    children: List.generate(
                      state.wifiConnectedList.length,
                      (index) => Slidable(
                        key: ValueKey(index),
                        endActionPane: ActionPane(
                          motion: const DrawerMotion(),
                          children: [
                            ///做占位使用
                            SlidableAction(
                              flex: 1,
                              onPressed: (context) {},
                              backgroundColor: Colors.transparent,
                              foregroundColor: Colors.transparent,
                              label: '',
                            ),
                            SlidableAction(
                              onPressed: (context) {
                                logic.wifiConnectedDetele(index);
                              },
                              backgroundColor: Colors.red,
                              foregroundColor: Colors.white,
                              icon: Icons.delete,
                              label: "console_paring_wifi_delete".tr,
                            )
                          ],
                        ),
                        child: WifiItem(
                          cIndex: index,
                          wifiName: state.wifiConnectedList[index],
                          isShowLine:
                              index == state.wifiConnectedList.length - 1
                                  ? false
                                  : true,
                          action: (cIndex) =>
                              logic.wifiConnectedSelectAction(cIndex),
                        ),
                      ),
                    ),
                  ),
                ),
                Container(
                  margin: EdgeInsets.only(top: 20.h, left: 40.w, right: 40.w),
                  child: Row(
                    children: [
                      Text(
                        "console_paring_wifi_list".tr,
                        style: StyleConfig.otherStyle(
                            color: ColorConfig.searchTextColor,
                            fontSize: 18,
                            fontWeight: FontWeight.w500),
                      ),
                      const Expanded(child: SizedBox()),
                      state.isScanWifi
                          ? LoadingAnimationWidget.fourRotatingDots(
                              color: const Color.fromRGBO(185, 185, 185, 1),
                              size: 20.sp)
                          : Images(
                              path: R.verify_refresh_png,
                              width: 20.r,
                              height: 20.r,
                            ).inkWell(() => logic.refreshWifi()),
                    ],
                  ),
                ),
                state.isScanWifiFail
                    ? Container(
                        margin: EdgeInsets.only(left: 40.w, right: 42.w),
                        height: 55.h,
                        child: FailBubble(
                          child: Text(
                            "console_paring_wifi_refresh_tips".tr,
                            style: StyleConfig.otherStyle(
                                color: const Color.fromRGBO(249, 56, 56, 1),
                                fontSize: 14),
                          ),
                        ),
                      )
                    : Container(
                        margin:
                            EdgeInsets.only(left: 20.w, right: 20.w, top: 10.h),
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(10.r),
                        ),
                        child: Column(
                          children: List.generate(
                            state.wifiList.length,
                            (index) => WifiItem(
                              cIndex: index,
                              wifiName: state.wifiList[index],
                              isShowLine: index == state.wifiList.length - 1
                                  ? false
                                  : true,
                              action: (cIndex) =>
                                  logic.wifiSelectAction(cIndex),
                            ),
                          ),
                        ),
                      ),
                Container(
                  width: 335.w,
                  height: 60.h,
                  margin: EdgeInsets.only(
                      left: 20.w, right: 20.w, top: 10.h, bottom: 20.h),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(10.r),
                  ),
                  child: Row(
                    children: [
                      SizedBox(width: 31.w),
                      Text(
                        'console_paring_wifi_input'.tr,
                        style: StyleConfig.otherStyle(
                            color: ColorConfig.searchTextColor, fontSize: 14),
                      ),
                      const Expanded(child: SizedBox()),
                      Images(
                        path: R.console_right_png,
                        width: 11.w,
                        height: 20.h,
                      ),
                      SizedBox(width: 20.w)
                    ],
                  ),
                ).inkWell(() => logic.toInputPage()),
              ],
            ),
          ),
        );
      },
    );
  }
}
