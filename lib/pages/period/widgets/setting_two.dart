import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:getx_xiaopa/base/base.dart';
import 'package:getx_xiaopa/extension/widget_extension.dart';
import 'package:getx_xiaopa/pages/period/index.dart';
import 'package:getx_xiaopa/pages/task/widget/infinite_picker.dart';
import 'package:getx_xiaopa/utils/utils.dart';
import 'package:getx_xiaopa/values/values.dart';
import 'package:getx_xiaopa/widget/widget.dart';

class SettingTwo extends BaseCommonView<PeriodController> {
  SettingTwo({super.key});

  @override
  bool? get isHiddenNav => true;

  @override
  Widget buildContent() {
    return ThemeContainerImage(
      fileName: 'period_select_bg.png',
      conWidget: 1.sw,
      conHeight: 1.sh,
      fit: BoxFit.fill,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(height: 132.h),
          Container(
            margin: EdgeInsets.only(left: 20.w, right: 20.w),
            child: Row(
              children: [
                Text(
                  'period_title'.tr,
                  style: StyleConfig.otherStyle(
                      color: ColorConfig.authenticationTextColor,
                      fontSize: 18,
                      fontWeight: FontWeight.w600),
                ),
                const Expanded(child: SizedBox()),
                ThemeText(
                  dataStr:
                      "${controller.sustainSelectDay}${'period_select_day'.tr}",
                  keyName: 'mineOnline',
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                )
              ],
            ),
          ),
          Container(
            margin: EdgeInsets.only(
                left: 20.w, right: 20.w, top: 15.h, bottom: 15.h),
            width: 335.w,
            height: 1.h,
            color: const Color.fromRGBO(233, 232, 233, 1),
          ),
          Container(
            margin: EdgeInsets.only(left: 20.w, right: 20.w),
            child: Text(
              'period_select_two_tips_one'.tr,
              style: StyleConfig.otherStyle(
                  color: ColorConfig.searchTextColor,
                  fontSize: 18,
                  fontWeight: FontWeight.w600),
            ),
          ),
          SizedBox(height: 10.h),
          Container(
            margin: EdgeInsets.only(left: 20.w, right: 25.w),
            child: Text(
              'period_select_two_tips_two'.tr,
              style: StyleConfig.otherStyle(
                  color: ColorConfig.shopDetailTextColor,
                  fontSize: 14,
                  fontWeight: FontWeight.w500),
            ),
          ),
          Container(
            margin: EdgeInsets.only(top: 127.h, left: 32.w, right: 32.w),
            width: 311.w,
            height: 227.h,
            decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(10.r), color: Colors.white),
            child: InfinitePicker<int>(
              data: controller.itemTwoList,
              initialValue: controller.intervalDay,
              itemExtent: 33,
              itemBuilder: (context, item, selected) {
                return Text(
                  '$item  ${selected ? "period_select_day".tr : "    "}',
                  style: StyleConfig.otherStyle(
                      fontSize: 22,
                      color: selected
                          ? Get.find<ThemeColorController>().gTextColor
                          : ColorConfig.shopDetailTextColor,
                      fontWeight: selected ? FontWeight.w600 : FontWeight.w500),
                );
              },
              onSelected: (val) {
                controller.intervalSelectDay = val;
              },
            ),
          ),
          const Expanded(child: SizedBox()),
          Container(
            margin: EdgeInsets.only(
                bottom: ScreenUtil().bottomBarHeight + 20.h,
                left: 20.w,
                right: 20.w),
            child: Row(
              children: [
                ThemeContainer(
                  tWidget: 120.w,
                  tHeight: 48.h,
                  radius: 24.r,
                  child: Center(
                    child: ThemeText(
                      dataStr: "back".tr,
                      keyName: 'mineOnline',
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ).inkWell(() {
                  Get.back();
                }),
                SizedBox(width: 14.w),
                Container(
                  width: 200.w,
                  height: 48.h,
                  decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(24.r),
                      color: const Color.fromRGBO(40, 39, 46, 1)),
                  child: Center(
                    child: ThemeText(
                      dataStr: "confirm".tr,
                      keyName: 'textColor',
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ).inkWell(() => controller.selectTwoAction())
              ],
            ),
          ),
        ],
      ),
    );
  }
}
