import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:getx_xiaopa/storage/storage.dart';
import 'package:getx_xiaopa/values/values.dart';
import 'package:getx_xiaopa/widget/theme_text.dart';
import 'package:table_calendar/table_calendar.dart';

class PeriodCalendar extends StatefulWidget {
  final DateTime focusedDay;
  final List<DateTime> completedDates;
  final Function(DateTime)? onTap;
  final Function(DateTime)? onPageChanged;

  const PeriodCalendar(
      {super.key,
      required this.focusedDay,
      required this.completedDates,
      this.onTap,
      this.onPageChanged});

  @override
  State<PeriodCalendar> createState() => _PeriodCalendarState();
}

class _PeriodCalendarState extends State<PeriodCalendar> {
  CalendarFormat calendarFormat = CalendarFormat.month;
  DateTime? _monthDay;
  DateTime? _selectedDay; // 新增选中的日期变量
  //防抖
  Timer? _debounce;

  @override
  void initState() {
    super.initState();
    _monthDay = widget.focusedDay;
    _selectedDay = DateTime.now(); // 第一次默认选中今天
  }

  @override
  Widget build(BuildContext context) {
    return TableCalendar(
      availableGestures: AvailableGestures.none,
      firstDay: DateTime.utc(2020, 1, 1),
      lastDay: DateTime.utc(2030, 12, 31),
      focusedDay: _monthDay!,
      calendarFormat: calendarFormat,
      rowHeight: 40.h,
      daysOfWeekHeight: 30.h,
      locale: ConfigStore.to.locale.toString(),
      headerStyle: HeaderStyle(
          formatButtonVisible: false,
          titleCentered: true,
          leftChevronMargin: EdgeInsets.symmetric(horizontal: 0.w),
          rightChevronMargin: EdgeInsets.symmetric(horizontal: 0.w),
          leftChevronIcon: Container(
            width: 20.r,
            height: 20.r,
            decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(20.r),
                color: const Color.fromRGBO(242, 241, 245, 1)),
            child: Center(
              child: Icon(
                size: 20.r,
                Icons.chevron_left,
                color: const Color.fromRGBO(185, 185, 185, 1),
              ),
            ),
          ),
          rightChevronIcon: Container(
            width: 20.r,
            height: 20.r,
            decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(20.r),
                color: const Color.fromRGBO(242, 241, 245, 1)),
            child: Center(
              child: Icon(
                size: 20.r,
                Icons.chevron_right,
                color: const Color.fromRGBO(185, 185, 185, 1),
              ),
            ),
          ),
          headerPadding: EdgeInsets.zero,
          titleTextStyle: StyleConfig.otherStyle(
              color: ColorConfig.searchTextColor,
              fontWeight: FontWeight.w400,
              fontSize: 18)),
      onDaySelected: (sDate, eDate) {
        setState(() {
          _selectedDay = sDate; // 更新选中日期
          _monthDay = sDate;
        });
        widget.onTap?.call(sDate);
      },
      selectedDayPredicate: (day) =>
          _isSameDay(day, _selectedDay ?? DateTime.now()),
      onPageChanged: (focusedDay) {
        if (calendarFormat == CalendarFormat.month) {
          _debounce?.cancel();
          _debounce = Timer(const Duration(milliseconds: 300), () {
            setState(() {
              _monthDay = focusedDay;
            });
            // 这里就是最后一次触发
            widget.onPageChanged?.call(focusedDay);
          });
        }
      },
      calendarStyle: const CalendarStyle(
        outsideDaysVisible: false,
        isTodayHighlighted: false,
      ),
      calendarBuilders: CalendarBuilders(
        dowBuilder: (context, day) {
          final text = [
            'diary_week_seven'.tr,
            'diary_week_one'.tr,
            'diary_week_two'.tr,
            'diary_week_three'.tr,
            'diary_week_four'.tr,
            'diary_week_five'.tr,
            'diary_week_six'.tr
          ][day.weekday % 7];
          return Center(
            child: Text(
              text,
              style: StyleConfig.otherStyle(
                  color: ColorConfig.authenticationTextColor, fontSize: 14),
            ),
          );
        },

        ///设置不是本月的样式
        outsideBuilder: (context, date, _) => _buildDayCell(date),
        defaultBuilder: (context, date, _) => _buildDayCell(date),
        todayBuilder: (context, date, _) => _buildDayCell(date),
        selectedBuilder: (context, date, _) => _buildDayCell(date),
      ),
    );
  }

  bool _isSameDay(DateTime a, DateTime b) {
    return a.year == b.year && a.month == b.month && a.day == b.day;
  }

  Widget _buildDayCell(DateTime date) {
    final bool isToday =
        _selectedDay != null && _isSameDay(date, _selectedDay!);

    return Column(
      children: [
        Container(
          margin: EdgeInsets.only(top: 3.h, bottom: 3.h),
          padding: EdgeInsets.all(3.r),
          decoration: isToday
              ? const BoxDecoration(
                  color: Color.fromRGBO(40, 39, 46, 1),
                  // borderRadius: radius,
                  shape: BoxShape.circle)
              : const BoxDecoration(
                  color: Color.fromRGBO(249, 249, 249, 1),
                  // borderRadius: radius,
                  shape: BoxShape.circle),
          child: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                ThemeText(
                  dataStr: '${date.day}',
                  keyName: 'textColor',
                  flag: isToday,
                  subColor: ColorConfig.shopDetailTextColor,
                  fontWeight:
                      isToday || isToday ? FontWeight.w500 : FontWeight.w400,
                  fontSize: 12,
                )
              ],
            ),
          ),
        ),
      ],
    );
  }
}
