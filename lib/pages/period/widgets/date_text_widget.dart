import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:getx_xiaopa/storage/storage.dart';
import 'package:getx_xiaopa/utils/utils.dart';
import 'package:getx_xiaopa/values/values.dart';
import 'package:intl/intl.dart';

class DateTextWidget extends StatefulWidget {
  final String tip;
  final int day;
  final bool isTime;
  final DateTime? lastDate;

  const DateTextWidget(
      {super.key,
      required this.tip,
      this.day = 0,
      this.isTime = false,
      this.lastDate});

  @override
  State<DateTextWidget> createState() => _DateTextWidgetState();
}

class _DateTextWidgetState extends State<DateTextWidget> {
  String formattedDate = '';

  @override
  void initState() {
    super.initState();
    if (widget.isTime) {
      // 使用 intl 格式化日期，适配多语言
      formattedDate = DateFormat.yMMMMd(ConfigStore.to.locale.toString())
          .format(widget.lastDate!);
      if (ConfigStore.to.getLocaleCode() == "zh") {
        formattedDate =
            DateFormat('yyyy-MM-dd', 'zh_CN').format(widget.lastDate!);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return RichText(
      text: TextSpan(
        style: StyleConfig.otherStyle(
            color: ColorConfig.authenticationTextColor,
            fontWeight: FontWeight.w600),
        children: [
          TextSpan(text: widget.tip), // 👉 这里换成多语言文案
          if (widget.isTime) ...{
            TextSpan(
              text: formattedDate,
              style: StyleConfig.otherStyle(
                color: Get.find<ThemeColorController>().gMineOnline,
                fontSize: 20,
                fontWeight: FontWeight.w600,
              ),
            )
          },
          if (!widget.isTime) ...{
            TextSpan(
              text: '${widget.day}',
              style: StyleConfig.otherStyle(
                color: Get.find<ThemeColorController>().gMineOnline,
                fontSize: 20,
                fontWeight: FontWeight.w600,
              ),
            ),
            const TextSpan(text: " 天"),
          }
        ],
      ),
    );
  }
}
