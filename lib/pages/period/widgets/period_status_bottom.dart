import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:getx_xiaopa/extension/images.dart';
import 'package:getx_xiaopa/extension/widget_extension.dart';
import 'package:getx_xiaopa/r.dart';
import 'package:getx_xiaopa/values/values.dart';

class PeriodStatusBottom extends StatefulWidget {
  final Function(String)? onTap;
  const PeriodStatusBottom({super.key, this.onTap});

  @override
  State<PeriodStatusBottom> createState() => _PeriodStatusBottomState();
}

class _PeriodStatusBottomState extends State<PeriodStatusBottom> {
  String _color = '浅红色';
  String _flux = '极少';
  String _pain = '完全不痛';
  final List<String> _colorStr = ['浅红色', '鲜红色', '深红色', '褐色', '黑色'];
  final List<String> _fluxStr = ['极少', '少', '中等', '多', '极多'];
  final List<String> _painStr = ['完全不痛', '轻微痛', '比较痛', '非常痛', '痛到极致'];
  final List<StatusType> _colorList = [];
  final List<StatusType> _fluxList = [];
  final List<StatusType> _painList = [];

  @override
  void initState() {
    super.initState();
    for (var i = 0; i < _colorStr.length; i++) {
      StatusType type = StatusType();
      type.image = 'assets/images/period_status_color_0${i + 1}.png';
      type.text = _colorStr[i];
      if (i == 0) {
        type.isSelect = true;
      }
      _colorList.add(type);
    }

    for (var i = 0; i < _fluxStr.length; i++) {
      StatusType type = StatusType();
      type.image = 'assets/images/period_status_flux_0${i + 1}.png';
      type.text = _fluxStr[i];
      if (i == 0) {
        type.isSelect = true;
      }
      _fluxList.add(type);
    }

    for (var i = 0; i < _painStr.length; i++) {
      StatusType type = StatusType();
      type.image = 'assets/images/period_status_pain_0${i + 1}.png';
      type.text = _painStr[i];
      if (i == 0) {
        type.isSelect = true;
      }
      _painList.add(type);
    }
  }

  _colorSelectAction(index) {
    for (var e in _colorList) {
      e.isSelect = false;
    }
    _color = _colorList[index].text;
    setState(() {
      _colorList[index].isSelect = true;
    });
  }

  _fluxSelectAction(index) {
    for (var e in _fluxList) {
      e.isSelect = false;
    }
    _flux = _fluxList[index].text;
    setState(() {
      _fluxList[index].isSelect = true;
    });
  }

  _painSelectAction(index) {
    for (var e in _painList) {
      e.isSelect = false;
    }
    _pain = _painList[index].text;
    setState(() {
      _painList[index].isSelect = true;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.only(left: 15.w, right: 15.w),
      width: 1.sw,
      height: 533.h,
      decoration: BoxDecoration(
          borderRadius: BorderRadius.only(
              topLeft: Radius.circular(20.r), topRight: Radius.circular(20.r)),
          color: const Color.fromRGBO(249, 249, 249, 1)),
      child: Column(
        children: [
          SizedBox(height: 20.h),
          Row(
            children: [
              const Expanded(child: SizedBox()),
              SizedBox(width: 28.w),
              Text(
                '小花情况',
                style: StyleConfig.otherStyle(
                    color: ColorConfig.searchTextColor,
                    fontSize: 20,
                    fontWeight: FontWeight.w600),
              ),
              const Expanded(child: SizedBox()),
              Text(
                '确定',
                style: StyleConfig.otherStyle(
                    color: ColorConfig.searchTextColor,
                    fontSize: 14,
                    fontWeight: FontWeight.w500),
              ).inkWell(() {
                String temp = '$_color $_flux $_pain';
                widget.onTap?.call(temp);
                Get.back();
              }),
              SizedBox(width: 2.w)
            ],
          ),
          SizedBox(height: 20.h),
          Container(
            padding: EdgeInsets.only(left: 15.w, top: 12.h),
            width: 345.w,
            height: 135.h,
            decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(8.r),
                color: Colors.white,
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.05),
                    // 阴影颜色和透明度
                    spreadRadius: 0,
                    // 阴影扩散范围
                    blurRadius: 10,
                    // 阴影模糊程度
                    offset: const Offset(0, 2),
                    // 阴影偏移量（水平，垂直）
                  )
                ]),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(
                  margin: EdgeInsets.only(left: 2.w),
                  child: Text(
                    '颜色',
                    style: StyleConfig.otherStyle(
                        color: ColorConfig.searchTextColor,
                        fontWeight: FontWeight.w600),
                  ),
                ),
                SizedBox(height: 12.h),
                SizedBox(
                  height: 73.h,
                  child: ListView(
                    scrollDirection: Axis.horizontal,
                    children: List.generate(_colorList.length, (index) {
                      return Container(
                        margin: EdgeInsets.only(left: index > 0 ? 16.w : 0),
                        child: Column(
                          children: [
                            SizedBox(
                              width: 50.r,
                              height: 50.r,
                              child: Stack(
                                clipBehavior: Clip.none,
                                children: [
                                  Images(
                                    path: _colorList[index].image,
                                    width: 50.r,
                                    height: 50.r,
                                  ),
                                  Positioned(
                                    bottom: -4,
                                    right: -2,
                                    child: _colorList[index].isSelect
                                        ? Images(
                                            path: R.mine_agree_png,
                                            width: 18.r,
                                            height: 18.r,
                                          )
                                        : const SizedBox(),
                                  )
                                ],
                              ),
                            ).inkWell(() => _colorSelectAction(index)),
                            SizedBox(height: 6.h),
                            Text(
                              _colorList[index].text,
                              style: StyleConfig.otherStyle(
                                  color: ColorConfig.searchTextColor,
                                  fontSize: 12,
                                  fontWeight: FontWeight.w500),
                            )
                          ],
                        ),
                      );
                    }),
                  ),
                )
              ],
            ),
          ),
          SizedBox(height: 10.h),
          Container(
            padding: EdgeInsets.only(left: 15.w, top: 12.h),
            width: 345.w,
            height: 135.h,
            decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(8.r),
                color: Colors.white,
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.05),
                    // 阴影颜色和透明度
                    spreadRadius: 0,
                    // 阴影扩散范围
                    blurRadius: 10,
                    // 阴影模糊程度
                    offset: const Offset(0, 2),
                    // 阴影偏移量（水平，垂直）
                  )
                ]),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(
                  margin: EdgeInsets.only(left: 2.w),
                  child: Text(
                    '流量',
                    style: StyleConfig.otherStyle(
                        color: ColorConfig.searchTextColor,
                        fontWeight: FontWeight.w600),
                  ),
                ),
                SizedBox(height: 12.h),
                SizedBox(
                  height: 73.h,
                  child: ListView(
                    scrollDirection: Axis.horizontal,
                    children: List.generate(_fluxList.length, (index) {
                      return Container(
                        margin: EdgeInsets.only(left: index > 0 ? 16.w : 0),
                        child: Column(
                          children: [
                            SizedBox(
                              width: 50.r,
                              height: 50.r,
                              child: Stack(
                                clipBehavior: Clip.none,
                                children: [
                                  Images(
                                    path: _fluxList[index].image,
                                    width: 50.r,
                                    height: 50.r,
                                  ),
                                  Positioned(
                                    bottom: -4,
                                    right: -2,
                                    child: _fluxList[index].isSelect
                                        ? Images(
                                            path: R.mine_agree_png,
                                            width: 18.r,
                                            height: 18.r,
                                          )
                                        : const SizedBox(),
                                  )
                                ],
                              ),
                            ).inkWell(() => _fluxSelectAction(index)),
                            SizedBox(height: 6.h),
                            Text(
                              _fluxList[index].text,
                              style: StyleConfig.otherStyle(
                                  color: ColorConfig.searchTextColor,
                                  fontSize: 12,
                                  fontWeight: FontWeight.w500),
                            )
                          ],
                        ),
                      );
                    }),
                  ),
                )
              ],
            ),
          ),
          SizedBox(height: 10.h),
          Container(
            padding: EdgeInsets.only(left: 15.w, top: 12.h),
            width: 345.w,
            height: 135.h,
            decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(8.r),
                color: Colors.white,
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.05),
                    // 阴影颜色和透明度
                    spreadRadius: 0,
                    // 阴影扩散范围
                    blurRadius: 10,
                    // 阴影模糊程度
                    offset: const Offset(0, 2),
                    // 阴影偏移量（水平，垂直）
                  )
                ]),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(
                  margin: EdgeInsets.only(left: 2.w),
                  child: Text(
                    '痛感',
                    style: StyleConfig.otherStyle(
                        color: ColorConfig.searchTextColor,
                        fontWeight: FontWeight.w600),
                  ),
                ),
                SizedBox(height: 12.h),
                SizedBox(
                  height: 73.h,
                  child: ListView(
                    scrollDirection: Axis.horizontal,
                    children: List.generate(_painList.length, (index) {
                      return Container(
                        margin: EdgeInsets.only(left: index > 0 ? 16.w : 0),
                        child: Column(
                          children: [
                            SizedBox(
                              width: 50.r,
                              height: 50.r,
                              child: Stack(
                                clipBehavior: Clip.none,
                                children: [
                                  Images(
                                    path: _painList[index].image,
                                    width: 50.r,
                                    height: 50.r,
                                  ),
                                  Positioned(
                                    bottom: -4,
                                    right: -2,
                                    child: _painList[index].isSelect
                                        ? Images(
                                            path: R.mine_agree_png,
                                            width: 18.r,
                                            height: 18.r,
                                          )
                                        : const SizedBox(),
                                  )
                                ],
                              ),
                            ).inkWell(() => _painSelectAction(index)),
                            SizedBox(height: 6.h),
                            Text(
                              _painList[index].text,
                              style: StyleConfig.otherStyle(
                                  color: ColorConfig.searchTextColor,
                                  fontSize: 12,
                                  fontWeight: FontWeight.w500),
                            )
                          ],
                        ),
                      );
                    }),
                  ),
                )
              ],
            ),
          )
        ],
      ),
    );
  }
}

class StatusType {
  String image;
  String text;
  bool isSelect;

  StatusType({this.image = '', this.text = '', this.isSelect = false});
}
