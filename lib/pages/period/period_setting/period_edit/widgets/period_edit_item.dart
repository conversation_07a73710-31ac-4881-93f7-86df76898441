import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:getx_xiaopa/extension/images.dart';
import 'package:getx_xiaopa/r.dart';
import 'package:getx_xiaopa/values/values.dart';

class PeriodEditItem extends StatelessWidget {
  final DateTime date;
  final bool inRange;

  const PeriodEditItem({
    super.key,
    required this.date,
    this.inRange = false,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.only(top: 2.h),
      child: Column(
        children: [
          Text(
            "${date.day}",
            style: StyleConfig.otherStyle(
                color: inRange
                    ? const Color.fromRGBO(254, 139, 172, 1)
                    : ColorConfig.shopDetailTextColor,
                fontWeight: FontWeight.w500),
          ),
          Images(
            path: inRange ? R.period_edit_s_image_png : R.period_edit_image_png,
            width: 22.w,
            height: 16.h,
          )
        ],
      ),
    );
  }
}
