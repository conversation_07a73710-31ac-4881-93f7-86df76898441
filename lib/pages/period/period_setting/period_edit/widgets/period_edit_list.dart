import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:getx_xiaopa/pages/period/period_setting/period_edit/index.dart';
import 'package:getx_xiaopa/storage/storage.dart';
import 'package:getx_xiaopa/values/values.dart';
import 'package:intl/intl.dart';

class PeriodEditList extends StatefulWidget {
  final int initialMonthCount; // 初始加载的月份数
  final int preloadCount; // 每次往前/后扩展的数量
  final DateTime initialDate; // 初始定位到的日期

  PeriodEditList({
    super.key,
    this.initialMonthCount = 24,
    this.preloadCount = 12,
    DateTime? initialDate,
  }) : initialDate = initialDate ?? DateTime.now();

  @override
  State<PeriodEditList> createState() => _PeriodEditListState();
}

class _PeriodEditListState extends State<PeriodEditList>
    with AutomaticKeepAliveClientMixin {
  late List<DateTime> _months; // 月份列表
  final ScrollController _controller = ScrollController();
  late int _centerIndex;

  @override
  bool get wantKeepAlive => true;

  @override
  void initState() {
    super.initState();

    // 初始化 month 列表，前后各 half
    final half = widget.initialMonthCount ~/ 2;
    _months = List.generate(
      widget.initialMonthCount,
      (i) => DateTime(
          widget.initialDate.year, widget.initialDate.month - half + i, 1),
    );
    _centerIndex = half;

    // 首帧渲染后滚动到当前月（两阶段，无动画）
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _jumpToMonthIndex(_centerIndex);
      // 再次在下一帧修正一次，降低首帧测量误差
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted) _jumpToMonthIndex(_centerIndex);
      });
    });
  }

  // 估算 index 之前的偏移量（使用已缓存高度，否则用平均值）
  double _estimatedOffsetForIndex(int index) {
    double offset = 0.0;
    for (int i = 0; i < index; i++) {
      final id = _monthId(_months[i]);
      offset += _monthHeights[id] ?? _avgMonthHeight;
    }
    return offset;
  }

  // 无动画跳转到某个索引对应的位置
  void _jumpToMonthIndex(int index) {
    if (!_controller.hasClients) return;
    final offset = _estimatedOffsetForIndex(index);
    final clamped = offset.clamp(0.0, _controller.position.maxScrollExtent);
    _controller.jumpTo(clamped);
  }

  final Map<String, GlobalKey> _itemKeys = {};

  final Map<String, double> _monthHeights = {}; // 缓存月份高度
  bool _isLoadingBefore = false;

  // 获取月份ID
  String _monthId(DateTime month) => '${month.year}-${month.month}';

  // 计算平均高度
  double get _avgMonthHeight {
    if (_monthHeights.isEmpty) return 400.0; // 默认估算高度
    final sum = _monthHeights.values.fold(0.0, (a, b) => a + b);
    return sum / _monthHeights.length;
  }

  void _loadMoreBefore() {
    if (_isLoadingBefore) return;
    _isLoadingBefore = true;

    final first = _months.first;
    final newMonths = List.generate(
      widget.preloadCount,
      (i) => DateTime(first.year, first.month - (widget.preloadCount - i), 1),
    );

    // 预建 key，避免索引偏移导致测量错乱
    for (var m in newMonths) {
      final id = _monthId(m);
      _itemKeys.putIfAbsent(id, () => GlobalKey());
    }

    // 估算插入的总高度
    double estimatedInsertedHeight = 0.0;
    for (var month in newMonths) {
      final id = _monthId(month);
      estimatedInsertedHeight += _monthHeights[id] ?? _avgMonthHeight;
    }

    final double oldOffset = _controller.offset;

    setState(() {
      _months.insertAll(0, newMonths);
      _centerIndex += newMonths.length;
    });

    // 立即补偿滚动位置
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (!mounted) return;

      try {
        final target = (oldOffset + estimatedInsertedHeight)
            .clamp(0.0, _controller.position.maxScrollExtent);
        _controller.jumpTo(target);

        // 测量实际高度并修正
        _measureAndCorrectHeight(newMonths, estimatedInsertedHeight);
      } catch (e) {
        // 忽略异常
      }

      _isLoadingBefore = false;
    });
  }

  // 测量实际高度并修正位置
  void _measureAndCorrectHeight(
      List<DateTime> newMonths, double estimatedHeight) {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (!mounted) return;

      double actualInsertedHeight = 0.0;
      for (int i = 0; i < newMonths.length; i++) {
        final id = _monthId(newMonths[i]);
        final key = _itemKeys[id];
        final box = key?.currentContext?.findRenderObject() as RenderBox?;
        if (box != null) {
          final height = box.size.height;
          _monthHeights[id] = height;
          actualInsertedHeight += height;
        } else {
          actualInsertedHeight += _avgMonthHeight;
        }
      }

      final diff = actualInsertedHeight - estimatedHeight;
      if (diff.abs() > 1.0) {
        try {
          final corrected = (_controller.offset + diff)
              .clamp(0.0, _controller.position.maxScrollExtent);
          _controller.jumpTo(corrected);
        } catch (e) {
          // 忽略异常
        }
      }
    });
  }

  void _loadMoreAfter() {
    final last = _months.last;
    final newMonths = List.generate(
      widget.preloadCount,
      (i) => DateTime(last.year, last.month + i + 1, 1),
    );

    // 预建 key
    for (var m in newMonths) {
      final id = _monthId(m);
      _itemKeys.putIfAbsent(id, () => GlobalKey());
    }

    setState(() {
      _months.addAll(newMonths);
    });
  }

  /// 返回一个月的所有格子（含空格）
  List<DateTime?> _daysInMonth(DateTime month) {
    final firstDay = DateTime(month.year, month.month, 1);
    final daysInMonth = DateTime(month.year, month.month + 1, 0).day;
    final firstWeekday = firstDay.weekday % 7; // 周日=0
    final totalCells = ((firstWeekday + daysInMonth) <= 35) ? 35 : 42;

    List<DateTime?> result = List<DateTime?>.filled(totalCells, null);
    for (int i = 0; i < daysInMonth; i++) {
      result[firstWeekday + i] = DateTime(month.year, month.month, i + 1);
    }
    return result;
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return NotificationListener<ScrollNotification>(
      onNotification: (n) {
        if (n is ScrollUpdateNotification && !_isLoadingBefore) {
          if (_controller.offset <= 300) {
            _loadMoreBefore();
          }
          if (_controller.offset >=
              _controller.position.maxScrollExtent - 200) {
            _loadMoreAfter();
          }
        }
        return false;
      },
      child: ListView.builder(
        controller: _controller,
        itemCount: _months.length,
        itemBuilder: (context, index) {
          final date = _months[index];
          final id = _monthId(date);
          final key = _itemKeys.putIfAbsent(id, () => GlobalKey());
          final days = _daysInMonth(date);

          // 在下一帧缓存高度（使用 monthId 作为稳定 key）
          WidgetsBinding.instance.addPostFrameCallback((_) {
            final box = key.currentContext?.findRenderObject() as RenderBox?;
            if (box != null) {
              _monthHeights[id] = box.size.height;
            }
          });

          return Container(
            key: key,
            child: Container(
              padding: const EdgeInsets.all(0),
              child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Container(
                      margin:
                          EdgeInsets.only(top: 16.h, bottom: 18.h, left: 20.w),
                      child: Text(
                        DateFormat.yMMMM(ConfigStore.to.locale.toString())
                            .format(date),
                        style: StyleConfig.otherStyle(
                            color: ColorConfig.searchTextColor,
                            fontSize: 18,
                            fontWeight: FontWeight.w600),
                      ),
                    ),
                    Container(
                      margin: EdgeInsets.only(left: 20.w, right: 20.w),
                      child: GridView.builder(
                        padding: EdgeInsets.zero,
                        physics: const NeverScrollableScrollPhysics(),
                        shrinkWrap: true,
                        gridDelegate:
                            const SliverGridDelegateWithFixedCrossAxisCount(
                          crossAxisCount: 7,
                          childAspectRatio: 1, // 宽高比 1:1
                        ),
                        itemCount: days.length,
                        itemBuilder: (context, i) {
                          final day = days[i];
                          if (day == null) return const SizedBox();
                          // 示例：任务区间 9月2号 ~ 9月10号
                          final taskStart = DateTime(2025, 9, 2);
                          final taskEnd = DateTime(2025, 9, 8);

                          final inRange =
                              !day.isBefore(taskStart) && !day.isAfter(taskEnd);

                          return PeriodEditItem(
                            date: day,
                            inRange: inRange,
                          );
                        },
                      ),
                    )
                  ]),
            ),
          );
        },
      ),
    );
  }
}
