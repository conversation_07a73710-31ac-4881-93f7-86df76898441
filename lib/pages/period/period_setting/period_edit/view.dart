import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:getx_xiaopa/base/base.dart';
import 'package:getx_xiaopa/extension/images.dart';
import 'package:getx_xiaopa/extension/widget_extension.dart';
import 'package:getx_xiaopa/r.dart';
import 'package:getx_xiaopa/values/values.dart';
import 'package:getx_xiaopa/widget/widget.dart';

import 'index.dart';

class PeriodEditPage extends BaseCommonView<PeriodEditController> {
  PeriodEditPage({super.key});

  @override
  String? get navTitle => "period_three_widget_two".tr;

  @override
  TextStyle? get navTitleStyle =>
      StyleConfig.blackStyle(fontSize: 18, fontWeight: FontWeight.w600);

  @override
  Color? get navColor => Colors.transparent;

  @override
  double? get leftWidth => 35.w;

  @override
  Widget? get leftButton => Padding(
        padding: EdgeInsets.only(left: 15.w),
        child: Images(
          path: R.back_png,
          width: 20.w,
          height: 20.h,
          scale: 2.8,
        ).inkWell(() => Get.back()),
      );

  @override
  Color? get contentColor => const Color.fromRGBO(249, 249, 249, 1);

  // 主视图
  Widget _buildView() {
    return Container(
      margin: EdgeInsets.only(top: 10.h, left: 15.w, right: 15.w),
      width: 345.w,
      height: 1.sh,
      decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(10.r), color: Colors.white),
      child: Stack(
        children: [
          Positioned.fill(
            child: PeriodEditList(),
          ),
          Positioned(
              left: 5.w,
              bottom: 44.h,
              child: Row(
                children: [
                  Container(
                    width: 120.w,
                    height: 48.h,
                    decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(24.r),
                        color: const Color.fromRGBO(242, 241, 245, 1)),
                    child: Center(
                      child: Text(
                        'cancel'.tr,
                        style: StyleConfig.otherStyle(
                          color: ColorConfig.authenticationTextColor,
                        ),
                      ),
                    ),
                  ).inkWell(() => Get.back()),
                  SizedBox(width: 14.w),
                  Container(
                    width: 200.w,
                    height: 48.h,
                    decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(24.r),
                        color: const Color.fromRGBO(40, 39, 46, 1)),
                    child: Center(
                      child: ThemeText(
                        dataStr: "console_role_save".tr,
                        keyName: 'textColor',
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ],
              ))
        ],
      ),
    );
  }

  @override
  Widget buildContent() {
    return GetBuilder<PeriodEditController>(
      init: PeriodEditController(),
      id: "period_edit",
      builder: (_) {
        return _buildView();
      },
    );
  }
}
