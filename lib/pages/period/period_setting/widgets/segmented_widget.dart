import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:getx_xiaopa/extension/widget_extension.dart';
import 'package:getx_xiaopa/utils/utils.dart';
import 'package:getx_xiaopa/widget/widget.dart';

class SegmentedWidget extends StatefulWidget {
  final Function(int) onSegmentAction;
  const SegmentedWidget({super.key, required this.onSegmentAction});

  @override
  State<SegmentedWidget> createState() => _SegmentedWidgetState();
}

class _SegmentedWidgetState extends State<SegmentedWidget> {
  bool isMonth = true;

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 200.w,
      height: 36.h,
      decoration: BoxDecoration(
        color: Get.find<ThemeColorController>().gTextColor,
        borderRadius: BorderRadius.circular(18.r),
      ),
      child: Stack(
        children: [
          // 选中的滑块
          AnimatedAlign(
            duration: const Duration(milliseconds: 300),
            curve: Curves.easeInOut,
            alignment: isMonth ? Alignment.centerLeft : Alignment.centerRight,
            child: Container(
              width: 100.w,
              decoration: BoxDecoration(
                color: const Color.fromRGBO(40, 39, 46, 1),
                borderRadius: BorderRadius.circular(18.r),
              ),
            ),
          ),
          // 文字按钮
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              SizedBox(
                width: 100.w,
                child: Center(
                  child: ThemeText(
                    dataStr: "diary_date_month".tr,
                    keyName: 'textColor',
                    flag: isMonth,
                    subColor: Get.find<ThemeColorController>().gMineOnline,
                  ),
                ),
              ).inkWell(() {
                setState(() => isMonth = true);
                Future.delayed(const Duration(milliseconds: 300), () {
                  widget.onSegmentAction(1);
                });
              }),
              SizedBox(
                width: 100.w,
                child: Center(
                  child: ThemeText(
                    dataStr: "diary_date_year".tr,
                    keyName: 'textColor',
                    flag: !isMonth,
                    subColor: Get.find<ThemeColorController>().gMineOnline,
                  ),
                ),
              ).inkWell(() {
                setState(() => isMonth = false);
                Future.delayed(const Duration(milliseconds: 300), () {
                  widget.onSegmentAction(2);
                });
              }),
            ],
          ),
        ],
      ),
    );
  }
}
