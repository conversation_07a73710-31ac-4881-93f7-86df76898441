import 'package:flutter/material.dart';
import 'package:getx_xiaopa/pages/period/period_setting/index.dart';
import 'package:getx_xiaopa/storage/storage.dart';
import 'package:getx_xiaopa/values/values.dart';
import 'package:intl/intl.dart';
import 'package:scrollable_positioned_list/scrollable_positioned_list.dart';

class CalendarYearList extends StatefulWidget {
  const CalendarYearList({super.key});

  @override
  State<CalendarYearList> createState() => _CalendarYearListState();
}

class _CalendarYearListState extends State<CalendarYearList> {
  final int baseYear = 1970; // 理论起点年份

  @override
  void initState() {
    super.initState();
  }

  List<DateTime?> _daysInMonth(DateTime month) {
    final firstDay = DateTime(month.year, month.month, 1);
    final daysInMonth = DateTime(month.year, month.month + 1, 0).day;
    final firstWeekday = firstDay.weekday % 7;
    final totalCells = ((firstWeekday + daysInMonth) <= 35) ? 35 : 42;

    List<DateTime?> result = List<DateTime?>.filled(totalCells, null);
    for (int i = 0; i < daysInMonth; i++) {
      result[firstWeekday + i] = DateTime(month.year, month.month, i + 1);
    }
    return result;
  }

  @override
  Widget build(BuildContext context) {
    final currentYear = DateTime.now().year;
    final initialIndex = currentYear - baseYear;

    return ScrollablePositionedList.builder(
      itemCount: 99999,
      initialScrollIndex: initialIndex,
      
      itemBuilder: (context, index) {
        final year = baseYear + index;
        final months = List.generate(12, (i) => DateTime(year, i + 1, 1));
        return Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            // 年份
            Padding(
              padding: const EdgeInsets.all(8),
              child: Text(
                "$year",
                style: StyleConfig.otherStyle(
                    color: ColorConfig.searchTextColor,
                    fontSize: 18,
                    fontWeight: FontWeight.w700),
              ),
            ),
            // 12 个月份网格，每行 3 个
            GridView.builder(
              padding: EdgeInsets.zero,
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: months.length,
              gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 3,
                crossAxisSpacing: 8,
                mainAxisSpacing: 8,
                childAspectRatio: 0.8,
              ),
              itemBuilder: (context, monthIndex) {
                final month = months[monthIndex];
                final days = _daysInMonth(month);

                return Container(
                  padding: const EdgeInsets.all(4),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(6),
                  ),
                  child: Column(
                    children: [
                      Text(
                        DateFormat.MMM(ConfigStore.to.locale.toString())
                            .format(month),
                        style: StyleConfig.otherStyle(
                            color: ColorConfig.searchTextColor, fontSize: 18),
                      ),
                      Expanded(
                        child: GridView.builder(
                          padding: EdgeInsets.zero,
                          physics: const NeverScrollableScrollPhysics(),
                          shrinkWrap: true,
                          gridDelegate:
                              const SliverGridDelegateWithFixedCrossAxisCount(
                                  crossAxisCount: 7,
                                  childAspectRatio: 1,
                                  mainAxisExtent: 18),
                          itemCount: days.length,
                          itemBuilder: (context, i) {
                            final day = days[i];
                            if (day == null) return const SizedBox();
                            // 示例：任务区间 9月2号 ~ 9月10号
                            final taskStart = DateTime(2025, 9, 2);
                            final taskEnd = DateTime(2025, 9, 8);

                            final inRange = !day.isBefore(taskStart) &&
                                !day.isAfter(taskEnd);

                            final isStart = day.isAtSameMomentAs(taskStart);
                            final isEnd = day.isAtSameMomentAs(taskEnd);

                            final Color tColor = inRange
                                ? Colors.white
                                : ColorConfig.searchTextColor;

                            return CalendarYearItem(
                              date: day,
                              inRange: inRange,
                              isStart: isStart,
                              isEnd: isEnd,
                              textColor: tColor,
                            );
                          },
                        ),
                      ),
                    ],
                  ),
                );
              },
            ),
          ],
        );
      },
    );
  }
}
