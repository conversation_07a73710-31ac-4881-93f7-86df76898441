import 'dart:math';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:getx_xiaopa/values/values.dart';

class CalendarMonthItem extends StatelessWidget {
  final DateTime date;
  final bool inRange;
  final bool isStart;
  final bool isEnd;
  final Color textColor;
  final bool isOvulation;

  const CalendarMonthItem(
      {super.key,
      required this.date,
      this.inRange = false,
      this.isStart = false,
      this.isEnd = false,
      this.textColor = Colors.black,
      this.isOvulation = false});

  @override
  Widget build(BuildContext context) {
    var radius = const Radius.circular(100);

    return isOvulation
        ? FlowerCalendarMonthItem(date: date, textColor: textColor)
        : Container(
            margin: EdgeInsets.only(top: 2.h),
            decoration: BoxDecoration(
              color: inRange ? const Color.fromRGBO(254, 139, 172, 1) : null,
              // 只按“是否起点/终点”决定左右半圆，跟周几无关
              borderRadius: BorderRadius.horizontal(
                left: isStart ? radius : Radius.zero,
                right: isEnd ? radius : Radius.zero,
              ),
            ),
            child: Center(
              child: Text(
                "${date.day}",
                style: StyleConfig.otherStyle(
                    color: textColor, fontWeight: FontWeight.w500),
              ),
            ),
          );
  }
}

class FlowerCalendarMonthItem extends StatelessWidget {
  final DateTime date;
  final Color textColor;

  const FlowerCalendarMonthItem(
      {super.key, required this.date, this.textColor = Colors.black});

  @override
  Widget build(BuildContext context) {
    return CustomPaint(
      painter: FlowerPainter(),
      child: Center(
        child: Text(
          "${date.day}",
          style: StyleConfig.otherStyle(
              color: textColor, fontWeight: FontWeight.w500),
        ),
      ),
    );
  }
}

class FlowerPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()..color = const Color.fromRGBO(172, 127, 255, 1);

    final center = Offset(size.width / 2, size.height / 2);
    final radius = size.width / 5;

    // 把旋转角度从度转成弧度
    const rotationRadian = 55 * 3.14159 / 180;

    // 画 5 个花瓣
    for (int i = 0; i < 5; i++) {
      final angle = (72 * i) * 3.14159 / 180 + rotationRadian;
      final dx = center.dx + radius * 0.8 * cos(angle);
      final dy = center.dy + radius * 0.8 * sin(angle);
      canvas.drawCircle(Offset(dx, dy), radius, paint);
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
