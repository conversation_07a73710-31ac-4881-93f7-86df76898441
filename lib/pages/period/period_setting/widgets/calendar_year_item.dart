import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:getx_xiaopa/values/values.dart';

class CalendarYearItem extends StatelessWidget {
  final DateTime date;
  final bool inRange;
  final bool isStart;
  final bool isEnd;
  final Color? textColor;

  const CalendarYearItem(
      {super.key,
      required this.date,
      this.inRange = false,
      this.isStart = false,
      this.isEnd = false,
      this.textColor});

  @override
  Widget build(BuildContext context) {
    var radius = const Radius.circular(100);

    return Container(
      margin: EdgeInsets.only(top: 2.h),
      decoration: BoxDecoration(
        color: inRange ? const Color.fromRGBO(254, 139, 172, 1) : null,
        // 只按“是否起点/终点”决定左右半圆，跟周几无关
        borderRadius: BorderRadius.horizontal(
          left: isStart ? radius : Radius.zero,
          right: isEnd ? radius : Radius.zero,
        ),
      ),
      child: Center(
        child: Text(
          "${date.day}",
          style: StyleConfig.otherStyle(
              color: textColor ?? ColorConfig.searchTextColor,
              fontSize: 8,
              fontWeight: FontWeight.w500),
        ),
      ),
    );
  }
}
