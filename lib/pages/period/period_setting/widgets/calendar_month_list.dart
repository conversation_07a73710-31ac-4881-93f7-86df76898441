import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:getx_xiaopa/pages/period/period_setting/index.dart';
import 'package:getx_xiaopa/storage/storage.dart';
import 'package:getx_xiaopa/values/values.dart';
import 'package:intl/intl.dart';
import 'package:scrollable_positioned_list/scrollable_positioned_list.dart';

class CalendarMonthList extends StatefulWidget {
  final bool isMonth;
  const CalendarMonthList({super.key, this.isMonth = true});

  @override
  State<CalendarMonthList> createState() => _CalendarMonthListState();
}

class _CalendarMonthListState extends State<CalendarMonthList>
    with AutomaticKeepAliveClientMixin {
  late List<DateTime> _months; // 月份列表
  late int _currentMonthIndex;

  final ItemScrollController _itemScrollController = ItemScrollController();

  @override
  bool get wantKeepAlive => true;

  @override
  void initState() {
    super.initState();

    final now = DateTime.now();

    // 构建去年1月 ~ 明年12月的月份
    _months = [];
    final start = DateTime(now.year - 1, 1, 1);
    final end = DateTime(now.year + 1, 12, 1);

    DateTime temp = start;
    while (temp.isBefore(end) || temp.isAtSameMomentAs(end)) {
      _months.add(temp);
      temp = DateTime(temp.year, temp.month + 1, 1);
    }

    // 找到当前月份的 index
    _currentMonthIndex =
        _months.indexWhere((d) => d.year == now.year && d.month == now.month);

    // 延迟跳转，等 ListView build 完成
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (widget.isMonth) {
        _scrollToCurrentMonth();
      }
    });
  }

  void _scrollToCurrentMonth() {
    _itemScrollController.jumpTo(index: _currentMonthIndex);
  }

  /// 返回一个月的所有格子（含空格）
  List<DateTime?> _daysInMonth(DateTime month) {
    final firstDay = DateTime(month.year, month.month, 1);
    final daysInMonth = DateTime(month.year, month.month + 1, 0).day;
    final firstWeekday = firstDay.weekday % 7; // 周日=0
    final totalCells = ((firstWeekday + daysInMonth) <= 35) ? 35 : 42;

    List<DateTime?> result = List<DateTime?>.filled(totalCells, null);
    for (int i = 0; i < daysInMonth; i++) {
      result[firstWeekday + i] = DateTime(month.year, month.month, i + 1);
    }
    return result;
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return ScrollablePositionedList.builder(
      itemScrollController: _itemScrollController,
      itemCount: _months.length,
      itemBuilder: (_, index) {
        final date = _months[index];
        final days = _daysInMonth(date);

        return Container(
          padding: const EdgeInsets.all(0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                margin: EdgeInsets.only(top: 16.h, bottom: 18.h, left: 20.w),
                child: Text(
                  DateFormat.yMMMM(ConfigStore.to.locale.toString())
                      .format(date),
                  style: StyleConfig.otherStyle(
                      color: ColorConfig.searchTextColor,
                      fontSize: 18,
                      fontWeight: FontWeight.w600),
                ),
              ),
              Container(
                margin: EdgeInsets.only(left: 20.w, right: 20.w),
                child: GridView.builder(
                  padding: EdgeInsets.zero,
                  physics: const NeverScrollableScrollPhysics(),
                  shrinkWrap: true,
                  gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                    crossAxisCount: 7,
                    childAspectRatio: 1, // 宽高比 1:1
                  ),
                  itemCount: days.length,
                  itemBuilder: (context, i) {
                    final day = days[i];
                    if (day == null) return const SizedBox();

                    // 示例：任务区间 9月2号 ~ 9月10号
                    final taskStart = DateTime(2025, 9, 2);
                    final taskEnd = DateTime(2025, 9, 8);

                    final inRange =
                        !day.isBefore(taskStart) && !day.isAfter(taskEnd);

                    final isStart = day.isAtSameMomentAs(taskStart);
                    final isEnd = day.isAtSameMomentAs(taskEnd);

                    final isOvulation =
                        day.isAtSameMomentAs(DateTime(2025, 9, 19));

                    final Color tColor =
                        (inRange || isOvulation) ? Colors.white : Colors.black;

                    return CalendarMonthItem(
                      date: day,
                      inRange: inRange,
                      isStart: isStart,
                      isEnd: isEnd,
                      textColor: tColor,
                      isOvulation: isOvulation,
                    );
                  },
                ),
              )
            ],
          ),
        );
      },
    );
  }
}
