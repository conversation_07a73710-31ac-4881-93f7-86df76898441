import 'package:get/get.dart';
import 'package:getx_xiaopa/routes/router.dart';

class PeriodSettingController extends GetxController {
  PeriodSettingController();

  bool isMonth = true;
  List<String> tipImage = [
    'assets/images/period_setting_image_01.png',
    'assets/images/period_setting_image_02.png',
    'assets/images/period_setting_image_03.png',
    'assets/images/period_setting_image_04.png',
    'assets/images/period_setting_image_05.png',
  ];

  List<String> tipStr = [
    "period_three_widget_two".tr,
    "period_three_widget_four".tr,
    "period_three_widget_three".tr,
    "period_three_widget_one".tr,
    "period_setting_tip_one".tr
  ];

  @override
  void onReady() {
    super.onReady();
    update(["period_setting"]);
  }

  changeCalandar(value) {
    isMonth = value == 1 ? true : false;
    update(["period_setting"]);
  }

  toSettingEdit() {
    Get.toNamed(AppRoutes.PERIOD_SETTING_EDIT);
  }

  toEdit(){
    Get.toNamed(AppRoutes.PERIOD_EDIT);
  }

  toExplainPage() {
    Get.toNamed(AppRoutes.PERIOD_EXPLAIN);
  }
}
