import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:getx_xiaopa/base/base.dart';
import 'package:getx_xiaopa/extension/images.dart';
import 'package:getx_xiaopa/extension/widget_extension.dart';
import 'package:getx_xiaopa/r.dart';
import 'package:getx_xiaopa/values/values.dart';

import 'index.dart';

class PeriodSettingEditPage
    extends BaseCommonView<PeriodSettingEditController> {
  PeriodSettingEditPage({super.key});

  @override
  String? get navTitle => "period_setting_edit_title".tr;

  @override
  TextStyle? get navTitleStyle =>
      StyleConfig.blackStyle(fontSize: 18, fontWeight: FontWeight.w600);

  @override
  Color? get navColor => Colors.transparent;

  @override
  double? get leftWidth => 35.w;

  @override
  Widget? get leftButton => Padding(
        padding: EdgeInsets.only(left: 15.w),
        child: Images(
          path: R.back_png,
          width: 20.w,
          height: 20.h,
          scale: 2.8,
        ).inkWell(() => Get.back()),
      );

  @override
  Color? get contentColor => const Color.fromRGBO(249, 249, 249, 1);

  // 主视图
  Widget _buildView() {
    return Container(
      margin: EdgeInsets.only(left: 15.w, right: 15.w),
      child: Column(
        children: [
          SizedBox(height: 10.h),
          Container(
            width: 345.w,
            height: 58.h,
            decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(10.r), color: Colors.white),
            child: Row(
              children: [
                SizedBox(width: 20.w),
                Text(
                  'period_setting_edit_one'.tr,
                  style: StyleConfig.otherStyle(
                      color: ColorConfig.searchTextColor,
                      fontSize: 14,
                      fontWeight: FontWeight.w600),
                ),
                const Expanded(child: SizedBox()),
                Text(
                  'period_analysis_one_widget_three'.trArgs(['32']),
                  style: StyleConfig.otherStyle(
                      color: const Color.fromRGBO(254, 139, 172, 1),
                      fontWeight: FontWeight.w600),
                ),
                SizedBox(width: 8.w),
                Images(
                  path: R.mine_right_png,
                  width: 11.w,
                  height: 20.h,
                ),
                SizedBox(width: 20.w)
              ],
            ),
          ).inkWell(() => controller.changeAction(1)),
          SizedBox(height: 10.h),
          Container(
            width: 345.w,
            height: 58.h,
            decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(10.r), color: Colors.white),
            child: Row(
              children: [
                SizedBox(width: 20.w),
                Text(
                  'period_setting_edit_two'.tr,
                  style: StyleConfig.otherStyle(
                      color: ColorConfig.searchTextColor,
                      fontSize: 14,
                      fontWeight: FontWeight.w600),
                ),
                const Expanded(child: SizedBox()),
                Text(
                  'period_analysis_one_widget_three'.trArgs(['8']),
                  style: StyleConfig.otherStyle(
                      color: const Color.fromRGBO(254, 139, 172, 1),
                      fontWeight: FontWeight.w600),
                ),
                SizedBox(width: 8.w),
                Images(
                  path: R.mine_right_png,
                  width: 11.w,
                  height: 20.h,
                ),
                SizedBox(width: 20.w)
              ],
            ),
          ).inkWell(() => controller.changeAction(2)),
        ],
      ),
    );
  }

  @override
  Widget buildContent() {
    return GetBuilder<PeriodSettingEditController>(
      init: PeriodSettingEditController(),
      id: "period_setting_edit",
      builder: (_) {
        return _buildView();
      },
    );
  }
}
