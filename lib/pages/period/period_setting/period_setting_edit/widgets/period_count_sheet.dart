import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:getx_xiaopa/extension/widget_extension.dart';
import 'package:getx_xiaopa/pages/task/widget/infinite_picker.dart';
import 'package:getx_xiaopa/utils/utils.dart';
import 'package:getx_xiaopa/values/values.dart';

// ignore: must_be_immutable
class PeriodCountSheet extends StatelessWidget {
  final int initialValue;
  final List<int> data;
  final Function(int) onTap;
  final String oneTip;

  PeriodCountSheet({
    super.key,
    required this.onTap,
    required this.data,
    required this.initialValue,
    this.oneTip = '',
  });

  late int _cIndex = initialValue;

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 1.sw,
      height: 375.h,
      decoration: BoxDecoration(
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(20.r),
            topRight: Radius.circular(20.r),
          ),
          color: Colors.white),
      child: Column(
        children: [
          SizedBox(height: 20.h),
          Text(
            oneTip,
            style: StyleConfig.otherStyle(
                color: ColorConfig.searchTextColor,
                fontWeight: FontWeight.w500,
                fontSize: 20),
          ),
          SizedBox(height: 8.h),
          Text(
            "period_setting_edit_unit".tr,
            style: StyleConfig.otherStyle(
                color: ColorConfig.authenticationTextColor,
                fontWeight: FontWeight.w400,
                fontSize: 14),
          ),
          Container(
            margin: EdgeInsets.only(top: 13.h),
            width: 335.w,
            height: 194.h,
            decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(10.r),
                color: const Color.fromRGBO(249, 249, 249, 1)),
            child: InfinitePicker<int>(
              data: data,
              initialValue: initialValue,
              itemBuilder: (context, item, selected) {
                return Text(
                  '$item',
                  style: StyleConfig.otherStyle(
                    fontSize: selected ? 18 : 16,
                    color: selected
                        ? Get.find<ThemeColorController>().gTextColor
                        : ColorConfig.shopDetailTextColor,
                  ),
                );
              },
              onSelected: (val) {
                _cIndex = val;
              },
            ),
          ),
          Container(
            margin: EdgeInsets.only(top: 20.h),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                Container(
                  width: 163.w,
                  height: 40.h,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(20.r),
                    color: Colors.white,
                    border: Border.all(
                      width: 1.r,
                      color: const Color.fromRGBO(120, 120, 120, 1),
                    ),
                  ),
                  child: Center(
                    child: Text(
                      "task_count_sheet_cancel".tr,
                      style: StyleConfig.otherStyle(
                        color: ColorConfig.searchTextColor,
                      ),
                    ),
                  ),
                ).inkWell(() => Get.back()),
                Container(
                  width: 163.w,
                  height: 40.h,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(20.r),
                    color: ColorConfig.searchTextColor,
                    border: Border.all(
                      width: 1.r,
                      color: const Color.fromRGBO(120, 120, 120, 1),
                    ),
                  ),
                  child: Center(
                    child: Text(
                      "task_count_sheet_confirm".tr,
                      style: StyleConfig.otherStyle(
                          color: Get.find<ThemeColorController>().gTextColor,
                          fontWeight: FontWeight.w500),
                    ),
                  ),
                ).inkWell(() {
                  Get.back();
                  onTap.call(_cIndex);
                })
              ],
            ),
          )
        ],
      ),
    );
  }
}
