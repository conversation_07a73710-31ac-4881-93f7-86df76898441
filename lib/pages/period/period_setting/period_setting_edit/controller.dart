import 'package:get/get.dart';
import 'package:getx_xiaopa/pages/period/period_setting/period_setting_edit/index.dart';

class PeriodSettingEditController extends GetxController {
  PeriodSettingEditController();

  List<int> sustainDay = [];
  int cSustain = 8;
  List<int> intervalDay = [];
  int cInterval = 32;

  @override
  void onReady() {
    super.onReady();

    for (var i = 3; i <= 15; i++) {
      sustainDay.add(i);
    }

    for (var i = 17; i <= 60; i++) {
      intervalDay.add(i);
    }

    update(["period_setting_edit"]);
  }

  changeAction(int type) {
    if (type == 1) {
      Get.bottomSheet(PeriodCountSheet(
          oneTip: 'period_setting_edit_interval'.tr,
          onTap: (value) {},
          data: intervalDay,
          initialValue: cInterval));
    }
    if (type == 2) {
      Get.bottomSheet(PeriodCountSheet(
          oneTip: 'period_setting_edit_sutain'.tr,
          onTap: (value) {},
          data: sustainDay,
          initialValue: cSustain));
    }
  }
}
