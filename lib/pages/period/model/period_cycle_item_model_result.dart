///
/// Code generated by jsonToDartModel https://ashamp.github.io/jsonToDartModel/
///
class PeriodCycleItemModelPeriod {
/*
{
  "id": "5130942885658625",
  "created_at": "2025-09-09T09:45:08.555+08:00",
  "updated_at": "2025-09-09T09:45:08.555+08:00",
  "deleted_at": null,
  "user_id": "5062306154151936",
  "cycle_id": "5130942885658624",
  "start_date": "2025-09-01T00:00:00+08:00",
  "end_date": "2025-09-05T00:00:00+08:00",
  "real_duration": 5,
  "expected_duration": 5,
  "is_end": true,
  "records": null
} 
*/

  String? id;
  String? createdAt;
  String? updatedAt;
  String? deletedAt;
  String? userId;
  String? cycleId;
  String? startDate;
  String? endDate;
  int? realDuration;
  int? expectedDuration;
  bool? isEnd;
  String? records;

  PeriodCycleItemModelPeriod({
    this.id,
    this.createdAt,
    this.updatedAt,
    this.deletedAt,
    this.userId,
    this.cycleId,
    this.startDate,
    this.endDate,
    this.realDuration,
    this.expectedDuration,
    this.isEnd,
    this.records,
  });
  PeriodCycleItemModelPeriod.fromJson(Map<String, dynamic> json) {
    id = json['id']?.toString();
    createdAt = json['created_at']?.toString();
    updatedAt = json['updated_at']?.toString();
    deletedAt = json['deleted_at']?.toString();
    userId = json['user_id']?.toString();
    cycleId = json['cycle_id']?.toString();
    startDate = json['start_date']?.toString();
    endDate = json['end_date']?.toString();
    realDuration = json['real_duration']?.toInt();
    expectedDuration = json['expected_duration']?.toInt();
    isEnd = json['is_end'];
    records = json['records']?.toString();
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['id'] = id;
    data['created_at'] = createdAt;
    data['updated_at'] = updatedAt;
    data['deleted_at'] = deletedAt;
    data['user_id'] = userId;
    data['cycle_id'] = cycleId;
    data['start_date'] = startDate;
    data['end_date'] = endDate;
    data['real_duration'] = realDuration;
    data['expected_duration'] = expectedDuration;
    data['is_end'] = isEnd;
    data['records'] = records;
    return data;
  }
}

class PeriodCycleItemModel {
/*
{
  "id": "5130942885658624",
  "created_at": "2025-09-09T09:45:08.543+08:00",
  "updated_at": "2025-09-09T11:30:32.938+08:00",
  "deleted_at": null,
  "user_id": "5062306154151936",
  "start_date": "2025-09-01T00:00:00+08:00",
  "end_date": "2025-09-04T00:00:00+08:00",
  "follicular_start": "2025-09-06T00:00:00+08:00",
  "follicular_end": "2025-09-04T00:00:00+08:00",
  "ovulation_date": "2025-09-05T00:00:00+08:00",
  "luteal_start": "2025-09-06T00:00:00+08:00",
  "luteal_end": "2025-09-04T00:00:00+08:00",
  "real_duration": 4,
  "expected_duration": 32,
  "is_end": true,
  "period": {
    "id": "5130942885658625",
    "created_at": "2025-09-09T09:45:08.555+08:00",
    "updated_at": "2025-09-09T09:45:08.555+08:00",
    "deleted_at": null,
    "user_id": "5062306154151936",
    "cycle_id": "5130942885658624",
    "start_date": "2025-09-01T00:00:00+08:00",
    "end_date": "2025-09-05T00:00:00+08:00",
    "real_duration": 5,
    "expected_duration": 5,
    "is_end": true,
    "records": null
  }
} 
*/

  String? id;
  String? createdAt;
  String? updatedAt;
  String? deletedAt;
  String? userId;
  String? startDate;
  String? endDate;
  String? follicularStart;
  String? follicularEnd;
  String? ovulationDate;
  String? lutealStart;
  String? lutealEnd;
  int? realDuration;
  int? expectedDuration;
  bool? isEnd;
  PeriodCycleItemModelPeriod? period;

  PeriodCycleItemModel({
    this.id,
    this.createdAt,
    this.updatedAt,
    this.deletedAt,
    this.userId,
    this.startDate,
    this.endDate,
    this.follicularStart,
    this.follicularEnd,
    this.ovulationDate,
    this.lutealStart,
    this.lutealEnd,
    this.realDuration,
    this.expectedDuration,
    this.isEnd,
    this.period,
  });
  PeriodCycleItemModel.fromJson(Map<String, dynamic> json) {
    id = json['id']?.toString();
    createdAt = json['created_at']?.toString();
    updatedAt = json['updated_at']?.toString();
    deletedAt = json['deleted_at']?.toString();
    userId = json['user_id']?.toString();
    startDate = json['start_date']?.toString();
    endDate = json['end_date']?.toString();
    follicularStart = json['follicular_start']?.toString();
    follicularEnd = json['follicular_end']?.toString();
    ovulationDate = json['ovulation_date']?.toString();
    lutealStart = json['luteal_start']?.toString();
    lutealEnd = json['luteal_end']?.toString();
    realDuration = json['real_duration']?.toInt();
    expectedDuration = json['expected_duration']?.toInt();
    isEnd = json['is_end'];
    period = (json['period'] != null) ? PeriodCycleItemModelPeriod.fromJson(json['period']) : null;
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['id'] = id;
    data['created_at'] = createdAt;
    data['updated_at'] = updatedAt;
    data['deleted_at'] = deletedAt;
    data['user_id'] = userId;
    data['start_date'] = startDate;
    data['end_date'] = endDate;
    data['follicular_start'] = follicularStart;
    data['follicular_end'] = follicularEnd;
    data['ovulation_date'] = ovulationDate;
    data['luteal_start'] = lutealStart;
    data['luteal_end'] = lutealEnd;
    data['real_duration'] = realDuration;
    data['expected_duration'] = expectedDuration;
    data['is_end'] = isEnd;
    if (period != null) {
      data['period'] = period!.toJson();
    }
    return data;
  }
}

class PeriodCycleItemModelResult {
/*
{
  "count": 2,
  "pageSize": 10,
  "pageNum": 1,
  "data": [
    {
      "id": "5130942885658624",
      "created_at": "2025-09-09T09:45:08.543+08:00",
      "updated_at": "2025-09-09T11:30:32.938+08:00",
      "deleted_at": null,
      "user_id": "5062306154151936",
      "start_date": "2025-09-01T00:00:00+08:00",
      "end_date": "2025-09-04T00:00:00+08:00",
      "follicular_start": "2025-09-06T00:00:00+08:00",
      "follicular_end": "2025-09-04T00:00:00+08:00",
      "ovulation_date": "2025-09-05T00:00:00+08:00",
      "luteal_start": "2025-09-06T00:00:00+08:00",
      "luteal_end": "2025-09-04T00:00:00+08:00",
      "real_duration": 4,
      "expected_duration": 32,
      "is_end": true,
      "period": {
        "id": "5130942885658625",
        "created_at": "2025-09-09T09:45:08.555+08:00",
        "updated_at": "2025-09-09T09:45:08.555+08:00",
        "deleted_at": null,
        "user_id": "5062306154151936",
        "cycle_id": "5130942885658624",
        "start_date": "2025-09-01T00:00:00+08:00",
        "end_date": "2025-09-05T00:00:00+08:00",
        "real_duration": 5,
        "expected_duration": 5,
        "is_end": true,
        "records": null
      }
    }
  ]
} 
*/

  int? count;
  int? pageSize;
  int? pageNum;
  List<PeriodCycleItemModel?>? data;

  PeriodCycleItemModelResult({
    this.count,
    this.pageSize,
    this.pageNum,
    this.data,
  });
  PeriodCycleItemModelResult.fromJson(Map<String, dynamic> json) {
    count = json['count']?.toInt();
    pageSize = json['pageSize']?.toInt();
    pageNum = json['pageNum']?.toInt();
  if (json['data'] != null) {
  final v = json['data'];
  final arr0 = <PeriodCycleItemModel>[];
  v.forEach((v) {
  arr0.add(PeriodCycleItemModel.fromJson(v));
  });
    data = arr0;
    }
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['count'] = count;
    data['pageSize'] = pageSize;
    data['pageNum'] = pageNum;
    if (this.data != null) {
      final v = this.data;
      final arr0 = [];
  for (var v in v!) {
  arr0.add(v!.toJson());
  }
      data['data'] = arr0;
    }
    return data;
  }
}
