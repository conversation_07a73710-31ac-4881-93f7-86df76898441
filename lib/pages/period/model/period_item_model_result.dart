///
/// Code generated by jsonToDartModel https://ashamp.github.io/jsonToDartModel/
///
class PeriodItemModelRecords {
/*
{
  "id": "5131179142414336",
  "created_at": "2025-09-09T11:42:29.755+08:00",
  "updated_at": "2025-09-09T11:42:29.755+08:00",
  "deleted_at": null,
  "period_id": "5130942885658625",
  "date": "2025-09-01T00:00:00+08:00",
  "color": "红色",
  "flow": "极多",
  "pain": "轻微痛",
  "day_number": 1
} 
*/

  String? id;
  String? createdAt;
  String? updatedAt;
  String? deletedAt;
  String? periodId;
  String? date;
  String? color;
  String? flow;
  String? pain;
  int? dayNumber;

  PeriodItemModelRecords({
    this.id,
    this.createdAt,
    this.updatedAt,
    this.deletedAt,
    this.periodId,
    this.date,
    this.color,
    this.flow,
    this.pain,
    this.dayNumber,
  });
  PeriodItemModelRecords.fromJson(Map<String, dynamic> json) {
    id = json['id']?.toString();
    createdAt = json['created_at']?.toString();
    updatedAt = json['updated_at']?.toString();
    deletedAt = json['deleted_at']?.toString();
    periodId = json['period_id']?.toString();
    date = json['date']?.toString();
    color = json['color']?.toString();
    flow = json['flow']?.toString();
    pain = json['pain']?.toString();
    dayNumber = json['day_number']?.toInt();
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['id'] = id;
    data['created_at'] = createdAt;
    data['updated_at'] = updatedAt;
    data['deleted_at'] = deletedAt;
    data['period_id'] = periodId;
    data['date'] = date;
    data['color'] = color;
    data['flow'] = flow;
    data['pain'] = pain;
    data['day_number'] = dayNumber;
    return data;
  }
}

class PeriodItemModel {
/*
{
  "id": "5130942885658625",
  "created_at": "2025-09-09T09:45:08.555+08:00",
  "updated_at": "2025-09-09T09:45:08.555+08:00",
  "deleted_at": null,
  "user_id": "5062306154151936",
  "cycle_id": "5130942885658624",
  "start_date": "2025-09-01T00:00:00+08:00",
  "end_date": "2025-09-05T00:00:00+08:00",
  "real_duration": 5,
  "expected_duration": 5,
  "is_end": true,
  "records": [
    {
      "id": "5131179142414336",
      "created_at": "2025-09-09T11:42:29.755+08:00",
      "updated_at": "2025-09-09T11:42:29.755+08:00",
      "deleted_at": null,
      "period_id": "5130942885658625",
      "date": "2025-09-01T00:00:00+08:00",
      "color": "红色",
      "flow": "极多",
      "pain": "轻微痛",
      "day_number": 1
    }
  ]
} 
*/

  String? id;
  String? createdAt;
  String? updatedAt;
  String? deletedAt;
  String? userId;
  String? cycleId;
  String? startDate;
  String? endDate;
  int? realDuration;
  int? expectedDuration;
  bool? isEnd;
  List<PeriodItemModelRecords?>? records;

  PeriodItemModel({
    this.id,
    this.createdAt,
    this.updatedAt,
    this.deletedAt,
    this.userId,
    this.cycleId,
    this.startDate,
    this.endDate,
    this.realDuration,
    this.expectedDuration,
    this.isEnd,
    this.records,
  });
  PeriodItemModel.fromJson(Map<String, dynamic> json) {
    id = json['id']?.toString();
    createdAt = json['created_at']?.toString();
    updatedAt = json['updated_at']?.toString();
    deletedAt = json['deleted_at']?.toString();
    userId = json['user_id']?.toString();
    cycleId = json['cycle_id']?.toString();
    startDate = json['start_date']?.toString();
    endDate = json['end_date']?.toString();
    realDuration = json['real_duration']?.toInt();
    expectedDuration = json['expected_duration']?.toInt();
    isEnd = json['is_end'];
  if (json['records'] != null) {
  final v = json['records'];
  final arr0 = <PeriodItemModelRecords>[];
  v.forEach((v) {
  arr0.add(PeriodItemModelRecords.fromJson(v));
  });
    records = arr0;
    }
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['id'] = id;
    data['created_at'] = createdAt;
    data['updated_at'] = updatedAt;
    data['deleted_at'] = deletedAt;
    data['user_id'] = userId;
    data['cycle_id'] = cycleId;
    data['start_date'] = startDate;
    data['end_date'] = endDate;
    data['real_duration'] = realDuration;
    data['expected_duration'] = expectedDuration;
    data['is_end'] = isEnd;
    if (records != null) {
      final v = records;
      final arr0 = [];
  for (var v in v!) {
  arr0.add(v!.toJson());
  }
      data['records'] = arr0;
    }
    return data;
  }
}

class PeriodItemModelResult {
/*
{
  "count": 3,
  "pageSize": 10,
  "pageNum": 1,
  "data": [
    {
      "id": "5130942885658625",
      "created_at": "2025-09-09T09:45:08.555+08:00",
      "updated_at": "2025-09-09T09:45:08.555+08:00",
      "deleted_at": null,
      "user_id": "5062306154151936",
      "cycle_id": "5130942885658624",
      "start_date": "2025-09-01T00:00:00+08:00",
      "end_date": "2025-09-05T00:00:00+08:00",
      "real_duration": 5,
      "expected_duration": 5,
      "is_end": true,
      "records": [
        {
          "id": "5131179142414336",
          "created_at": "2025-09-09T11:42:29.755+08:00",
          "updated_at": "2025-09-09T11:42:29.755+08:00",
          "deleted_at": null,
          "period_id": "5130942885658625",
          "date": "2025-09-01T00:00:00+08:00",
          "color": "红色",
          "flow": "极多",
          "pain": "轻微痛",
          "day_number": 1
        }
      ]
    }
  ]
} 
*/

  int? count;
  int? pageSize;
  int? pageNum;
  List<PeriodItemModel?>? data;

  PeriodItemModelResult({
    this.count,
    this.pageSize,
    this.pageNum,
    this.data,
  });
  PeriodItemModelResult.fromJson(Map<String, dynamic> json) {
    count = json['count']?.toInt();
    pageSize = json['pageSize']?.toInt();
    pageNum = json['pageNum']?.toInt();
  if (json['data'] != null) {
  final v = json['data'];
  final arr0 = <PeriodItemModel>[];
  v.forEach((v) {
  arr0.add(PeriodItemModel.fromJson(v));
  });
    data = arr0;
    }
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['count'] = count;
    data['pageSize'] = pageSize;
    data['pageNum'] = pageNum;
    if (this.data != null) {
      final v = this.data;
      final arr0 = [];
  for (var v in v!) {
  arr0.add(v!.toJson());
  }
      data['data'] = arr0;
    }
    return data;
  }
}
