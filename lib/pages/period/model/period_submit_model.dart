///
/// Code generated by jsonToDartModel https://ashamp.github.io/jsonToDartModel/
///
class PeriodSubmitModel {
/*
{
  "id": "5128850162843648",
  "created_at": "2025-09-08T16:25:40.584+08:00",
  "updated_at": "2025-09-09T11:30:32.997+08:00",
  "deleted_at": null,
  "user_id": "5062306154151936",
  "menstrual_cycle_duration": 31,
  "menstrual_period_duration": 9,
  "last_menstrual_date": "2025-09-05T00:00:00+08:00"
} 
*/

  String? id;
  String? createdAt;
  String? updatedAt;
  String? deletedAt;
  String? userId;
  int? menstrualCycleDuration;
  int? menstrualPeriodDuration;
  String? lastMenstrualDate;

  PeriodSubmitModel({
    this.id,
    this.createdAt,
    this.updatedAt,
    this.deletedAt,
    this.userId,
    this.menstrualCycleDuration,
    this.menstrualPeriodDuration,
    this.lastMenstrualDate,
  });
  PeriodSubmitModel.fromJson(Map<String, dynamic> json) {
    id = json['id']?.toString();
    createdAt = json['created_at']?.toString();
    updatedAt = json['updated_at']?.toString();
    deletedAt = json['deleted_at']?.toString();
    userId = json['user_id']?.toString();
    menstrualCycleDuration = json['menstrual_cycle_duration']?.toInt();
    menstrualPeriodDuration = json['menstrual_period_duration']?.toInt();
    lastMenstrualDate = json['last_menstrual_date']?.toString();
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['id'] = id;
    data['created_at'] = createdAt;
    data['updated_at'] = updatedAt;
    data['deleted_at'] = deletedAt;
    data['user_id'] = userId;
    data['menstrual_cycle_duration'] = menstrualCycleDuration;
    data['menstrual_period_duration'] = menstrualPeriodDuration;
    data['last_menstrual_date'] = lastMenstrualDate;
    return data;
  }
}
