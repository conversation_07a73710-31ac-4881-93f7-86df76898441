///
/// Code generated by jsonToDartModel https://ashamp.github.io/jsonToDartModel/
///
class PeriodCycleInfoModel {
/*
{
  "cycle_id": "5131155117441024",
  "period_id": "5131155117441025",
  "cycle_start": "2025-09-05T00:00:00+08:00",
  "cycle_end": "2025-10-05T00:00:00+08:00",
  "cycle_duration": 31,
  "period_start": "2025-09-05T00:00:00+08:00",
  "period_end": "2025-09-13T00:00:00+08:00",
  "period_duration": 9,
  "follicular_start": "2025-09-14T00:00:00+08:00",
  "follicular_end": "2025-09-20T00:00:00+08:00",
  "follicular_duration": 7,
  "ovulation_date": "2025-09-21T00:00:00+08:00",
  "ovulation_duration": 1,
  "luteal_start": "2025-09-22T00:00:00+08:00",
  "luteal_end": "2025-10-05T00:00:00+08:00",
  "luteal_duration": 14,
  "cycle_is_end": false,
  "period_is_end": false,
  "phase": "4",  1:月经期 2:卵泡期 3:排卵日 4:黄体期 
  "day_in_phase": 5,
  "days_left_in_phase": 4,
  "days_until_next_period": 26
} 
*/

  String? cycleId;
  String? periodId;
  String? cycleStart;
  String? cycleEnd;
  int? cycleDuration;
  String? periodStart;
  String? periodEnd;
  int? periodDuration;
  String? follicularStart;
  String? follicularEnd;
  int? follicularDuration;
  String? ovulationDate;
  int? ovulationDuration;
  String? lutealStart;
  String? lutealEnd;
  int? lutealDuration;
  bool? cycleIsEnd;
  bool? periodIsEnd;
  int? phase;
  int? dayInPhase;
  int? daysLeftInPhase;
  int? daysUntilNextPeriod;

  PeriodCycleInfoModel({
    this.cycleId,
    this.periodId,
    this.cycleStart,
    this.cycleEnd,
    this.cycleDuration,
    this.periodStart,
    this.periodEnd,
    this.periodDuration,
    this.follicularStart,
    this.follicularEnd,
    this.follicularDuration,
    this.ovulationDate,
    this.ovulationDuration,
    this.lutealStart,
    this.lutealEnd,
    this.lutealDuration,
    this.cycleIsEnd,
    this.periodIsEnd,
    this.phase,
    this.dayInPhase,
    this.daysLeftInPhase,
    this.daysUntilNextPeriod,
  });
  PeriodCycleInfoModel.fromJson(Map<String, dynamic> json) {
    cycleId = json['cycle_id']?.toString();
    periodId = json['period_id']?.toString();
    cycleStart = json['cycle_start']?.toString();
    cycleEnd = json['cycle_end']?.toString();
    cycleDuration = json['cycle_duration']?.toInt();
    periodStart = json['period_start']?.toString();
    periodEnd = json['period_end']?.toString();
    periodDuration = json['period_duration']?.toInt();
    follicularStart = json['follicular_start']?.toString();
    follicularEnd = json['follicular_end']?.toString();
    follicularDuration = json['follicular_duration']?.toInt();
    ovulationDate = json['ovulation_date']?.toString();
    ovulationDuration = json['ovulation_duration']?.toInt();
    lutealStart = json['luteal_start']?.toString();
    lutealEnd = json['luteal_end']?.toString();
    lutealDuration = json['luteal_duration']?.toInt();
    cycleIsEnd = json['cycle_is_end'];
    periodIsEnd = json['period_is_end'];
    phase = json['phase']?.toInt();
    dayInPhase = json['day_in_phase']?.toInt();
    daysLeftInPhase = json['days_left_in_phase']?.toInt();
    daysUntilNextPeriod = json['days_until_next_period']?.toInt();
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['cycle_id'] = cycleId;
    data['period_id'] = periodId;
    data['cycle_start'] = cycleStart;
    data['cycle_end'] = cycleEnd;
    data['cycle_duration'] = cycleDuration;
    data['period_start'] = periodStart;
    data['period_end'] = periodEnd;
    data['period_duration'] = periodDuration;
    data['follicular_start'] = follicularStart;
    data['follicular_end'] = follicularEnd;
    data['follicular_duration'] = follicularDuration;
    data['ovulation_date'] = ovulationDate;
    data['ovulation_duration'] = ovulationDuration;
    data['luteal_start'] = lutealStart;
    data['luteal_end'] = lutealEnd;
    data['luteal_duration'] = lutealDuration;
    data['cycle_is_end'] = cycleIsEnd;
    data['period_is_end'] = periodIsEnd;
    data['phase'] = phase;
    data['day_in_phase'] = dayInPhase;
    data['days_left_in_phase'] = daysLeftInPhase;
    data['days_until_next_period'] = daysUntilNextPeriod;
    return data;
  }
}
