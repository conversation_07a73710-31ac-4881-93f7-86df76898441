import 'package:flutter/material.dart';
import 'package:getx_xiaopa/pages/period/index.dart';
import 'package:getx_xiaopa/utils/utils.dart';

class PeriodUtils {
  ///获取时期的主颜色
  static Color getPersionMainColor(int phase) {
    switch (phase) {
      case 1:
        return const Color.fromRGBO(254, 139, 172, 1);
      case 2:
        return const Color.fromRGBO(154, 231, 119, 1);
      case 3:
        return const Color.fromRGBO(172, 127, 255, 1);
      case 4:
        return const Color.fromRGBO(255, 211, 9, 1);
      default:
        return Colors.white;
    }
  }

  ///获取其他日期的对应颜色
  static PeriodType getPersionColor(
      {required PeriodCycleInfoModel model, required DateTime cTime}) {
    PeriodType temp = PeriodType();
    if (model.cycleId!.isEmpty) return temp;

    ///整个周期的日期
    DateTime cycleSDt = TimeUtil.parseIsoDate(model.cycleStart ?? '');
    DateTime cycleEDt = TimeUtil.parseIsoDate(model.cycleEnd ?? '');

    if (TimeUtil.isTimeInRange(
        cTime: cTime, startTime: cycleSDt, endTime: cycleEDt)) {
      ///经期持续的日期
      DateTime periodSDt = TimeUtil.parseIsoDate(model.periodStart ?? '');
      DateTime periodEDt = TimeUtil.parseIsoDate(model.periodEnd ?? '');
      if (TimeUtil.isTimeInRange(
          cTime: cTime, startTime: periodSDt, endTime: periodEDt)) {
        temp.color = getPersionMainColor(1);
        temp.isPeriod = true;
      }

      ///卵泡期
      DateTime follicularSDt =
          TimeUtil.parseIsoDate(model.follicularStart ?? '');
      DateTime follicularEDt = TimeUtil.parseIsoDate(model.follicularEnd ?? '');
      if (TimeUtil.isTimeInRange(
          cTime: cTime, startTime: follicularSDt, endTime: follicularEDt)) {
        temp.color = getPersionMainColor(2);
      }

      ///排卵日
      DateTime ovulationDt = TimeUtil.parseIsoDate(model.ovulationDate ?? '');
      if (TimeUtil.isTimeInRange(
          cTime: cTime, startTime: ovulationDt, endTime: ovulationDt)) {
        temp.color = getPersionMainColor(3);
      }

      ///黄体期
      DateTime lutealSDt = TimeUtil.parseIsoDate(model.lutealStart ?? '');
      DateTime lutealEDt = TimeUtil.parseIsoDate(model.lutealEnd ?? '');
      if (TimeUtil.isTimeInRange(
          cTime: cTime, startTime: lutealSDt, endTime: lutealEDt)) {
        temp.color = getPersionMainColor(4);
      }
    } else {
      temp.color = getPersionMainColor(4);
    }

    return temp;
  }
}

///经期数据类型
class PeriodType {
  bool isPeriod;
  Color color;
  PeriodType({this.isPeriod = false, this.color = Colors.transparent});
}
