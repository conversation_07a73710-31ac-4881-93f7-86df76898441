import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:getx_xiaopa/base/base.dart';
import 'package:getx_xiaopa/extension/images.dart';
import 'package:getx_xiaopa/extension/widget_extension.dart';
import 'package:getx_xiaopa/pages/period/widgets/period_cycle.dart';
import 'package:getx_xiaopa/pages/period/widgets/week_date_selector.dart';
import 'package:getx_xiaopa/r.dart';
import 'package:getx_xiaopa/utils/utils.dart';
import 'package:getx_xiaopa/values/values.dart';
import 'package:getx_xiaopa/widget/widget.dart';

import 'index.dart';

class PeriodPage extends BaseCommonView<PeriodController> {
  PeriodPage({super.key});

  @override
  bool get customWidget => true;

  // 主视图
  Widget _buildView() {
    return Scaffold(
      backgroundColor: Colors.white,
      extendBodyBehindAppBar: true,
      appBar: AppBar(
        scrolledUnderElevation: 0,
        elevation: 0,
        centerTitle: true,
        backgroundColor: Colors.transparent,
        title: Text(
          "period_title".tr,
          style:
              StyleConfig.blackStyle(fontSize: 18, fontWeight: FontWeight.w600),
        ),
        leadingWidth: 35.w,
        leading: Padding(
          padding: EdgeInsets.only(left: 15.w),
          child: Images(
            path: R.back_png,
            width: 20.w,
            height: 20.h,
            scale: 2.8,
          ).inkWell(() => Get.back()),
        ),
        actions: [
          Padding(
            padding: EdgeInsets.only(right: 20.w),
            child: Images(
              path: R.period_calendar_png,
              width: 32.r,
              height: 32.r,
            ),
          ).inkWell(() => controller.toHistoryPage())
        ],
        systemOverlayStyle: const SystemUiOverlayStyle(
          statusBarColor: Colors.transparent,
          statusBarIconBrightness: Brightness.dark,
        ),
      ),
      body: ThemeContainerImage(
        fileName: 'period_bg.png',
        conWidget: 1.sw,
        conHeight: 1.sh,
        fit: BoxFit.fill,
        padding: EdgeInsets.only(top: 110.h),
        child: SingleChildScrollView(
          child: Column(
            children: [
              _oneWidget(),
              SizedBox(height: 25.h),
              _twoWidget(),
              SizedBox(height: 30.h),
              _threeWidget(),
              SizedBox(height: 5.h),
              _fourWidget(),
              _fiveWidget(),
              SizedBox(height: 20.h),
            ],
          ),
        ),
      ),
    );
  }

  Widget _oneWidget() {
    return Container(
      margin: EdgeInsets.only(left: 22.w),
      child: Row(
        children: [
          Text(
            controller.oneString,
            style: StyleConfig.otherStyle(
                color: ColorConfig.searchTextColor,
                fontWeight: FontWeight.w700,
                fontSize: 18),
          ),
          SizedBox(width: 4.w),
          Offstage(
            offstage: controller.cycleInfoModel.periodIsEnd ?? true,
            child: Container(
              padding: EdgeInsets.only(left: 21.w, right: 15.w),
              height: 22.h,
              decoration: const BoxDecoration(
                image: DecorationImage(
                  image: AssetImage(R.period_image_01_png),
                ),
              ),
              child: Text(
                'period_one_widget_two'
                    .trArgs(['${controller.cycleInfoModel.dayInPhase ?? 0}']),
                style: StyleConfig.witheStyle(fontWeight: FontWeight.w700),
              ),
            ),
          )
        ],
      ),
    );
  }

  Widget _twoWidget() {
    return Container(
      margin: EdgeInsets.only(left: 15.w, right: 15.w),
      child: WeekDateSelector(
        model: controller.cycleInfoModel,
      ),
    );
  }

  Widget _threeWidget() {
    return SizedBox(
      width: 302.w,
      height: 236.h,
      child: Stack(
        children: [
          Positioned(
              left: 46.w, child: PeriodCycle(model: controller.cycleInfoModel)),
          Positioned(
            top: 50.h,
            child: Text(
              'period_three_widget_one'.tr,
              style: StyleConfig.otherStyle(
                  color: ColorConfig.searchTextColor,
                  fontSize: 14,
                  fontWeight: FontWeight.w500),
            ),
          ),
          Positioned(
            right: 0,
            top: 0.h,
            child: Text(
              'period_three_widget_two'.tr,
              style: StyleConfig.otherStyle(
                  color: ColorConfig.searchTextColor,
                  fontSize: 14,
                  fontWeight: FontWeight.w500),
            ),
          ),
          Positioned(
            bottom: 0.h,
            left: 60.w,
            child: Text(
              'period_three_widget_three'.tr,
              style: StyleConfig.otherStyle(
                  color: ColorConfig.searchTextColor,
                  fontSize: 14,
                  fontWeight: FontWeight.w500),
            ),
          ),
          Positioned(
            bottom: 17.h,
            right: 7.w,
            child: Text(
              'period_three_widget_four'.tr,
              style: StyleConfig.otherStyle(
                  color: ColorConfig.searchTextColor,
                  fontSize: 14,
                  fontWeight: FontWeight.w500),
            ),
          ),
        ],
      ),
    );
  }

  Widget _fourWidget() {
    return Container(
      width: 365.w,
      height: controller.isPeriod ? 244.h : 182.h,
      decoration: BoxDecoration(
          image: DecorationImage(
              image: AssetImage(controller.isPeriod
                  ? R.period_four_bg_png
                  : R.period_four_short_bg_png),
              fit: BoxFit.fill)),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(height: 70.h),
          Container(
            margin: EdgeInsets.only(left: 30.w),
            child: Text(
              'period_four_widget_one'.tr,
              style: StyleConfig.otherStyle(
                  color: ColorConfig.searchTextColor,
                  fontWeight: FontWeight.w700),
            ),
          ),
          SizedBox(height: 14.h),
          Container(
            width: 315.w,
            height: 50.h,
            margin: EdgeInsets.only(left: 27.w),
            decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(8.r),
                color: const Color.fromRGBO(249, 249, 249, 1)),
            child: Row(
              children: [
                SizedBox(width: 10.w),
                Images(
                  path: R.period_four_image_png,
                  width: 24.r,
                  height: 24.r,
                ),
                SizedBox(width: 10.w),
                Text(
                  'period_four_widget_two'.tr,
                  style: StyleConfig.otherStyle(
                      color: ColorConfig.searchTextColor,
                      fontSize: 14,
                      fontWeight: FontWeight.w600),
                ),
                const Expanded(child: SizedBox()),
                CupertinoSwitch(
                    activeTrackColor:
                        Get.find<ThemeColorController>().gPeriodSwitchColor,
                    value: controller.isPeriod,
                    onChanged: (value) => controller.isPeriodAction(value)),
                SizedBox(width: 10.w),
              ],
            ),
          ),
          if (controller.isPeriod) ...{
            SizedBox(height: 10.h),
            Container(
              width: 315.w,
              height: 50.h,
              margin: EdgeInsets.only(left: 27.w),
              decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(8.r),
                  color: const Color.fromRGBO(249, 249, 249, 1)),
              child: Row(
                children: [
                  SizedBox(width: 10.w),
                  Text(
                    'period_four_widget_three'.tr,
                    style: StyleConfig.otherStyle(
                        color: ColorConfig.searchTextColor,
                        fontSize: 14,
                        fontWeight: FontWeight.w600),
                  ),
                  const Expanded(child: SizedBox()),
                  Text(
                    controller.periodStatus,
                    style: StyleConfig.otherStyle(
                        color: const Color.fromRGBO(254, 139, 172, 1),
                        fontSize: 14,
                        fontWeight: FontWeight.w500),
                  ),
                  SizedBox(width: 10.w),
                  Images(
                    path: R.mine_right_png,
                    width: 9.w,
                    height: 16.h,
                    color: const Color.fromRGBO(254, 139, 172, 1),
                  ),
                  SizedBox(width: 15.w),
                ],
              ),
            ).inkWell(() => controller.periodStatusaction())
          }
        ],
      ),
    );
  }

  Widget _fiveWidget() {
    Widget temp;
    if (controller.isPeriod) {
      temp = SizedBox(
        width: 345.w,
        height: 420.h,
        child: Stack(
          children: [
            Container(
              padding: EdgeInsets.only(left: 20.w, right: 20.w),
              width: 345.w,
              height: 195.h,
              decoration: BoxDecoration(
                  image: DecorationImage(
                      image: AssetImage(controller.periodDay >= 3
                          ? R.period_remind_bg_01_png
                          : R.period_remind_bg_png),
                      fit: BoxFit.fill),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.05),
                      // 阴影颜色和透明度
                      spreadRadius: 0,
                      // 阴影扩散范围
                      blurRadius: 10,
                      // 阴影模糊程度
                      offset: const Offset(0, 2),
                      // 阴影偏移量（水平，垂直）
                    )
                  ]),
              child: Column(
                children: [
                  SizedBox(height: 16.h),
                  Row(
                    children: [
                      Text(
                        "period_five_widget_one".tr,
                        style: StyleConfig.otherStyle(
                            color: ColorConfig.searchTextColor,
                            fontWeight: FontWeight.w700),
                      ),
                      SizedBox(width: 8.w),
                      Container(
                        padding: EdgeInsets.only(
                            top: 1.h, bottom: 2.h, left: 8.w, right: 8.w),
                        height: 17.h,
                        decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(11.r),
                            color: const Color.fromRGBO(255, 220, 224, 1)),
                        child: Text(
                          'period_five_widget_two'.tr,
                          style: StyleConfig.otherStyle(
                              color: const Color.fromRGBO(254, 139, 172, 1),
                              fontSize: 10,
                              fontWeight: FontWeight.w500),
                        ),
                      )
                    ],
                  ),
                  SizedBox(height: 10.h),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                    children: [
                      Column(
                        children: [
                          Text(
                            '2',
                            style: StyleConfig.otherStyle(
                                color: const Color.fromRGBO(254, 139, 172, 1),
                                fontSize: 20,
                                fontWeight: FontWeight.w700),
                          ),
                          Text(
                            'period_five_widget_three'.tr,
                            style: StyleConfig.otherStyle(
                                color: ColorConfig.shopDetailTextColor,
                                fontSize: 12,
                                fontWeight: FontWeight.w500),
                          ),
                        ],
                      ),
                      Column(
                        children: [
                          Text(
                            '22',
                            style: StyleConfig.otherStyle(
                                color: ColorConfig.searchTextColor,
                                fontSize: 20,
                                fontWeight: FontWeight.w700),
                          ),
                          Text(
                            'period_five_widget_four'.tr,
                            style: StyleConfig.otherStyle(
                                color: ColorConfig.shopDetailTextColor,
                                fontSize: 12,
                                fontWeight: FontWeight.w500),
                          ),
                        ],
                      )
                    ],
                  ),
                ],
              ),
            ),
            Positioned(
              top: 205.h,
              child: _sixWidget(),
            ),
            Positioned(
              left: 20.w,
              top: 115.h,
              width: 199.w,
              height: 103.h,
              child: controller.periodDay >= 3
                  ? const SizedBox()
                  : Images(path: R.period_remind_image_png),
            )
          ],
        ),
      );
    } else {
      temp = SizedBox(
        width: 345.w,
        height: 421.h,
        child: Column(
          children: [
            Container(
              padding: EdgeInsets.only(left: 20.w, right: 20.w),
              width: 345.w,
              height: 195.h,
              decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(10.r),
                  color: Colors.white,
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.05),
                      // 阴影颜色和透明度
                      spreadRadius: 0,
                      // 阴影扩散范围
                      blurRadius: 10,
                      // 阴影模糊程度
                      offset: const Offset(0, 2),
                      // 阴影偏移量（水平，垂直）
                    )
                  ]),
              child: Column(
                children: [
                  SizedBox(height: 16.h),
                  Row(
                    children: [
                      Text(
                        "period_five_widget_six".tr,
                        style: StyleConfig.otherStyle(
                            color: ColorConfig.searchTextColor,
                            fontWeight: FontWeight.w700),
                      ),
                      SizedBox(width: 8.w),
                      Container(
                        padding: EdgeInsets.only(
                            top: 1.h, bottom: 2.h, left: 8.w, right: 8.w),
                        height: 17.h,
                        decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(11.r),
                            color: const Color.fromRGBO(228, 255, 214, 1)),
                        child: Text(
                          'period_five_widget_seven'.tr,
                          style: StyleConfig.otherStyle(
                              color: const Color.fromRGBO(56, 197, 94, 1),
                              fontSize: 10,
                              fontWeight: FontWeight.w500),
                        ),
                      )
                    ],
                  ),
                  SizedBox(height: 10.h),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                    children: [
                      Column(
                        children: [
                          Text(
                            '2',
                            style: StyleConfig.otherStyle(
                                color: const Color.fromRGBO(143, 227, 96, 1),
                                fontSize: 20,
                                fontWeight: FontWeight.w700),
                          ),
                          Text(
                            'period_five_widget_enght'.tr,
                            style: StyleConfig.otherStyle(
                                color: ColorConfig.shopDetailTextColor,
                                fontSize: 12,
                                fontWeight: FontWeight.w500),
                          ),
                        ],
                      ),
                      Column(
                        children: [
                          Text(
                            '22',
                            style: StyleConfig.otherStyle(
                                color: ColorConfig.searchTextColor,
                                fontSize: 20,
                                fontWeight: FontWeight.w700),
                          ),
                          Text(
                            'period_five_widget_nine'.tr,
                            style: StyleConfig.otherStyle(
                                color: ColorConfig.shopDetailTextColor,
                                fontSize: 12,
                                fontWeight: FontWeight.w500),
                          ),
                        ],
                      )
                    ],
                  ),
                  SizedBox(height: 8.h),
                  Container(
                    padding: EdgeInsets.only(left: 15.w, right: 15.w),
                    width: 305.w,
                    height: 80.h,
                    decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(6.r),
                        color: const Color.fromRGBO(249, 249, 249, 1)),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        SizedBox(height: 10.h),
                        Container(
                          margin: EdgeInsets.only(left: 5.w),
                          child: Text(
                            '8月',
                            style: StyleConfig.otherStyle(
                                color: ColorConfig.shopDetailTextColor,
                                fontSize: 10.r,
                                fontWeight: FontWeight.w500),
                          ),
                        ),
                        SizedBox(height: 6.h),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: List.generate(7, (index) {
                            return Container(
                              width: 30.r,
                              height: 30.r,
                              decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(4.r),
                                  color: Colors.white,
                                  boxShadow: [
                                    BoxShadow(
                                      color:
                                          Colors.black.withValues(alpha: 0.04),
                                      // 阴影颜色和透明度
                                      spreadRadius: 0,
                                      // 阴影扩散范围
                                      blurRadius: 3,
                                      // 阴影模糊程度
                                      offset: const Offset(0, 2),
                                      // 阴影偏移量（水平，垂直）
                                    )
                                  ]),
                              child: Center(
                                child: Text(
                                  '${index + 1}',
                                  style: StyleConfig.otherStyle(
                                      color: ColorConfig.shopDetailTextColor,
                                      fontSize: 12,
                                      fontWeight: FontWeight.w500),
                                ),
                              ),
                            );
                          }),
                        )
                      ],
                    ),
                  )
                ],
              ),
            ),
            SizedBox(height: 10.h),
            _sixWidget(),
          ],
        ),
      );
    }
    return temp;
  }

  Widget _sixWidget() {
    return Container(
      padding: EdgeInsets.only(left: 20.w, right: 20.w),
      width: 345.w,
      height: 207.h,
      decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(10.r),
          color: Colors.white,
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.05),
              // 阴影颜色和透明度
              spreadRadius: 0,
              // 阴影扩散范围
              blurRadius: 10,
              // 阴影模糊程度
              offset: const Offset(0, 2),
              // 阴影偏移量（水平，垂直）
            )
          ]),
      child: Column(
        children: [
          SizedBox(height: 16.h),
          SizedBox(
            width: 305.w,
            height: 39.h,
            child: Column(
              children: [
                Row(
                  children: [
                    Text(
                      'period_six_widget_one'.tr,
                      style: StyleConfig.otherStyle(
                          color: ColorConfig.searchTextColor,
                          fontWeight: FontWeight.w700),
                    ),
                    const Expanded(child: SizedBox()),
                    Text(
                      'period_six_widget_two'.tr,
                      style: StyleConfig.otherStyle(
                          color: ColorConfig.shopDetailTextColor,
                          fontSize: 12,
                          fontWeight: FontWeight.w500),
                    ),
                    SizedBox(width: 4.w),
                    Images(
                      path: R.mine_right_png,
                      width: 9.w,
                      height: 11.h,
                    )
                  ],
                ),
                const Expanded(child: SizedBox()),
                Container(
                  width: 305.w,
                  height: 1.h,
                  color: const Color.fromRGBO(233, 232, 233, 1),
                )
              ],
            ),
          ),
          SizedBox(height: 16.h),
          SizedBox(
            width: 305.w,
            height: 57.h,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'period_six_widget_three'.trArgs(['7']),
                  style: StyleConfig.otherStyle(
                      color: ColorConfig.searchTextColor,
                      fontSize: 14,
                      fontWeight: FontWeight.w700),
                ),
                SizedBox(height: 6.h),
                SizedBox(
                  height: 14.h,
                  child: ListView.builder(
                    scrollDirection: Axis.horizontal,
                    itemCount: 30,
                    itemBuilder: (_, index) {
                      return Container(
                        margin: EdgeInsets.only(left: index > 0 ? 2.w : 0),
                        width: 10.w,
                        height: 14.h,
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(6.r),
                          color: const Color.fromRGBO(238, 238, 238, 1),
                        ),
                      );
                    },
                  ),
                ),
                const Expanded(child: SizedBox()),
                Container(
                  width: 305.w,
                  height: 1.h,
                  color: const Color.fromRGBO(233, 232, 233, 1),
                )
              ],
            ),
          ),
          SizedBox(height: 16.h),
          SizedBox(
            width: 305.w,
            height: 57.h,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Text(
                      'period_six_widget_four'.trArgs(['29']),
                      style: StyleConfig.otherStyle(
                          color: ColorConfig.searchTextColor,
                          fontSize: 14,
                          fontWeight: FontWeight.w700),
                    ),
                    Container(
                      margin: EdgeInsets.only(top: 3.h),
                      child: Text(
                        '6月29日-7月27日',
                        style: StyleConfig.otherStyle(
                            color: ColorConfig.authenticationTextColor,
                            fontSize: 12,
                            fontWeight: FontWeight.w500),
                      ),
                    )
                  ],
                ),
                SizedBox(height: 6.h),
                SizedBox(
                  height: 14.h,
                  child: ListView.builder(
                    scrollDirection: Axis.horizontal,
                    itemCount: 30,
                    itemBuilder: (_, index) {
                      return Container(
                        margin: EdgeInsets.only(left: index > 0 ? 2.w : 0),
                        width: 10.w,
                        height: 14.h,
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(6.r),
                          color: const Color.fromRGBO(238, 238, 238, 1),
                        ),
                      );
                    },
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    ).inkWell(() => controller.toAnalysisPage());
  }

  @override
  Widget buildContent() {
    return GetBuilder<PeriodController>(
      init: PeriodController(),
      id: "period",
      builder: (_) {
        return createCommonView(
          controller,
          (_) => controller.isSelect ? SettingOne() : _buildView(),
          initBuilder: () => const LoadStatusWidget(),
        );
      },
    );
  }
}
