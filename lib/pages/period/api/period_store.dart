import 'package:get/get.dart';
import 'package:getx_xiaopa/extension/widget_extension.dart';
import 'package:getx_xiaopa/network/http_client.dart';
import 'package:getx_xiaopa/network/model/result.dart';
import 'package:getx_xiaopa/pages/period/index.dart';
import 'package:getx_xiaopa/storage/storage.dart';

class PeriodStore extends GetxService {
  static PeriodStore get to => Get.find();

  bool _isSelect = false;

  bool get isSelect => _isSelect;

  PeriodSubmitModel _periodSubmitModel = PeriodSubmitModel();

  PeriodSubmitModel get periodSubmitModel => _periodSubmitModel;

  PeriodCycleInfoModel _periodCycleInfoModel = PeriodCycleInfoModel();

  PeriodCycleInfoModel get periodCycleInfoModel => _periodCycleInfoModel;

  @override
  void onInit() {
    super.onInit();
    persiodSubmitInfo();
  }

  ///获取经期信息
  persiodSubmitInfo() async {
    Result result = await http.periodSubmitInfo({});
    if (result.code == 0) {
      _periodSubmitModel = result.data;
      _isSelect = false;
      if (_periodSubmitModel.id!.isEmpty) {
        _isSelect = true;
      }
    }
  }

  ///提交经期
  persiodSubmitCreate(
      {required int cycle, required int period, required String time}) async {
    CToast.showLoading();
    var map = {
      "menstrual_cycle_duration": cycle,
      "menstrual_period_duration": period,
      "last_menstrual_date": time
    };
    Result result = await http.periodSubmitCreate(map);
    if (result.code == 0) {
      _persiodFirstCreate();
    }
  }

  _persiodFirstCreate() async {
    var map = {"user_id": UserStore.to.userInfo.id};
    Result result = await http.periodCycleCreate(map);
    if (result.code == 0) {}
  }

  ///获取当前周几各时期信息
  persionCycleInfo() async {
    Result result = await http.periodCycleInfo({});
    if (result.code == 0) {
      _periodCycleInfoModel = result.data;
    }
  }
}
