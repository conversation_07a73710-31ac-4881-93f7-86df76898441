import 'package:flutter/widgets.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:getx_xiaopa/extension/images.dart';
import 'package:getx_xiaopa/values/values.dart';

class PeriodColorItem extends StatefulWidget {
  const PeriodColorItem({super.key});

  @override
  State<PeriodColorItem> createState() => _PeriodColorItemState();
}

class _PeriodColorItemState extends State<PeriodColorItem> {
  final List<String> _image = [];
  final List<String> _str = [
    'period_analysis_three_item_one'.tr,
    'period_analysis_three_item_two'.tr,
    'period_analysis_three_item_three'.tr,
    'period_analysis_three_item_four'.tr,
    'period_analysis_three_item_five'.tr
  ];

  @override
  void initState() {
    super.initState();
    for (var i = 0; i < 5; i++) {
      _image.add('assets/images/period_analysis_three_image_0${i + 1}.png');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.only(top: 14.h),
      padding: EdgeInsets.only(left: 10.w, right: 10.w),
      width: 325.w,
      constraints: BoxConstraints(minHeight: 69.h),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            margin: EdgeInsets.only(left: 5.w),
            child: Text(
              'period_analysis_color_one'.trArgs(['6月29日', '今日']),
              style: StyleConfig.otherStyle(color: ColorConfig.searchTextColor),
            ),
          ),
          SizedBox(height: 14.h),
          Container(
            margin: EdgeInsets.only(left: 4.w),
            height: 74.h,
            child: ListView(
              scrollDirection: Axis.horizontal,
              children: List.generate(5, (index) {
                return Container(
                  margin: EdgeInsets.only(left: index > 0 ? 20.w : 0),
                  child: Column(
                    children: [
                      Text(
                        'period_analysis_pain_chart_day'
                            .trArgs(['${index + 1}']),
                        style: StyleConfig.otherStyle(
                            color: ColorConfig.authenticationTextColor,
                            fontSize: 12,
                            fontWeight: FontWeight.w500),
                      ),
                      Images(
                        path: _image[index],
                        width: 40.r,
                        height: 40.r,
                      ),
                      Text(
                        _str[index],
                        style: StyleConfig.otherStyle(
                            color: ColorConfig.shopDetailTextColor,
                            fontSize: 12,
                            fontWeight: FontWeight.w500),
                      ),
                    ],
                  ),
                );
              }),
            ),
          ),
          Container(
            margin: EdgeInsets.only(top: 16.h),
            width: 325.w,
            height: 1.h,
            color: const Color.fromRGBO(233, 232, 233, 1),
          )
        ],
      ),
    );
  }
}
