import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:getx_xiaopa/pages/period/period_analysis/index.dart';
import 'package:getx_xiaopa/values/values.dart';

class PeriodDayItem extends StatelessWidget {
  final String date;
  final String tip;
  final String cDaysTip;
  final Color pColor;
  final double pValue;
  final double pMaxValue;

  const PeriodDayItem(
      {super.key,
      required this.date,
      required this.tip,
      required this.pColor,
      required this.pValue,
      this.pMaxValue = 37,
      required this.cDaysTip});

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.only(top: 12.h),
      height: 20.h,
      child: Row(
        children: [
          Text(
            date,
            style: StyleConfig.otherStyle(
                color: ColorConfig.shopDetailTextColor, fontSize: 12),
          ),
          SizedBox(width: 8.w),
          Text(
            tip,
            style: StyleConfig.otherStyle(
                color: ColorConfig.searchTextColor, fontSize: 12),
          ),
          SizedBox(width: 10.w),
          PeriodProgress(
            size: 200.w,
            strokeWidth: 20.r,
            segmentCount: 1,
            segmentColors: [pColor],
            mode: ProgressMode.linear,
            backgroundColor: Colors.transparent,
            value: pValue,
            maxValue: pMaxValue,
            rightText: cDaysTip,
          ),
        ],
      ),
    );
  }
}
