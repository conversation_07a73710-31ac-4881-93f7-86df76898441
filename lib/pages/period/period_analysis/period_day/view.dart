import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:getx_xiaopa/base/base.dart';
import 'package:getx_xiaopa/extension/images.dart';
import 'package:getx_xiaopa/extension/widget_extension.dart';
import 'package:getx_xiaopa/r.dart';
import 'package:getx_xiaopa/values/values.dart';

import 'index.dart';

class PeriodDayPage extends BaseCommonView<PeriodDayController> {
  PeriodDayPage({super.key});

  @override
  String? get navTitle => "period_analysis_two_widget_one".tr;

  @override
  TextStyle? get navTitleStyle =>
      StyleConfig.blackStyle(fontSize: 18, fontWeight: FontWeight.w600);

  @override
  Color? get navColor => Colors.transparent;

  @override
  double? get leftWidth => 35.w;

  @override
  Widget? get leftButton => Padding(
        padding: EdgeInsets.only(left: 15.w),
        child: Images(
          path: R.back_png,
          width: 20.w,
          height: 20.h,
          scale: 2.8,
        ).inkWell(() => Get.back()),
      );

  @override
  Color? get contentColor => const Color.fromRGBO(249, 249, 249, 1);

  // 主视图
  Widget _buildView() {
    const itemCount = 12; // 列表项数量

    // 计算实际内容高度
    final containerHeight = 677.h;
    final topPadding = 15.h;
    final bottomPadding = 15.h;
    final headerHeight = 25.h;
    final itemHeight = 32.h; // CycleRecordItem的实际高度
    final maxListHeight =
        containerHeight - topPadding - bottomPadding - headerHeight;
    final actualContentHeight = itemCount * itemHeight;
    final backgroundHeight = actualContentHeight > maxListHeight
        ? maxListHeight
        : actualContentHeight;

    return Container(
        margin:
            EdgeInsets.only(left: 15.w, right: 15.w, top: 10.h, bottom: 26.h),
        width: 345.w,
        height: containerHeight,
        decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(8.r),
            color: Colors.white,
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.05),
                spreadRadius: 0,
                blurRadius: 10,
                offset: const Offset(0, 2),
              )
            ]),
        child: Stack(
          children: [
            Positioned(
              top: topPadding + headerHeight,
              right: 73.w,
              child: Container(
                width: 150.w,
                height: backgroundHeight, // 使用计算出的背景高度
                color: const Color.fromRGBO(255, 245, 247, 1),
              ),
            ),
            Positioned(
              top: topPadding,
              bottom: bottomPadding,
              left: 15.w,
              right: 10.w,
              child: Column(
                children: [
                  Container(
                    margin: EdgeInsets.only(right: 5.w),
                    height: headerHeight,
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.end,
                      children: [
                        Container(
                          margin: EdgeInsets.only(bottom: 3.h),
                          child: Text(
                            'period_analysis_day_title'.tr,
                            style: StyleConfig.otherStyle(
                                color: ColorConfig.searchTextColor,
                                fontWeight: FontWeight.w700),
                          ),
                        ),
                        const Expanded(child: SizedBox()),
                        Container(
                          margin: EdgeInsets.only(right: 10.w),
                          child: Text(
                            'period_analysis_two_widget_three'.tr,
                            style: StyleConfig.otherStyle(
                                color: const Color.fromRGBO(255, 80, 110, 1),
                                fontSize: 10),
                          ),
                        )
                      ],
                    ),
                  ),
                  Expanded(
                    child: ListView(
                      padding: EdgeInsets.zero,
                      children: List.generate(itemCount, (index) {
                        return PeriodDayItem(
                            date: '6月29日',
                            tip: 'period_analysis_one_widget_three'
                                .trArgs(['8']),
                            pColor: const Color.fromRGBO(254, 139, 172, 1),
                            pValue: index > 5 ? 7 : 1,
                            pMaxValue: 8,
                            cDaysTip: '');
                      }),
                    ),
                  )
                ],
              ),
            ),
          ],
        ));
  }

  @override
  Widget buildContent() {
    return GetBuilder<PeriodDayController>(
      init: PeriodDayController(),
      id: "period_day",
      builder: (_) {
        return _buildView();
      },
    );
  }
}
