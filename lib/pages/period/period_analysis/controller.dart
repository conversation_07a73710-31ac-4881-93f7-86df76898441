import 'package:get/get.dart';
import 'package:getx_xiaopa/routes/router.dart';

class PeriodAnalysisController extends GetxController {
  PeriodAnalysisController();

  List<int> fourPainLevels = [1, 4, 3, 2, 0, 1, 3, 2];
  List<String> fourDayLabels = [];
  List<String> fourStatusLabels = ["极多", "多", "中等", "少", "极少"];

  List<int> fivePainLevels = [1, 4, 3, 2, 0, 1, 3, 2];
  List<String> fiveDayLabels = [];
  List<String> fiveStatusLabels = ["通到极致", "非常痛", "比较痛", "轻微痛", "完全不痛"];

  @override
  void onReady() {
    super.onReady();
    for (var i = 0; i < 8; i++) {
      fourDayLabels.add('period_analysis_pain_chart_day'.trArgs(['${i + 1}']));
      fiveDayLabels.add('period_analysis_pain_chart_day'.trArgs(['${i + 1}']));
    }
    update(["period_analysis"]);
  }

  periodCycle() {
    Get.toNamed(AppRoutes.PERIOD_CYCLE);
  }

  periodDay() {
    Get.toNamed(AppRoutes.PERIOD_DAY);
  }

  periodColor() {
    Get.toNamed(AppRoutes.PERIOD_COLOR);
  }

  periodFlux() {
    Get.toNamed(AppRoutes.PERIOD_FLUX);
  }

  periodPain() {
    Get.toNamed(AppRoutes.PERIOD_PAIN);
  }
}
