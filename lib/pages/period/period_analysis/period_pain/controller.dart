import 'package:get/get.dart';

class PeriodPainController extends GetxController {
  PeriodPainController();

  List<int> painLevels = [1, 4, 3, 2, 0, 1, 3, 2];
  List<String> dayLabels = [];
  List<String> statusLabels = ["通到极致", "非常痛", "比较痛", "轻微痛", "完全不痛"];

  @override
  void onReady() {
    super.onReady();
    for (var i = 0; i < 8; i++) {
      dayLabels.add('period_analysis_pain_chart_day'.trArgs(['${i + 1}']));
    }
    update(["period_pain"]);
  }
}
