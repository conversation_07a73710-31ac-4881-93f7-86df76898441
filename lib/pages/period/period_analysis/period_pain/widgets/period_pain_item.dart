import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:getx_xiaopa/pages/period/period_analysis/period_flux/index.dart';
import 'package:getx_xiaopa/values/values.dart';

class PeriodPaintem extends StatelessWidget {
  final int index;
  final List<int> painLevels; // 0-4 对应完全不痛到痛到极致
  final List<String> dayLabels; // 第1天、第2天等
  final List<String> statusLabels;
  const PeriodPaintem(
      {super.key,
      required this.index,
      required this.painLevels,
      required this.dayLabels,
      required this.statusLabels});

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.only(top: 14.h),
      padding: EdgeInsets.only(left: 10.w, right: 10.w),
      width: 325.w,
      constraints: BoxConstraints(minHeight: 69.h),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            margin: EdgeInsets.only(left: 5.w),
            child: Text(
              'period_analysis_color_one'.trArgs(['9月26日', '今日']),
              style: StyleConfig.otherStyle(color: ColorConfig.searchTextColor),
            ),
          ),
          SizedBox(height: 12.h),
          index == 1
              ? Container(
                  margin: EdgeInsets.only(left: 5.w),
                  child: Text(
                    'period_analysis_no_data'.tr,
                    style: StyleConfig.otherStyle(
                        color: ColorConfig.shopDetailTextColor, fontSize: 14),
                  ),
                )
              : FluxChartWidget(
                  painLevels: painLevels,
                  dayLabels: dayLabels,
                  statusLabels: statusLabels),
          SizedBox(height: 16.h),
          Container(
            width: 325.w,
            height: 1.h,
            color: const Color.fromRGBO(233, 232, 233, 1),
          )
        ],
      ),
    );
  }
}
