import 'package:get/get.dart';

class PeriodFluxController extends GetxController {
  PeriodFluxController();

  List<int> painLevels = [1, 4, 3, 2, 0, 1, 3, 2];
  List<String> dayLabels = [];
  List<String> statusLabels = ["极多", "多", "中等", "少", "极少"];

  @override
  void onReady() {
    super.onReady();
    for (var i = 0; i < 8; i++) {
      dayLabels.add('period_analysis_pain_chart_day'.trArgs(['${i + 1}']));
    }
    update(["period_flux"]);
  }
}
