import 'package:flutter/cupertino.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:getx_xiaopa/extension/images.dart';
import 'package:getx_xiaopa/values/values.dart';

class ThreeItem extends StatefulWidget {
  const ThreeItem({super.key});

  @override
  State<ThreeItem> createState() => _ThreeItemState();
}

class _ThreeItemState extends State<ThreeItem> {
  final List<String> _image = [];
  final List<String> _str = [
    'period_analysis_three_item_one'.tr,
    'period_analysis_three_item_two'.tr,
    'period_analysis_three_item_three'.tr,
    'period_analysis_three_item_four'.tr,
    'period_analysis_three_item_five'.tr
  ];

  @override
  void initState() {
    for (var i = 0; i < 5; i++) {
      _image.add('assets/images/period_analysis_three_image_0${i + 1}.png');
    }
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 74.h,
      child: ListView(
        scrollDirection: Axis.horizontal,
        children: List.generate(5, (index) {
          return Container(
            margin: EdgeInsets.only(left: index > 0 ? 20.w : 0),
            child: Column(
              children: [
                Text(
                  'period_analysis_pain_chart_day'.trArgs(['${index + 1}']),
                  style: StyleConfig.otherStyle(
                      color: ColorConfig.authenticationTextColor,
                      fontSize: 12,
                      fontWeight: FontWeight.w500),
                ),
                Images(
                  path: _image[index],
                  width: 40.r,
                  height: 40.r,
                ),
                Text(
                  _str[index],
                  style: StyleConfig.otherStyle(
                      color: ColorConfig.shopDetailTextColor,
                      fontSize: 12,
                      fontWeight: FontWeight.w500),
                ),
              ],
            ),
          );
        }),
      ),
    );
  }
}
