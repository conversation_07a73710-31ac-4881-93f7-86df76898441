import 'dart:math';
import 'package:fl_chart/fl_chart.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:getx_xiaopa/extension/images.dart';
import 'package:getx_xiaopa/r.dart';
import 'package:getx_xiaopa/values/values.dart';

class TwoItem extends StatefulWidget {
  const TwoItem({super.key});

  @override
  State<TwoItem> createState() => _TwoItemState();
}

class _TwoItemState extends State<TwoItem> {
  late double _maxValue;
  late double _topPadding;
  late double _maxY;
  final List<Map> _dataList = [
    {"key": "2/26", "num": 10},
    {"key": "3/26", "num": 7},
    {"key": "4/29", "num": 1},
    {"key": "5/29", "num": 7},
    {"key": "6/29", "num": 7},
    {"key": "7/29", "num": 0}
  ];
  late final List<String> _keys = [];
  late final List<int> _values = [];

  @override
  void initState() {
    super.initState();

    _keys.clear();
    _values.clear();

    for (var data in _dataList) {
      _keys.add(data["key"]);
      _values.add(data["num"]);
    }

    _maxValue = _values.reduce((a, b) => a > b ? a : b).toDouble();
    _topPadding = _maxValue * 0.2; // 留20%顶部空间
    _maxY = _maxValue + _topPadding;
  }

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: 313.w,
      height: 93.h,
      child: Stack(
        children: [
          Positioned(
            top: 68.h,
            width: 313.w,
            height: 2.h,
            child: Images(
              path: R.period_analysis_image_04_png,
              boxFit: BoxFit.fill,
            ),
          ),
          Positioned(
            top: 15.h,
            width: 313.w,
            height: 75.h,
            child: Stack(
              children: [
                BarChart(
                  ///不加这个有动画会报错。加这个没有动画不会报错
                  // duration: Duration.zero,
                  BarChartData(
                    maxY: _maxY,
                    barGroups: _values.asMap().entries.map((entry) {
                      int index = entry.key;
                      int value = entry.value;
                      return BarChartGroupData(
                        x: index,
                        barRods: [
                          BarChartRodData(
                              toY: value.toDouble() == 0 ? 4 : value.toDouble(),
                              width: 13.w,
                              borderRadius: BorderRadius.circular(2.r),
                              color: value.toDouble() == 0
                                  ? Colors.transparent
                                  : value.toDouble() >= 8
                                      ? const Color.fromRGBO(255, 211, 9, 1)
                                      : value.toDouble() >= 2
                                          ? const Color.fromRGBO(
                                              254, 139, 172, 1)
                                          : const Color.fromRGBO(
                                              255, 211, 9, 1)),
                        ],
                        showingTooltipIndicators: value > 0 ? [0] : [],
                      );
                    }).toList(),
                    titlesData: FlTitlesData(
                      bottomTitles: AxisTitles(
                        sideTitles: SideTitles(
                          showTitles: true,
                          getTitlesWidget: (value, _) {
                            return Padding(
                              padding: EdgeInsets.only(top: 6.h),
                              child: Text(
                                _keys[value.toInt()],
                                style: StyleConfig.otherStyle(
                                    color: ColorConfig.authenticationTextColor,
                                    fontSize: 12),
                              ),
                            );
                          },
                        ),
                      ),
                      leftTitles: const AxisTitles(
                          sideTitles: SideTitles(showTitles: false)),
                      topTitles: const AxisTitles(
                          sideTitles: SideTitles(showTitles: false)),
                      rightTitles: const AxisTitles(
                          sideTitles: SideTitles(showTitles: false)),
                    ),
                    barTouchData: BarTouchData(
                      enabled: true,
                      touchTooltipData: BarTouchTooltipData(
                        tooltipPadding: EdgeInsets.zero,
                        tooltipMargin: 0.h,
                        getTooltipColor: (group) => Colors.transparent,
                        getTooltipItem: (group, groupIndex, rod, rodIndex) {
                          return BarTooltipItem(
                            '${rod.toY.toInt()}',
                            StyleConfig.otherStyle(
                                color: ColorConfig.authenticationTextColor,
                                fontWeight: FontWeight.w500,
                                fontSize: 12),
                          );
                        },
                      ),
                    ),
                    gridData: const FlGridData(show: false),
                    borderData: FlBorderData(show: false),
                  ),
                ),
                CustomPaint(
                  size: Size(313.w, 75.h),
                  painter: DashedBarPainter(
                    values: _values,
                    barWidth: 13.w,
                    maxY: _maxY,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

class DashedBarPainter extends CustomPainter {
  final List<int> values;
  final double barWidth;
  final double maxY;

  DashedBarPainter({
    required this.values,
    required this.barWidth,
    required this.maxY,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = const Color.fromRGBO(254, 139, 172, 1)
      ..strokeWidth = 1
      ..style = PaintingStyle.stroke;

    final barSpacing = size.width / values.length - 3;
    final chartHeight = size.height - 20; // 留出底部标题空间

    for (int i = 0; i < values.length; i++) {
      if (values[i] == 0) {
        final centerX = barSpacing * i + barSpacing / 2;
        final barHeight = chartHeight * 0.25 * 2; // 虚线矩形高度
        final top = chartHeight - barHeight - 2;

        final rect = Rect.fromLTWH(
          centerX - barWidth / 2,
          top,
          barWidth,
          barHeight,
        );

        _drawDashedRect(canvas, rect, paint);
      }
    }
  }

  void _drawDashedRect(Canvas canvas, Rect rect, Paint paint) {
    const dashWidth = 3.0;
    const dashSpace = 3.0;

    // 绘制顶边
    _drawDashedLine(
        canvas, rect.topLeft, rect.topRight, paint, dashWidth, dashSpace);
    // 绘制右边
    _drawDashedLine(
        canvas, rect.topRight, rect.bottomRight, paint, dashWidth, dashSpace);
    // 绘制底边
    _drawDashedLine(
        canvas, rect.bottomRight, rect.bottomLeft, paint, dashWidth, dashSpace);
    // 绘制左边
    _drawDashedLine(
        canvas, rect.bottomLeft, rect.topLeft, paint, dashWidth, dashSpace);
  }

  void _drawDashedLine(Canvas canvas, Offset start, Offset end, Paint paint,
      double dashWidth, double dashSpace) {
    final dx = end.dx - start.dx;
    final dy = end.dy - start.dy;
    final distance = sqrt(dx * dx + dy * dy);
    final dashCount = (distance / (dashWidth + dashSpace)).floor();
    final direction = Offset(dx / distance, dy / distance);

    for (int i = 0; i < dashCount; i++) {
      final currentLength = i * (dashWidth + dashSpace);
      final offsetStart = start + direction * currentLength;
      final offsetEnd = offsetStart + direction * dashWidth;
      canvas.drawLine(offsetStart, offsetEnd, paint);
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
