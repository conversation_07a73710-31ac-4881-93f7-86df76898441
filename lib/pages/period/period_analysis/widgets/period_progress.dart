import 'package:flutter/material.dart';
import 'dart:math';

import 'package:getx_xiaopa/values/values.dart';

enum ProgressMode { circular, linear }

class PeriodProgress extends StatefulWidget {
  final int segmentCount;
  final List<Color> segmentColors;
  final Color backgroundColor;
  final double strokeWidth;
  final double size;
  final Duration duration;
  final ProgressMode mode; // 新增
  final double? maxValue; // 可选，直线模式下使用
  final double? value;
  final String? rightText; // 新增右边文字参数

  const PeriodProgress(
      {super.key,
      required this.segmentCount,
      required this.segmentColors,
      this.backgroundColor = Colors.transparent,
      this.strokeWidth = 10.0,
      this.size = 100.0,
      this.duration = const Duration(milliseconds: 500),
      this.mode = ProgressMode.circular, // 默认是圆形
      this.maxValue,
      this.value,
      this.rightText})
      : assert(segmentColors.length == segmentCount, '颜色数组的长度跟segmentCount要一直'),
        assert(
            mode == ProgressMode.circular ||
                (maxValue != null && value != null),
            '直线模式下必须传value和maxValue');

  @override
  State<PeriodProgress> createState() => _PeriodProgressState();
}

class _PeriodProgressState extends State<PeriodProgress>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(vsync: this, duration: widget.duration);
    _animation = CurvedAnimation(parent: _controller, curve: Curves.easeOut);
    _controller.forward();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animation,
      builder: (_, __) {
        return CustomPaint(
          size: Size.square(widget.size),
          painter: _SegmentedCircularPainter(
              segmentCount: widget.segmentCount,
              segmentColors: widget.segmentColors,
              backgroundColor: widget.backgroundColor,
              strokeWidth: widget.strokeWidth,
              progress: _animation.value,
              mode: widget.mode,
              value: widget.value ?? 0,
              maxValue: widget.maxValue ?? 0,
              rightText: widget.rightText),
        );
      },
    );
  }
}

class _SegmentedCircularPainter extends CustomPainter {
  final int segmentCount;
  final List<Color> segmentColors;
  final Color backgroundColor;
  final double strokeWidth;
  final double progress; // 0.0 ~ 1.0 动画进度
  final ProgressMode mode;
  final double maxValue; // 可选，直线模式下使用
  final double value;
  final String? rightText; // 新增文字参数

  _SegmentedCircularPainter(
      {required this.segmentCount,
      required this.segmentColors,
      required this.backgroundColor,
      required this.strokeWidth,
      required this.progress,
      required this.mode,
      required this.maxValue,
      required this.value,
      this.rightText});

  @override
  void paint(Canvas canvas, Size size) {
    if (mode == ProgressMode.circular) {
      _paintCircular(canvas, size);
    }
    if (mode == ProgressMode.linear) {
      _paintLinear(canvas, size);
    }
  }

  void _paintCircular(Canvas canvas, Size size) {
    final center = size.center(Offset.zero);
    final radius = (size.shortestSide - strokeWidth) / 2;
    final arcRect = Rect.fromCircle(center: center, radius: radius);

    final bgPaint = Paint()
      ..color = backgroundColor
      ..strokeWidth = strokeWidth
      ..style = PaintingStyle.stroke;

    canvas.drawCircle(center, radius, bgPaint);

    final segmentAngle = 2 * pi / segmentCount;
    final maxDrawn =
        (progress * segmentCount).clamp(0.0, segmentCount.toDouble());

    final paint = Paint()
      ..strokeWidth = strokeWidth
      ..style = PaintingStyle.stroke
      ..strokeCap = StrokeCap.butt;

    for (int i = 0; i < segmentCount; i++) {
      final startAngle = -pi / 2 + i * segmentAngle;

      if (i + 1 <= maxDrawn) {
        // 完整 segment
        paint.color = segmentColors[i];
        canvas.drawArc(arcRect, startAngle, segmentAngle, false, paint);
      } else if (i < maxDrawn) {
        // 部分 segment
        final sweep = (maxDrawn - i) * segmentAngle;
        paint.color = segmentColors[i];
        canvas.drawArc(arcRect, startAngle, sweep, false, paint);
      } else {
        break;
      }
    }
  }

  void _paintLinear(Canvas canvas, Size size) {
    final width = size.width;
    final height = strokeWidth;
    final top = (size.height - strokeWidth) / 2;

    final bgPaint = Paint()
      ..color = backgroundColor
      ..strokeWidth = height
      ..style = PaintingStyle.stroke
      ..strokeCap = StrokeCap.butt;

    canvas.drawLine(
      Offset(0, top + height / 2),
      Offset(width, top + height / 2),
      bgPaint,
    );

    // 有效宽度计算
    final usableValue = (value / maxValue).clamp(0.0, 1.0);
    final coloredLength = usableValue * width * progress;

    if (coloredLength > 0) {
      final paint = Paint()
        ..color = segmentColors[0]
        ..style = PaintingStyle.fill;

      final rect = RRect.fromRectAndCorners(
        Rect.fromLTWH(0, top, coloredLength, height),
        topRight: const Radius.circular(4),
        bottomRight: const Radius.circular(4),
      );

      canvas.drawRRect(rect, paint);
    }

    // 绘制右边文字
    if (rightText != null && rightText!.isNotEmpty) {
      final textPainter = TextPainter(
        text: TextSpan(
          text: rightText,
          style: StyleConfig.otherStyle(
              color: Colors.white, fontSize: 12, fontWeight: FontWeight.w500),
        ),
        textDirection: TextDirection.ltr,
      );
      textPainter.layout();

      final textX = coloredLength - textPainter.width - 6; // 右边距离8像素

      textPainter.paint(canvas, Offset(textX, 1));
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}
