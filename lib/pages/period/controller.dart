import 'package:get/get.dart';
import 'package:getx_xiaopa/base/base.dart';
import 'package:getx_xiaopa/pages/period/index.dart';
import 'package:getx_xiaopa/pages/period/widgets/period_status_bottom.dart';
import 'package:getx_xiaopa/routes/router.dart';
import 'package:getx_xiaopa/utils/utils.dart';

class PeriodController extends BaseCommonController {
  PeriodController();

  bool isSelect = false;

  List<int> itemOneList = [];
  int sustainDay = 6;
  int sustainSelectDay = 6;

  List<int> itemTwoList = [];
  int intervalDay = 17;
  int intervalSelectDay = 17;

  DateTime sDate = DateTime.now();

  bool isPeriod = false;

  ///1:月经期 2:卵泡期 3:排卵日 4:黄体期
  int phase = 1;

  ///经期的天数
  int periodDay = 3;

  String periodStatus = '浅红色 极少 完全不痛';

  String oneString = '';

  PeriodCycleInfoModel cycleInfoModel = PeriodCycleInfoModel();

  @override
  void initData() {
    isSelect = PeriodStore.to.isSelect;
    if (isSelect) {
      for (var i = 3; i <= 15; i++) {
        itemOneList.add(i);
      }

      for (var i = 17; i <= 60; i++) {
        itemTwoList.add(i);
      }

      netState = NetState.dataSuccessState;
      update(["period"]);
    } else {
      _initData();
    }
  }

  @override
  void onHidden() {}

  _initData() async {
    oneString = TimeUtil.getLocaleTimeWithWeek(DateTime.now());

    await PeriodStore.to.persionCycleInfo();
    cycleInfoModel = PeriodStore.to.periodCycleInfoModel;

    isPeriod = !cycleInfoModel.periodIsEnd!;
    phase = cycleInfoModel.phase ?? 1;

    PeriodUtils.getPersionColor(model: cycleInfoModel, cTime: DateTime.now());

    netState = NetState.dataSuccessState;
    update(["period"]);
  }

  selectOneAction() {
    if (sustainSelectDay == 0) return;
    Get.to(() => SettingTwo());
  }

  selectTwoAction() {
    if (intervalSelectDay == 0) return;
    Get.to(() => SettingThree());
  }

  selectThreeAction() {
    Get.to(() => SettingComplete());
  }

  selectCompleteAction() async {
    await PeriodStore.to.persiodSubmitCreate(
        cycle: intervalSelectDay,
        period: sustainSelectDay,
        time: TimeUtil.toIso8601String(sDate));
    await PeriodStore.to.persiodSubmitInfo();
    isSelect = PeriodStore.to.isSelect;
    if (!isSelect) {
      Get.until((route) => Get.currentRoute == AppRoutes.PERIOD);

      ///请求数据
      _initData();
    }
    update(["period"]);
  }

  toHistoryPage() {
    Get.toNamed(AppRoutes.PERIOD_SETTING);
  }

  toAnalysisPage() {
    Get.toNamed(AppRoutes.PERIOD_ANALYSIS);
  }

  isPeriodAction(value) {
    isPeriod = value;
    update(["period"]);
  }

  periodStatusaction() {
    Get.bottomSheet(PeriodStatusBottom(
      onTap: (value) {
        periodStatus = value;
        update(["period"]);
      },
    ), isScrollControlled: true);
  }
}
