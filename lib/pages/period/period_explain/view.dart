import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:getx_xiaopa/base/base.dart';
import 'package:getx_xiaopa/extension/images.dart';
import 'package:getx_xiaopa/extension/widget_extension.dart';
import 'package:getx_xiaopa/r.dart';
import 'package:getx_xiaopa/values/values.dart';

import 'index.dart';

class PeriodExplainPage extends BaseCommonView<PeriodExplainController> {
  PeriodExplainPage({super.key});

  @override
  String? get navTitle => "period_explain_title".tr;

  @override
  TextStyle? get navTitleStyle =>
      StyleConfig.blackStyle(fontSize: 18, fontWeight: FontWeight.w600);

  @override
  Color? get navColor => Colors.transparent;

  @override
  double? get leftWidth => 35.w;

  @override
  Widget? get leftButton => Padding(
        padding: EdgeInsets.only(left: 15.w),
        child: Images(
          path: R.back_png,
          width: 20.w,
          height: 20.h,
          scale: 2.8,
        ).inkWell(() => Get.back()),
      );

  @override
  Color? get contentColor => const Color.fromRGBO(249, 249, 249, 1);

  // 主视图
  Widget _buildView() {
    return Container(
      margin: EdgeInsets.only(left: 15.w, right: 15.w),
      child: SingleChildScrollView(
        child: Column(
          children: List.generate(controller.tipImage.length, (index) {
            return Container(
              margin: EdgeInsets.only(top: 10.h),
              padding: EdgeInsets.only(
                  left: 16.w, right: 16.w, top: 19.h, bottom: 18.h),
              width: 345.w,
              decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(8.r),
                  color: Colors.white,
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.05),
                      // 阴影颜色和透明度
                      spreadRadius: 0,
                      // 阴影扩散范围
                      blurRadius: 10,
                      // 阴影模糊程度
                      offset: const Offset(0, 2),
                      // 阴影偏移量（水平，垂直）
                    )
                  ]),
              child: Column(
                children: [
                  Row(
                    children: [
                      Images(
                        path: controller.tipImage[index],
                        width: 26.w,
                        height: 18.h,
                      ),
                      SizedBox(width: 8.w),
                      Text(
                        controller.tipStr[index],
                        style: StyleConfig.otherStyle(
                            color: ColorConfig.searchTextColor,
                            fontWeight: FontWeight.w600),
                      )
                    ],
                  ),
                  SizedBox(height: 10.h),
                  Text(
                    controller.tipsExplain[index],
                    style: StyleConfig.otherStyle(
                        color: ColorConfig.shopDetailTextColor, fontSize: 14),
                  )
                ],
              ),
            );
          }),
        ),
      ),
    );
  }

  @override
  Widget buildContent() {
    return GetBuilder<PeriodExplainController>(
      init: PeriodExplainController(),
      id: "period_explain",
      builder: (_) {
        return _buildView();
      },
    );
  }
}
