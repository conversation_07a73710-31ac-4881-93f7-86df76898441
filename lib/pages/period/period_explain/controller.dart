import 'package:get/get.dart';

class PeriodExplainController extends GetxController {
  PeriodExplainController();

  List<String> tipImage = [
    'assets/images/period_setting_image_01.png',
    'assets/images/period_setting_image_02.png',
    'assets/images/period_setting_image_03.png',
    'assets/images/period_setting_image_04.png',
    'assets/images/period_setting_image_05.png',
  ];

  List<String> tipStr = [
    "period_three_widget_two".tr,
    "period_three_widget_four".tr,
    "period_three_widget_three".tr,
    "period_three_widget_one".tr,
    "period_setting_tip_one".tr
  ];

  List<String> tipsExplain = [
    'period_explain_one'.tr,
    'period_explain_two'.tr,
    'period_explain_three'.tr,
    'period_explain_three'.tr,
    'period_explain_five'.tr
  ];

  @override
  void onReady() {
    super.onReady();
    update(["period_explain"]);
  }
}
