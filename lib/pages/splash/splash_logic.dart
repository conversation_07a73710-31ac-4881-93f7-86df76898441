import 'dart:io';
import 'dart:math';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:getx_xiaopa/base/base.dart';
import 'package:getx_xiaopa/network/http_client.dart';
import 'package:getx_xiaopa/network/model/request_body.dart';
import 'package:getx_xiaopa/network/model/result.dart';
import 'package:getx_xiaopa/pages/console/console_schedule/utils/schedule_store.dart';
import 'package:getx_xiaopa/pages/diary/utils/diary_store.dart';
import 'package:getx_xiaopa/pages/mine/widget/update_dialog.dart';
import 'package:getx_xiaopa/routes/router.dart';
import 'package:getx_xiaopa/storage/storage.dart';
import 'package:getx_xiaopa/widget/widget.dart';

import 'splash_state.dart';

class SplashLogic extends BaseCommonController {
  @override
  final SplashState state = SplashState();

  @override
  void initData() {
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      state.isFirst = ConfigStore.to.isFirst;

      ///第一次打开app  弹出隐私政策
      if (state.isFirst) {
        _firstDialog();
      } else {
        _versionAction();
      }
    });
  }

  @override
  void onHidden() {}

  _getAd() async {
    state.adUrl = await _imagePath();
    state.isStarTimer = true;
    netState = NetState.dataSuccessState;
    update(["splash_view"]);
  }

  Future<String> _imagePath() async {
    state.isRequestInfo = await UserStore.to.getUserInfo(isSaveToken: true);
    if (!state.isRequestInfo) return "";

    await CommonStore.to.getBaseAd();

    if (UserStore.to.userInfo.birthday?.isNotEmpty ?? false) {
      DateTime date =
          DateTime.parse(UserStore.to.userInfo.birthday?.split(" ")[0] ?? "");
      DateTime now = DateTime.now();
      if (date.month == now.month && date.day == now.day) {
        if (CommonStore.to.baseBrithdayAd.isEmpty) return "";
        int a = Random().nextInt(CommonStore.to.baseBrithdayAd.length);

        return CommonStore.to.baseBrithdayAd[a].picture ?? "";
      }
      
    }

    if (CommonStore.to.baseSpecialAd.isNotEmpty) {
      int a = Random().nextInt(CommonStore.to.baseSpecialAd.length);
      return CommonStore.to.baseSpecialAd[a].picture ?? "";
    }

    if (CommonStore.to.baseAd.isEmpty) return "";
    int a = Random().nextInt(CommonStore.to.baseAd.length);
    return CommonStore.to.baseAd[a].picture ?? "";
  }

  _firstDialog() {
    String mContext = 'splash_first_tips'.tr;
    Get.dialog(
      CommonDialog(
        isPop: false,
        mTitle: "splash_first_title".tr,
        mContent: mContext,
        mContextAlign: TextAlign.center,
        mHeight: 283,
        mContextSize: 12,
        mCancel: "splash_first_cancel".tr,
        mConfirm: "splash_first_comfirm".tr,
        onTapClick: (string) {
          if (string == "login_protocol_two".tr) {
            Get.to(const WebView(), arguments: ConfigStore.to.privacyPolicy);
          }
          if (string == "login_protocol_one".tr) {
            Get.to(const WebView(), arguments: ConfigStore.to.userAgreement);
          }
        },
        confirmAction: () {
          state.isFirst = false;
          ConfigStore.to.setIsFirst(false);

          ///校验app版本
          _versionAction();
        },
        cancelAction: () {
          // 退出app
          exit(0);
        },
        closeAction: () {
          // 退出app
          exit(0);
        },
      ),
    );
  }

  _versionAction() async {
    var map = RequestBody(filters: [
      Filter(name: 'type', expr: '=', value: Platform.isAndroid ? '1' : '2'),
      Filter(expr: "=", name: "is_publish", value: 1)
    ]).toJson();
    Result result = await http.getVersion(map);
    if (result.code == 0) {
      state.vModel = result.data;
      ConfigStore.to.downUrl = state.vModel.url ?? '';
      if (state.vModel.code != UserStore.to.version) {
        if (state.vModel.code!.isEmpty) return;
        List<String> sCode = state.vModel.code.toString().split('.');
        List<String> lCode = UserStore.to.version.split('.');
        if (int.parse(sCode[0]) > int.parse(lCode[0]) ||
            int.parse(sCode[1]) > int.parse(lCode[1])) {
          ///强制更新
          _updateDialog(false);
          return;
        } else if (int.parse(sCode[0]) == int.parse(lCode[0]) &&
            int.parse(sCode[1]) == int.parse(lCode[1]) &&
            int.parse(sCode[2]) > int.parse(lCode[2])) {
          ///自愿更新
          _updateDialog(true);
        } else {
          // CToast.showToast("APP已是最新版本!");
          _getAd();
        }
      } else {
        _getAd();
      }
    }
  }

  _updateDialog(isClose) {
    Get.dialog(UpdateDialog(
            upDateTitle: state.vModel.name,
            upDateContent: state.vModel.logs,
            mUpdateUrl: state.vModel.url,
            iosUrl: state.vModel.redirect,
            mUpdateType: state.vModel.type,
            isClose: isClose))
        .then((value) {
      _getAd();
    });
  }

  goPageAction() async {
    if (state.isRequestInfo) {
      ///前面请求成功了，直接执行下面逻辑
      await UserStore.to.deviceItems(isChangeTheme: true);
      if (UserStore.to.deviceList.isNotEmpty) {
        DiaryStore.to.getDiaryItems();
        ScheduleStore.to.init();
      }
      await UserStore.to.getAccountItems();
      Get.offAndToNamed(AppRoutes.APPLICATION);
    } else {
      bool flag = await UserStore.to.getUserInfo(isSaveToken: true);
      if (flag) {
        await UserStore.to.deviceItems(isChangeTheme: true);
        if (UserStore.to.deviceList.isNotEmpty) {
          DiaryStore.to.getDiaryItems();
          ScheduleStore.to.init();
        }
        await UserStore.to.getAccountItems();
        Get.offAndToNamed(AppRoutes.APPLICATION);
      } else {
        Get.offAndToNamed(AppRoutes.LOGIN);
      }
    }
  }
}
