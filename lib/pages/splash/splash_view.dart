import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:getx_xiaopa/base/base.dart';
import 'package:getx_xiaopa/extension/images.dart';
import 'package:getx_xiaopa/network/http_client.dart';
import 'package:getx_xiaopa/r.dart';
import 'package:getx_xiaopa/storage/storage.dart';
import 'package:getx_xiaopa/widget/widget.dart';

import 'splash_logic.dart';

class SplashPage extends BaseCommonView {
  SplashPage({super.key});

  final logic = Get.put(SplashLogic());
  final state = Get.find<SplashLogic>().state;

  @override
  bool? get isHiddenNav => true;

  @override
  Widget buildContent() {
    return GetBuilder<SplashLogic>(
      id: "splash_view",
      builder: (_) => createCommonView(
        logic,
        (_) {
          return Stack(
            alignment: Alignment.topCenter,
            children: [
              Container(
                constraints: const BoxConstraints.expand(),
                child: Images(
                  path: fileUrl(state.adUrl.isEmpty
                      ? ConfigStore.to.getLocaleCode() == "zh"
                          ? R.splash_bg_02_png
                          : R.splash_bg_02_en_png
                      : state.adUrl),
                  placeholder: R.splash_bg_png,
                  boxFit: BoxFit.fill,
                ),
              ),
              Positioned(
                top: 5.h,
                right: 5.w,
                child: Container(
                    child: state.isStarTimer
                        ? CountDownWidget(
                            countDownTime: state.timeCount,
                            onTop: () => logic.goPageAction(),
                          )
                        : const SizedBox()),
              ),
            ],
          );
        },
        initBuilder: () {
          return Container(
            decoration: const BoxDecoration(
                image: DecorationImage(
                    image: AssetImage(R.splash_bg_png), fit: BoxFit.fill)),
          );
        },
      ),
    );
  }
}
