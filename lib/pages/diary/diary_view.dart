import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:getx_xiaopa/base/base.dart';
import 'package:getx_xiaopa/extension/images.dart';
import 'package:getx_xiaopa/extension/widget_extension.dart';
import 'package:getx_xiaopa/network/http_client.dart';
import 'package:getx_xiaopa/pages/diary/widget/segment_circular_progress.dart';
import 'package:getx_xiaopa/r.dart';
import 'package:getx_xiaopa/values/values.dart';
import 'package:getx_xiaopa/widget/widget.dart';

import 'diary_logic.dart';
import 'diary_state.dart';

class DiaryPage extends BaseCommonView {
  DiaryPage({super.key});

  final DiaryLogic logic = Get.put(DiaryLogic());
  final DiaryState state = Get.find<DiaryLogic>().state;

  @override
  bool? get isHiddenNav => true;

  @override
  bool? get extendBodyBehindAppBar => true;

  @override
  Widget buildContent() {
    return GetBuilder<DiaryLogic>(
      id: "diary_view",
      builder: (_) {
        return ThemeContainerImage(
          fileName: 'diary_bg.png',
          fit: BoxFit.fill,
          conWidget: 1.sw,
          conHeight: 1.sh,
          child: SingleChildScrollView(
            physics: const ClampingScrollPhysics(),
            child: Column(
              children: [
                Container(
                    margin: EdgeInsets.only(top: 46.h, right: 8.w),
                    alignment: Alignment.centerRight,
                    child: ThemeImagePath(
                      fileName: "diary_share.png",
                      imgWidget: 50.r,
                      imgHeight: 50.r,
                    ).inkWell(() => logic.toSharePage())),
                ThemeContainerImage(
                  fileName: 'diary_bg_02.png',
                  fit: BoxFit.fill,
                  margin: EdgeInsets.only(top: 2.h),
                  conWidget: 341.w,
                  conHeight: 107.h,
                  child: Row(
                    children: [
                      SizedBox(width: 28.w),
                      SegmentedCircularProgress(
                        size: 44.r,
                        strokeWidth: 6.r,
                        segmentCount: state.segmentCount,
                        segmentColors: state.segmentColors,
                      ),
                      SizedBox(width: 11.w),
                      Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            "diary_record".tr,
                            style: StyleConfig.otherStyle(
                                color: const Color.fromRGBO(69, 67, 74, 1),
                                fontWeight: FontWeight.w500),
                          ),
                          SizedBox(height: 2.h),
                          SizedBox(
                            width: 142.w,
                            child: Text(
                              "diary_record_tips".tr,
                              maxLines: 2,
                              overflow: TextOverflow.ellipsis,
                              style: StyleConfig.otherStyle(
                                  color: ColorConfig.shopDetailTextColor,
                                  fontWeight: FontWeight.w400,
                                  fontSize: 12),
                            ),
                          )
                        ],
                      ),
                      SizedBox(width: 19.w),
                      Container(
                        width: 69.w,
                        height: 32.h,
                        decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(100.r),
                            color: const Color.fromRGBO(40, 39, 46, 1)),
                        child: Center(
                          child: ThemeText(
                            dataStr: "diary_check".tr,
                            keyName: "textColor",
                            fontSize: 12,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ).inkWell(() => logic.toRecordPage())
                    ],
                  ),
                ),
                Container(
                  margin: EdgeInsets.only(left: 40.w, top: 25.h),
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      ThemeImagePath(
                        fileName: 'task_achievement_image.png',
                        imgWidget: 23.r,
                        imgHeight: 23.r,
                      ),
                      Container(
                        margin: EdgeInsets.only(left: 4.w),
                        width: 279.w,
                        alignment: Alignment.centerLeft,
                        child: SingleChildScrollView(
                          child: Text(
                            state.quote,
                            style: StyleConfig.otherStyle(
                                color: ColorConfig.searchTextColor,
                                fontSize: 14,
                                fontWeight: FontWeight.w500),
                          ),
                        ),
                      )
                    ],
                  ),
                ),
                Container(
                  margin: EdgeInsets.only(top: 20.h, bottom: 20.h),
                  width: 315.w,
                  height: 427.h,
                  child: Stack(
                    children: [
                      Positioned(
                        top: 42.h,
                        width: 315.w,
                        height: 385.h,
                        child: Container(
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(14.r),
                          ),
                          clipBehavior: Clip.antiAlias,
                          child: Images(
                            path: state.moodImage.isEmpty
                                ? R.diary_image_png
                                : fileUrl(state.moodImage),
                            boxFit: BoxFit.fill,
                          ),
                        ),
                      ),
                      Images(
                        path: state.moodTopImage == 0
                            ? R.diary_image_01_png
                            : R.diary_image_02_png,
                        width: 315.w,
                        height: 85.h,
                      ),
                    ],
                  ),
                )
              ],
            ),
          ),
        );
      },
    );
  }
}
