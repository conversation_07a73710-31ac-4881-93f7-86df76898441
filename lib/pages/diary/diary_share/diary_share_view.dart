import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:getx_xiaopa/base/base.dart';
import 'package:getx_xiaopa/extension/images.dart';
import 'package:getx_xiaopa/extension/widget_extension.dart';
import 'package:getx_xiaopa/network/http_client.dart';
import 'package:getx_xiaopa/r.dart';
import 'package:getx_xiaopa/storage/storage.dart';
import 'package:getx_xiaopa/values/colors.dart';
import 'package:getx_xiaopa/values/style.dart';
import 'package:getx_xiaopa/widget/widget.dart';
import 'package:qr_flutter/qr_flutter.dart';

import 'diary_share_logic.dart';
import 'diary_share_state.dart';

class DiarySharePage extends BaseCommonView {
  DiarySharePage({super.key});

  final DiaryShareLogic logic = Get.put(DiaryShareLogic());
  final DiaryShareState state = Get.find<DiaryShareLogic>().state;

  @override
  Color? get navColor => Colors.transparent;

  @override
  TextStyle? get navTitleStyle =>
      StyleConfig.blackStyle(fontSize: 18, fontWeight: FontWeight.w500);

  @override
  double? get leftWidth => 35.w;

  @override
  Widget? get leftButton => Padding(
        padding: EdgeInsets.only(left: 15.w),
        child: Images(
          path: R.back_png,
          width: 20.w,
          height: 20.h,
          scale: 2.8,
        ).inkWell(() {
          CToast.dismiss();
          Get.back();
        }),
      );

  @override
  bool? get extendBodyBehindAppBar => true;

  @override
  Widget buildContent() {
    return GetBuilder<DiaryShareLogic>(
      id: "diary_share_view",
      builder: (_) {
        return ThemeContainerImage(
          fileName: 'diary_share_bg.png',
          fit: BoxFit.fill,
          conWidget: 1.sw,
          conHeight: 1.sh,
          child: Column(
            children: [
              Container(
                margin: EdgeInsets.only(top: 132.h),
                width: 299.w,
                height: 400.h,
                decoration: BoxDecoration(
                    borderRadius:
                        BorderRadius.circular(state.isInit ? 10.r : 0.r),
                    boxShadow: const [
                      BoxShadow(
                        color: Color.fromRGBO(209, 104, 24, 0.16),
                        // 阴影颜色和透明度
                        spreadRadius: 0,
                        // 阴影扩散范围
                        blurRadius: 16,
                        // 阴影模糊程度
                        offset: Offset(0, 2),
                        // 阴影偏移量（水平，垂直）
                      )
                    ]),
                clipBehavior: Clip.hardEdge,
                child: RepaintBoundary(
                  key: state.repaintKey,
                  child: SizedBox(
                    width: 299.w,
                    height: 400.h,
                    child: Stack(
                      children: [
                        Positioned(
                          top: 0.h,
                          left: 5.w,
                          right: 5.w,
                          child: Images(
                            path: state.image.isNotEmpty
                                ? fileUrl(state.image)
                                : R.diary_image_png,
                            width: 280.w,
                            height: 341.h,
                            boxFit: BoxFit.cover,
                          ),
                        ),
                        Positioned.fill(
                          child: Images(
                            path: ConfigStore.to.getLocaleCode() == "zh"
                                ? R.diary_share_image_bg_png
                                : R.diary_share_image_bg_en_png,
                            boxFit: BoxFit.fill,
                          ),
                        ),
                        Positioned(
                          top: 305.h,
                          left: 35.w,
                          right: 85.w,
                          child: Text(
                            state.shareStr,
                            style: StyleConfig.otherStyle(
                                color: ColorConfig.searchTextColor,
                                fontSize: 12,
                                fontWeight: FontWeight.w600,
                                height: 1.2),
                          ),
                        ),
                        Positioned(
                          bottom: 11.h,
                          left: 19.w,
                          child: QrImageView(
                            data: ConfigStore.to.officialDownUrl,
                            size: 26,
                            backgroundColor: Colors.white,
                            padding: EdgeInsets.zero,
                          ),
                        )
                      ],
                    ),
                  ),
                ),
              ),
              Container(
                margin: EdgeInsets.only(top: 55.h),
                width: 200.w,
                height: 48.h,
                decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(24.r),
                    color: const Color.fromRGBO(40, 39, 46, 1)),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    ThemeImagePath(
                      fileName: 'task_share_friends.png',
                      imgWidget: 20.r,
                      imgHeight: 20.r,
                    ),
                    SizedBox(width: 5.w),
                    ThemeText(
                      dataStr: "diary_share_btn".tr,
                      keyName: "textColor",
                      fontWeight: FontWeight.w500,
                    )
                  ],
                ),
              ).inkWell(() => logic.shareAction(3)),
              Container(
                margin: EdgeInsets.only(top: 23.h, left: 40.w, right: 40.w),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceAround,
                  children: List.generate(state.shareList.length, (index) {
                    return Column(
                      children: [
                        Images(
                          path: state.shareList[index].imagePath,
                          width: 40.r,
                          height: 40.r,
                        ),
                        SizedBox(height: 6.h),
                        Text(
                          state.shareList[index].tip,
                          style: StyleConfig.otherStyle(
                              color: ColorConfig.authenticationTextColor,
                              fontSize: 12),
                        )
                      ],
                    ).inkWell(() => logic.shareAction(index));
                  }),
                ),
              )
            ],
          ),
        );
      },
    );
  }
}
