import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:getx_xiaopa/pages/task/task_share/task_share_state.dart';
import 'package:getx_xiaopa/r.dart';

class DiaryShareState {
  GlobalKey repaintKey = GlobalKey();

  String image = "";
  String shareStr = "";

  List<ShareType> shareList = [
    ShareType(tip: "diary_share_save".tr, imagePath: R.task_share_save_png),
    ShareType(tip: "diary_share_wechat".tr, imagePath: R.task_share_wx_png),
    // ShareType(tip: "QQ", imagePath: R.task_share_qq_png),
    // ShareType(tip: "微博", imagePath: R.task_share_wb_png),
    ShareType(tip: "diary_share_xhs".tr, imagePath: R.task_share_xhs_png),
  ];

  late Uint8List? imgData;
  late String imageUri;
  bool isInit = false;

  StreamSubscription? xhsResultSubscription;

  DiaryShareState() {
    ///Initialize variables
  }
}
