import 'dart:ui';

import 'package:get/get.dart';
import 'package:getx_xiaopa/pages/application/application_logic.dart';
import 'package:getx_xiaopa/pages/diary/model/diary_items_result.dart';
import 'package:getx_xiaopa/pages/diary/utils/diary_store.dart';
import 'package:getx_xiaopa/routes/router.dart';
import 'package:getx_xiaopa/storage/storage.dart';

import 'diary_state.dart';

class DiaryLogic extends GetxController {
  final DiaryState state = DiaryState();

  @override
  void onInit() {
    super.onInit();
    _refreshUI();
  }

  toRecordPage() {
    if (UserStore.to.deviceList.isEmpty) {
      Get.find<ApplicationLogic>().tChange(0, isShow: true);
      return;
    }
    Get.toNamed(AppRoutes.DIARY_RECORD)?.then((value) async {
      await DiaryStore.to.getDiaryItems();
      _refreshUI();
    });
  }

  toSharePage() {
    var map = {"image": state.moodImage, "share_str": state.quote};
    Get.toNamed(AppRoutes.DIARY_SHARE, arguments: map);
  }

  ///给外部使用，切换设备更新ui
  updateStatus() async {
    await DiaryStore.to.getDiaryItems();
    _refreshUI();
  }

  _resetData() {
    state.segmentCount = 0;
    state.segmentColors.clear();
    state.diaryItems.clear();
    state.quote = "";
    state.moodImage = "";
    state.moodTopImage = 0;
  }

  _refreshUI() async {
    _resetData();

    if (DiaryStore.to.diaryItemsModel.isNotEmpty) {
      state.diaryItems = DiaryStore.to.diaryItemsModel;
      String quote = state.diaryItems[0].mood!;
      if (quote == "开心" || quote == "好" || quote == "一般") {
        state.moodTopImage = 0;
      } else {
        state.moodTopImage = 1;
      }

      state.quote = state.diaryItems[0].say ??
          await state.moodQuoteManager.getTodayQuote("一般");
      state.moodImage = state.diaryItems[0].moodPicture!;

      for (DiaryItemsModel model in state.diaryItems) {
        if (model.mood == "开心") {
          state.segmentCount += 1;
          state.segmentColors.add(const Color.fromRGBO(255, 128, 118, 1));
        }
        if (model.mood == "好") {
          state.segmentCount += 1;
          state.segmentColors.add(const Color.fromRGBO(255, 190, 101, 1));
        }
        if (model.mood == "一般") {
          state.segmentCount += 1;
          state.segmentColors.add(const Color.fromRGBO(163, 233, 146, 1));
        }
        if (model.mood == "不好") {
          state.segmentCount += 1;
          state.segmentColors.add(const Color.fromRGBO(118, 194, 245, 1));
        }
        if (model.mood == "很不好") {
          state.segmentCount += 1;
          state.segmentColors.add(const Color.fromRGBO(188, 137, 227, 1));
        }
      }
    } else {
      state.segmentCount = 1;
      state.segmentColors.add(const Color.fromRGBO(242, 241, 245, 1));
      state.quote = await state.moodQuoteManager.getTodayQuote("一般");
    }
    update(["diary_view"]);
  }
}
