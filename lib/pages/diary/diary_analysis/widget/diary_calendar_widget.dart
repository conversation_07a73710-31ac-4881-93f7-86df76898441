import 'package:animations/animations.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:getx_xiaopa/extension/images.dart';
import 'package:getx_xiaopa/extension/widget_extension.dart';
import 'package:getx_xiaopa/pages/diary/widget/segment_circular_progress.dart';
import 'package:getx_xiaopa/r.dart';
import 'package:getx_xiaopa/storage/storage.dart';
import 'package:getx_xiaopa/values/values.dart';
import 'package:getx_xiaopa/widget/widget.dart';
import 'package:table_calendar/table_calendar.dart';

class DiaryCalendarWidget extends StatefulWidget {
  final Map<String, List<Color>> completedDates;
  final Function(int, DateTime) typeAction;
  final Function(DateTime) weekAction;
  final Function(DateTime) monthAction;
  final Function(DateTime) yearAction;

  const DiaryCalendarWidget(
      {super.key,
      required this.completedDates,
      required this.typeAction,
      required this.weekAction,
      required this.monthAction,
      required this.yearAction});

  @override
  State<DiaryCalendarWidget> createState() => _DiaryCalendarWidgetState();
}

class _DiaryCalendarWidgetState extends State<DiaryCalendarWidget> {
  DateTime _weekDay = DateTime.now();
  DateTime _monthDay = DateTime.now();
  final List<String> _tabs = [
    "diary_date_week".tr,
    "diary_date_month".tr,
    "diary_date_year".tr
  ];
  int _selectedIndex = 0;

  DateTime _year = DateTime.now();
  int _direction = 1; // 1: forward, -1: backward

  /// 是否回调
  bool _isCallback = true;

  bool _isSameDay(String a, DateTime b) {
    List<String> parts = a.split('-');
    DateTime dt;
    if (parts.length == 3) {
      dt = DateTime(
          int.parse(parts[0]), int.parse(parts[1]), int.parse(parts[2]));
      return dt.year == b.year && dt.month == b.month && dt.day == b.day;
    } else {
      dt = DateTime(int.parse(parts[0]), int.parse(parts[1]), 1);
      return dt.year == b.year && dt.month == b.month;
    }
  }

  bool _isCompleted(DateTime date) {
    return widget.completedDates.keys.any((d) => _isSameDay(d, date));
  }

  @override
  void initState() {
    super.initState();

    ///年视图重置1月份开始
    _year = DateTime(_year.year, 1, 1);
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: List.generate(_tabs.length, (index) {
            bool selected = index == _selectedIndex;
            return GestureDetector(
              onTap: () {
                if (_selectedIndex == index) return;
                _isCallback = false;
                Future.delayed(const Duration(milliseconds: 500), () {
                  _isCallback = true;
                });
                setState(() {
                  _selectedIndex = index;
                  if (_selectedIndex == 0) {
                    widget.typeAction.call(_selectedIndex, _weekDay);
                  }

                  if (_selectedIndex == 1) {
                    widget.typeAction.call(_selectedIndex, _monthDay);
                  }

                  if (_selectedIndex == 2) {
                    widget.typeAction.call(_selectedIndex, _year);
                  }
                });
              },
              child: Container(
                margin: EdgeInsets.only(top: 20.h, left: 14.w, right: 14.w),
                width: 80.w,
                height: 36.h,
                decoration: BoxDecoration(
                  color: selected
                      ? const Color.fromRGBO(40, 39, 46, 1)
                      : const Color.fromRGBO(242, 241, 245, 1),
                  borderRadius: BorderRadius.circular(18.r),
                ),
                child: Center(
                  child: ThemeText(
                    dataStr: _tabs[index],
                    keyName: 'textColor',
                    flag: selected,
                    subColor: ColorConfig.shopDetailTextColor,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            );
          }),
        ),
        SizedBox(height: 26.h),
        _bodyWidget(),
        SizedBox(height: 30.h)
      ],
    );
  }

  Widget _bodyWidget() {
    Widget rWidget = Container();
    if (_tabs[_selectedIndex] == "diary_date_week".tr) {
      rWidget = TableCalendar(
        availableGestures: AvailableGestures.horizontalSwipe,
        firstDay: DateTime.utc(2020, 1, 1),
        lastDay: DateTime.utc(2099, 12, 31),
        focusedDay: _weekDay,
        rowHeight: 44.h,
        daysOfWeekHeight: 44.h,
        locale: ConfigStore.to.locale.toString(),
        calendarFormat: CalendarFormat.week,
        headerStyle: HeaderStyle(
            formatButtonVisible: false,
            titleCentered: true,
            headerMargin: EdgeInsets.symmetric(vertical: 2.h, horizontal: 10.w),
            leftChevronIcon: Container(
              width: 20.r,
              height: 20.r,
              decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(20.r),
                  color: const Color.fromRGBO(242, 241, 245, 1)),
              child: Center(
                child: Icon(
                  size: 20.r,
                  Icons.chevron_left,
                  color: const Color.fromRGBO(185, 185, 185, 1),
                ),
              ),
            ),
            rightChevronIcon: Container(
              width: 20.r,
              height: 20.r,
              decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(20.r),
                  color: const Color.fromRGBO(242, 241, 245, 1)),
              child: Center(
                child: Icon(
                  size: 20.r,
                  Icons.chevron_right,
                  color: const Color.fromRGBO(185, 185, 185, 1),
                ),
              ),
            ),
            headerPadding: EdgeInsets.zero,
            titleTextStyle: StyleConfig.otherStyle(
                color: ColorConfig.searchTextColor,
                fontWeight: FontWeight.w500,
                fontSize: 18)),
        calendarStyle: const CalendarStyle(
          outsideDaysVisible: false,
          isTodayHighlighted: false,
        ),
        calendarBuilders: CalendarBuilders(
          dowBuilder: (context, day) {
            final text = [
              'diary_week_seven'.tr,
              'diary_week_one'.tr,
              'diary_week_two'.tr,
              'diary_week_three'.tr,
              'diary_week_four'.tr,
              'diary_week_five'.tr,
              'diary_week_six'.tr
            ][day.weekday % 7];
            return Center(
              child: Text(
                text,
                style: StyleConfig.otherStyle(
                    color: ColorConfig.authenticationTextColor, fontSize: 14),
              ),
            );
          },
          headerTitleBuilder: (context, date) {
            String weekRange = _getWeekRange(date);
            return Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Images(path: R.diary_item_image_png, width: 15.r, height: 15.r),
                SizedBox(width: 5.w),
                Text(
                  weekRange,
                  style: StyleConfig.otherStyle(
                      color: ColorConfig.searchTextColor),
                )
              ],
            );
          },

          ///设置不是本月的样式
          outsideBuilder: (context, date, _) => _buildDayCell(date),
          defaultBuilder: (context, date, _) => _buildDayCell(date),
          todayBuilder: (context, date, _) => _buildDayCell(date),
          selectedBuilder: (context, date, _) => _buildDayCell(date),
        ),
        onPageChanged: (focusedDay) {
          setState(() {
            _weekDay = focusedDay;
            if (_isCallback) {
              widget.weekAction.call(_weekDay);
            }
          });
        },
      );
    }
    if (_tabs[_selectedIndex] == "diary_date_month".tr) {
      rWidget = TableCalendar(
        availableGestures: AvailableGestures.horizontalSwipe,
        firstDay: DateTime.utc(2020, 1, 1),
        lastDay: DateTime.utc(2099, 12, 31),
        focusedDay: _monthDay,
        rowHeight: 44.h,
        daysOfWeekHeight: 44.h,
        locale: ConfigStore.to.locale.toString(),
        calendarFormat: CalendarFormat.month,
        headerStyle: HeaderStyle(
            formatButtonVisible: false,
            titleCentered: true,
            headerMargin: EdgeInsets.symmetric(vertical: 2.h, horizontal: 10.w),
            leftChevronIcon: Container(
              width: 20.r,
              height: 20.r,
              decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(20.r),
                  color: const Color.fromRGBO(242, 241, 245, 1)),
              child: Center(
                child: Icon(
                  size: 20.r,
                  Icons.chevron_left,
                  color: const Color.fromRGBO(185, 185, 185, 1),
                ),
              ),
            ),
            rightChevronIcon: Container(
              width: 20.r,
              height: 20.r,
              decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(20.r),
                  color: const Color.fromRGBO(242, 241, 245, 1)),
              child: Center(
                child: Icon(
                  size: 20.r,
                  Icons.chevron_right,
                  color: const Color.fromRGBO(185, 185, 185, 1),
                ),
              ),
            ),
            headerPadding: EdgeInsets.zero,
            titleTextStyle: StyleConfig.otherStyle(
                color: ColorConfig.searchTextColor,
                fontWeight: FontWeight.w500,
                fontSize: 18)),
        calendarStyle: const CalendarStyle(
          outsideDaysVisible: false,
          isTodayHighlighted: false,
        ),
        calendarBuilders: CalendarBuilders(
          dowBuilder: (context, day) {
            final text = [
              'diary_week_seven'.tr,
              'diary_week_one'.tr,
              'diary_week_two'.tr,
              'diary_week_three'.tr,
              'diary_week_four'.tr,
              'diary_week_five'.tr,
              'diary_week_six'.tr
            ][day.weekday % 7];
            return Center(
              child: Text(
                text,
                style: StyleConfig.otherStyle(
                    color: ColorConfig.authenticationTextColor, fontSize: 14),
              ),
            );
          },
          headerTitleBuilder: (context, date) {
            String weekRange = _getWeekRange(date);
            return Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Images(path: R.diary_item_image_png, width: 15.r, height: 15.r),
                SizedBox(width: 5.w),
                Text(
                  weekRange,
                  style: StyleConfig.otherStyle(
                      color: ColorConfig.searchTextColor),
                )
              ],
            );
          },

          ///设置不是本月的样式
          outsideBuilder: (context, date, _) => _buildDayCell(date),
          defaultBuilder: (context, date, _) => _buildDayCell(date),
          todayBuilder: (context, date, _) => _buildDayCell(date),
          selectedBuilder: (context, date, _) => _buildDayCell(date),
        ),
        onPageChanged: (focusedDay) {
          setState(() {
            _monthDay = focusedDay;
            if (_isCallback) {
              widget.monthAction.call(_monthDay);
            }
          });
        },
      );
    }
    if (_tabs[_selectedIndex] == "diary_date_year".tr) {
      rWidget = Stack(
        children: [
          Container(
            margin: EdgeInsets.only(left: 9.w, right: 9.w),
            child: Column(
              children: [
                Container(
                  color: Colors.white,
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Container(
                        margin: EdgeInsets.only(left: 25.w),
                        child: Images(
                                path: R.diary_left_png,
                                width: 20.r,
                                height: 20.r)
                            .inkWell(() {
                          _changeYear(-1);
                        }),
                      ),
                      Row(
                        children: [
                          Images(
                              path: R.diary_item_image_png,
                              width: 15.r,
                              height: 15.r),
                          SizedBox(width: 5.w),
                          Text(
                            "${_year.year}",
                            style: StyleConfig.otherStyle(
                                color: ColorConfig.searchTextColor),
                          ),
                        ],
                      ),
                      Container(
                        margin: EdgeInsets.only(right: 25.w),
                        child: Images(
                                path: R.diary_right_png,
                                width: 20.r,
                                height: 20.r)
                            .inkWell(() {
                          _changeYear(1);
                        }),
                      ),
                    ],
                  ),
                ),
                SizedBox(height: 20.h),
                PageTransitionSwitcher(
                  duration: const Duration(milliseconds: 300),
                  reverse: _direction == -1,
                  transitionBuilder: (child, animation, secondaryAnimation) {
                    return SharedAxisTransition(
                      animation: animation,
                      secondaryAnimation: secondaryAnimation,
                      transitionType: SharedAxisTransitionType.horizontal,
                      fillColor: Colors.transparent,
                      child: child,
                    );
                  },
                  child: _buildMonthGrid(_year),
                ),
              ],
            ),
          ),
          // 全屏手势监听层（透明）
          Positioned.fill(
            child: GestureDetector(
              behavior: HitTestBehavior.translucent, // 👈 关键，让空白区域也响应
              onHorizontalDragEnd: (details) {
                if (details.primaryVelocity == null) return;
                if (details.primaryVelocity! < 0) {
                  _changeYear(1); // 左滑，下一年
                } else {
                  _changeYear(-1); // 右滑，上一年
                }
              },
            ),
          ),
        ],
      );
    }
    return rWidget;
  }

  void _changeYear(int delta) {
    setState(() {
      _direction = delta > 0 ? 1 : -1;
      _year = DateTime(_year.year + delta);
    });
    widget.yearAction.call(_year);
  }

  Widget _buildMonthGrid(DateTime date) {
    return Container(
      color: Colors.white,
      key: ValueKey(date.year),
      child: Wrap(
        alignment: WrapAlignment.spaceBetween,
        runSpacing: 20.w,
        spacing: 25.w,
        children: List.generate(12, (index) {
          final bool isCompleted =
              _isCompleted(DateTime(date.year, date.month + index, 1));

          List<Color> temp = [];
          String tempStr =
              "${date.year}-${(date.month + index).toString().padLeft(2, '0')}";

          if (widget.completedDates.keys.contains(tempStr)) {
            temp = widget.completedDates[tempStr]!;
          }

          List<String> dateList = [
            "dary_date_one".tr,
            "diary_date_two".tr,
            "diary_date_three".tr,
            "diary_date_four".tr,
            "diary_date_five".tr,
            "diary_date_six".tr,
            "diary_date_seven".tr,
            "diary_date_eight".tr,
            "diary_date_nine".tr,
            "diary_date_ten".tr,
            "diary_date_eleven".tr,
            "diary_date_twelve".tr
          ];

          return SizedBox(
            width: 32.w,
            height: 58.h,
            child: Column(
              children: [
                Text(
                  dateList[index],
                  style: StyleConfig.otherStyle(
                      color: ColorConfig.authenticationTextColor, fontSize: 14),
                ),
                SizedBox(height: 8.h),
                if (isCompleted) ...{
                  SegmentedCircularProgress(
                    size: 26.r,
                    strokeWidth: 3.r,
                    segmentCount: temp.length,
                    segmentColors: temp,
                  )
                } else ...{
                  SegmentedCircularProgress(
                    size: 26.r,
                    strokeWidth: 3.r,
                    segmentCount: 1,
                    segmentColors: const [Color.fromRGBO(242, 241, 245, 1)],
                  )
                },
              ],
            ),
          );
        }),
      ),
    );
  }

  // 获取一周的日期范围
  String _getWeekRange(DateTime date) {
    String temp = '';
    final startOfWeek = date.subtract(Duration(days: date.weekday));
    final endOfWeek = startOfWeek.add(const Duration(days: 6));
    final startDate =
        "${startOfWeek.year}.${startOfWeek.month}.${startOfWeek.day}";
    final endDate = "${endOfWeek.year}.${endOfWeek.month}.${endOfWeek.day}";
    if (_tabs[_selectedIndex] == "diary_date_week".tr) {
      temp = '$startDate - $endDate';
    }
    if (_tabs[_selectedIndex] == "diary_date_month".tr) {
      temp = '${date.year}.${date.month.toString().padLeft(2, "0")}';
    }

    return temp;
  }

  Widget _buildDayCell(DateTime date) {
    final bool isCompleted = _isCompleted(date);

    List<Color> temp = [];
    String tempStr =
        "${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}";

    if (widget.completedDates.keys.contains(tempStr)) {
      temp = widget.completedDates[tempStr]!;
    }

    // logD("$tempStr == $temp");

    return Container(
      margin: EdgeInsets.symmetric(vertical: 5.w, horizontal: 5.h),
      decoration: const BoxDecoration(
        color: Color.fromRGBO(249, 249, 249, 1),
        shape: BoxShape.circle,
      ),
      child: Stack(
        children: [
          Center(
            child: Text(
              '${date.day}',
              style: StyleConfig.otherStyle(
                  fontSize: 10,
                  color: ColorConfig.shopDetailTextColor,
                  fontWeight: FontWeight.w400),
            ),
          ),
          if (isCompleted) ...{
            SegmentedCircularProgress(
              size: 44.r,
              strokeWidth: 3.r,
              segmentCount: temp.length,
              segmentColors: temp,
            ),
          }
        ],
      ),
    );
  }
}
