import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:getx_xiaopa/pages/diary/widget/segment_circular_progress.dart';
import 'package:getx_xiaopa/utils/utils.dart';
import 'package:getx_xiaopa/values/values.dart';

class DiaryDistributionWidget extends StatefulWidget {
  final Color renderColor;
  final double value;
  final double maxValue;
  final String tag;

  const DiaryDistributionWidget({
    super.key,
    required this.renderColor,
    required this.value,
    required this.maxValue,
    required this.tag,
  });

  @override
  State<DiaryDistributionWidget> createState() =>
      _DiaryDistributionWidgetState();
}

class _DiaryDistributionWidgetState extends State<DiaryDistributionWidget> {
  ///字体颜色
  Color tagColor = Colors.black38;

  @override
  void initState() {
    super.initState();

    ///周视图，value大于1就显示白色
    if (widget.maxValue <= 21) {
      if (widget.value > 0) {
        tagColor = Colors.white;
      } else {
        tagColor = Colors.black38;
      }
    }

    ///月视图，value大于3就显示白色
    if (widget.maxValue > 80 && widget.maxValue < 300) {
      if (widget.value > 3) {
        tagColor = Colors.white;
      } else {
        tagColor = Colors.black38;
      }
    }

    /// 年视图，value大于10显示白色
    if (widget.maxValue > 300) {
      if (widget.value >= 10) {
        tagColor = Colors.white;
      } else {
        tagColor = Colors.black38;
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Container(
          width: 20.r,
          height: 20.r,
          decoration:
              BoxDecoration(shape: BoxShape.circle, color: widget.renderColor),
        ),
        SizedBox(width: 10.w),
        Container(
          width: 283.w,
          height: 20.h,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(10.r),
          ),
          clipBehavior: Clip.hardEdge,
          child: Stack(
            children: [
              SegmentedCircularProgress(
                size: 283.w,
                strokeWidth: 20.r,
                segmentCount: 1,
                segmentColors: [widget.renderColor],
                mode: ProgressMode.linear,
                backgroundColor: const Color.fromRGBO(242, 241, 245, 1),
                value: widget.value,
                maxValue: widget.maxValue,
              ),
              Positioned(
                top: 2.h,
                left: 11.w,
                child: Text(
                  NumUtil.moneyDoubleOrInt(widget.value, isMoney: false),
                  style: StyleConfig.otherStyle(color: tagColor, fontSize: 12),
                ),
              ),
              Positioned(
                top: 2.h,
                right: 12.w,
                child: Text(
                  widget.tag,
                  style: StyleConfig.otherStyle(
                      color: Colors.black38, fontSize: 12),
                ),
              )
            ],
          ),
        )
      ],
    );
  }
}
