import 'dart:math';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:getx_xiaopa/utils/utils.dart';
import 'package:getx_xiaopa/values/values.dart';

enum TrendMode { week, month, year }

// ignore: must_be_immutable
class DiaryTrendWidget extends StatefulWidget {
  final TrendMode mode;
  final int dayCount; // 仅用于月视图：28~31
  final List<List<int>> weekValues; // 每天3个情绪值，范围 0~4（代表不同颜色）

  late List<double> monthValues; // 月视图数据，对应每一天的情绪值（0 表示不绘制）
  late List<double> yearValues; // 年视图数据，对应每月的情绪值（0 表示不绘制）

  DiaryTrendWidget({
    super.key,
    this.dayCount = 30,
    this.mode = TrendMode.week,
    required this.weekValues,
    List<double>? monthValues,
    List<double>? yearValues,
  })  : monthValues = List<double>.empty(),
        yearValues = List<double>.empty() {
    // 构造函数体中做补全
    this.monthValues = List<double>.generate(
      dayCount,
      (i) => i < (monthValues?.length ?? 0) ? monthValues![i] : 0,
    );

    this.yearValues = List<double>.generate(
      12,
      (i) => i < (yearValues?.length ?? 0) ? yearValues![i] : 0,
    );
  }

  @override
  State<DiaryTrendWidget> createState() => _DiaryTrendWidgetState();
}

class _DiaryTrendWidgetState extends State<DiaryTrendWidget>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 500),
    )..forward(); // 自动播放
  }

  @override
  void didUpdateWidget(covariant DiaryTrendWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.weekValues != widget.weekValues ||
        oldWidget.mode != widget.mode ||
        oldWidget.dayCount != widget.dayCount) {
      _controller.forward(from: 0);
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _controller,
      builder: (_, __) => CustomPaint(
        painter: _DiaryTrendPainter(
            mode: widget.mode,
            progress: _controller.value,
            dayCount: widget.dayCount,
            weekValues: widget.weekValues,
            monthValues: widget.monthValues,
            yearValues: widget.yearValues),
        size: Size.infinite,
      ),
    );
  }
}

class _DiaryTrendPainter extends CustomPainter {
  final TrendMode mode;
  final double progress;
  final int dayCount;
  final List<List<int>> weekValues;
  final List<double> monthValues; // 月视图数据，对应每一天的情绪值（0 表示不绘制）
  final List<double> yearValues; // 年视图数据，对应每月的情绪值（0 表示不绘制）

  _DiaryTrendPainter({
    required this.weekValues,
    required this.mode,
    required this.progress,
    required this.dayCount,
    required this.monthValues,
    required this.yearValues,
  });

  final double padding = 4.0;
  final double dotRadius = 3.0;
  final int moodLevels = 5;

  @override
  void paint(Canvas canvas, Size size) {
    final width = size.width - padding * 2;
    final height = size.height - padding * 2 - 15.h;

    int count;
    List<String> bottomLabels = [];

    switch (mode) {
      case TrendMode.week:
        count = 7;
        bottomLabels = [
          'diary_week_seven'.tr,
          'diary_week_one'.tr,
          'diary_week_two'.tr,
          'diary_week_three'.tr,
          'diary_week_four'.tr,
          'diary_week_five'.tr,
          'diary_week_six'.tr
        ];
        break;
      case TrendMode.month:
        count = dayCount;
        for (int i = 1; i <= dayCount; i++) {
          if (i == 1 || i % 5 == 0 || i == dayCount) {
            bottomLabels.add(i.toString());
          } else {
            bottomLabels.add('');
          }
        }
        break;
      case TrendMode.year:
        count = 12;
        for (int i = 1; i <= 12; i++) {
          bottomLabels.add(i.toString());
        }
        break;
    }

    final double daySpacing = width / count;
    final double lineSpacing = daySpacing / 3;

    const moodColors = [
      Color.fromRGBO(255, 128, 118, 1),
      Color.fromRGBO(255, 190, 101, 1),
      Color.fromRGBO(163, 233, 146, 1),
      Color.fromRGBO(118, 194, 245, 1),
      Color.fromRGBO(188, 137, 227, 1)
    ];
    for (int i = 0; i < moodColors.length; i++) {
      final y = padding + 4 + i * height / (moodLevels - 1);
      final paint = Paint()..color = moodColors[i];
      canvas.drawCircle(Offset(padding / 2, y), 7.r, paint);
    }

    // 底部虚线与文字
    final dashPaint = Paint()
      ..color = const Color.fromRGBO(215, 215, 215, 1)
      ..strokeWidth = 1
      ..style = PaintingStyle.stroke;
    final textPainter = TextPainter(
        textAlign: TextAlign.center, textDirection: TextDirection.ltr);

    ///绘制虚线（不同模式下）
    for (int i = 0; i < count; i++) {
      final baseX = padding + daySpacing * i;

      if (mode == TrendMode.week) {
        // 每天三条虚线
        for (int j = 0; j < 3; j++) {
          final x = baseX + j * lineSpacing + 15.w;
          _drawDashedLine(canvas, Offset(x, padding),
              Offset(x, size.height - padding), dashPaint);
        }
      } else {
        // 每天或每月一条虚线
        final x = baseX + lineSpacing + 15.w;
        _drawDashedLine(canvas, Offset(x, padding),
            Offset(x, size.height - padding), dashPaint);
      }

      final text = bottomLabels[i];
      if (text.isNotEmpty) {
        textPainter.text = TextSpan(
          text: text,
          style: StyleConfig.otherStyle(
              color: ColorConfig.authenticationTextColor, fontSize: 14),
        );
        textPainter.layout();
        final x = baseX + lineSpacing + 15.w;
        final y = size.height - padding;
        textPainter.paint(canvas, Offset(x - textPainter.width / 2, y));
      }
    }

    // 情绪曲线绘制
    Offset? lastPoint;

    for (int day = 0; day < count; day++) {
      if (mode == TrendMode.week) {
        /// 周视图绘制曲线
        if (day >= weekValues.length || weekValues[day].isEmpty) {
          ///打开不相邻的2个点不连线
          // lastPoint = null;
          continue;
        }

        final daily = weekValues[day];
        final baseX = padding + daySpacing * day;
        final path = Path();
        final points = <Offset>[];

        for (int i = 0; i < daily.length; i++) {
          final mood = daily[i];
          if (mood == 0) continue;
          final x = baseX + i * lineSpacing + 15.w;
          final y =
              padding + 4 + (moodLevels - mood) * height / (moodLevels - 1);
          points.add(Offset(x, y));
        }

        // 添加空值检查
        if (points.isEmpty) {
          lastPoint = null;
          continue;
        }

        if (lastPoint != null) {
          points.insert(0, lastPoint);
        }

        path.moveTo(points[0].dx, points[0].dy);
        for (int i = 1; i < points.length; i++) {
          path.lineTo(points[i].dx, points[i].dy);
        }
        final pathMetrics = path.computeMetrics();
        for (final metric in pathMetrics) {
          final animated = metric.extractPath(0, metric.length * progress);

          LinearGradient gradient = LinearGradient(
            colors: [
              Get.find<ThemeColorController>().gIconColor,
              // Color.fromRGBO(255, 232, 174, 1),
              const Color.fromRGBO(255, 245, 215, 0),
            ],
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
          );

          final fill = Path.from(animated);
          final start = points.first;
          final end = points.last;
          fill
            ..lineTo(end.dx, size.height - padding)
            ..lineTo(start.dx, size.height - padding)
            ..close();

          final fillPaint = Paint()
            ..shader = gradient
                .createShader(Rect.fromLTWH(0, 0, size.width, size.height))
            ..style = PaintingStyle.fill;
          canvas.drawPath(fill, fillPaint);

          final strokePaint = Paint()
            ..color = Get.find<ThemeColorController>().gIconColor
            ..style = PaintingStyle.stroke
            ..strokeWidth = 0.1;
          canvas.drawPath(animated, strokePaint);
        }

        final maxX = points.last.dx * progress;
        for (final p in points) {
          if (p.dx <= maxX) {
            canvas.drawCircle(p, dotRadius,
                Paint()..color = Get.find<ThemeColorController>().gIconColor);
          }
        }

        lastPoint = points.last;
      } else if (mode == TrendMode.month) {
        /// 月视图绘制曲线
        if (day >= dayCount || day >= monthValues.length) {
          ///打开不相邻的2个点不连线
          // lastPoint = null;
          continue;
        }
        final value = monthValues[day];
        if (value == 0) {
          ///打开不相邻的2个点不连线
          // lastPoint = null;
          continue;
        }

        final x = padding + daySpacing * day + lineSpacing + 15.w;
        final y =
            padding + 4 + (moodLevels - value) * height / (moodLevels - 1);

        final currentPoint = Offset(x, y);

        if (lastPoint != null) {
          final path = Path();
          path.moveTo(lastPoint.dx, lastPoint.dy);
          path.lineTo(currentPoint.dx, currentPoint.dy);

          final pathMetrics = path.computeMetrics();
          for (final metric in pathMetrics) {
            final animated = metric.extractPath(0, metric.length * progress);

            LinearGradient gradient = LinearGradient(
              colors: [
                Get.find<ThemeColorController>().gIconColor,
                const Color.fromRGBO(255, 245, 215, 0),
              ],
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
            );

            final fill = Path.from(animated)
              ..lineTo(currentPoint.dx, size.height - padding)
              ..lineTo(lastPoint.dx, size.height - padding)
              ..close();

            final fillPaint = Paint()
              ..shader = gradient.createShader(
                Rect.fromLTWH(0, 0, size.width, size.height),
              )
              ..style = PaintingStyle.fill;
            canvas.drawPath(fill, fillPaint);

            final strokePaint = Paint()
              ..color = Get.find<ThemeColorController>().gIconColor
              ..style = PaintingStyle.stroke
              ..strokeWidth = 0.1;
            canvas.drawPath(animated, strokePaint);
          }
        }

        if (currentPoint.dx <= currentPoint.dx * progress) {
          canvas.drawCircle(
            currentPoint,
            dotRadius,
            Paint()..color = Get.find<ThemeColorController>().gIconColor,
          );
        }

        lastPoint = currentPoint;
      } else if (mode == TrendMode.year) {
        /// 年视图绘制曲线
        if (day >= yearValues.length) {
          ///打开不相邻的2个点不连线
          // lastPoint = null;
          continue;
        }
        final value = yearValues[day];
        if (value == 0) {
          lastPoint = null;
          continue;
        }

        final x = padding + daySpacing * day + lineSpacing + 15.w;
        final y =
            padding + 4 + (moodLevels - value) * height / (moodLevels - 1);

        final currentPoint = Offset(x, y);

        if (lastPoint != null) {
          final path = Path();
          path.moveTo(lastPoint.dx, lastPoint.dy);
          path.lineTo(currentPoint.dx, currentPoint.dy);

          final pathMetrics = path.computeMetrics();
          for (final metric in pathMetrics) {
            final animated = metric.extractPath(0, metric.length * progress);

            LinearGradient gradient = LinearGradient(
              colors: [
                Get.find<ThemeColorController>().gIconColor,
                const Color.fromRGBO(255, 245, 215, 0),
              ],
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
            );

            final fill = Path.from(animated)
              ..lineTo(currentPoint.dx, size.height - padding)
              ..lineTo(lastPoint.dx, size.height - padding)
              ..close();

            final fillPaint = Paint()
              ..shader = gradient.createShader(
                Rect.fromLTWH(0, 0, size.width, size.height),
              )
              ..style = PaintingStyle.fill;
            canvas.drawPath(fill, fillPaint);

            final strokePaint = Paint()
              ..color = Get.find<ThemeColorController>().gIconColor
              ..style = PaintingStyle.stroke
              ..strokeWidth = 0.1;
            canvas.drawPath(animated, strokePaint);
          }
        }

        if (currentPoint.dx <= currentPoint.dx * progress) {
          canvas.drawCircle(
            currentPoint,
            dotRadius,
            Paint()..color = Get.find<ThemeColorController>().gIconColor,
          );
        }

        lastPoint = currentPoint;
      }
    }
  }

  void _drawDashedLine(Canvas canvas, Offset start, Offset end, Paint paint) {
    const dashWidth = 4.0;
    const dashSpace = 4.0;
    final dx = end.dx - start.dx;
    final dy = end.dy - start.dy;
    final distance = sqrt(dx * dx + dy * dy);
    final dashCount = (distance / (dashWidth + dashSpace)).floor();
    final direction = Offset(dx / distance, dy / distance);
    for (int i = 0; i < dashCount; i++) {
      final double currentLength = i * (dashWidth + dashSpace);
      final Offset offsetStart = start + direction * currentLength;
      final Offset offsetEnd = offsetStart + direction * dashWidth;
      canvas.drawLine(offsetStart, offsetEnd, paint);
    }
  }

  @override
  bool shouldRepaint(covariant _DiaryTrendPainter oldDelegate) =>
      oldDelegate.weekValues != weekValues ||
      oldDelegate.progress != progress ||
      oldDelegate.mode != mode ||
      oldDelegate.dayCount != dayCount;
}
