import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:getx_xiaopa/base/base.dart';
import 'package:getx_xiaopa/extension/images.dart';
import 'package:getx_xiaopa/extension/widget_extension.dart';
import 'package:getx_xiaopa/pages/diary/diary_analysis/widget/diary_calendar_widget.dart';
import 'package:getx_xiaopa/pages/diary/diary_analysis/widget/diary_distribution_widget.dart';
import 'package:getx_xiaopa/pages/diary/diary_analysis/widget/diary_trend_widget.dart';
import 'package:getx_xiaopa/r.dart';
import 'package:getx_xiaopa/values/values.dart';
import 'package:getx_xiaopa/widget/widget.dart';

import 'diary_analysis_logic.dart';
import 'diary_analysis_state.dart';

class DiaryAnalysisPage extends BaseCommonView {
  DiaryAnalysisPage({super.key});

  final DiaryAnalysisLogic logic = Get.put(DiaryAnalysisLogic());
  final DiaryAnalysisState state = Get.find<DiaryAnalysisLogic>().state;

  @override
  bool get customWidget => true;

  @override
  Widget buildContent() {
    return GetBuilder<DiaryAnalysisLogic>(
      id: "diary_analysis_view",
      builder: (_) {
        return Scaffold(
          backgroundColor: const Color.fromRGBO(249, 249, 249, 1),
          extendBodyBehindAppBar: true,
          appBar: AppBar(
            elevation: 0,
            scrolledUnderElevation: 0,
            backgroundColor: state.navColor,
            centerTitle: true,
            title: Text(
              "diary_analysis_title".tr,
              style: StyleConfig.blackStyle(
                  fontSize: 18, fontWeight: FontWeight.w500),
            ),
            leadingWidth: 35.w,
            leading: Padding(
              padding: EdgeInsets.only(left: 15.w),
              child: Images(
                path: R.back_png,
                width: 20.w,
                height: 20.h,
                scale: 2.8,
              ).inkWell(() => Get.back()),
            ),
            systemOverlayStyle: const SystemUiOverlayStyle(
              statusBarColor: Colors.transparent,
              statusBarIconBrightness: Brightness.dark,
            ),
          ),
          body: createCommonView(
            logic,
            (_) {
              return SingleChildScrollView(
                physics: const ClampingScrollPhysics(),
                controller: state.sController,
                child: Column(
                  children: [
                    Container(
                      margin: EdgeInsets.only(left: 20.w, top: 109.h),
                      child: Row(
                        children: [
                          ThemeImagePath(
                            fileName: 'diary_analysis_image.png',
                            imgWidget: 6.w,
                            imgHeight: 17.h,
                          ),
                          SizedBox(width: 8.w),
                          Text(
                            "diary_calendar".tr,
                            style: StyleConfig.otherStyle(
                                color: ColorConfig.searchTextColor,
                                fontWeight: FontWeight.w600,
                                fontSize: 18),
                          )
                        ],
                      ),
                    ),
                    Container(
                      margin:
                          EdgeInsets.only(top: 14.h, left: 15.w, right: 15.w),
                      decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(10.r),
                          color: Colors.white),
                      child: DiaryCalendarWidget(
                        completedDates: state.completedDates,
                        typeAction: (index, date) =>
                            logic.tapAction(index, date),
                        weekAction: (date) {
                          logic.dateTimeChange("week", date);
                        },
                        monthAction: (date) {
                          logic.dateTimeChange("month", date);
                        },
                        yearAction: (date) {
                          logic.dateTimeChange("year", date);
                        },
                      ),
                    ),
                    Container(
                      margin: EdgeInsets.only(left: 20.w, top: 20.h),
                      child: Row(
                        children: [
                          ThemeImagePath(
                            fileName: 'diary_analysis_image.png',
                            imgWidget: 6.w,
                            imgHeight: 17.h,
                          ),
                          SizedBox(width: 8.w),
                          Text(
                            "diary_distribution".tr,
                            style: StyleConfig.otherStyle(
                                color: ColorConfig.searchTextColor,
                                fontWeight: FontWeight.w600,
                                fontSize: 18),
                          )
                        ],
                      ),
                    ),
                    Container(
                      margin:
                          EdgeInsets.only(top: 14.h, left: 15.w, right: 15.w),
                      padding: EdgeInsets.symmetric(
                          vertical: 20.h, horizontal: 16.w),
                      width: 345.w,
                      height: 196.h,
                      decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(10.r),
                          color: Colors.white),
                      child: state.isReload
                          ? Column(
                              children: [
                                DiaryDistributionWidget(
                                  renderColor:
                                      const Color.fromRGBO(255, 128, 118, 1),
                                  value: state.distributionCount005,
                                  maxValue: state.distributionTotal,
                                  tag: 'diary_type_happy'.tr,
                                ),
                                SizedBox(height: 14.h),
                                DiaryDistributionWidget(
                                  renderColor:
                                      const Color.fromRGBO(255, 190, 101, 1),
                                  value: state.distributionCount004,
                                  maxValue: state.distributionTotal,
                                  tag: 'diary_type_good'.tr,
                                ),
                                SizedBox(height: 14.h),
                                DiaryDistributionWidget(
                                  renderColor:
                                      const Color.fromRGBO(163, 233, 146, 1),
                                  value: state.distributionCount003,
                                  maxValue: state.distributionTotal,
                                  tag: 'diary_type_normal'.tr,
                                ),
                                SizedBox(height: 14.h),
                                DiaryDistributionWidget(
                                  renderColor:
                                      const Color.fromRGBO(118, 194, 245, 1),
                                  value: state.distributionCount002,
                                  maxValue: state.distributionTotal,
                                  tag: 'diary_type_bad'.tr,
                                ),
                                SizedBox(height: 14.h),
                                DiaryDistributionWidget(
                                  renderColor:
                                      const Color.fromRGBO(188, 137, 227, 1),
                                  value: state.distributionCount001,
                                  maxValue: state.distributionTotal,
                                  tag: 'diary_type_very_bad'.tr,
                                )
                              ],
                            )
                          : const SizedBox(),
                    ),
                    Container(
                      margin: EdgeInsets.only(left: 20.w, top: 20.h),
                      child: Row(
                        children: [
                          ThemeImagePath(
                            fileName: 'diary_analysis_image.png',
                            imgWidget: 6.w,
                            imgHeight: 17.h,
                          ),
                          SizedBox(width: 8.w),
                          Text(
                            "diary_trend".tr,
                            style: StyleConfig.otherStyle(
                                color: ColorConfig.searchTextColor,
                                fontWeight: FontWeight.w600,
                                fontSize: 18),
                          )
                        ],
                      ),
                    ),
                    Container(
                      margin:
                          EdgeInsets.only(top: 14.h, left: 15.w, right: 15.w),
                      padding: EdgeInsets.symmetric(
                          vertical: 20.h, horizontal: 16.w),
                      width: 345.w,
                      height: 216.h,
                      decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(10.r),
                          color: Colors.white),
                      child: DiaryTrendWidget(
                        mode: state.tModel,
                        weekValues: state.weekValues,
                        monthValues: state.monthValues,
                        yearValues: state.yearValues,
                      ),
                    ),
                    SizedBox(height: ScreenUtil().bottomBarHeight + 20.h)
                  ],
                ),
              );
            },
            initBuilder: () => const LoadStatusWidget(),
          ),
        );
      },
    );
  }
}
