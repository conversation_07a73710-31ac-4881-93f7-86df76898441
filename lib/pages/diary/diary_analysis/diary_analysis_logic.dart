import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:getx_xiaopa/base/base.dart';
import 'package:getx_xiaopa/extension/widget_extension.dart';
import 'package:getx_xiaopa/network/http_client.dart';
import 'package:getx_xiaopa/network/model/request_body.dart';
import 'package:getx_xiaopa/network/model/result.dart';
import 'package:getx_xiaopa/pages/diary/diary_analysis/widget/diary_trend_widget.dart';
import 'package:getx_xiaopa/pages/diary/model/diary_statics_items_result.dart';
import 'package:getx_xiaopa/storage/storage.dart';
import 'package:getx_xiaopa/utils/utils.dart';

import 'diary_analysis_state.dart';
import 'package:intl/intl.dart';

class DiaryAnalysisLogic extends BaseCommonController {
  @override
  final DiaryAnalysisState state = DiaryAnalysisState();

  @override
  void initData() async {
    state.sController.addListener(() {
      ///监听滚动位置设置导航栏颜色
      double opacity;
      opacity = state.sController.offset / 100;

      if (opacity > 0.8) opacity = 1;
      if (opacity > 1) opacity = 1;

      state.navColor = state.sController.offset > 10.h
          ? Colors.white.withValues(alpha: opacity)
          : Colors.transparent;

      update(["diary_analysis_view"]);
    });

    await _moodStatisticsList(
      type: state.requestType,
      between: _getWeekStartAndEndDate(state.currentDate),
    );
  }

  @override
  void onHidden() {}

  @override
  void onClose() {
    super.onClose();
    state.sController.dispose();
  }

  tapAction(index, date) {
    state.currentDate = date;
    state.isReload = false;
    update(["diary_analysis_view"]);
    if (index == 0) {
      state.distributionTotal = 21;
      state.tModel = TrendMode.week;
      state.requestType = 1;
      List<String> temp = _getWeekStartAndEndDate(date);
      _moodStatisticsList(type: state.requestType, between: temp);
    }
    if (index == 1) {
      state.distributionTotal = _daysInMonth(date) * 3;
      state.tModel = TrendMode.month;
      state.requestType = 1;
      List<String> temp = _getMonthStartAndEndDate(date);
      _moodStatisticsList(type: state.requestType, between: temp);
    }
    if (index == 2) {
      bool isLeapYear = _isLeapYear(date.year);
      state.distributionTotal = isLeapYear ? 366 : 365;
      state.tModel = TrendMode.year;
      state.requestType = 3;
      List<String> temp = _getYearStartAndEndDate(date);
      _moodStatisticsList(type: state.requestType, between: temp);
    }
  }

  dateTimeChange(String str, DateTime date) {
    state.currentDate = date;
    if (str == "week") {
      List<String> temp = _getWeekStartAndEndDate(date);
      _moodStatisticsList(type: state.requestType, between: temp);
    }
    if (str == "month") {
      List<String> temp = _getMonthStartAndEndDate(date);
      _moodStatisticsList(type: state.requestType, between: temp);
    }
    if (str == "year") {
      List<String> temp = _getYearStartAndEndDate(date);
      _moodStatisticsList(type: state.requestType, between: temp);
    }
  }

  ///获取到当月有多少天
  double _daysInMonth(DateTime date) {
    final firstDayNextMonth = DateTime(date.year, date.month + 1, 1);
    final lastDayThisMonth =
        firstDayNextMonth.subtract(const Duration(days: 1));
    return lastDayThisMonth.day.toDouble();
  }

  ///判断是否是闰年  闰年是366天  其他365天
  bool _isLeapYear(int year) =>
      (year % 4 == 0) && ((year % 100 != 0) || (year % 400 == 0));

  /// 获取传入日期的本周第一天和最后一天（周日开始）
  _getWeekStartAndEndDate(DateTime date) {
    // 获取本周的第一天（周日）
    final firstDayOfWeek = date.subtract(Duration(days: date.weekday % 7));
    // 获取本周的最后一天（周六）
    final lastDayOfWeek = firstDayOfWeek.add(const Duration(days: 6));

    // 将起始和结束时间都往后推一天
    final shiftedStart = firstDayOfWeek.add(const Duration(days: 1));
    final shiftedEnd = lastDayOfWeek.add(const Duration(days: 1));

    return [
      "${shiftedStart.year}-${shiftedStart.month.toString().padLeft(2, '0')}-${shiftedStart.day.toString().padLeft(2, '0')} 00:00:00",
      "${shiftedEnd.year}-${shiftedEnd.month.toString().padLeft(2, '0')}-${shiftedEnd.day.toString().padLeft(2, '0')} 23:59:59"
    ];
  }

  /// 获取传入日期的本周的每一天（周日开始）
  List<String> _getWeekDays(DateTime date) {
    // 获取本周的第一天（周日）
    final firstDayOfWeek = date.subtract(Duration(days: date.weekday % 7));

    final formatter = DateFormat('yyyy-MM-dd');

    // 生成一周的日期列表
    return List.generate(7,
        (index) => formatter.format(firstDayOfWeek.add(Duration(days: index))));
  }

  /// 获取传入月份的每一天
  List<String> _getMonthDays(DateTime date) {
    final firstDayOfMonth = DateTime(date.year, date.month, 1);
    final lastDayOfMonth = DateTime(date.year, date.month + 1, 0);
    final formatter = DateFormat('yyyy-MM-dd');
    return List.generate(
        lastDayOfMonth.day,
        (index) =>
            formatter.format(firstDayOfMonth.add(Duration(days: index))));
  }

  /// 获取传入年份的每一月
  List<String> _getYearDays(DateTime date) {
    final formatter = DateFormat('yyyy-MM');
    return List.generate(
        12, (index) => formatter.format(DateTime(date.year, index + 1, 1)));
  }

  /// 获取传入月份的第一天和最后一天 [2025-06-02 00:00:00, 2025-07-01 23:59:59]
  _getMonthStartAndEndDate(DateTime date) {
    final firstDayOfMonth = DateTime(date.year, date.month, 1);
    final lastDayOfMonth = DateTime(date.year, date.month + 1, 0);

    // 将起始和结束时间都往后推一天
    final shiftedStart = firstDayOfMonth.add(const Duration(days: 1));
    final shiftedEnd = lastDayOfMonth.add(const Duration(days: 1));

    return [
      "${shiftedStart.year}-${shiftedStart.month.toString().padLeft(2, '0')}-${shiftedStart.day.toString().padLeft(2, '0')} 00:00:00",
      "${shiftedEnd.year}-${shiftedEnd.month.toString().padLeft(2, '0')}-${shiftedEnd.day.toString().padLeft(2, '0')} 23:59:59"
    ];
  }

  ///获取传入年份的1月和12月 [2025-02-01 00:00:00, 2026-01-01 23:59:59]
  _getYearStartAndEndDate(DateTime date) {
    final firstDayOfYear = DateTime(date.year, 2, 1);
    final lastDayOfYear = DateTime(date.year + 1, 1, 1);
    return [
      "${firstDayOfYear.year}-${firstDayOfYear.month.toString().padLeft(2, '0')}-${firstDayOfYear.day.toString().padLeft(2, '0')} 00:00:00",
      "${lastDayOfYear.year}-${lastDayOfYear.month.toString().padLeft(2, '0')}-${lastDayOfYear.day.toString().padLeft(2, '0')} 23:59:59"
    ];
  }

  ///========================网络请求==========================
  ///获取心情统计列表
  _moodStatisticsList({int type = 1, List<String> between = const []}) async {
    var map = RequestBody(pagination: false, filters: [
      Filter(
          expr: "=",
          name: "device_id",
          value: UserStore.to.deviceList[UserStore.to.selectDevice].id),
      Filter(expr: "=", name: "type", value: type),
      Filter(expr: "between", name: "created_at", value: between)
    ]).toJson();
    Result result = await http.diaryStatisticItems(map);
    if (result.code == 0) {
      state.distributionCount001 = 0;
      state.distributionCount002 = 0;
      state.distributionCount003 = 0;
      state.distributionCount004 = 0;
      state.distributionCount005 = 0;

      state.diaryStaticsItems.clear();
      state.completedDates.clear();
      state.weekValues.clear();
      state.monthValues.clear();
      state.yearValues.clear();

      _moodStatisticsCurrent(type: type, itemsModel: result.data.data);
    } else {
      CToast.showToast(result.msg!);
    }
  }

  _moodStatisticsCurrent(
      {int type = 1, required List<DiaryStaticsItemsModel> itemsModel}) async {
    var map = {
      "device_id": UserStore.to.deviceList[UserStore.to.selectDevice].id,
      "type": type
    };
    Result result = await http.diaryStatisticCurrent(map);
    if (result.code == 0) {
      ///先把列表的存起来
      state.diaryStaticsItems = itemsModel;

      ///在保存当前请求的
      DiaryStaticsItemsModel temp =
          DiaryStaticsItemsModel.fromJson(result.data);
      state.diaryStaticsItems.add(temp);

      Map<String, List<String>> dateTemp = {};

      for (var item in state.diaryStaticsItems) {
        if (type == 3) {
          var data = jsonDecode(item.data!);
          for (var k in data["mood_counts"].keys) {
            _distributionCountYear(k, data["mood_counts"][k]);

            _moodColorYear(k, item.duration!);
          }

          dateTemp[item.duration!] = [];
          if (state.tModel == TrendMode.year) {
            if (data["average_score"] != null) {
              dateTemp[item.duration!]!.add(data["average_score"].toString());
            }
          }
        } else {
          var data = item.data!.split(",");

          _moodColor(data[0], item.duration!);
          _moodColor(data[1], item.duration!);
          _moodColor(data[2], item.duration!);

          _distributionCount(data[0]);
          _distributionCount(data[1]);
          _distributionCount(data[2]);

          dateTemp[item.duration!] = [];
          if (state.tModel == TrendMode.week) {
            dateTemp[item.duration!]!.addAll([data[0], data[1], data[2]]);
          }
          if (state.tModel == TrendMode.month) {
            dateTemp[item.duration!]!.add(data[3]);
          }
          if (state.tModel == TrendMode.year) {
            dateTemp[item.duration!]!.add(data[0]);
          }
        }
      }

      _trendData(dateTemp);
    } else {
      CToast.showToast(result.msg!);
    }
    state.isReload = true;
    netState = NetState.dataSuccessState;
    update(["diary_analysis_view"]);
  }

  ///曲线数据
  _trendData(Map<String, List<String>> date) {
    logD(date);

    ///周视图处理
    if (state.tModel == TrendMode.week) {
      List<String> dateTemp = _getWeekDays(state.currentDate);
      for (var d in dateTemp) {
        List<int> temp = [];
        for (var v in date.keys) {
          if (v.contains(d)) {
            temp.addAll(date[v]!.map((e) => int.parse(e)));
          }
        }
        state.weekValues.add(temp);
      }
    }

    ///月视图处理
    if (state.tModel == TrendMode.month) {
      List<String> dateTemp = _getMonthDays(state.currentDate);
      for (var d in dateTemp) {
        List<double> temp = [];
        for (var v in date.keys) {
          if (v.contains(d)) {
            temp.addAll(date[v]!.map((e) => double.parse(e)));
          }
        }
        if (temp.isEmpty) {
          temp.add(0);
        }
        state.monthValues.addAll(temp);
      }
    }

    ///年视图处理
    if (state.tModel == TrendMode.year) {
      List<String> dateTemp = _getYearDays(state.currentDate);

      for (var d in dateTemp) {
        List<double> temp = [];

        for (var v in date.keys) {
          if (v.contains(d)) {
            temp.addAll(date[v]!.map((e) {
              return double.parse(e);
            }));
          }
        }
        if (temp.isEmpty) {
          temp.add(0);
        }
        state.yearValues.addAll(temp);
      }
    }
  }

  ///记录心情的颜色（周视图和月视图）
  _moodColor(String data, String date) {
    int mood = int.parse(data);
    if (mood == 0) return;
    Map<String, List<Color>> temp = {};
    temp[date] = [];

    switch (mood) {
      case 1:
        temp[date]!.add(const Color.fromRGBO(188, 137, 227, 1));
        break;
      case 2:
        temp[date]!.add(const Color.fromRGBO(118, 194, 245, 1));
        break;
      case 3:
        temp[date]!.add(const Color.fromRGBO(163, 233, 146, 1));
        break;
      case 4:
        temp[date]!.add(const Color.fromRGBO(255, 190, 101, 1));
        break;
      case 5:
        temp[date]!.add(const Color.fromRGBO(255, 128, 118, 1));
        break;
    }

    if (state.completedDates.keys.contains(date)) {
      state.completedDates[date]!.addAll(temp[date]!);
    } else {
      state.completedDates[date] = temp[date]!;
    }
  }

  ///记录心情的颜色（年视图）
  _moodColorYear(String data, String date) {
    Map<String, List<Color>> temp = {};
    temp[date] = [];
    switch (data) {
      case "开心":
        temp[date]!.add(const Color.fromRGBO(255, 128, 118, 1));
        break;
      case "好":
        temp[date]!.add(const Color.fromRGBO(255, 190, 101, 1));
        break;
      case "一般":
        temp[date]!.add(const Color.fromRGBO(163, 233, 146, 1));
        break;
      case "不好":
        temp[date]!.add(const Color.fromRGBO(118, 194, 245, 1));
        break;
      case "很不好":
        temp[date]!.add(const Color.fromRGBO(188, 137, 227, 1));

        break;
    }
    if (state.completedDates.keys.contains(date)) {
      state.completedDates[date]!.addAll(temp[date]!);
    } else {
      state.completedDates[date] = temp[date]!;
    }
  }

  ///记录心情次数（周视图和月视图）
  _distributionCount(String data) {
    int distribution = int.parse(data);
    switch (distribution) {
      case 1:
        state.distributionCount001++;
        break;
      case 2:
        state.distributionCount002++;
        break;
      case 3:
        state.distributionCount003++;
        break;
      case 4:
        state.distributionCount004++;
        break;
      case 5:
        state.distributionCount005++;
        break;
    }
  }

  _distributionCountYear(String kData, int vData) {
    switch (kData) {
      case "开心":
        state.distributionCount005 += vData;
        break;
      case "好":
        state.distributionCount004 += vData;
        break;
      case "一般":
        state.distributionCount003 += vData;
        break;
      case "不好":
        state.distributionCount002 += vData;
        break;
      case "很不好":
        state.distributionCount001 += vData;
        break;
      default:
    }
  }
}
