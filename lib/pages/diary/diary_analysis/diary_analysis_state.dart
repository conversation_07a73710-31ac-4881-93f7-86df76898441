import 'package:flutter/material.dart';
import 'package:getx_xiaopa/pages/diary/diary_analysis/widget/diary_trend_widget.dart';
import 'package:getx_xiaopa/pages/diary/model/diary_statics_items_result.dart';

class DiaryAnalysisState {
  Color navColor = Colors.transparent;
  final ScrollController sController = ScrollController();

  /// 代表心情分布的每个值
  /// 很不好
  double distributionCount001 = 0;

  /// 不好
  double distributionCount002 = 0;

  /// 一般
  double distributionCount003 = 0;

  /// 好
  double distributionCount004 = 0;

  /// 很好
  double distributionCount005 = 0;

  /// 代表选择了周  默认7天
  double distributionTotal = 21;

  ///请求的type 1=日 2=月 3=年
  int requestType = 1;

  ///日历外圈数据
  Map<String, List<Color>> completedDates = {};

  ///心情统计列表
  List<DiaryStaticsItemsModel> diaryStaticsItems = [];

  ///曲线图的样式
  TrendMode tModel = TrendMode.week;

  ///当前时间
  DateTime currentDate = DateTime.now();

  ///曲线数据
  List<List<int>> weekValues = [];
  List<double> monthValues = [];
  List<double> yearValues = [];

  bool isReload = false;

  DiaryAnalysisState() {
    ///Initialize variables
  }
}
