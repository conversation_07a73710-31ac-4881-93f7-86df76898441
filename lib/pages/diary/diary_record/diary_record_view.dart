import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:getx_xiaopa/base/base_list_view.dart';
import 'package:getx_xiaopa/extension/images.dart';
import 'package:getx_xiaopa/extension/widget_extension.dart';
import 'package:getx_xiaopa/pages/diary/diary_record/widget/diary_calendar_widget.dart';
import 'package:getx_xiaopa/pages/diary/diary_record/widget/diary_item.dart';
import 'package:getx_xiaopa/r.dart';
import 'package:getx_xiaopa/values/values.dart';
import 'package:getx_xiaopa/widget/empty_data_widget.dart';
import 'package:getx_xiaopa/widget/load_status_widget.dart';
import 'package:getx_xiaopa/widget/theme_image.dart';

import 'diary_record_logic.dart';
import 'diary_record_state.dart';

class DiaryRecordPage extends BaseListView {
  DiaryRecordPage({super.key});

  final DiaryRecordLogic logic = Get.put(DiaryRecordLogic());
  final DiaryRecordState state = Get.find<DiaryRecordLogic>().state;

  @override
  Color? get navColor => Colors.transparent;

  @override
  String? get navTitle => "diary_record".tr;

  @override
  TextStyle? get navTitleStyle =>
      StyleConfig.blackStyle(fontSize: 18, fontWeight: FontWeight.w500);

  @override
  double? get leftWidth => 35.w;

  @override
  Widget? get leftButton => Padding(
        padding: EdgeInsets.only(left: 15.w),
        child: Images(
          path: R.back_png,
          width: 20.w,
          height: 20.h,
          scale: 2.8,
        ).inkWell(() => Get.back()),
      );

  @override
  List<Widget>? get rightActionList => [
        Padding(
          padding: EdgeInsets.only(right: 20.w),
          child: ThemeImagePath(
            fileName: 'diary_calendar.png',
            imgWidget: 32.r,
            imgHeight: 32.r,
          ),
        ).inkWell(() => logic.toAnalysisPage())
      ];

  @override
  Color? get contentColor => const Color.fromRGBO(249, 249, 249, 1);

  @override
  bool? get extendBodyBehindAppBar => true;

  @override
  Widget buildContent() {
    return GetBuilder<DiaryRecordLogic>(
      id: "diary_record_view",
      builder: (_) {
        return Column(
          children: [
            GestureDetector(
              onVerticalDragUpdate: logic.onDragUpdate,
              onVerticalDragEnd: logic.onDragEnd,
              child: DiaryCalendarWidget(
                focusedDay: state.signDate,
                completedDates: state.completedDates,
                controller: state.diaryCalendarController,
                onTap: (date) {
                  logic.toMoodList(date);
                },
                onPageChanged: (date) {
                  logic.onPageChanged(date);
                },
              ),
            ),
            Expanded(
              child: createRefresherListView(
                logic,
                enablePullUp: false,
                (_) {
                  return state.diaryItemsMap.isNotEmpty
                      ? ListView.builder(
                          itemCount: state.diaryItemsMap.keys.length,
                          itemBuilder: (_, index) {
                            String dateKey =
                                state.diaryItemsMap.keys.toList()[index];
                            return DiaryItemGroup(
                              dateKey: dateKey,
                              items: state.diaryItemsMap[dateKey]!,
                              onTap: (date) {
                                logic.itemTap(date);
                              },
                            );
                          },
                        )
                      : Container(
                          margin: EdgeInsets.only(bottom: 80.h),
                          child: EmptyDataWidget(
                            title: "diary_record_empty_tips".tr,
                            imagePath: R.no_data_diary_png,
                          ),
                        );
                },
                initBuilder: () => const LoadStatusWidget(),
              ),
            ),
            SizedBox(height: 20.h)
          ],
        );
      },
    );
  }
}
