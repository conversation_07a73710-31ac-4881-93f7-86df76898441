import 'package:flutter/material.dart';
import 'package:getx_xiaopa/pages/diary/diary_record/widget/diary_calendar_widget.dart';
import 'package:getx_xiaopa/pages/diary/diary_record/widget/diary_item.dart';

class DiaryRecordState {
  final double minHeight = 200;
  final double maxHeight = 400;
  double currentHeight = 100;

  final ScrollController scrollController = ScrollController();

  bool get isExpanded =>
      currentHeight > (minHeight + (maxHeight - minHeight) / 2);

  ///已完成喝水的日期List
  List<DateTime> completedDates = [];

  final DiaryCalendarController<bool> diaryCalendarController =
      DiaryCalendarController();

  Map<String, List<DiaryItemData>> diaryItemsMap = {};

  DateTime signDate = DateTime.now();

  DiaryRecordState() {
    ///Initialize variables
  }
}
