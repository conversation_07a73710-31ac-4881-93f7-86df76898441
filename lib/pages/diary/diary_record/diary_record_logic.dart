import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:getx_xiaopa/base/base.dart';
import 'package:getx_xiaopa/extension/widget_extension.dart';
import 'package:getx_xiaopa/network/http_client.dart';
import 'package:getx_xiaopa/network/model/request_body.dart';
import 'package:getx_xiaopa/pages/diary/diary_record/widget/diary_item.dart';
import 'package:getx_xiaopa/pages/diary/model/diary_items_result.dart';
import 'package:getx_xiaopa/routes/router.dart';
import 'package:getx_xiaopa/storage/storage.dart';
import 'package:getx_xiaopa/utils/utils.dart';

import 'diary_record_state.dart';

class DiaryRecordLogic extends BaseListController
    with GetSingleTickerProviderStateMixin {
  @override
  final DiaryRecordState state = DiaryRecordState();
  late AnimationController _controller;
  late Animation<double> _animation;

  @override
  void initData() async {
    _controller = AnimationController(
        vsync: this, duration: const Duration(milliseconds: 300));

    _diaryItems();
    _diaryItemsByMonth();

    if (CommonStore.to.diaryThingItems.isEmpty) {
      await CommonStore.to.getDiaryThingItems();
    }
  }

  @override
  void onHidden() {}

  void onDragUpdate(DragUpdateDetails details) {
    state.currentHeight += details.delta.dy;
    state.currentHeight =
        state.currentHeight.clamp(state.minHeight, state.maxHeight);
    update(["diary_record_view"]);
  }

  void onDragEnd(DragEndDetails details) {
    final shouldExpand = state.isExpanded;
    _animateTo(shouldExpand ? state.maxHeight : state.minHeight);

    state.diaryCalendarController.onDragEnd(shouldExpand);

    update(["diary_record_view"]);
  }

  void _animateTo(double targetHeight) {
    _animation =
        Tween<double>(begin: state.currentHeight, end: targetHeight).animate(
      CurvedAnimation(parent: _controller, curve: Curves.easeOut),
    )..addListener(() {
            state.currentHeight = _animation.value;
            update(["diary_record_view"]);
          });
    _controller.forward(from: 0);
  }

  itemTap(date) {
    String temp = _getDiaryDate(date);
    DateTime tempDate = _parseChineseDate(temp, DateTime.now().year);
    toMoodList(tempDate);
  }

  DateTime _parseChineseDate(String input, int year) {
    final monthText = 'diary_date_month'.tr;
    final dayText = 'diary_date_day'.tr;
    final pattern = RegExp(r'(\d{1,2})' + monthText + r'(\d{1,2})' + dayText);
    final match = pattern.firstMatch(input);
    if (match != null) {
      return DateTime(
          year, int.parse(match.group(1)!), int.parse(match.group(2)!));
    }
    throw const FormatException('');
  }

  toMoodList(DateTime date) {
    Get.toNamed(AppRoutes.DIARY_MOOD, arguments: date)?.then((value) {
      netState = NetState.initializeState;
      update(["diary_record_view"]);
      _diaryItems();
      _diaryItemsByMonth();
    });
  }

  onPageChanged(DateTime date) {
    ///月份不同则请求数据
    state.signDate = date;
    _diaryItems();
    _diaryItemsByMonth();
  }

  toAnalysisPage() {
    Get.toNamed(AppRoutes.DIARY_ANALYSIS);
  }

  @override
  void onClose() {
    _controller.dispose();
    state.scrollController.dispose();
    super.onClose();
  }

  /// ==================网络请求===============================
  @override
  void refreshData() {
    _diaryItems();

    super.refreshData();
  }

  /// 心情日记记录
  _diaryItems() async {
    state.diaryItemsMap.clear();
    netState = NetState.initializeState;
    update(["diary_record_view"]);

    var map = RequestBody(pagination: false, filters: [
      Filter(
          expr: "=",
          name: "device_id",
          value: UserStore.to.deviceList[UserStore.to.selectDevice].id),
      Filter(
        expr: "between",
        name: "created_at",
        value: state.isExpanded
            ? _getMonthStartAndEndDate(state.signDate)
            : _getWeekStartAndEndDate(state.signDate),
      ),
    ]).toJson();
    final result = await http.diaryItems(map);
    if (result.code == 0) {
      if (result.data!.data!.isNotEmpty) {
        Map<String, List<DiaryItemData>> tempMap = {};

        for (DiaryItemsModel model in result.data!.data!) {
          String temp = TimeUtil.getSimpleDate(model.createdAt!, isHMS: false);
          DiaryItemData item = DiaryItemData();

          item.title = CommonStore.to.getMoodName(model.mood!);
          item.dotColor = _getMoodColor(model.mood!);
          item.time = model.createdAt!;
          item.eventTags = model.thing;
          item.signTags = model.feeling;
          item.content = model.content;
          item.imageUrl = model.picture;

          if (!tempMap.containsKey(temp)) {
            tempMap[temp] = [];
          }
          tempMap[temp]!.add(item);
        }
        state.diaryItemsMap = tempMap;
      }

      refreshController.refreshCompleted();
      netState = NetState.dataSuccessState;
      update(["diary_record_view"]);
    } else {
      CToast.showToast(result.msg!);
    }
  }

  /// 获取传入日期的本周第一天和最后一天（周日开始）
  _getWeekStartAndEndDate(DateTime date) {
    // 获取本周的第一天（周日）
    final firstDayOfWeek = date.subtract(Duration(days: date.weekday % 7));
    // 获取本周的最后一天（周六）
    final lastDayOfWeek = firstDayOfWeek.add(const Duration(days: 6));

    // 将起始和结束时间都往后推一天
    final shiftedStart = firstDayOfWeek.add(const Duration(days: 0));
    final shiftedEnd = lastDayOfWeek.add(const Duration(days: 0));

    return [
      "${shiftedStart.year}-${shiftedStart.month.toString().padLeft(2, '0')}-${shiftedStart.day.toString().padLeft(2, '0')} 00:00:00",
      "${shiftedEnd.year}-${shiftedEnd.month.toString().padLeft(2, '0')}-${shiftedEnd.day.toString().padLeft(2, '0')} 23:59:59"
    ];
  }

  /// 请求一个月的日记，标记有心情的情况
  _diaryItemsByMonth() async {
    var map = RequestBody(pagination: false, filters: [
      Filter(
          expr: "=",
          name: "device_id",
          value: UserStore.to.deviceList[UserStore.to.selectDevice].id),
      Filter(
          expr: "between",
          name: "created_at",
          value: _getMonthStartAndEndDate(state.signDate))
    ]).toJson();
    final result = await http.diaryItems(map);
    if (result.code == 0) {
      state.completedDates.clear();
      for (DiaryItemsModel model in result.data!.data!) {
        DateTime temp = DateTime.parse(model.createdAt!);
        state.completedDates.add(DateTime(temp.year, temp.month, temp.day));
      }

      update(["diary_record_view"]);
    } else {
      CToast.showToast(result.msg!);
    }
  }

  /// 获取本月第一天和最后一天
  _getMonthStartAndEndDate(DateTime date) {
    final firstDayOfMonth = DateTime(date.year, date.month, 1);
    final lastDayOfMonth = DateTime(date.year, date.month + 1, 0);
    return [firstDayOfMonth.toString(), lastDayOfMonth.toString()];
  }

  /// 获取日期
  /// isTime是否返回时间
  _getDiaryDate(String date, {bool isTime = false}) {
    DateTime temp = DateTime.parse(date).toLocal();
    return isTime
        ? "${temp.hour.toString().padLeft(2, "0")}:${temp.minute.toString().padLeft(2, "0")}"
        : "${temp.month}${'diary_date_month'.tr}${temp.day}${"diary_date_day".tr}";
  }

  ///返回对应心情的颜色
  _getMoodColor(String mood) {
    switch (mood) {
      case '开心':
      case 'Happy':
        return const Color.fromRGBO(255, 128, 118, 1);
      case '好':
      case 'Good':
        return const Color.fromRGBO(255, 190, 101, 1);
      case '一般':
      case 'Mediocre':
        return const Color.fromRGBO(163, 233, 146, 1);
      case '不好':
      case 'Bad':
        return const Color.fromRGBO(118, 194, 245, 1);
      case '很不好':
      case 'Terrible':
        return const Color.fromRGBO(118, 137, 227, 1);
      default:
        return const Color.fromRGBO(242, 241, 245, 1);
    }
  }
}
