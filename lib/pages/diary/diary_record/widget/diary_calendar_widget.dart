import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:getx_xiaopa/storage/storage.dart';
import 'package:getx_xiaopa/utils/utils.dart';
import 'package:getx_xiaopa/values/values.dart';
import 'package:getx_xiaopa/widget/theme_image.dart';
import 'package:getx_xiaopa/widget/theme_text.dart';
import 'package:table_calendar/table_calendar.dart';

class DiaryCalendarController<T> {
  ValueChanged<T>? _onDragEndCallback;

  void _bind({required ValueChanged<T> onDragEnd}) {
    _onDragEndCallback = onDragEnd;
  }

  void onDragEnd(value) {
    _onDragEndCallback?.call(value);
  }
}

class DiaryCalendarWidget extends StatefulWidget {
  final DateTime focusedDay;
  final List<DateTime> completedDates;
  final DiaryCalendarController controller;
  final Function(DateTime)? onTap;
  final Function(DateTime)? onPageChanged;

  const DiaryCalendarWidget(
      {super.key,
      required this.focusedDay,
      required this.completedDates,
      required this.controller,
      this.onTap,
      this.onPageChanged});

  @override
  State<DiaryCalendarWidget> createState() => _DiaryCalendarWidgetState();
}

class _DiaryCalendarWidgetState extends State<DiaryCalendarWidget> {
  CalendarFormat calendarFormat = CalendarFormat.week;

  @override
  void initState() {
    super.initState();
    widget.controller._bind(onDragEnd: _onDragEnd);
  }

  void _onDragEnd(value) {
    setState(() {
      calendarFormat = value ? CalendarFormat.month : CalendarFormat.week;
    });
  }

  @override
  Widget build(BuildContext context) {
    return ThemeContainerImage(
      fileName: 'diary_record_bg.png',
      fit: BoxFit.fill,
      padding: EdgeInsets.only(
          top: ScreenUtil().statusBarHeight + 64.h,
          bottom: 5.h,
          left: 10.w,
          right: 10.w),
      conWidget: 1.sw,
      child: TableCalendar(
        availableGestures: AvailableGestures.none,
        firstDay: DateTime.utc(2020, 1, 1),
        lastDay: DateTime.utc(2030, 12, 31),
        focusedDay: widget.focusedDay,
        calendarFormat: calendarFormat,
        rowHeight: 40.h,
        daysOfWeekHeight: 30.h,
        locale: ConfigStore.to.locale.toString(),
        headerStyle: HeaderStyle(
            formatButtonVisible: false,
            titleCentered: true,
            leftChevronMargin: EdgeInsets.symmetric(horizontal: 30.w),
            rightChevronMargin: EdgeInsets.symmetric(horizontal: 30.w),
            leftChevronIcon: Container(
              width: 20.r,
              height: 20.r,
              decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(20.r),
                  color: const Color.fromRGBO(242, 241, 245, 1)),
              child: Center(
                child: Icon(
                  size: 20.r,
                  Icons.chevron_left,
                  color: const Color.fromRGBO(185, 185, 185, 1),
                ),
              ),
            ),
            rightChevronIcon: Container(
              width: 20.r,
              height: 20.r,
              decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(20.r),
                  color: const Color.fromRGBO(242, 241, 245, 1)),
              child: Center(
                child: Icon(
                  size: 20.r,
                  Icons.chevron_right,
                  color: const Color.fromRGBO(185, 185, 185, 1),
                ),
              ),
            ),
            headerPadding: EdgeInsets.zero,
            titleTextStyle: StyleConfig.otherStyle(
                color: ColorConfig.searchTextColor,
                fontWeight: FontWeight.w400,
                fontSize: 18)),
        onDaySelected: (sDate, eDate) => widget.onTap?.call(sDate),
        onPageChanged: (focusedDay) {
          widget.onPageChanged?.call(focusedDay);
        },
        calendarStyle: const CalendarStyle(
          outsideDaysVisible: false,
          isTodayHighlighted: false,
        ),
        calendarBuilders: CalendarBuilders(
          dowBuilder: (context, day) {
            final text = [
              'diary_week_seven'.tr,
              'diary_week_one'.tr,
              'diary_week_two'.tr,
              'diary_week_three'.tr,
              'diary_week_four'.tr,
              'diary_week_five'.tr,
              'diary_week_six'.tr
            ][day.weekday % 7];
            return Center(
              child: Text(
                text,
                style: StyleConfig.otherStyle(
                    color: ColorConfig.authenticationTextColor, fontSize: 14),
              ),
            );
          },

          ///设置不是本月的样式
          outsideBuilder: (context, date, _) => _buildDayCell(date),
          defaultBuilder: (context, date, _) => _buildDayCell(date),
          todayBuilder: (context, date, _) => _buildDayCell(date),
          selectedBuilder: (context, date, _) => _buildDayCell(date),
        ),
      ),
    );
  }

  bool _isCompleted(DateTime date) {
    return widget.completedDates.any((d) => _isSameDay(d, date));
  }

  bool _isSameDay(DateTime a, DateTime b) {
    return a.year == b.year && a.month == b.month && a.day == b.day;
  }

  Widget _buildDayCell(DateTime date) {
    final bool isToday = _isSameDay(date, DateTime.now());
    final bool isCompleted = _isCompleted(date);

    return Column(
      children: [
        Container(
          margin: EdgeInsets.only(top: 3.h, bottom: 3.h),
          padding: EdgeInsets.all(3.r),
          decoration: isCompleted
              ? const BoxDecoration(
                  color: Color.fromRGBO(40, 39, 46, 1),
                  // borderRadius: radius,
                  shape: BoxShape.circle)
              : const BoxDecoration(
                  color: Color.fromRGBO(249, 249, 249, 1),
                  // borderRadius: radius,
                  shape: BoxShape.circle),
          child: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                ThemeText(
                  dataStr: '${date.day}',
                  keyName: 'textColor',
                  flag: isCompleted,
                  subColor: ColorConfig.shopDetailTextColor,
                  fontWeight: isCompleted || isCompleted
                      ? FontWeight.w500
                      : FontWeight.w400,
                  fontSize: 12,
                )
              ],
            ),
          ),
        ),
        if (isToday) ...{
          Container(
            width: 6.r,
            height: 6.r,
            decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: Get.find<ThemeColorController>().gDiaryCircle),
          )
        }
      ],
    );
  }
}
