import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:getx_xiaopa/extension/images.dart';
import 'package:getx_xiaopa/extension/widget_extension.dart';
import 'package:getx_xiaopa/network/http_client.dart';
import 'package:getx_xiaopa/r.dart';
import 'package:getx_xiaopa/storage/storage.dart';
import 'package:getx_xiaopa/values/values.dart';
import 'package:getx_xiaopa/widget/widget.dart';

class DiaryItemGroup extends StatefulWidget {
  final String dateKey;
  final List<DiaryItemData> items;
  final Function(String) onTap;

  const DiaryItemGroup({
    super.key,
    required this.items,
    required this.onTap,
    required this.dateKey,
  });

  @override
  State<DiaryItemGroup> createState() => _DiaryItemGroupState();
}

class _DiaryItemGroupState extends State<DiaryItemGroup> {
  String _date = '';
  List<DiaryItemData> _diaryItems = [];
  String _dateShow = '';
  @override
  void initState() {
    super.initState();
    _date = widget.dateKey;
    if (widget.items.isNotEmpty) {
      _dateShow = _getDiaryDate(_date);
      _diaryItems = widget.items;
    }
  }

  _getDiaryDate(String date, {bool isTime = false}) {
    DateTime temp = DateTime.parse(date).toLocal();
    return isTime
        ? "${temp.hour.toString().padLeft(2, "0")}:${temp.minute.toString().padLeft(2, "0")}"
        : ConfigStore.to.getLocaleCode() == "zh"
            ? "${temp.month}${'diary_date_month'.tr}${temp.day}${"diary_date_day".tr}"
            : "${temp.month}/${temp.day}";
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.only(top: 10.h, left: 20.w, right: 20.w),
      padding:
          EdgeInsets.only(top: 12.h, left: 15.w, right: 15.w, bottom: 15.h),
      decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(10.r), color: Colors.white),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 日期标题
          Row(
            children: [
              Images(
                path: R.diary_item_image_png,
                width: 13.r,
                height: 13.r,
              ),
              SizedBox(width: 8.w),
              Text(_dateShow,
                  style: StyleConfig.otherStyle(
                      color: ColorConfig.searchTextColor,
                      fontWeight: FontWeight.w500)),
            ],
          ),
          // 多条心情
          ..._diaryItems.asMap().entries.map((entry) {
            final item = entry.value;
            final time = _getDiaryDate(item.time!, isTime: true);
            return DiaryItem(
              dotColor: item.dotColor ?? Colors.transparent,
              title: item.title ?? '',
              time: time,
              eventTags: item.eventTags ?? "",
              signTags: item.signTags ?? "",
              imageUrl: item.imageUrl,
              content: item.content,
            );
          }),
        ],
      ),
    ).inkWell(() => widget.onTap(_date));
  }
}

class DiaryItem extends StatefulWidget {
  final Color dotColor;
  final String time;
  final String title;
  final String eventTags;
  final String signTags;
  final String? imageUrl;
  final String? content;

  const DiaryItem({
    super.key,
    required this.dotColor,
    required this.time,
    required this.title,
    required this.eventTags,
    required this.signTags,
    this.imageUrl,
    this.content,
  });

  @override
  State<DiaryItem> createState() => _DiaryItemState();
}

class _DiaryItemState extends State<DiaryItem> {
  // final GlobalKey _contentKey = GlobalKey();
  // double _contentHeight = 0;
  String? displayedText = '';
  bool isShow = false;
  bool isOverflow = true;

  @override
  void initState() {
    super.initState();
    // if (widget.content != null) {
    //   isOverflow = widget.content!.length > 40;
    //   displayedText =
    //       !isOverflow ? widget.content : '${widget.content!.substring(0, 40)}…';
    //   WidgetsBinding.instance.addPostFrameCallback((_) => _measure());
    //   isShow = isOverflow;
    // }
  }

  // void _measure() {
  //   final box = _contentKey.currentContext?.findRenderObject() as RenderBox?;
  //   if (box != null && mounted) {
  //     final height = box.size.height;
  //     if (_contentHeight != height) {
  //       setState(() {
  //         _contentHeight = height;
  //       });
  //     }
  //   }
  // }

  // _onToggleExpand() {
  //   setState(() {
  //     isOverflow = !isOverflow;
  //     displayedText =
  //         !isOverflow ? widget.content : '${widget.content!.substring(0, 40)}…';
  //   });
  //   WidgetsBinding.instance.addPostFrameCallback((_) => _measure());
  // }

  @override
  Widget build(BuildContext context) {
    return IntrinsicHeight(
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Column(
            children: [
              SizedBox(height: 10.h),
              Container(
                width: 20.r,
                height: 20.r,
                decoration: BoxDecoration(
                  color: widget.dotColor,
                  shape: BoxShape.circle,
                ),
              ),
              Expanded(
                child: CustomPaint(
                  painter: DottedLinePainter(),
                  size: Size(12.r, double.infinity),
                ),
              )
            ],
          ),
          SizedBox(width: 16.w),
          Expanded(
            child: Container(
              margin: EdgeInsets.only(top: 8.h),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Text(widget.title,
                          style: StyleConfig.otherStyle(
                              color: ColorConfig.searchTextColor,
                              fontWeight: FontWeight.w600)),
                      const Expanded(child: SizedBox()),
                      Text(widget.time,
                          style: StyleConfig.otherStyle(
                              color: ColorConfig.shopDetailTextColor,
                              fontSize: 14)),
                      SizedBox(width: 10.w),
                      Images(
                        path: R.diary_right_01_png,
                        width: 9.w,
                        height: 16.h,
                      )
                    ],
                  ),
                  SizedBox(height: 6.h),
                  Wrap(
                    spacing: 5.w,
                    runSpacing: 6.h,
                    children: List.generate(widget.eventTags.split(",").length,
                        (index) {
                      return _buildTag(widget.eventTags.split(",")[index]);
                    }),
                  ),
                  SizedBox(height: 6.h),
                  Wrap(
                    spacing: 10.w,
                    runSpacing: 10.h,
                    children: List.generate(widget.signTags.split(",").length,
                        (index) {
                      return _buildSign(widget.signTags.split(",")[index]);
                    }),
                  ),
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Container(
                        // key: _contentKey,
                        margin: EdgeInsets.only(top: 10.w),
                        width: 267.w,
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              widget.content!,
                              style: StyleConfig.otherStyle(
                                color: ColorConfig.searchTextColor,
                                fontSize: 12,
                              ),
                            ),
                            // if (displayedText!.isNotEmpty) ...{
                            //   RichText(
                            //     text: TextSpan(
                            //       children: [
                            //         TextSpan(
                            //             text: displayedText!,
                            //             style: StyleConfig.otherStyle(
                            //               color: ColorConfig.searchTextColor,
                            //               fontSize: 12,
                            //             )),
                            //         if (isShow)
                            //           WidgetSpan(
                            //             alignment:
                            //                 PlaceholderAlignment.middle, // 中线对齐
                            //             child: GestureDetector(
                            //               onTap: _onToggleExpand,
                            //               child: Text(
                            //                 isOverflow
                            //                     ? '   ${"diary_record_item_show".tr}'
                            //                     : '   ${"diary_record_item_closed".tr}',
                            //                 style: StyleConfig.otherStyle(
                            //                     color: Get.find<
                            //                             ThemeColorController>()
                            //                         .getColor('iconColor'),
                            //                     fontSize: 12),
                            //               ),
                            //             ),
                            //           ),
                            //       ],
                            //     ),
                            //   ),
                            // },
                            if (widget.imageUrl != null &&
                                widget.imageUrl!.isNotEmpty) ...{
                              SizedBox(height: 12.h),
                              Row(
                                mainAxisAlignment: MainAxisAlignment.start,
                                children: List.generate(
                                    widget.imageUrl!.split(",").length,
                                    (index) {
                                  return Container(
                                    margin: EdgeInsets.only(right: 10.w),
                                    width: 59.r,
                                    height: 59.r,
                                    decoration: BoxDecoration(
                                      borderRadius: BorderRadius.circular(4.r),
                                    ),
                                    clipBehavior: Clip.hardEdge,
                                    child: Images(
                                      path: fileUrl(
                                          widget.imageUrl!.split(",")[index]),
                                      boxFit: BoxFit.contain,
                                    ),
                                  );
                                }),
                              ),
                            }
                          ],
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTag(String text) {
    return IntrinsicWidth(
      child: ThemeContainer(
        tPandding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 2.h),
        // tWidget: 79.w,
        radius: 4.r,
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Images(
              path: fileUrl(CommonStore.to.getDiaryThingImage(text)),
              width: 29.r,
              height: 29.r,
            ),
            SizedBox(width: 4.w),
            ThemeText(
              dataStr: text,
              keyName: 'diaryRecord',
              fontSize: 14,
            )
          ],
        ),
      ),
    );
  }

  Widget _buildSign(String text) {
    return IntrinsicWidth(
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 10.w, vertical: 4.h),
        decoration: BoxDecoration(
          color: const Color.fromRGBO(249, 249, 249, 1),
          borderRadius: BorderRadius.circular(4.r),
        ),
        child: Center(
          child: Text(text,
              style: StyleConfig.otherStyle(
                  color: ColorConfig.shopDetailTextColor, fontSize: 12)),
        ),
      ),
    );
  }
}

class DottedLinePainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    const dashHeight = 4.0, dashSpace = 4.0;
    double startY = 0;
    final paint = Paint()
      ..color = const Color.fromRGBO(215, 215, 215, 1)
      ..strokeWidth = 1;

    while (startY < size.height) {
      canvas.drawLine(
        Offset(size.width / 2, startY),
        Offset(size.width / 2, startY + dashHeight),
        paint,
      );
      startY += dashHeight + dashSpace;
    }
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => false;
}

class DiaryItemData {
  Color? dotColor;
  String? title;
  String? time;
  String? eventTags;
  String? signTags;
  String? imageUrl;
  String? content;

  DiaryItemData({
    this.dotColor,
    this.title,
    this.time,
    this.eventTags,
    this.signTags,
    this.imageUrl,
    this.content,
  });
}
