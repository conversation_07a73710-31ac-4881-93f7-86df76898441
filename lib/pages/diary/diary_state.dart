import 'package:flutter/material.dart';
import 'package:getx_xiaopa/pages/diary/model/diary_items_result.dart';
import 'package:getx_xiaopa/pages/diary/utils/mood_quote_manager.dart';

class DiaryState {
  /// 出现多少种心情
  int segmentCount = 0;

  List<Color> segmentColors = [];

  String quote = "";
  String moodImage = "";

  /// 图片上方的图片，心情不好和很不好就是贴纸(0)，其他的是花(1)
  int moodTopImage = 0;
  MoodQuoteManager moodQuoteManager = MoodQuoteManager();

  List<DiaryItemsModel> diaryItems = [];

  DiaryState() {
    ///Initialize variables
  }
}
