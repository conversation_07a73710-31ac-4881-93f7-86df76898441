import 'package:get/get.dart';
import 'package:getx_xiaopa/storage/storage.dart';
import 'package:intl/intl.dart';

class MoodQuoteManager {
  late int _todayIndex;

  static const _shownKey = "shown_quote_indices";
  static const _lastDateKey = "quote_last_date";
  static const _todayIndexKey = "quote_today_index";
  static const _moodKey = "mood_quote";

  final Map<String, List<String>> _quotes = {
    "开心".tr: [
      "感谢此刻的晴朗，也感谢你看到它。",
      "开心时别忘了多爱自己一点。",
      "有些日子，光是存在就值得庆祝。",
      "保持热爱，奔赴每一场山海。",
      "心里有光的人，总不会迷路。",
      "今天的你，刚刚好。",
      "所有的努力，正在开花。",
      "世界因为你，温柔了一点。",
      "此刻的快乐，是未来的力量。",
      "把好心情收进记忆里，哪天也别忘了。",
      "幸福不是突然降临，是你一直在积累。",
      "今天的你，刚刚好，别忘了拥抱自己。",
      "阳光照进心里，一切都变得温柔。",
      "所有美好都不应被忽略，尤其是你自己。",
      "别让忙碌吞噬了快乐的感受。",
      "记住快乐的样子，它也会记住你。",
      "偶尔放松，也是前进的一部分。",
      "别急着追光，你自己也在发光。",
      "笑容是真实的光环，你配得上它。",
      "今天不需要多完美，快乐就够了。",
      "快乐是被发现的，而不是制造的。",
      "你今天的笑，一定很好看。",
      "愿你的快乐无需假装。",
      "满心欢喜，是值得炫耀的事。",
      "活在当下的你，最有光彩。",
      "不用担心太多，好好享受就对了。",
      "幸福，是你决定了它的样子。",
      "晴朗的天空，像极了你心情的样子。",
      "今天是属于你的好天气。",
      "生活值得你一遍遍热爱。",
      "你在微笑，世界就跟着亮了。",
      "你发光的样子，自带温暖。",
      "美好的瞬间别忘了收藏。",
      "今天的你，是闪亮的存在。",
      "你走过的路，都是风景。",
      "小确幸堆叠起来，就是大满足。",
      "有你在的地方，连空气都在跳舞。",
      "快乐其实很简单，是你让它变得珍贵。",
      "今天很棒，明天也不差。",
      "快乐的你，让人羡慕又安心。"
    ],
    "好": [
      "平凡的日子，也有闪光的价值",
      "稳稳的日常，藏着最踏实的幸福",
      "慢慢来，生活也会哄你开心的",
      "你不说话的时候，也在发光",
      "有些轻松，是刚刚好的温柔",
      "小满足，就是心安理得的快乐",
      "喜欢今天这样简单的你",
      "不需要太多，好好过就行",
      "平静也是一种珍贵的力量",
      "这份舒服，是你给自己的",
      "心情不错的日子，要轻轻收藏",
      "世界在用温柔回应你温柔的心",
      "不惊不扰的生活，是最舒服的状态",
      "你已经在慢慢变好，别怀疑",
      "被生活善待的时候，也要谢谢自己",
      "宁静的幸福，值得用心感受",
      "别低估稳定的日子，那是你给自己的安全感",
      "小确幸里藏着生活的大智慧",
      "你是今天最温柔的风景",
      "有好心情时，好好生活就行",
      "轻松的时刻，也是对自己的犒赏",
      "被温柔包裹的一天，值得纪念",
      "你给生活的微笑，生活都会记得",
      "没那么热烈，但很舒服",
      "温吞的幸福也足够温暖人心",
      "这样的日子，再来十天也不腻",
      "生活正在朝你喜欢的方向靠近",
      "你笑着面对，世界也就柔和了许多",
      "你是此刻最合适的答案",
      "把今天的小美好放进口袋里",
      "你并不需要完美，也足够被喜欢",
      "被平凡温柔包围的你，最动人",
      "放松也很重要，别总想着赶路",
      "你允许自己开心，生活就亮了",
      "那些静好的时光，很适合你",
      "好日子是积攒出来的，而你刚好在积攒",
      "今天刚刚好，明天会更好",
      "安稳的幸福也有它的光芒",
      "舒服的状态，就是你现在这样",
      "你静静地笑，整个世界就亮了",
    ],
    "一般": [
      "diary_normal_quotes_one".tr,
      "diary_normal_quotes_two".tr,
      "diary_normal_quotes_three".tr,
      "diary_normal_quotes_four".tr,
      "diary_normal_quotes_five".tr,
      "diary_normal_quotes_six".tr,
      "diary_normal_quotes_seven".tr,
      "diary_normal_quotes_eight".tr,
      "diary_normal_quotes_nine".tr,
      "diary_normal_quotes_ten".tr,
      "diary_normal_quotes_eleven".tr,
      "diary_normal_quotes_twelve".tr,
      "diary_normal_quotes_thirteen".tr,
      "diary_normal_quotes_fourteen".tr,
      "diary_normal_quotes_fifteen".tr,
      "diary_normal_quotes_sixteen".tr,
      "diary_normal_quotes_seventeen".tr,
      "diary_normal_quotes_eighteen".tr,
      "diary_normal_quotes_nineteen".tr,
      "diary_normal_quotes_twenty".tr,
      "diary_normal_quotes_twenty_one".tr,
      "diary_normal_quotes_twenty_two".tr,
      "diary_normal_quotes_twenty_three".tr,
      "diary_normal_quotes_twenty_four".tr,
      "diary_normal_quotes_twenty_five".tr,
      "diary_normal_quotes_twenty_six".tr,
      "diary_normal_quotes_twenty_seven".tr,
      "diary_normal_quotes_twenty_eight".tr,
      "diary_normal_quotes_twenty_nine".tr,
      "diary_normal_quotes_thirty".tr,
      "diary_normal_quotes_thirty_one".tr,
      "diary_normal_quotes_thirty_two".tr,
      "diary_normal_quotes_thirty_three".tr,
      "diary_normal_quotes_thirty_four".tr,
      "diary_normal_quotes_thirty_five".tr,
      "diary_normal_quotes_thirty_six".tr,
      "diary_normal_quotes_thirty_seven".tr,
      "diary_normal_quotes_thirty_eight".tr,
      "diary_normal_quotes_thirty_nine".tr,
      "diary_normal_quotes_forty".tr,
    ],
    "不好": [
      "感到难过不是弱点，是你真实的样子。",
      "情绪可以暂停，世界不会责怪你。",
      "即使很累，也别责怪自己。",
      "所有低落，都会有出口。",
      "允许自己有不开心的一天。",
      "没关系，今天就先这样吧。",
      "雨天终会过去，连乌云都记得让光透进来。",
      "你已经很勇敢了，别逼自己更快。",
      "哪怕一点点前进，也是在往前。",
      "请温柔地对待自己，哪怕今天很难。",
      "你可以不坚强一会儿，世界不会因此崩塌。",
      "生活偶尔沉重，也请别忘了自己很重要。",
      "疲惫的时候，请允许自己软下来。",
      "你已经够好了，不需要一直逞强。",
      "没关系，难过是修复的一部分。",
      "别催自己好起来，慢一点也没关系。",
      "即使今天难受，明天也还有机会温柔地开始。",
      "生活不是比赛，慢慢来就行。",
      "你能撑过今天，就能撑过很多事。",
      "不要急着恢复，先接住现在的你。",
      "今天不好没关系，明天还会来的。",
      "难过不是失败，是人之常情。",
      "你的感受很真实，值得被理解。",
      "请给情绪一点出口，而不是关上门。",
      "别强迫自己好起来，慢慢就行。",
      "不开心也没关系，谁都可以偶尔不勇敢。",
      "今天先照顾好自己，别勉强。",
      "这段阴霾只是过客，别忘了阳光还在。",
      "你的心情不需要隐藏。",
      "不舒服就休息，别委屈了自己。",
      "有时情绪来了就让它来，别憋着。",
      "难受的时候，先陪陪自己。",
      "所有低落都会过去，就像雨停后总会晴。",
      "不是你太敏感，而是世界偶尔刺痛了你。",
      "你已经很努力了，别对自己太苛刻。",
      "允许自己停一会儿，不等于放弃。",
      "没有谁能一直坚强，也不必强撑。",
      "今天也许有点糟，但你依然值得被爱。",
      "别让烦恼吞没你温柔的心。",
      "你值得被温柔对待，尤其是现在。",
    ],
    "很不好": [
      "没关系，跌倒的时候也可以躺一会儿。",
      "没有人能一直坚强，你可以休息一下。",
      "黑夜不会一直持续，等一等，会亮的。",
      "别急着振作，你已经够努力了。",
      "有些痛苦，只能慢慢熬过去，但你会熬过去。",
      "情绪翻涌时，别忘了你还有呼吸。",
      "就算崩溃也不丢人，你在尽力生活。",
      "很痛的时候，先活着就好。",
      "不需要马上好起来，你可以慢慢来。",
      "希望你别太难为自己，哪怕此刻很难。",
      "崩溃的时候，先好好活着就行。",
      "你已经做得很好了，哪怕今天真的很难。",
      "别怕黑夜长，总会有一丝光透进来。",
      "深夜的情绪很重，也别忘了你值得被爱。",
      "哪怕明天没有好转，你也不需要自责。",
      "被困住也不代表你永远停在原地。",
      "你允许自己伤心，才是真正的勇敢。",
      "撑不住的时候，就歇一会儿吧。",
      "你不是孤单的，有很多人也曾这样难过过。",
      "眼泪也是你在修补自己的方式。",
      "别怕情绪溃堤，它也是你的一部分。",
      "黑暗里你不是孤单的，总有人在等你回头。",
      "你今天的眼泪，是在替你疗伤。",
      "请给自己多一点耐心，你正在熬过去。",
      "伤心时就哭一场，不用解释。",
      "你不必一直坚强，现在这样就很好。",
      "生活不总是光亮的，但你是。",
      "痛苦是暂时的，爱你的是永久的。",
      "你已经在尽力了，别再责怪自己。",
      "情绪低谷的时候，更要好好活着。",
      "别忘了，你从来都不孤单。",
      "脆弱并不可耻，它很勇敢。",
      "你值得全部的理解和抱抱。",
      "躲起来没关系，别迷失就好。",
      "所有黑夜，都会有尽头。",
      "你现在的痛，是未来温柔的来源。",
      "别压抑感受，它们不是负担。",
      "此刻的难，也终会过去。",
      "请相信自己，哪怕只剩一点点力气。",
      "你不是坏掉了，只是在慢慢修补。",
    ]
  };

  Future<void> init(String mood) async {
    final now = DateTime.now();
    final todayStr = DateFormat('yyyy-MM-dd').format(now);

    final lastDate = SharedPreferencesUtil.to.getString(_lastDateKey);
    final lastIndexStr = SharedPreferencesUtil.to.getString(_todayIndexKey);

    // 如果已经是今天的，就直接使用之前的 index
    if (lastDate == todayStr && lastIndexStr.isNotEmpty) {
      ///获取上次获得金句的心情
      final moodStr = SharedPreferencesUtil.to.getString(_moodKey);
      if (moodStr != mood) {
        ///心情变了，保存新的心情，需要重新获取金句
        SharedPreferencesUtil.to.setString(_moodKey, mood);
      } else {
        ///心情没变，使用之前的 index
        _todayIndex = int.tryParse(lastIndexStr) ?? 0;
        return;
      }
    }

    // 获取已展示记录
    final shown = SharedPreferencesUtil.to
        .getList(_shownKey)
        .map((e) => int.tryParse(e))
        .whereType<int>()
        .toList();

    // 如果已全部展示，重置打乱重新开始
    if (shown.length >= _quotes[mood]!.length) {
      shown.clear();
    }

    // 获取剩余索引并打乱
    final remaining = List<int>.generate(_quotes[mood]!.length, (i) => i)
      ..removeWhere((i) => shown.contains(i))
      ..shuffle();

    _todayIndex = remaining.first;

    // 保存今天的数据
    SharedPreferencesUtil.to.setString(_lastDateKey, todayStr);
    SharedPreferencesUtil.to.setString(_todayIndexKey, _todayIndex.toString());
    SharedPreferencesUtil.to.setList(_shownKey, [
      ...shown.map((e) => e.toString()),
      _todayIndex.toString(),
    ]);
  }

  /// 获取今天的金言
  Future<String> getTodayQuote(String moodStr) async {
    await init(moodStr);
    return _quotes[moodStr]![_todayIndex];
  }
}
