import 'package:get/get.dart';
import 'package:getx_xiaopa/extension/widget_extension.dart';
import 'package:getx_xiaopa/network/http_client.dart';
import 'package:getx_xiaopa/network/model/request_body.dart';
import 'package:getx_xiaopa/pages/diary/model/diary_items_result.dart';
import 'package:getx_xiaopa/storage/storage.dart';

class DiaryStore extends GetxService {
  static DiaryStore get to => Get.find();

  List<DiaryItemsModel> _diaryItemsModel = [];
  List<DiaryItemsModel> get diaryItemsModel => _diaryItemsModel;

  ///心情mood
  String _mood = "";
  String get mood => _mood;

  ///不说话视频
  String _motionUrl = "";
  String get motionUrl => _motionUrl;

  ///说话视频
  String _talkUrl = "";
  String get talkUrl => _talkUrl;

  getDiaryItems() async {
    _diaryItemsModel.clear();
    if (UserStore.to.authorization.isEmpty || UserStore.to.deviceList.isEmpty) {
      return;
    }

    var map = RequestBody(filters: [
      Filter(
          expr: "=",
          name: "device_id",
          value: UserStore.to.deviceList[UserStore.to.selectDevice].id),
      Filter(expr: ">=", name: "created_at", value: _getToDay())
    ]).toJson();

    final result = await http.diaryItems(map);
    if (result.code == 0) {
      _diaryItemsModel = result.data!.data!;
      if (_diaryItemsModel.isNotEmpty) {
        _mood = _diaryItemsModel[0].mood!;
        _motionUrl = _diaryItemsModel[0].motionUrl!;
        _talkUrl = _diaryItemsModel[0].talkUrl!;
      } else {
        _mood = "一般";
        _motionUrl = "";
        _talkUrl = "";
      }
    } else {
      CToast.showToast(result.msg!);
    }
  }

  String _getToDay() {
    DateTime date = DateTime.now();
    return "${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')} 00:00:00";
  }

  String getMoodType() {
    if (_mood == "开心") {
      return "very_happy";
    }
    if (_mood == "好") {
      return "happy";
    }
    if (_mood == "一般") {
      return "normal";
    }
    if (_mood == "不好") {
      return "unhappy";
    }
    if (_mood == "很不好") {
      return "very_unhappy";
    }
    return "normal";
  }
}
