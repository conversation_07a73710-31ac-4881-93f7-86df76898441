///
/// Code generated by jsonToDartModel https://ashamp.github.io/jsonToDartModel/
///
class DiaryStaticsItemsModel {
/*
{
  "id": "4793468917383169",
  "created_at": "2025-05-16T00:00:00.06+08:00",
  "updated_at": "2025-05-16T00:00:00.06+08:00",
  "deleted_at": null,
  "type": 1,
  "data": "0,0,0,0.00",
  "duration": "2025-05-16",
  "user_id": "4470670919467008",
  "device_id": "4163364096835585"
} 
*/

  String? id;
  String? createdAt;
  String? updatedAt;
  String? deletedAt;
  int? type;
  String? data;
  String? duration;
  String? userId;
  String? deviceId;

  DiaryStaticsItemsModel({
    this.id,
    this.createdAt,
    this.updatedAt,
    this.deletedAt,
    this.type,
    this.data,
    this.duration,
    this.userId,
    this.deviceId,
  });
  DiaryStaticsItemsModel.fromJson(Map<String, dynamic> json) {
    id = json['id']?.toString();
    createdAt = json['created_at']?.toString();
    updatedAt = json['updated_at']?.toString();
    deletedAt = json['deleted_at']?.toString();
    type = json['type']?.toInt();
    data = json['data']?.toString();
    duration = json['duration']?.toString();
    userId = json['user_id']?.toString();
    deviceId = json['device_id']?.toString();
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['id'] = id;
    data['created_at'] = createdAt;
    data['updated_at'] = updatedAt;
    data['deleted_at'] = deletedAt;
    data['type'] = type;
    data['data'] = this.data;
    data['duration'] = duration;
    data['user_id'] = userId;
    data['device_id'] = deviceId;
    return data;
  }
}

class DiaryStaticsItemsResult {
/*
{
  "count": 5,
  "pageSize": 10,
  "pageNum": 1,
  "data": [
    {
      "id": "4793468917383169",
      "created_at": "2025-05-16T00:00:00.06+08:00",
      "updated_at": "2025-05-16T00:00:00.06+08:00",
      "deleted_at": null,
      "type": 1,
      "data": "0,0,0,0.00",
      "duration": "2025-05-16",
      "user_id": "4470670919467008",
      "device_id": "4163364096835585"
    }
  ]
} 
*/

  int? count;
  int? pageSize;
  int? pageNum;
  List<DiaryStaticsItemsModel?>? data;

  DiaryStaticsItemsResult({
    this.count,
    this.pageSize,
    this.pageNum,
    this.data,
  });
  DiaryStaticsItemsResult.fromJson(Map<String, dynamic> json) {
    count = json['count']?.toInt();
    pageSize = json['pageSize']?.toInt();
    pageNum = json['pageNum']?.toInt();
    if (json['data'] != null) {
      final v = json['data'];
      final arr0 = <DiaryStaticsItemsModel>[];
      v.forEach((v) {
        arr0.add(DiaryStaticsItemsModel.fromJson(v));
      });
      data = arr0;
    }
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['count'] = count;
    data['pageSize'] = pageSize;
    data['pageNum'] = pageNum;
    if (this.data != null) {
      final v = this.data;
      final arr0 = [];
      for (var v in v!) {
        arr0.add(v!.toJson());
      }
      data['data'] = arr0;
    }
    return data;
  }
}
