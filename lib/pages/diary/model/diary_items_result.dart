///
/// Code generated by jsonToDartModel https://ashamp.github.io/jsonToDartModel/
///
///
/// Code generated by jsonToDartModel https://ashamp.github.io/jsonToDartModel/
///
class DiaryItemsModel {
/*
{
  "id": "4967052805668864",
  "created_at": "2025-07-14T21:00:03.27+08:00",
  "updated_at": "2025-07-14T21:00:03.27+08:00",
  "deleted_at": null,
  "mood": "好",
  "thing": "",
  "feeling": "开心",
  "content": "今天和宝贝交流，宝贝一直在调整我的音量大小，还说了些我不太懂的话。我耐心配合着，期待宝贝和我分享新鲜事。",
  "picture": "",
  "say": "小耙发现你今天走路都带着轻快的节奏，为你感到开心",
  "mood_picture": "upload/bot-diary/202506/e1b486178e5daa4667572cf62df1a7d6.png",
  "motion_url": "",
  "talk_url": "",
  "user_id": "4928810819518464",
  "device_id": "4163364096835585"
} 
*/

  String? id;
  String? createdAt;
  String? updatedAt;
  String? deletedAt;
  String? mood;
  String? thing;
  String? feeling;
  String? content;
  String? picture;
  String? say;
  String? moodPicture;
  String? motionUrl;
  String? talkUrl;
  String? userId;
  String? deviceId;

  DiaryItemsModel({
    this.id,
    this.createdAt,
    this.updatedAt,
    this.deletedAt,
    this.mood,
    this.thing,
    this.feeling,
    this.content,
    this.picture,
    this.say,
    this.moodPicture,
    this.motionUrl,
    this.talkUrl,
    this.userId,
    this.deviceId,
  });
  DiaryItemsModel.fromJson(Map<String, dynamic> json) {
    id = json['id']?.toString();
    createdAt = json['created_at']?.toString();
    updatedAt = json['updated_at']?.toString();
    deletedAt = json['deleted_at']?.toString();
    mood = json['mood']?.toString();
    thing = json['thing']?.toString();
    feeling = json['feeling']?.toString();
    content = json['content']?.toString();
    picture = json['picture']?.toString();
    say = json['say']?.toString();
    moodPicture = json['mood_picture']?.toString();
    motionUrl = json['motion_url']?.toString();
    talkUrl = json['talk_url']?.toString();
    userId = json['user_id']?.toString();
    deviceId = json['device_id']?.toString();
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['id'] = id;
    data['created_at'] = createdAt;
    data['updated_at'] = updatedAt;
    data['deleted_at'] = deletedAt;
    data['mood'] = mood;
    data['thing'] = thing;
    data['feeling'] = feeling;
    data['content'] = content;
    data['picture'] = picture;
    data['say'] = say;
    data['mood_picture'] = moodPicture;
    data['motion_url'] = motionUrl;
    data['talk_url'] = talkUrl;
    data['user_id'] = userId;
    data['device_id'] = deviceId;
    return data;
  }
}

class DiaryItemsResult {
/*
{
  "count": 1,
  "pageSize": 10,
  "pageNum": 1,
  "data": [
    {
      "id": "1",
      "created_at": "2025-05-16T17:59:30.031+08:00",
      "updated_at": "0001-01-01T00:00:00Z",
      "deleted_at": null,
      "mood": "开心",
      "thing": "工作",
      "feeling": "兴奋",
      "content": "工作很顺利，很开心",
      "picture": "",
      "mood_picture": "",
      "user_id": "4470670919467008",
      "device_id": "4163364096835585"
    }
  ]
} 
*/

  int? count;
  int? pageSize;
  int? pageNum;
  List<DiaryItemsModel>? data;

  DiaryItemsResult({
    this.count,
    this.pageSize,
    this.pageNum,
    this.data,
  });
  DiaryItemsResult.fromJson(Map<String, dynamic> json) {
    count = json['count']?.toInt();
    pageSize = json['pageSize']?.toInt();
    pageNum = json['pageNum']?.toInt();
    if (json['data'] != null) {
      final v = json['data'];
      final arr0 = <DiaryItemsModel>[];
      v.forEach((v) {
        arr0.add(DiaryItemsModel.fromJson(v));
      });
      data = arr0;
    }
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['count'] = count;
    data['pageSize'] = pageSize;
    data['pageNum'] = pageNum;
    if (this.data != null) {
      final v = this.data;
      final arr0 = [];
      for (var v in v!) {
        arr0.add(v.toJson());
      }
      data['data'] = arr0;
    }
    return data;
  }
}
