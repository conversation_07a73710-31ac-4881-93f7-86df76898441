///
/// Code generated by jsonToDartModel https://ashamp.github.io/jsonToDartModel/
///
class DiaryMoodItemsModel {
/*
{
  "id": "1",
  "created_at": "0001-01-01T00:00:00Z",
  "updated_at": "0001-01-01T00:00:00Z",
  "deleted_at": null,
  "picture": "",
  "name": "开心",
  "score": 5,
  "sort": 1
} 
*/

  String? id;
  String? createdAt;
  String? updatedAt;
  String? deletedAt;
  String? picture;
  String? name;
  int? score;
  int? sort;
  String? lang;
  String? code;

  DiaryMoodItemsModel({
    this.id,
    this.createdAt,
    this.updatedAt,
    this.deletedAt,
    this.picture,
    this.name,
    this.score,
    this.sort,
    this.lang,
    this.code,
  });
  DiaryMoodItemsModel.fromJson(Map<String, dynamic> json) {
    id = json['id']?.toString();
    createdAt = json['created_at']?.toString();
    updatedAt = json['updated_at']?.toString();
    deletedAt = json['deleted_at']?.toString();
    picture = json['picture']?.toString();
    name = json['name']?.toString();
    score = json['score']?.toInt();
    sort = json['sort']?.toInt();
    lang = json['lang']?.toString();
    code = json['code']?.toString();
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['id'] = id;
    data['created_at'] = createdAt;
    data['updated_at'] = updatedAt;
    data['deleted_at'] = deletedAt;
    data['picture'] = picture;
    data['name'] = name;
    data['score'] = score;
    data['sort'] = sort;
    data['lang'] = lang;
    data['code'] = code;
    return data;
  }
}

class DiaryMoodItemsResult {
/*
{
  "count": 5,
  "pageSize": 10,
  "pageNum": 1,
  "data": [
    {
      "id": "1",
      "created_at": "0001-01-01T00:00:00Z",
      "updated_at": "0001-01-01T00:00:00Z",
      "deleted_at": null,
      "picture": "",
      "name": "开心",
      "score": 5,
      "sort": 1
    }
  ]
} 
*/

  int? count;
  int? pageSize;
  int? pageNum;
  List<DiaryMoodItemsModel?>? data;

  DiaryMoodItemsResult({
    this.count,
    this.pageSize,
    this.pageNum,
    this.data,
  });
  DiaryMoodItemsResult.fromJson(Map<String, dynamic> json) {
    count = json['count']?.toInt();
    pageSize = json['pageSize']?.toInt();
    pageNum = json['pageNum']?.toInt();
    if (json['data'] != null) {
      final v = json['data'];
      final arr0 = <DiaryMoodItemsModel>[];
      v.forEach((v) {
        arr0.add(DiaryMoodItemsModel.fromJson(v));
      });
      data = arr0;
    }
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['count'] = count;
    data['pageSize'] = pageSize;
    data['pageNum'] = pageNum;
    if (this.data != null) {
      final v = this.data;
      final arr0 = [];
      for (var v in v!) {
        arr0.add(v!.toJson());
      }
      data['data'] = arr0;
    }
    return data;
  }
}
