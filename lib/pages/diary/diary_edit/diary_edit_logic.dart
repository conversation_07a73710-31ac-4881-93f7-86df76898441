import 'dart:io';
import 'dart:math';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:getx_xiaopa/base/base.dart';
import 'package:getx_xiaopa/extension/widget_extension.dart';
import 'package:getx_xiaopa/network/dio_util.dart';
import 'package:getx_xiaopa/network/http_client.dart';
import 'package:getx_xiaopa/network/model/result.dart';
import 'package:getx_xiaopa/pages/ai_chat/ai_chat_logic.dart';
import 'package:getx_xiaopa/pages/diary/diary_edit/widget/edit_three.dart';
import 'package:getx_xiaopa/pages/diary/diary_edit/widget/edit_two.dart';
import 'package:getx_xiaopa/pages/diary/utils/diary_store.dart';
import 'package:getx_xiaopa/r.dart';
import 'package:getx_xiaopa/routes/router.dart';
import 'package:getx_xiaopa/storage/storage.dart';
import 'package:getx_xiaopa/utils/utils.dart';
import 'package:image_cropper/image_cropper.dart';
import 'package:wechat_assets_picker/wechat_assets_picker.dart';

import 'diary_edit_state.dart';

class DiaryEditLogic extends BaseCommonController {
  @override
  final DiaryEditState state = DiaryEditState();

  @override
  void initData() async {
    state.diaryId = Get.arguments;

    if (CommonStore.to.diaryMoodItems.isEmpty) {
      await CommonStore.to.getDiaryMoodItems();
    }

    if (CommonStore.to.diaryThingItems.isEmpty) {
      await CommonStore.to.getDiaryThingItems();
    }

    if (CommonStore.to.diaryFeelingItems.isEmpty) {
      await CommonStore.to.getDiaryFeelingItems();
    }

    _initValue();

    _diaryQuery();
  }

  @override
  void onHidden() {}

  _initValue() {
    for (var e in CommonStore.to.diaryMoodItems) {
      state.editMoodTypeData.add(e.name ?? "");
    }

    for (var e in CommonStore.to.diaryThingItems) {
      EditTwoType editTwoType = EditTwoType();
      editTwoType.tip = e.name ?? "";
      editTwoType.image = e.picture ?? "";
      if (ConfigStore.to.locale.languageCode == e.lang) {
        state.editThingTypeData.add(editTwoType);
      }
    }

    for (var e in CommonStore.to.diaryFeelingItems) {
      EditThreeType editThreeType = EditThreeType();
      editThreeType.tip = e.name ?? "";
      state.editFeelingTypeData.add(editThreeType);
    }
  }

  statusAction(String tip) {
    if (tip.isNotEmpty) {
      state.isNextTwo = true;
    } else {
      state.isNextTwo = false;
    }

    state.editDiaryMood = tip;

    update(["edit_one_widget"]);
  }

  tagSelectAction(index) {
    state.editThingTypeData[index].isSelect =
        !state.editThingTypeData[index].isSelect;

    if (_checkTwoNext()) {
      state.isNextThree = true;
    } else {
      state.isNextThree = false;
    }

    update(["edit_two_widget"]);
  }

  bool _checkTwoNext() {
    List<EditTwoType> temp =
        state.editThingTypeData.where((item) => item.isSelect == true).toList();

    return temp.isNotEmpty;
  }

  signSelectAction(index) {
    state.editFeelingTypeData[index].isSelect =
        !state.editFeelingTypeData[index].isSelect;

    if (_checkThreeNext()) {
      state.isCompleted = true;
    } else {
      state.isCompleted = false;
    }

    update(["edit_three_widget"]);
  }

  bool _checkThreeNext() {
    List<EditThreeType> temp = state.editFeelingTypeData
        .where((item) => item.isSelect == true)
        .toList();

    return temp.isNotEmpty;
  }

  editAction() {
    toEditTwo();
    // Get.to(() => EditOne())?.then((value) {
    //   if (value == "back") {
    //     state.isNextTwo = false;
    //     state.editDiaryMood = "";
    //     update(["edit_one_widget"]);
    //   }
    // });
  }

  toEditTwo() {
    // if (!state.isNextTwo) return;

    Get.to(() => EditTwo())?.then((value) {
      if (value == "back") {
        for (var e in state.editThingTypeData) {
          e.isSelect = false;
        }
        state.isNextThree = false;
        state.editDiaryThing = "";
        update(["edit_two_widget"]);
      }
    });
  }

  toEditThree() {
    if (!state.isNextThree) return;

    for (var e in state.editThingTypeData) {
      if (e.isSelect) {
        state.editDiaryThing += "${e.tip},";
      }
    }

    state.editDiaryThing =
        state.editDiaryThing.substring(0, state.editDiaryThing.length - 1);

    Get.to(() => EditThree())?.then((value) {
      if (value == "back") {
        for (var e in state.editFeelingTypeData) {
          e.isSelect = false;
        }
        state.isCompleted = false;
        state.editDiaryFeeling = "";
        update(["edit_three_widget"]);
      }
    });
  }

  toEditCompleted() {
    if (!state.isCompleted) return;

    for (var e in state.editFeelingTypeData) {
      if (e.isSelect) {
        state.editDiaryFeeling += "${e.tip},";
      }
    }

    state.editDiaryFeeling =
        state.editDiaryFeeling.substring(0, state.editDiaryFeeling.length - 1);

    state.isEdit = true;

    update(["diary_edit_view"]);

    Get.until((route) => Get.currentRoute == AppRoutes.DIARY_EDIT);
  }

  pickerImageAction() async {
    PermissionUtil.media(Get.context!, action: () {
      PermissionUtil.storage(Get.context!, action: () async {
        List<AssetEntity>? result = await AssetPicker.pickAssets(
          Get.context!,
          pickerConfig: AssetPickerConfig(
            maxAssets: 3 - state.diaryMedia.length,
            requestType: RequestType.image,
            gridCount: 4,
            dragToSelect: false,
            pathNameBuilder: (AssetPathEntity path) => switch (path) {
              final p when p.isAll => "diary_edit_photo_title".tr,
              _ => path.name,
            },
          ),
        );
        if (result != null) {
          for (AssetEntity imgFile in result) {
            File? img = await imgFile.file;
            if (img != null) {
              String imgPath = await _editImage(img.path);
              if (imgPath.isNotEmpty) {
                state.diaryMedia.add(imgPath);
                state.diaryAddMedia.add(imgPath);
              }
              state.isCompleted = true;
            }
          }
          update(["diary_edit_view", "image_picker_grid"]);
        }
      });
    });
  }

  Future<String> _editImage(String imagePath) async {
    CroppedFile? croppedFile = await ImageCropper().cropImage(
      sourcePath: imagePath,
      uiSettings: [
        AndroidUiSettings(
            toolbarTitle: 'diary_edit_photo_edit'.tr,
            hideBottomControls: true,
            initAspectRatio: CropAspectRatioPreset.square,
            cropGridColumnCount: 0,
            cropGridRowCount: 0,
            cropFrameColor: Colors.transparent),
        IOSUiSettings(
            title: 'diary_edit_photo_edit'.tr,
            cropStyle: CropStyle.circle,
            aspectRatioPickerButtonHidden: true),
      ],
    );
    if (croppedFile != null) {
      return croppedFile.path;
    }
    return "";
  }

  pickerImageRemove(index) async {
    state.isCompleted = true;
    state.diaryMedia.removeAt(index);
    state.diaryTempMedia.clear();
    state.diaryTempMedia.addAll(state.diaryMedia);
    update(["diary_edit_view", "image_picker_grid"]);
  }

  changeAction() async {
    if (state.editDiaryMood.isEmpty) {
      state.editDiaryMood = state.diaryItemsModel.mood ?? '';
    }

    if (state.editDiaryThing.isEmpty) {
      state.editDiaryThing = state.diaryItemsModel.thing ?? '';
    }

    if (state.editDiaryFeeling.isEmpty) {
      state.editDiaryFeeling = state.diaryItemsModel.feeling ?? '';
    }

    state.diaryAddMedia.addAll(state.diaryTempMedia);

    for (var e in state.diaryAddMedia) {
      bool exists = state.diaryTempMedia.contains(e);
      if (exists) continue;
      final result =
          await DioUtil.getInstance().uploadFile(e, access: "bot-diary");
      if (result.isNotEmpty) {
        state.editDiaryPicture += "$result,";
      }
    }

    if (state.editDiaryPicture.isNotEmpty) {
      if (state.diaryTempMedia.isNotEmpty) {
        state.editDiaryPicture =
            state.editDiaryPicture + state.diaryTempMedia.join(",");
      } else {
        state.editDiaryPicture = state.editDiaryPicture
            .substring(0, state.editDiaryPicture.length - 1);
      }
    } else {
      state.editDiaryPicture = state.diaryMedia.join(",");
    }

    _diaryUpdate();
  }

  ///重置数据
  _resetValue() {
    state.diaryAddMedia.clear();

    state.editDiaryMood = "";
    state.editDiaryThing = "";
    state.editDiaryFeeling = "";
    state.isNextTwo = false;
    state.isNextThree = false;
    state.isCompleted = false;
    state.isEdit = false;

    state.editDiaryPicture = "";
  }

  changeContentAction(String value) {
    state.editDiaryContentLength = value.length;
    update(["diary_edit_view"]);
  }

  _dateFormat(String date) {
    DateTime dateTime = DateTime.parse(date).toLocal();
    if (ConfigStore.to.getLocaleCode() == "zh") {
      state.diaryDate =
          "${dateTime.month}${'diary_date_month'.tr}${dateTime.day}${'diary_date_day'.tr} ${dateTime.hour.toString().padLeft(2, "0")}:${dateTime.minute.toString().padLeft(2, "0")}";
    } else {
      state.diaryDate =
          "${dateTime.month}/${dateTime.day} ${dateTime.hour.toString().padLeft(2, "0")}:${dateTime.minute.toString().padLeft(2, "0")}";
    }
  }

  ///返回对应心情的图片
  String _getMoodImage(String str) {
    String temp = "";
    switch (str) {
      case "开心":
        temp = ConfigStore.to.getLocaleCode() == "zh"
            ? R.diary_very_good_png
            : R.diary_very_good_en_png;
        break;
      case "好":
        temp = ConfigStore.to.getLocaleCode() == "zh"
            ? R.diary_good_png
            : R.diary_good_en_png;
        break;
      case "一般":
        temp = ConfigStore.to.getLocaleCode() == "zh"
            ? R.diary_general_png
            : R.diary_general_en_png;
        break;
      case "不好":
        temp = ConfigStore.to.getLocaleCode() == "zh"
            ? R.diary_bad_png
            : R.diary_bad_en_png;
        break;
      case "很不好":
        temp = ConfigStore.to.getLocaleCode() == "zh"
            ? R.diary_no_good_png
            : R.diary_no_good_en_png;
        break;
      default:
        temp = ConfigStore.to.getLocaleCode() == "zh"
            ? R.diary_general_png
            : R.diary_general_en_png;
    }
    return temp;
  }

  ///=================网络请求=========================
  _diaryQuery() async {
    Result result = await http.diaryQuery({"id": state.diaryId});
    if (result.code == 0) {
      state.diaryItemsModel = result.data;
      _dateFormat(state.diaryItemsModel.createdAt!);

      state.signTags = state.diaryItemsModel.feeling ?? "";
      state.diaryMoodImage = _getMoodImage(state.diaryItemsModel.mood ?? '');
      state.eventTags = state.diaryItemsModel.thing ?? "其他";
      state.editDiaryContentController.text =
          state.diaryItemsModel.content ?? "";
      state.editDiaryContentLength =
          state.editDiaryContentController.text.length;

      if (state.diaryItemsModel.picture!.isNotEmpty) {
        state.diaryMedia = state.diaryItemsModel.picture.toString().split(",");
        state.diaryTempMedia.clear();
        state.diaryTempMedia.addAll(state.diaryMedia);
      }

      netState = NetState.dataSuccessState;
      update(["diary_edit_view"]);
    } else {
      CToast.showToast(result.msg!);
    }
  }

  ///心情日记修改
  _diaryUpdate() async {
    CToast.showLoading();
    var map = {
      "id": state.diaryId,
      "mood": state.editDiaryMood,
      "thing": state.editDiaryThing,
      "feeling": state.editDiaryFeeling,
      "content": state.editDiaryContentController.text,
      "picture": state.editDiaryPicture,
      "motion_url": "",
      "talk_url": ""
    };
    Result result = await http.diaryUpdate(map);
    if (result.code == 0) {
      state.diaryItemsModel = result.data;

      state.signTags = state.diaryItemsModel.feeling ?? "";
      state.eventTags = state.diaryItemsModel.thing ?? "";
      state.editDiaryContentController.text =
          state.diaryItemsModel.content ?? "";

      if (state.diaryItemsModel.picture!.isNotEmpty) {
        state.diaryMedia = state.diaryItemsModel.picture.toString().split(",");
        state.diaryTempMedia.clear();
        state.diaryTempMedia.addAll(state.diaryMedia);
      }

      _resetValue();

      update(["diary_edit_view"]);

      await DiaryStore.to.getDiaryItems();

      ///修改心情变化视频
      if (CommonStore.to.showAichat == 1) {
        Get.find<AiChatLogic>().changeDiary();
      }
    } else {
      CToast.showToast(result.msg!);
    }
  }
}
