import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:getx_xiaopa/base/base.dart';
import 'package:getx_xiaopa/extension/images.dart';
import 'package:getx_xiaopa/extension/widget_extension.dart';
import 'package:getx_xiaopa/pages/diary/diary_edit/diary_edit_logic.dart';
import 'package:getx_xiaopa/pages/diary/diary_edit/widget/mood_selector.dart';
import 'package:getx_xiaopa/r.dart';
import 'package:getx_xiaopa/values/values.dart';
import 'package:getx_xiaopa/widget/widget.dart';

class EditOne extends BaseCommonView<DiaryEditLogic> {
  EditOne({super.key});

  @override
  String? get navTitle => "diary_edit_one_title".tr;

  @override
  TextStyle? get navTitleStyle =>
      StyleConfig.blackStyle(fontSize: 18, fontWeight: FontWeight.w500);

  @override
  Color? get navColor => Colors.white;

  @override
  double? get leftWidth => 35.w;

  @override
  Widget? get leftButton => Padding(
        padding: EdgeInsets.only(left: 15.w),
        child: Images(
          path: R.back_png,
          width: 20.w,
          height: 20.h,
          scale: 2.8,
        ).inkWell(() => Get.back(result: "back")),
      );

  @override
  Color? get contentColor => Colors.white;

  @override
  Widget buildContent() {
    return GetBuilder<DiaryEditLogic>(
      id: "edit_one_widget",
      builder: (_) {
        return PopScope(
          canPop: false,
          onPopInvokedWithResult: (didPop, result) {
            if (!didPop) {
              Get.back(result: "back");
            }
          },
          child: Container(
            margin: EdgeInsets.only(top: 44.h),
            width: 1.sw,
            height: 1.sh,
            child: Column(
              children: [
                SizedBox(
                  width: 156.w,
                  height: 106.h * 5,
                  child: MoodSelector(
                    moods: controller.state.editMoodTypeData,
                    statusAction: (value) {
                      controller.statusAction(value);
                    },
                  ),
                ),
                const Expanded(child: SizedBox()),
                Container(
                  margin: EdgeInsets.only(bottom: 20.h),
                  width: 283.w,
                  height: 48.h,
                  decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(24.r),
                      color: controller.state.isNextTwo
                          ? const Color.fromRGBO(40, 39, 46, 1)
                          : const Color.fromRGBO(215, 215, 215, 1)),
                  child: Center(
                    child: ThemeText(
                      dataStr: "console_paring_wifi_next".tr,
                      keyName: 'textColor',
                      flag: controller.state.isNextTwo,
                      subColor: ColorConfig.authenticationTextColor,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ).inkWell(() => controller.toEditTwo())
              ],
            ),
          ),
        );
      },
    );
  }
}
