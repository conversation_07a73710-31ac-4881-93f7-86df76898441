import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:getx_xiaopa/base/base.dart';
import 'package:getx_xiaopa/extension/images.dart';
import 'package:getx_xiaopa/extension/widget_extension.dart';
import 'package:getx_xiaopa/network/http_client.dart';
import 'package:getx_xiaopa/pages/diary/diary_edit/diary_edit_logic.dart';
import 'package:getx_xiaopa/r.dart';
import 'package:getx_xiaopa/storage/storage.dart';
import 'package:getx_xiaopa/values/values.dart';
import 'package:getx_xiaopa/widget/widget.dart';

class EditTwo extends BaseCommonView<DiaryEditLogic> {
  EditTwo({super.key});

  @override
  String? get navTitle =>
      "${"diary_edit_two_title".tr}${controller.state.editDiaryMood}";

  @override
  TextStyle? get navTitleStyle =>
      StyleConfig.blackStyle(fontSize: 18, fontWeight: FontWeight.w500);

  @override
  Color? get navColor => Colors.white;

  @override
  double? get leftWidth => 35.w;

  @override
  Widget? get leftButton => Padding(
        padding: EdgeInsets.only(left: 15.w),
        child: Images(
          path: R.back_png,
          width: 20.w,
          height: 20.h,
          scale: 2.8,
        ).inkWell(() => Get.back(result: "back")),
      );

  @override
  Color? get contentColor => Colors.white;

  @override
  Widget buildContent() {
    return GetBuilder<DiaryEditLogic>(
      id: "edit_two_widget",
      builder: (_) {
        return PopScope(
          canPop: false,
          onPopInvokedWithResult: (didPop, result) {
            if (!didPop) {
              Get.back(result: "back");
            }
          },
          child: Container(
            margin: EdgeInsets.only(left: 30.w, right: 30.w),
            child: Column(
              children: [
                SizedBox(height: 23.h),
                Expanded(
                  child: SingleChildScrollView(
                    child: Wrap(
                      runSpacing: 16.h,
                      spacing: 25.w,
                      children: List.generate(
                          controller.state.editThingTypeData.length, (index) {
                        return SizedBox(
                          width: 60.w,
                          height: 90.h,
                          child: Column(
                            children: [
                              ThemeContainer(
                                keyName: 'textColor',
                                flag: controller
                                    .state.editThingTypeData[index].isSelect,
                                subColor: Colors.transparent,
                                radius: 10.r,
                                child: Images(
                                  path: fileUrl(controller
                                      .state.editThingTypeData[index].image),
                                  width: 60.r,
                                  height: 60.r,
                                  boxFit: BoxFit.fill,
                                ),
                              ),
                              SizedBox(height: 5.h),
                              ThemeText(
                                dataStr: controller
                                    .state.editThingTypeData[index].tip,
                                keyName: 'iconColor',
                                flag: controller
                                    .state.editThingTypeData[index].isSelect,
                                subColor: ColorConfig.shopDetailTextColor,
                                fontSize: ConfigStore.to.getLocaleCode() == "zh"
                                    ? 14
                                    : 12,
                              )
                            ],
                          ),
                        ).inkWell(() => controller.tagSelectAction(index));
                      }),
                    ),
                  ),
                ),
                Container(
                  margin: EdgeInsets.only(bottom: 20.h),
                  width: 283.w,
                  height: 48.h,
                  decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(24.r),
                      color: controller.state.isNextThree
                          ? const Color.fromRGBO(40, 39, 46, 1)
                          : const Color.fromRGBO(215, 215, 215, 1)),
                  child: Center(
                    child: ThemeText(
                      dataStr: "console_paring_wifi_next".tr,
                      keyName: 'textColor',
                      flag: controller.state.isNextThree,
                      subColor: ColorConfig.authenticationTextColor,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ).inkWell(() => controller.toEditThree())
              ],
            ),
          ),
        );
      },
    );
  }
}
