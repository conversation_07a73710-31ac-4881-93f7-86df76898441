import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:getx_xiaopa/base/base.dart';
import 'package:getx_xiaopa/extension/images.dart';
import 'package:getx_xiaopa/network/http_client.dart';
import 'package:getx_xiaopa/pages/diary/diary_edit/diary_edit_logic.dart';
import 'package:getx_xiaopa/r.dart';
import 'package:getx_xiaopa/widget/theme_image.dart';

class ImagePickerGrid extends BaseCommonView {
  final List<String> images;
  final void Function() onAddImage;
  final void Function(int index) onRemoveImage;
  final int maxImages;

  ImagePickerGrid({
    super.key,
    required this.images,
    required this.onAddImage,
    required this.onRemoveImage,
    this.maxImages = 3,
  });

  @override
  bool get customWidget => true;

  @override
  Widget buildContent() {
    final showAddButton = images.length < maxImages;
    final itemCount = showAddButton ? images.length + 1 : images.length;

    return GetBuilder<DiaryEditLogic>(
        id: "image_picker_grid",
        builder: (_) {
          return Container(
            margin: EdgeInsets.only(left: 39.w, right: 39.w),
            child: Wrap(
              spacing: 8,
              runSpacing: 8,
              children: List.generate(itemCount, (index) {
                if (showAddButton && index == images.length) {
                  return _buildAddButton();
                } else {
                  return _buildImageItem(index);
                }
              }),
            ),
          );
        });
  }

  Widget _buildImageItem(int index) {
    return Stack(
      clipBehavior: Clip.none,
      children: [
        ClipRRect(
          borderRadius: BorderRadius.circular(6.r),
          child: Images(
            path: fileUrl(images[index]),
            width: 50.r,
            height: 50.r,
            boxFit: BoxFit.fill,
          ),
        ),
        Positioned(
          top: 2.h,
          right: 2.w,
          child: GestureDetector(
            onTap: () => onRemoveImage(index),
            child: Container(
              padding: const EdgeInsets.all(2),
              decoration: const BoxDecoration(
                color: Colors.white,
                shape: BoxShape.circle,
              ),
              child: Images(
                path: R.diary_close_png,
                width: 14.r,
                height: 14.r,
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildAddButton() {
    return GestureDetector(
      onTap: onAddImage,
      child: ThemeImagePath(
        fileName: "diary_add.png",
        imgWidget: 50.r,
        imgHeight: 50.r,
      ),
    );
  }
}
