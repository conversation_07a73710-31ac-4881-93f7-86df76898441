import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:getx_xiaopa/extension/images.dart';
import 'package:getx_xiaopa/r.dart';
import 'package:getx_xiaopa/values/values.dart';

class MoodSelector extends StatefulWidget {
  final List<String> moods;
  final Function(String) statusAction;

  const MoodSelector(
      {super.key, required this.statusAction, required this.moods});

  @override
  State<MoodSelector> createState() => _MoodSelectorState();
}

class _MoodSelectorState extends State<MoodSelector> {
  int selectedLevel = -1; // 当前选中的层数
  double lastDy = 0;

  List<String> moods = [];

  final List<String> imagePath = [
    R.diary_edit_very_kaixin_png,
    R.diary_edit_kaixin_png,
    R.diary_edit_yiban_png,
    R.diary_edit_buhao_png,
    R.diary_edit_very_buhao_png,
  ];

  final List<Color> fillColors = const [
    Color.fromRGBO(188, 137, 227, 1),
    Color.fromRGBO(118, 194, 245, 1), // 填满第一层
    Color.fromRGBO(163, 233, 146, 1), // 填满第一二层
    Color.fromRGBO(255, 190, 101, 1), // 填满第一二三层
    Color.fromRGBO(255, 128, 118, 1), // 填满全部
  ];

  Color get currentFillColor {
    if (selectedLevel == 0) return fillColors[0];
    if (selectedLevel == 1) return fillColors[1];
    if (selectedLevel == 2) return fillColors[2];
    if (selectedLevel == 3) return fillColors[3];
    if (selectedLevel >= 4) return fillColors[4];
    return Colors.transparent;
  }

  void handleDrag(double dy, double height, {bool isTap = false}) {
    if (dy < 0) {
      // 超出顶部，保持在最大层
      setState(() {
        selectedLevel = moods.length - 1;
      });
      return;
    }

    if (dy > height) {
      // 超出底部，恢复初始
      setState(() {
        selectedLevel = -1;
      });
      widget.statusAction.call("");
      return;
    }

    double sectionHeight = height / moods.length;
    int levelFromBottom =
        ((height - dy) ~/ sectionHeight).clamp(0, moods.length - 1);

    if (selectedLevel == -1) {
      if (dy < lastDy) {
        // 初始状态，只允许从下往上滑
        setState(() {
          selectedLevel = levelFromBottom;
        });
      }
      if (isTap) {
        setState(() {
          selectedLevel = levelFromBottom;
        });
      }
    } else {
      // 已有填充时允许任意方向滑动
      setState(() {
        selectedLevel = levelFromBottom;
      });
    }

    lastDy = dy;

    if (selectedLevel != -1) {
      widget.statusAction.call(moods[moods.length - selectedLevel - 1]);
    }
  }

  @override
  void initState() {
    super.initState();
    moods = widget.moods;
  }

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        double totalHeight = constraints.maxHeight;
        double fillHeight = selectedLevel >= 0
            ? totalHeight * (selectedLevel + 1) / moods.length
            : 0;
        return GestureDetector(
          onTapDown: (details) {
            lastDy = details.localPosition.dy;
            handleDrag(details.localPosition.dy, totalHeight, isTap: true);
          },
          onVerticalDragStart: (details) {
            lastDy = details.localPosition.dy;
          },
          onVerticalDragUpdate: (details) {
            handleDrag(details.localPosition.dy, totalHeight);
          },
          child: Stack(
            children: [
              // 背景
              Container(
                decoration: BoxDecoration(
                  color: const Color.fromRGBO(238, 238, 238, 1),
                  borderRadius: BorderRadius.circular(100.r),
                ),
              ),
              // 填充颜色（从底部开始）
              Positioned(
                bottom: 0,
                left: 0,
                right: 0,
                child: AnimatedContainer(
                  duration: const Duration(milliseconds: 100),
                  height: fillHeight,
                  decoration: BoxDecoration(
                    color: currentFillColor,
                    borderRadius: BorderRadius.vertical(
                      top: Radius.circular(
                          selectedLevel == moods.length - 1 ? 100.r : 0),
                      bottom: Radius.circular(100.r),
                    ),
                  ),
                ),
              ),
              // 白色线和文字
              Column(
                children: List.generate(moods.length, (index) {
                  return Expanded(
                    child: Container(
                      decoration: BoxDecoration(
                        border: index != 0
                            ? const Border(
                                top: BorderSide(color: Colors.white, width: 1),
                              )
                            : null,
                      ),
                      child: Center(
                        child: selectedLevel == (moods.length - index - 1)
                            ? Images(
                                path: imagePath[index],
                                width: 150.r,
                                height: 150.r,
                              ) // 选中第一层时展示图标
                            : Text(
                                moods[index],
                                style: StyleConfig.otherStyle(
                                    color: selectedLevel <
                                            (moods.length - index - 1)
                                        ? ColorConfig.shopDetailTextColor
                                        : Colors.white,
                                    fontWeight: FontWeight.w700),
                              ),
                      ),
                    ),
                  );
                }),
              ),
            ],
          ),
        );
      },
    );
  }
}
