import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:getx_xiaopa/base/base.dart';
import 'package:getx_xiaopa/extension/images.dart';
import 'package:getx_xiaopa/extension/widget_extension.dart';
import 'package:getx_xiaopa/pages/diary/diary_edit/diary_edit_logic.dart';
import 'package:getx_xiaopa/r.dart';
import 'package:getx_xiaopa/storage/storage.dart';
import 'package:getx_xiaopa/values/values.dart';
import 'package:getx_xiaopa/widget/widget.dart';

class EditThree extends BaseCommonView<DiaryEditLogic> {
  EditThree({super.key});

  @override
  String? get navTitle => "diary_edit_three_title".tr;

  @override
  TextStyle? get navTitleStyle =>
      StyleConfig.blackStyle(fontSize: 18, fontWeight: FontWeight.w500);

  @override
  Color? get navColor => Colors.white;

  @override
  double? get leftWidth => 35.w;

  @override
  Widget? get leftButton => Padding(
        padding: EdgeInsets.only(left: 15.w),
        child: Images(
          path: R.back_png,
          width: 20.w,
          height: 20.h,
          scale: 2.8,
        ).inkWell(() => Get.back(result: "back")),
      );

  @override
  Color? get contentColor => Colors.white;

  @override
  Widget buildContent() {
    return GetBuilder<DiaryEditLogic>(
      id: "edit_three_widget",
      builder: (_) {
        return PopScope(
          canPop: false,
          onPopInvokedWithResult: (didPop, result) {
            if (!didPop) {
              Get.back(result: "back");
            }
          },
          child: Container(
            alignment: Alignment.center,
            margin: EdgeInsets.only(left: 20.w, right: 20.w),
            child: Column(
              children: [
                SizedBox(height: 27.h),
                Expanded(
                  child: SingleChildScrollView(
                    child: Wrap(
                      runSpacing: 20.h,
                      spacing: 9.w,
                      children: List.generate(
                          controller.state.editFeelingTypeData.length, (index) {
                        return Container(
                          width: 72.w,
                          height: 40.h,
                          decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(23.r),
                              color: controller
                                      .state.editFeelingTypeData[index].isSelect
                                  ? const Color.fromRGBO(40, 39, 46, 1)
                                  : const Color.fromRGBO(249, 249, 249, 1)),
                          child: Center(
                            child: ThemeText(
                              dataStr: controller
                                  .state.editFeelingTypeData[index].tip,
                              keyName: 'textColor',
                              flag: controller
                                  .state.editFeelingTypeData[index].isSelect,
                              subColor: ColorConfig.shopDetailTextColor,
                              fontSize: ConfigStore.to.getLocaleCode() == "zh"
                                  ? 16
                                  : 12,
                            ),
                          ),
                        ).inkWell(() => controller.signSelectAction(index));
                      }),
                    ),
                  ),
                ),
                Container(
                  margin: EdgeInsets.only(bottom: 20.h),
                  width: 283.w,
                  height: 48.h,
                  decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(24.r),
                      color: controller.state.isCompleted
                          ? const Color.fromRGBO(40, 39, 46, 1)
                          : const Color.fromRGBO(215, 215, 215, 1)),
                  child: Center(
                    child: ThemeText(
                      dataStr: "console_paring_wifi_next".tr,
                      keyName: 'textColor',
                      flag: controller.state.isCompleted,
                      subColor: ColorConfig.authenticationTextColor,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ).inkWell(() => controller.toEditCompleted())
              ],
            ),
          ),
        );
      },
    );
  }
}
