import 'package:flutter/material.dart';
import 'package:getx_xiaopa/pages/diary/model/diary_items_result.dart';
import 'package:getx_xiaopa/storage/storage.dart';

class DiaryEditState {
  TextEditingController editDiaryContentController = TextEditingController();

  String signTags = "";

  String eventTags = "";

  bool isEdit = false;

  int editDiaryContentLength = 0;
  int editDiaryContentMaxLength =
      ConfigStore.to.getLocaleCode() == "zh" ? 150 : 300;

  bool isNextTwo = false;

  ///心情
  List<String> editMoodTypeData = [];

  ///事件
  List<EditTwoType> editThingTypeData = [];

  bool isNextThree = false;

  List<EditThreeType> editFeelingTypeData = [];

  bool isCompleted = false;

  List<String> diaryImage = [];

  List<String> diaryMedia = [];
  List<String> diaryAddMedia = [];
  List<String> diaryTempMedia = [];

  String diaryId = "";
  String diaryDate = "";

  late DiaryItemsModel diaryItemsModel;

  ///用户没有上传图片时显示的图片
  String diaryMoodImage = "";

  ///临时保存编辑的心情
  String editDiaryMood = "";
  String editDiaryThing = "";
  String editDiaryFeeling = "";
  String editDiaryContent = "";
  String editDiaryPicture = "";

  DiaryEditState() {
    ///Initialize variables
  }
}

class EditTwoType {
  String image;
  String tip;
  bool isSelect;

  EditTwoType({
    this.image = "",
    this.tip = "",
    this.isSelect = false,
  });
}

class EditThreeType {
  String tip;
  bool isSelect;

  EditThreeType({
    this.tip = "",
    this.isSelect = false,
  });
}
