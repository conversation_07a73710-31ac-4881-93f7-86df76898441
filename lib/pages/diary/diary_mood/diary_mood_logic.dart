import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:getx_xiaopa/base/base.dart';
import 'package:getx_xiaopa/extension/widget_extension.dart';
import 'package:getx_xiaopa/network/http_client.dart';
import 'package:getx_xiaopa/network/model/request_body.dart';
import 'package:getx_xiaopa/pages/diary/model/diary_items_result.dart';
import 'package:getx_xiaopa/routes/router.dart';
import 'package:getx_xiaopa/storage/storage.dart';

import 'diary_mood_state.dart';

class DiaryMoodLogic extends BaseCommonController {
  @override
  final DiaryMoodState state = DiaryMoodState();

  @override
  void initData() {
    state.date = Get.arguments;

    state.title = ConfigStore.to.getLocaleCode() == "zh"
        ? "${state.date.year}.${state.date.month}.${state.date.day} ${"diary_date_week".tr}${_getWeekdayString(state.date.weekday)}"
        : "${state.date.month}/${state.date.day}/${state.date.year} ${_getWeekdayString(state.date.weekday)}";

    _diaryItems();
  }

  String _getWeekdayString(int weekday) {
    String weekdayString;
    switch (weekday) {
      case 1:
        weekdayString = 'diary_week_one'.tr;
        break;
      case 2:
        weekdayString = 'diary_week_two'.tr;
        break;
      case 3:
        weekdayString = 'diary_week_three'.tr;
        break;
      case 4:
        weekdayString = 'diary_week_four'.tr;
        break;
      case 5:
        weekdayString = 'diary_week_five'.tr;
        break;
      case 6:
        weekdayString = 'diary_week_six'.tr;
        break;
      case 7:
        weekdayString = 'diary_week_seven'.tr;
        break;
      default:
        weekdayString = 'Unknown';
    }
    return weekdayString;
  }

  managerAction() {
    state.isManager = !state.isManager;
    if (state.isManager) {
      state.managerTip = "diart_mood_complete".tr;
    } else {
      state.managerTip = "diary_mood_title".tr;
    }
    update(["diary_mood_view"]);
  }

  tapAction(int cIndex) {
    Get.toNamed(AppRoutes.DIARY_EDIT, arguments: state.diaryItems[cIndex].id)
        ?.then((value) {
      _diaryItems();
    });
  }

  @override
  void onHidden() {}

  getDiaryDate(String date) {
    DateTime temp = DateTime.parse(date).toLocal();
    return "${temp.hour.toString().padLeft(2, "0")}:${temp.minute.toString().padLeft(2, "0")}";
  }

  ///返回对应心情的颜色
  getMoodColor(String mood) {
    switch (mood) {
      case '开心':
      case 'Happy':
        return const Color.fromRGBO(255, 128, 118, 1);
      case '好':
      case 'Good':
        return const Color.fromRGBO(255, 190, 101, 1);
      case '一般':
      case 'Mediocre':
        return const Color.fromRGBO(163, 233, 146, 1);
      case '不好':
      case 'Bad':
        return const Color.fromRGBO(118, 194, 245, 1);
      case '很不好':
      case 'Terrible':
        return const Color.fromRGBO(118, 137, 227, 1);
      default:
        return const Color.fromRGBO(163, 233, 146, 1);
    }
  }

  delAction(index, flag) {
    if (flag) {
      state.deleteId.add(state.diaryItems[index].id ?? "");
    } else {
      state.deleteId.remove(state.diaryItems[index].id ?? "");
    }
  }

  delTapAction() {
    if (state.deleteId.isEmpty) return;
    for (int i = 0; i < state.deleteId.length; i++) {
      if (i == state.deleteId.length - 1) {
        _diaryDelete(state.deleteId[i], isRefresh: true);
      } else {
        _diaryDelete(state.deleteId[i]);
      }
    }
  }

  /// ==================网络请求===============================
  /// 心情日记记录
  _diaryItems() async {
    var map = RequestBody(pagination: false, filters: [
      Filter(
          expr: "=",
          name: "device_id",
          value: UserStore.to.deviceList[UserStore.to.selectDevice].id),
      Filter(expr: "between", name: "created_at", value: [
        "${state.date.year}-${state.date.month}-${state.date.day} 00:00:00",
        "${state.date.year}-${state.date.month}-${state.date.day} 23:59:59"
      ])
    ]).toJson();
    final result = await http.diaryItems(map);
    if (result.code == 0) {
      state.diaryItems.clear();
      if (result.data!.data!.isNotEmpty) {
        for (DiaryItemsModel model in result.data!.data!) {
          state.diaryItems.add(model);
        }
      }

      netState = NetState.dataSuccessState;
      update(["diary_mood_view"]);
    } else {
      CToast.showToast(result.msg!);
    }
  }

  _diaryDelete(String id, {bool isRefresh = false}) async {
    var map = {"id": id};
    if (isRefresh) {
      CToast.showLoading();
    }
    final result = await http.diaryDelete(map);
    if (result.code == 0) {
      if (isRefresh) {
        state.deleteId.clear();
        state.isManager = false;
        _diaryItems();
      }
    } else {
      state.deleteId.clear();
      CToast.showToast(result.msg!);
    }
  }
}
