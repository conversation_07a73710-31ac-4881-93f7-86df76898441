import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:getx_xiaopa/extension/images.dart';
import 'package:getx_xiaopa/extension/widget_extension.dart';
import 'package:getx_xiaopa/network/http_client.dart';
import 'package:getx_xiaopa/r.dart';
import 'package:getx_xiaopa/storage/storage.dart';
import 'package:getx_xiaopa/utils/utils.dart';
import 'package:getx_xiaopa/values/values.dart';
import 'package:getx_xiaopa/widget/widget.dart';

class DiaryMoodItem extends StatelessWidget {
  final int cIndex;
  final String dateText;
  final Color diaryColor;
  final String diaryText;
  final String eventTags;
  final String signTags;
  final String? displayedText;
  final String diaryImage;
  final bool isManager;
  final Function(int) onTap;
  final Function(int, bool) managerAction;

  const DiaryMoodItem(
      {super.key,
      required this.cIndex,
      this.dateText = "",
      this.diaryColor = const Color.fromRGBO(255, 128, 118, 1),
      this.diaryText = "",
      required this.eventTags,
      required this.signTags,
      this.displayedText,
      this.diaryImage = "",
      this.isManager = false,
      required this.onTap,
      required this.managerAction});

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.only(top: 10.h, left: 20.w, right: 20.w),
      padding:
          EdgeInsets.only(top: 10.h, left: 15.w, right: 15.w, bottom: 20.h),
      width: 335.w,
      decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(10.r),
          color: Colors.white,
          boxShadow: const [
            BoxShadow(
              color: Color.fromRGBO(0, 0, 0, 0.06),
              // 阴影颜色和透明度
              spreadRadius: 0,
              // 阴影扩散范围
              blurRadius: 6,
              // 阴影模糊程度
              offset: Offset(0, 2),
              // 阴影偏移量（水平，垂直）
            )
          ]),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Text(
                dateText,
                style: StyleConfig.otherStyle(
                    color: ColorConfig.searchTextColor,
                    fontWeight: FontWeight.w700),
              ),
              const Expanded(child: SizedBox()),
              isManager
                  ? CheckBoxRounded(
                      size: 22.r,
                      isChecked: false,
                      checkedColor: Get.find<ThemeColorController>()
                          .getColor('textColor'),
                      checkedBgColor: ColorConfig.searchTextColor,
                      uncheckedBgColor: Colors.white,
                      borderColor: const Color.fromRGBO(215, 215, 215, 1),
                      onTap: (flag) {
                        managerAction.call(cIndex, flag!);
                      },
                    )
                  : Images(
                      path: R.diary_right_01_png,
                      width: 9.w,
                      height: 16.h,
                    )
            ],
          ),
          SizedBox(height: 16.h),
          Row(
            children: [
              Container(
                width: 20.r,
                height: 20.r,
                decoration:
                    BoxDecoration(shape: BoxShape.circle, color: diaryColor),
              ),
              SizedBox(width: 16.w),
              Text(
                diaryText,
                style: StyleConfig.otherStyle(
                    color: ColorConfig.searchTextColor,
                    fontWeight: FontWeight.w700),
              ),
            ],
          ),
          SizedBox(height: 14.h),
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                margin: EdgeInsets.only(top: 5.h),
                child: Text(
                  "diary_mood_item_one".tr,
                  style: StyleConfig.otherStyle(
                      color: ColorConfig.authenticationTextColor,
                      fontWeight: FontWeight.w400,
                      fontSize: 14),
                ),
              ),
              SizedBox(width: 28.w),
              Flexible(
                child: Wrap(
                  spacing: 5.w,
                  runSpacing: 6.h,
                  children: List.generate(eventTags.split(",").length, (index) {
                    return _buildTag(eventTags.split(",")[index]);
                  }),
                ),
              )
            ],
          ),
          SizedBox(height: 10.h),
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                margin: EdgeInsets.only(top: 4.h),
                child: Text(
                  "diary_mood_item_two".tr,
                  style: StyleConfig.otherStyle(
                      color: ColorConfig.authenticationTextColor,
                      fontWeight: FontWeight.w400,
                      fontSize: 14),
                ),
              ),
              SizedBox(width: 14.w),
              Flexible(
                  child: Wrap(
                spacing: 5.w,
                runSpacing: 6.h,
                children: List.generate(signTags.split(",").length, (index) {
                  return _buildSign(signTags.split(",")[index]);
                }),
              )),
            ],
          ),
          if (displayedText != null && displayedText!.isNotEmpty) ...{
            SizedBox(height: 18.h),
            SizedBox(
              width: 305.w,
              child: Text(
                displayedText!,
                // overflow: TextOverflow.ellipsis,
                style: StyleConfig.otherStyle(
                    color: ColorConfig.searchTextColor, fontSize: 14),
              ),
            )
          },
          if (diaryImage.isNotEmpty) ...{
            SizedBox(height: 12.h),
            Row(
              mainAxisAlignment: MainAxisAlignment.start,
              children: List.generate(diaryImage.split(",").length, (index) {
                return Container(
                  margin: EdgeInsets.only(right: 8.w),
                  width: 92.r,
                  height: 92.r,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(4.r),
                  ),
                  clipBehavior: Clip.hardEdge,
                  child: Images(
                    path: fileUrl(diaryImage.split(",")[index]),
                    boxFit: BoxFit.contain,
                  ),
                );
              }),
            ),
          }
        ],
      ),
    ).inkWell(() => onTap.call(cIndex));
  }

  Widget _buildTag(String text) {
    return ThemeContainer(
      tPandding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 2.h),
      // tWidget: 79.w,
      radius: 4.r,
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Images(
            path: fileUrl(CommonStore.to.getDiaryThingImage(text)),
            width: 29.r,
            height: 29.r,
          ),
          SizedBox(width: 4.w),
          ThemeText(
            dataStr: text,
            keyName: 'diaryRecord',
            fontSize: 12,
          )
        ],
      ),
    );
  }

  Widget _buildSign(String text) {
    return IntrinsicWidth(
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 10.w, vertical: 4.h),
        // width: 40.w,
        // height: 29.h,
        decoration: BoxDecoration(
          color: const Color.fromRGBO(249, 249, 249, 1),
          borderRadius: BorderRadius.circular(4.r),
        ),
        child: Center(
          child: Text(text,
              style: StyleConfig.otherStyle(
                  color: ColorConfig.shopDetailTextColor, fontSize: 12)),
        ),
      ),
    );
  }
}
