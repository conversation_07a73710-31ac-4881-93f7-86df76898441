import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:getx_xiaopa/base/base.dart';
import 'package:getx_xiaopa/extension/images.dart';
import 'package:getx_xiaopa/extension/widget_extension.dart';
import 'package:getx_xiaopa/pages/diary/diary_mood/widget/diary_mood_item.dart';
import 'package:getx_xiaopa/r.dart';
import 'package:getx_xiaopa/storage/storage.dart';
import 'package:getx_xiaopa/values/values.dart';
import 'package:getx_xiaopa/widget/widget.dart';

import 'diary_mood_logic.dart';
import 'diary_mood_state.dart';

class DiaryMoodPage extends BaseCommonView {
  DiaryMoodPage({super.key});

  final DiaryMoodLogic logic = Get.put(DiaryMoodLogic());
  final DiaryMoodState state = Get.find<DiaryMoodLogic>().state;

  @override
  bool get customWidget => true;

  @override
  Widget buildContent() {
    return GetBuilder<DiaryMoodLogic>(
      id: "diary_mood_view",
      builder: (_) {
        return Scaffold(
          backgroundColor: const Color.fromRGBO(249, 249, 249, 1),
          extendBodyBehindAppBar: true,
          appBar: AppBar(
            elevation: 0,
            scrolledUnderElevation: 0,
            backgroundColor: const Color.fromRGBO(249, 249, 249, 1),
            centerTitle: true,
            title: Text(
              state.title,
              style: StyleConfig.blackStyle(
                  fontSize: 18, fontWeight: FontWeight.w500),
            ),
            leadingWidth: 35.w,
            leading: Padding(
              padding: EdgeInsets.only(left: 15.w),
              child: Images(
                path: R.back_png,
                width: 20.w,
                height: 20.h,
                scale: 2.8,
              ).inkWell(() => Get.back()),
            ),
            actions: [
              Padding(
                padding: EdgeInsets.only(right: 15.w),
                child: Text(
                  state.managerTip,
                  style: StyleConfig.otherStyle(
                      color: state.isManager
                          ? ColorConfig.searchTextColor
                          : const Color.fromRGBO(253, 105, 82, 1),
                      fontSize: 12),
                ),
              ).inkWell(() => logic.managerAction())
            ],
          ),
          body: Column(
            children: [
              Expanded(
                child: Container(
                  margin: EdgeInsets.only(top: 10.h, bottom: 10.h),
                  child: createCommonView(logic, (_) {
                    return state.diaryItems.isNotEmpty
                        ? ListView.builder(
                            itemCount: state.diaryItems.length,
                            itemBuilder: (_, index) {
                              return DiaryMoodItem(
                                cIndex: index,
                                isManager: state.isManager,
                                dateText: logic.getDiaryDate(
                                    state.diaryItems[index].createdAt ?? ''),
                                diaryText: CommonStore.to.getMoodName(
                                    state.diaryItems[index].mood ?? ''),
                                diaryColor: logic.getMoodColor(
                                    state.diaryItems[index].mood ?? ''),
                                eventTags: state.diaryItems[index].thing ?? '',
                                signTags: state.diaryItems[index].feeling ?? '',
                                displayedText: state.diaryItems[index].content,
                                diaryImage:
                                    state.diaryItems[index].picture ?? '',
                                onTap: (cIndex) {
                                  logic.tapAction(cIndex);
                                },
                                managerAction: (cIndex, flag) {
                                  logic.delAction(cIndex, flag);
                                },
                              );
                            })
                        : EmptyDataWidget(
                            title: "diary_mood_empty_tips".tr,
                            imagePath: R.no_data_diary_png,
                          );
                  }, initBuilder: () => const LoadStatusWidget()),
                ),
              ),
              state.isManager
                  ? Container(
                      margin: EdgeInsets.only(
                          bottom: ScreenUtil().bottomBarHeight + 10.h),
                      width: 283.w,
                      height: 48.h,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(24.r),
                        color: const Color.fromRGBO(40, 39, 46, 1),
                      ),
                      child: Center(
                        child: ThemeText(
                            dataStr: "console_paring_wifi_delete".tr,
                            keyName: 'textColor',
                            fontWeight: FontWeight.w500),
                      ).inkWell(() => logic.delTapAction()),
                    )
                  : const SizedBox()
            ],
          ),
        );
      },
    );
  }
}
