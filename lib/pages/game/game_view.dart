import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:getx_xiaopa/base/base.dart';
import 'package:getx_xiaopa/storage/storage.dart';
import 'package:getx_xiaopa/widget/widget.dart';
import 'package:webview_flutter/webview_flutter.dart';

import 'game_logic.dart';
import 'game_state.dart';

class GamePage extends BaseCommonView {
  GamePage({super.key});

  final GameLogic logic = Get.put(GameLogic());
  final GameState state = Get.find<GameLogic>().state;

  @override
  bool? get isHiddenNav => true;

  @override
  Widget buildContent() {
    const PlatformWebViewControllerCreationParams params =
        PlatformWebViewControllerCreationParams();
    late WebViewController wvController =
        WebViewController.fromPlatformCreationParams(params);
    wvController.clearCache();
    wvController.setJavaScriptMode(JavaScriptMode.unrestricted);
    wvController.setBackgroundColor(Colors.black);
    wvController.loadRequest(
        Uri.parse("https://meta.marspt.cn?token=${UserStore.to.authorization}"),
        headers: {
          "User-Agent": "Mozilla/5.0 AppleWebKit/537.36 (KHTML, like Gecko)"
        });
    wvController.setNavigationDelegate(NavigationDelegate(
      onPageFinished: (url) {
        logic.reloadUI();
        const jsCode = """
                document.querySelector('body').setAttribute('data-weui-theme', 'dark');
                const sections = document.querySelectorAll('section');
                sections.forEach(function(section) {
                  section.style.backgroundColor = 'black';
                  section.style.color = 'white'; // 设置文字颜色为白色以便能看清内容
                });
                """;
        //改变网页背景颜色和文字颜色
        wvController.runJavaScript(jsCode);
      },
    ));

    return GetBuilder<GameLogic>(
      id: "game_view",
      builder: (_) {
        return state.isLoad
            ? WebViewWidget(controller: wvController)
            : const LoadStatusWidget();
      },
    );
  }
}
