import 'dart:async';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:getx_xiaopa/r.dart';
import 'package:getx_xiaopa/utils/utils.dart';
import 'package:getx_xiaopa/values/values.dart';

class RegisterState {
  ///1=忘记密码  2=用户注册
  int type = 1;

  String phoneStr = '';
  TextEditingController passwordController = TextEditingController();
  TextEditingController password2Controller = TextEditingController();
  TextEditingController verifyController = TextEditingController();

  var agree = false;

  var verifyStr = 'login_verify_send'.tr;
  late Timer timer;
  late int seconds;
  var inkWellStyle = StyleConfig.otherStyle(
      color: Get.find<ThemeColorController>().gTextColor, fontSize: 12);

  var isObscureText = true;
  var isObscureText2 = true;

  String iconData = R.login_eye_on_png;
  String iconData2 = R.login_eye_on_png;

  bool isSend = false;
  bool isComfire = false;

  String areaCode = "cn";
  String areaDialCode = "86";

  ///限制手机号码位数，根据不同国家来
  int phoneMaxLenght = 11;

  RegisterState() {
    ///Initialize variables
    seconds = 60;
  }
}
