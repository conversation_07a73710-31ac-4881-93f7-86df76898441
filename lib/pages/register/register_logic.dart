import 'dart:async';
import 'package:get/get.dart';
import 'package:getx_xiaopa/extension/widget_extension.dart';
import 'package:getx_xiaopa/network/http_client.dart';
import 'package:getx_xiaopa/network/model/result.dart';
import 'package:getx_xiaopa/r.dart';
import 'package:getx_xiaopa/routes/router.dart';
import 'package:getx_xiaopa/storage/storage.dart';
import 'package:getx_xiaopa/utils/utils.dart';
import 'package:getx_xiaopa/values/values.dart';

import 'register_state.dart';

class RegisterLogic extends GetxController {
  final RegisterState state = RegisterState();

  @override
  void onInit() {
    super.onInit();
    state.type = Get.arguments["type"];

    state.areaCode = ConfigStore.to.areaCode;
    state.areaDialCode = ConfigStore.to.areaDialCode;

    _phoneMaxLenght();
  }

  selectCountryAction(String code, String dialCode) {
    state.areaCode = code;
    state.areaDialCode = dialCode;
    ConfigStore.to.setAreaCode(code);
    ConfigStore.to.setAreaDialCode(dialCode);
    _phoneMaxLenght();
  }

  _phoneMaxLenght() {
    if (state.areaDialCode == "86") {
      state.phoneMaxLenght = 11;
    } else if (state.areaDialCode == "852" || state.areaDialCode == "853") {
      state.phoneMaxLenght = 8;
    } else {
      state.phoneMaxLenght = 15;
    }
    update(["register_view"]);
  }

  obscureAction() {
    state.isObscureText = !state.isObscureText;
    if (state.isObscureText) {
      state.iconData = R.login_eye_on_png;
    } else {
      state.iconData = R.login_eye_off_png;
    }
    update(["register_view"]);
  }

  obscureAction2() {
    state.isObscureText2 = !state.isObscureText2;
    if (state.isObscureText2) {
      state.iconData2 = R.login_eye_on_png;
    } else {
      state.iconData2 = R.login_eye_off_png;
    }
    update(["register_view"]);
  }

  ///注册事件
  registerAction() async {
    if (!state.isComfire) return;

    if (state.passwordController.text.length < 8 ||
        PatternUtil.isLetterAndNumber(state.passwordController.text) == false) {
      CToast.showToast("register_pwd_hint_text".tr);
      return;
    }

    if (state.passwordController.text != state.password2Controller.text) {
      CToast.showToast("mine_pwd_error_tips".tr);
      return;
    }
    if (state.verifyController.text.isEmpty) {
      CToast.showToast("login_error_tips_three".tr);
      return;
    }

    ///注册时才需要判断这个
    if (state.type == 2) {
      if (state.agree == false) {
        CToast.showToast("login_error_tips_five".tr);
        return;
      }
    }

    CToast.showLoading();
    int resultCode = 1;
    if (state.type == 1) {
      resultCode = await retrievePassword();
    }
    if (state.type == 2) {
      resultCode = await register();
    }

    if (resultCode == 0) {
      Get.offAndToNamed(AppRoutes.LOGIN);
    }
  }

  ///获取验证码事件
  sendPhoneAction() async {
    if (state.isSend) return;

    bool flag = await _sendPhoneVerify();
    if (flag) {
      state.isSend = true;
      _startTimer();
    }
  }

  phoneIsComponse(String value) {
    state.phoneStr = value;
    isComponse();
  }

  isComponse() {
    if (state.phoneStr.isNotEmpty &&
        state.passwordController.text.isNotEmpty &&
        state.password2Controller.text.isNotEmpty &&
        state.verifyController.text.isNotEmpty) {
      state.isComfire = true;
    } else {
      state.isComfire = false;
    }
    update(["register_view"]);
  }

  /// 启动倒计时的计时器。
  void _startTimer() {
    if (state.seconds != 60) return;
    state.inkWellStyle = StyleConfig.otherStyle(
        color: ColorConfig.shopDetailTextColor, fontSize: 12);
    // 计时器（`Timer`）组件的定期（`periodic`）构造函数，创建一个新的重复计时器。
    state.timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (state.seconds == 0) {
        _cancelTimer();
        state.isSend = false;
        state.seconds = 60;
        state.inkWellStyle = StyleConfig.otherStyle(
            color: Get.find<ThemeColorController>().gTextColor, fontSize: 12);
        state.verifyStr = 'login_verify_resend'.tr;
        update(["register_view"]);
        return;
      }
      state.seconds--;
      state.verifyStr = '${state.seconds}s';
      update(["register_view"]);
    });
  }

  void _cancelTimer() {
    state.timer.cancel();
  }

  ///================================网络请求=======================================
  Future<bool> _sendPhoneVerify() async {
    var map = {
      "mobile_area": state.areaDialCode,
      "mobile": "${state.areaDialCode}${state.phoneStr}",
      "scene": state.type == 1 ? "confirm" : "login",
    };

    Result result = await http.sendPhoneVerify(map);
    CToast.showToast(result.msg!);
    if (result.code == 0) {
      return true;
    }
    return false;
  }

  Future<int> retrievePassword() async {
    CToast.showLoading();
    var map = {
      "mobile": "${state.areaDialCode}${state.phoneStr}",
      "password": state.passwordController.text,
      "passwordConfirm": state.password2Controller.text,
      "verifyCode": state.verifyController.text,
    };
    Result result = await http.retrieveUserInfo(map);
    CToast.showToast(result.msg!);
    return result.code;
  }

  Future<int> register() async {
    var map = {
      "mobile_area": state.areaDialCode,
      "mobile_number": state.phoneStr,
      "mobile": "",
      "password": state.passwordController.text,
      "passwordConfirm": state.password2Controller.text,
      "verifyCode": state.verifyController.text,
      "client": 3
    };
    Result result = await http.loginRegiter(map);
    CToast.showToast(result.msg!);
    return result.code;
  }
}
