import 'dart:async';
import 'package:get/get.dart';
import 'package:getx_xiaopa/extension/widget_extension.dart';
import 'package:getx_xiaopa/network/http_client.dart';
import 'package:getx_xiaopa/network/model/result.dart';
import 'package:getx_xiaopa/pages/console/console_schedule/utils/schedule_store.dart';
import 'package:getx_xiaopa/pages/diary/utils/diary_store.dart';
import 'package:getx_xiaopa/pages/login/model/user_info_model.dart';
import 'package:getx_xiaopa/r.dart';
import 'package:getx_xiaopa/routes/router.dart';
import 'package:getx_xiaopa/storage/storage.dart';
import 'package:getx_xiaopa/values/values.dart';

import 'login_state.dart';

class LoginLogic extends GetxController {
  final LoginState state = LoginState();

  @override
  void onInit() async {
    super.onInit();
    state.phoneStr = UserStore.to.account;
    state.passwordController.text = UserStore.to.password;

    state.areaCode = ConfigStore.to.areaCode;
    state.areaDialCode = ConfigStore.to.areaDialCode;

    _phoneMaxLenght();
  }

  @override
  void onClose() {
    state.passwordController.dispose();
    state.verifyController.dispose();
    super.onClose();
  }

  phoneInputAction(String str) {
    state.phoneStr = str;
  }

  selectCountryAction(String code, String dialCode) {
    state.areaCode = code;
    state.areaDialCode = dialCode;

    ConfigStore.to.setAreaCode(code);
    ConfigStore.to.setAreaDialCode(dialCode);

    _phoneMaxLenght();
  }

  _phoneMaxLenght() {
    if (state.areaDialCode == "86") {
      state.phoneMaxLenght = 11;
    } else if (state.areaDialCode == "852" || state.areaDialCode == "853") {
      state.phoneMaxLenght = 8;
    } else {
      state.phoneMaxLenght = 15;
    }
    update(["login_view"]);
  }

  loginTypeAction(int type) {
    switch (type) {
      case 1:
        state.loginType = 1;
        state.verifyController.text = "";
        break;
      case 2:
        state.loginType = 2;
        break;
      default:
        break;
    }
    update(["login_view"]);
  }

  obscureAction() {
    state.isObscureText = !state.isObscureText;
    if (state.isObscureText) {
      state.iconData = R.login_eye_on_png;
    } else {
      state.iconData = R.login_eye_off_png;
    }
    update(["login_view"]);
  }

  agreeAction(bool flag) {
    state.agree = flag;
    update(["login_view"]);
  }

  sendPhoneVerifyAction() async {
    if (state.isSend) return;
    bool flag = await _sendPhoneVerify();
    if (flag) {
      state.isSend = true;
      _startTimer();
    }
  }

  /// 启动倒计时的计时器。
  void _startTimer() {
    if (state.seconds != 60) return;
    state.inkWellStyle = StyleConfig.otherStyle(
        color: ColorConfig.shopDetailTextColor, fontSize: 12);
    // 计时器（`Timer`）组件的定期（`periodic`）构造函数，创建一个新的重复计时器。
    state.timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (state.seconds == 0) {
        _cancelTimer();
        state.isSend = false;
        state.seconds = 60;
        state.inkWellStyle =
            StyleConfig.otherStyle(color: ColorConfig.mainColor, fontSize: 12);
        state.verifyStr = 'login_verify_resend'.tr;
        update(["login_view"]);
        return;
      }
      state.seconds--;
      state.verifyStr = '${state.seconds}s';
      update(["login_view"]);
    });
  }

  void _cancelTimer() {
    state.timer.cancel();
  }

  ///登录事件
  loginAction() {
    if (state.phoneStr.isEmpty) {
      CToast.showToast("login_error_tips_one".tr);
      return;
    }
    switch (state.loginType) {
      case 1:
        if (state.passwordController.text.isEmpty) {
          CToast.showToast("login_error_tips_two".tr);
          return;
        }
        break;
      case 2:
        if (state.verifyController.text.isEmpty) {
          CToast.showToast("login_error_tips_three".tr);
          return;
        }
        if (state.verifyController.text.length < 6) {
          CToast.showToast("login_error_tips_four".tr);
          return;
        }
        break;
      default:
        break;
    }
    if (state.agree == false) {
      CToast.showToast("login_error_tips_five".tr);
      return;
    }

    _login();
  }

  ///忘记密码 注册账号
  otherAction(int type) {
    ///type=1忘记密码  type=2注册账号
    Get.toNamed(AppRoutes.REGISTER, arguments: {"type": type})?.then((result) {
      state.areaCode = ConfigStore.to.areaCode;
      update(["login_view"]);
    });
  }

  ///======================网络请求===========================================
  Future<bool> _sendPhoneVerify() async {
    var map = {
      "mobile_area": state.areaDialCode,
      "mobile": "${state.areaDialCode}${state.phoneStr}",
      "scene": "login",
    };
    Result result = await http.sendPhoneVerify(map);
    CToast.showToast(result.msg!);
    if (result.code == 0) {
      return true;
    }
    return false;
  }

  _login() async {
    CToast.showLoading();
    var map = {
      "mobile_number": state.phoneStr,
      "mobile": "${state.areaDialCode}${state.phoneStr}",
      "password": state.loginType == 1 ? state.passwordController.text : "",
      "verifyCode": state.loginType == 2 ? state.verifyController.text : "",
      "client": 3
    };
    Result result = await http.login(map);
    if (result.code == 0) {
      UserInfoModel model = result.data;

      UserStore.to.setAccount(state.phoneStr);
      UserStore.to.setPassword(state.passwordController.text);

      UserStore.to.setSelectDevice(0);
      UserStore.to.setLoggingOut(true);

      if (state.loginType == 1) {
        UserStore.to.setPassword(state.passwordController.text);
      } else {
        UserStore.to.removePassword();
      }

      UserStore.to.setPwdLogin('${state.loginType}');

      await UserStore.to.getUserInfo(token: model.token!);

      await UserStore.to.deviceItems(isChangeTheme: true);
      if (UserStore.to.deviceList.isNotEmpty) {
        DiaryStore.to.getDiaryItems();
        ScheduleStore.to.init();
      }
      await UserStore.to.getAccountItems();

      Get.offAndToNamed(AppRoutes.APPLICATION);
    } else {
      if (state.loginType == 2) {
        state.verifyController.text = "";
      }
      CToast.showToast(result.msg!);
    }
  }
}
