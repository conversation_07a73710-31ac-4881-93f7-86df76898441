class UserInfoModerMerchant {
/*
{
  "user_id": "",
  "merchant_code": "",
  "qq": "",
  "wechat": "",
  "merchant_name": "",
  "declaration": "",
  "introduction": "",
  "apply_time": "0001-01-01T00:00:00Z",
  "status": 0,
  "reject_reason": "",
  "approval_time": "0001-01-01T00:00:00Z",
  "approval_user_id": "",
  "user_code": ""
} 
*/

  String? userId;
  String? merchantCode;
  String? qq;
  String? wechat;
  String? merchantName;
  String? declaration;
  String? introduction;
  String? applyTime;
  int? status;
  String? rejectReason;
  String? approvalTime;
  String? approvalUserId;
  String? userCode;

  UserInfoModerMerchant({
    this.userId,
    this.merchantCode,
    this.qq,
    this.wechat,
    this.merchantName,
    this.declaration,
    this.introduction,
    this.applyTime,
    this.status,
    this.rejectReason,
    this.approvalTime,
    this.approvalUserId,
    this.userCode,
  });
  UserInfoModerMerchant.fromJson(Map<String, dynamic> json) {
    userId = json['user_id']?.toString();
    merchantCode = json['merchant_code']?.toString();
    qq = json['qq']?.toString();
    wechat = json['wechat']?.toString();
    merchantName = json['merchant_name']?.toString();
    declaration = json['declaration']?.toString();
    introduction = json['introduction']?.toString();
    applyTime = json['apply_time']?.toString();
    status = json['status']?.toInt();
    rejectReason = json['reject_reason']?.toString();
    approvalTime = json['approval_time']?.toString();
    approvalUserId = json['approval_user_id']?.toString();
    userCode = json['user_code']?.toString();
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['user_id'] = userId;
    data['merchant_code'] = merchantCode;
    data['qq'] = qq;
    data['wechat'] = wechat;
    data['merchant_name'] = merchantName;
    data['declaration'] = declaration;
    data['introduction'] = introduction;
    data['apply_time'] = applyTime;
    data['status'] = status;
    data['reject_reason'] = rejectReason;
    data['approval_time'] = approvalTime;
    data['approval_user_id'] = approvalUserId;
    data['user_code'] = userCode;
    return data;
  }
}

class UserInfoModelUserAccount {
/*
{
  "created_at": "0001-01-01T00:00:00Z",
  "updated_at": "0001-01-01T00:00:00Z",
  "deleted_at": null,
  "id": "",
  "user_id": "",
  "diamond_balance": 0,
  "available_diamond_balance": 0,
  "kat_balance": 0,
  "total_biz_buy_diamond_num": 0,
  "total_sys_transfer_diamond_num": 0,
  "total_matrix_principal_num": 0,
  "total_matrix_profit_num": 0,
  "basic_card_id": "",
  "basic_card_name": "",
  "user_code": ""
} 
*/

  String? createdAt;
  String? updatedAt;
  String? deletedAt;
  String? id;
  String? userId;
  int? diamondBalance;
  int? availableDiamondBalance;
  int? katBalance;
  int? totalBizBuyDiamondNum;
  int? totalSysTransferDiamondNum;
  int? totalMatrixPrincipalNum;
  int? totalMatrixProfitNum;
  String? basicCardId;
  String? basicCardName;
  String? userCode;

  UserInfoModelUserAccount({
    this.createdAt,
    this.updatedAt,
    this.deletedAt,
    this.id,
    this.userId,
    this.diamondBalance,
    this.availableDiamondBalance,
    this.katBalance,
    this.totalBizBuyDiamondNum,
    this.totalSysTransferDiamondNum,
    this.totalMatrixPrincipalNum,
    this.totalMatrixProfitNum,
    this.basicCardId,
    this.basicCardName,
    this.userCode,
  });
  UserInfoModelUserAccount.fromJson(Map<String, dynamic> json) {
    createdAt = json['created_at']?.toString();
    updatedAt = json['updated_at']?.toString();
    deletedAt = json['deleted_at']?.toString();
    id = json['id']?.toString();
    userId = json['user_id']?.toString();
    diamondBalance = json['diamond_balance']?.toInt();
    availableDiamondBalance = json['available_diamond_balance']?.toInt();
    katBalance = json['kat_balance']?.toInt();
    totalBizBuyDiamondNum = json['total_biz_buy_diamond_num']?.toInt();
    totalSysTransferDiamondNum =
        json['total_sys_transfer_diamond_num']?.toInt();
    totalMatrixPrincipalNum = json['total_matrix_principal_num']?.toInt();
    totalMatrixProfitNum = json['total_matrix_profit_num']?.toInt();
    basicCardId = json['basic_card_id']?.toString();
    basicCardName = json['basic_card_name']?.toString();
    userCode = json['user_code']?.toString();
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['created_at'] = createdAt;
    data['updated_at'] = updatedAt;
    data['deleted_at'] = deletedAt;
    data['id'] = id;
    data['user_id'] = userId;
    data['diamond_balance'] = diamondBalance;
    data['available_diamond_balance'] = availableDiamondBalance;
    data['kat_balance'] = katBalance;
    data['total_biz_buy_diamond_num'] = totalBizBuyDiamondNum;
    data['total_sys_transfer_diamond_num'] = totalSysTransferDiamondNum;
    data['total_matrix_principal_num'] = totalMatrixPrincipalNum;
    data['total_matrix_profit_num'] = totalMatrixProfitNum;
    data['basic_card_id'] = basicCardId;
    data['basic_card_name'] = basicCardName;
    data['user_code'] = userCode;
    return data;
  }
}

class UserInfoModelUserTeam {
/*
{
  "created_at": "0001-01-01T00:00:00Z",
  "updated_at": "0001-01-01T00:00:00Z",
  "deleted_at": null,
  "id": "",
  "user_id": "",
  "level_id": 0,
  "level_name": "",
  "team_num": 0,
  "team_active_num": 0,
  "direct_num": 0,
  "direct_active_num": 0,
  "pool_level_id": 0,
  "pool_level_name": "",
  "pool_team_achievement": 0
} 
*/

  String? createdAt;
  String? updatedAt;
  String? deletedAt;
  String? id;
  String? userId;
  int? levelId;
  String? levelName;
  int? teamNum;
  int? teamActiveNum;
  int? directNum;
  int? directActiveNum;
  int? poolLevelId;
  String? poolLevelName;
  int? poolTeamAchievement;

  UserInfoModelUserTeam({
    this.createdAt,
    this.updatedAt,
    this.deletedAt,
    this.id,
    this.userId,
    this.levelId,
    this.levelName,
    this.teamNum,
    this.teamActiveNum,
    this.directNum,
    this.directActiveNum,
    this.poolLevelId,
    this.poolLevelName,
    this.poolTeamAchievement,
  });
  UserInfoModelUserTeam.fromJson(Map<String, dynamic> json) {
    createdAt = json['created_at']?.toString();
    updatedAt = json['updated_at']?.toString();
    deletedAt = json['deleted_at']?.toString();
    id = json['id']?.toString();
    userId = json['user_id']?.toString();
    levelId = json['level_id']?.toInt();
    levelName = json['level_name']?.toString();
    teamNum = json['team_num']?.toInt();
    teamActiveNum = json['team_active_num']?.toInt();
    directNum = json['direct_num']?.toInt();
    directActiveNum = json['direct_active_num']?.toInt();
    poolLevelId = json['pool_level_id']?.toInt();
    poolLevelName = json['pool_level_name']?.toString();
    poolTeamAchievement = json['pool_team_achievement']?.toInt();
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['created_at'] = createdAt;
    data['updated_at'] = updatedAt;
    data['deleted_at'] = deletedAt;
    data['id'] = id;
    data['user_id'] = userId;
    data['level_id'] = levelId;
    data['level_name'] = levelName;
    data['team_num'] = teamNum;
    data['team_active_num'] = teamActiveNum;
    data['direct_num'] = directNum;
    data['direct_active_num'] = directActiveNum;
    data['pool_level_id'] = poolLevelId;
    data['pool_level_name'] = poolLevelName;
    data['pool_team_achievement'] = poolTeamAchievement;
    return data;
  }
}

class UserInfoModelUserParents {
/*
{
  "created_at": "0001-01-01T00:00:00Z",
  "updated_at": "0001-01-01T00:00:00Z",
  "deleted_at": null,
  "id": "",
  "user_id": "",
  "parent_id": "",
  "parents": ""
} 
*/

  String? createdAt;
  String? updatedAt;
  String? deletedAt;
  String? id;
  String? userId;
  String? parentId;
  String? parents;

  UserInfoModelUserParents({
    this.createdAt,
    this.updatedAt,
    this.deletedAt,
    this.id,
    this.userId,
    this.parentId,
    this.parents,
  });
  UserInfoModelUserParents.fromJson(Map<String, dynamic> json) {
    createdAt = json['created_at']?.toString();
    updatedAt = json['updated_at']?.toString();
    deletedAt = json['deleted_at']?.toString();
    id = json['id']?.toString();
    userId = json['user_id']?.toString();
    parentId = json['parent_id']?.toString();
    parents = json['parents']?.toString();
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['created_at'] = createdAt;
    data['updated_at'] = updatedAt;
    data['deleted_at'] = deletedAt;
    data['id'] = id;
    data['user_id'] = userId;
    data['parent_id'] = parentId;
    data['parents'] = parents;
    return data;
  }
}

class UserInfoModelIdentity {
/*
{
  "id": "",
  "created_at": "0001-01-01T00:00:00Z",
  "updated_at": "0001-01-01T00:00:00Z",
  "deleted_at": null,
  "name": "",
  "idcard": "",
  "user_id": "",
  "user_mobile": ""
} 
*/

  String? id;
  String? createdAt;
  String? updatedAt;
  String? deletedAt;
  String? name;
  String? idcard;
  String? userId;
  String? userMobile;

  UserInfoModelIdentity({
    this.id,
    this.createdAt,
    this.updatedAt,
    this.deletedAt,
    this.name,
    this.idcard,
    this.userId,
    this.userMobile,
  });
  UserInfoModelIdentity.fromJson(Map<String, dynamic> json) {
    id = json['id']?.toString();
    createdAt = json['created_at']?.toString();
    updatedAt = json['updated_at']?.toString();
    deletedAt = json['deleted_at']?.toString();
    name = json['name']?.toString();
    idcard = json['idcard']?.toString();
    userId = json['user_id']?.toString();
    userMobile = json['user_mobile']?.toString();
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['id'] = id;
    data['created_at'] = createdAt;
    data['updated_at'] = updatedAt;
    data['deleted_at'] = deletedAt;
    data['name'] = name;
    data['idcard'] = idcard;
    data['user_id'] = userId;
    data['user_mobile'] = userMobile;
    return data;
  }
}

class UserInfoModel {
/*
{
  "id": "3830242641707008",
  "created_at": "2024-06-17T18:00:20.049+08:00",
  "updated_at": "2024-06-24T09:53:08.795+08:00",
  "deleted_at": null,
  "username": "",
  "code": "7879285",
  "pid": "3803228069167104",
  "nickname": "19999999999",
  "avatar": "upload/brain-avatar/202406/dc8035180a8bd7a86ab83304d5dcba93.jpg",
  "sign": "",
  "mobile": "19999999999",
  "email": "",
  "password": "7a3c45144ce0cbe5d8ce811e6fa21771",
  "pay_password": "14e1b600b1fd579f47433b88e8d85291",
  "weixin": "",
  "qq": "",
  "address": "",
  "open_id": "",
  "alipay_user_id": "",
  "alipay_bind_at": 0,
  "status": 1,
  "is_admin": 2,
  "is_freeze": 0,
  "is_sms_freeze": 0,
  "theme": "",
  "awards": 0,
  "partner_id": "",
  "package_id": "",
  "poster": "",
  "token": "e570c5eef922166d5c5771093d1cf5b3",
  "login_count": 12,
  "freeze_count": 1,
  "freeze_time": 0,
  "sms_freeze_time": 0,
  "last_login_time": 1719193988794,
  "last_login_ip": "************",
  "role_ids": "",
  "identity": {
    "id": "",
    "created_at": "0001-01-01T00:00:00Z",
    "updated_at": "0001-01-01T00:00:00Z",
    "deleted_at": null,
    "name": "",
    "idcard": "",
    "user_id": "",
    "user_mobile": ""
  },
  "user_parents": {
    "created_at": "0001-01-01T00:00:00Z",
    "updated_at": "0001-01-01T00:00:00Z",
    "deleted_at": null,
    "id": "",
    "user_id": "",
    "parent_id": "",
    "parents": ""
  },
  "user_team": {
    "created_at": "0001-01-01T00:00:00Z",
    "updated_at": "0001-01-01T00:00:00Z",
    "deleted_at": null,
    "id": "",
    "user_id": "",
    "level_id": 0,
    "level_name": "",
    "team_num": 0,
    "team_active_num": 0,
    "direct_num": 0,
    "direct_active_num": 0,
    "pool_level_id": 0,
    "pool_level_name": "",
    "pool_team_achievement": 0
  },
  "user_account": {
    "created_at": "0001-01-01T00:00:00Z",
    "updated_at": "0001-01-01T00:00:00Z",
    "deleted_at": null,
    "id": "",
    "user_id": "",
    "diamond_balance": 0,
    "available_diamond_balance": 0,
    "kat_balance": 0,
    "total_biz_buy_diamond_num": 0,
    "total_sys_transfer_diamond_num": 0,
    "total_matrix_principal_num": 0,
    "total_matrix_profit_num": 0,
    "basic_card_id": "",
    "basic_card_name": "",
    "user_code": ""
  }
} 
*/

  String? id;
  String? createdAt;
  String? updatedAt;
  String? deletedAt;
  String? username;
  String? code;
  String? pid;
  String? nickname;
  String? avatar;
  String? sign;
  String? mobile;
  String? mobileArea;
  String? mobileNumber;
  String? email;
  String? password;
  String? payPassword;
  String? weixin;
  String? qq;
  String? address;
  String? openId;
  String? alipayUserId;
  int? alipayBindAt;
  int? status;
  int? isAdmin;
  int? isFreeze;
  int? isSmsFreeze;
  String? isMerchant;
  String? theme;
  int? awards;
  String? partnerId;
  String? packageId;
  String? poster;
  String? token;
  int? loginCount;
  int? freezeCount;
  int? freezeTime;
  int? smsFreezeTime;
  int? lastLoginTime;
  String? lastLoginIp;
  String? roleIds;
  UserInfoModelIdentity? identity;
  UserInfoModelUserParents? userParents;
  UserInfoModelUserTeam? userTeam;
  UserInfoModelUserAccount? userAccount;
  UserInfoModerMerchant? userMerchant;
  int? gender;
  String? birthday;

  UserInfoModel({
    this.id,
    this.createdAt,
    this.updatedAt,
    this.deletedAt,
    this.username,
    this.code,
    this.pid,
    this.nickname,
    this.avatar,
    this.sign,
    this.mobile,
    this.mobileArea,
    this.mobileNumber,
    this.email,
    this.password,
    this.payPassword,
    this.weixin,
    this.qq,
    this.address,
    this.openId,
    this.alipayUserId,
    this.alipayBindAt,
    this.status,
    this.isAdmin,
    this.isFreeze,
    this.isSmsFreeze,
    this.isMerchant,
    this.theme,
    this.awards,
    this.partnerId,
    this.packageId,
    this.poster,
    this.token,
    this.loginCount,
    this.freezeCount,
    this.freezeTime,
    this.smsFreezeTime,
    this.lastLoginTime,
    this.lastLoginIp,
    this.roleIds,
    this.identity,
    this.userParents,
    this.userTeam,
    this.userAccount,
    this.userMerchant,
    this.gender,
    this.birthday,
  });
  UserInfoModel.fromJson(Map<String, dynamic> json) {
    id = json['id']?.toString();
    createdAt = json['created_at']?.toString();
    updatedAt = json['updated_at']?.toString();
    deletedAt = json['deleted_at']?.toString();
    username = json['username']?.toString();
    code = json['code']?.toString();
    pid = json['pid']?.toString();
    nickname = json['nickname']?.toString();
    avatar = json['avatar']?.toString();
    sign = json['sign']?.toString();
    mobile = json['mobile']?.toString();
    mobileArea = json['mobile_area']?.toString();
    mobileNumber = json['mobile_number']?.toString();
    email = json['email']?.toString();
    password = json['password']?.toString();
    payPassword = json['pay_password']?.toString();
    weixin = json['weixin']?.toString();
    qq = json['qq']?.toString();
    address = json['address']?.toString();
    openId = json['open_id']?.toString();
    alipayUserId = json['alipay_user_id']?.toString();
    alipayBindAt = json['alipay_bind_at']?.toInt();
    status = json['status']?.toInt();
    isAdmin = json['is_admin']?.toInt();
    isFreeze = json['is_freeze']?.toInt();
    isSmsFreeze = json['is_sms_freeze']?.toInt();
    isMerchant = json['is_merchant']?.toString();
    theme = json['theme']?.toString();
    awards = json['awards']?.toInt();
    partnerId = json['partner_id']?.toString();
    packageId = json['package_id']?.toString();
    poster = json['poster']?.toString();
    token = json['token']?.toString();
    loginCount = json['login_count']?.toInt();
    freezeCount = json['freeze_count']?.toInt();
    freezeTime = json['freeze_time']?.toInt();
    smsFreezeTime = json['sms_freeze_time']?.toInt();
    lastLoginTime = json['last_login_time']?.toInt();
    lastLoginIp = json['last_login_ip']?.toString();
    roleIds = json['role_ids']?.toString();
    identity = (json['identity'] != null)
        ? UserInfoModelIdentity.fromJson(json['identity'])
        : null;
    userParents = (json['user_parents'] != null)
        ? UserInfoModelUserParents.fromJson(json['user_parents'])
        : null;
    userTeam = (json['user_team'] != null)
        ? UserInfoModelUserTeam.fromJson(json['user_team'])
        : null;
    userAccount = (json['user_account'] != null)
        ? UserInfoModelUserAccount.fromJson(json['user_account'])
        : null;
    userMerchant = (json['merchant'] != null)
        ? UserInfoModerMerchant.fromJson(json['merchant'])
        : null;
    gender = json['gender']?.toInt();
    birthday = json['birthday']?.toString();
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['id'] = id;
    data['created_at'] = createdAt;
    data['updated_at'] = updatedAt;
    data['deleted_at'] = deletedAt;
    data['username'] = username;
    data['code'] = code;
    data['pid'] = pid;
    data['nickname'] = nickname;
    data['avatar'] = avatar;
    data['sign'] = sign;
    data['mobile'] = mobile;
    data['mobile_area'] = mobileArea;
    data['mobile_number'] = mobileNumber;
    data['email'] = email;
    data['password'] = password;
    data['pay_password'] = payPassword;
    data['weixin'] = weixin;
    data['qq'] = qq;
    data['address'] = address;
    data['open_id'] = openId;
    data['alipay_user_id'] = alipayUserId;
    data['alipay_bind_at'] = alipayBindAt;
    data['status'] = status;
    data['is_admin'] = isAdmin;
    data['is_freeze'] = isFreeze;
    data['is_sms_freeze'] = isSmsFreeze;
    data['is_merchant'] = isMerchant;
    data['theme'] = theme;
    data['awards'] = awards;
    data['partner_id'] = partnerId;
    data['package_id'] = packageId;
    data['poster'] = poster;
    data['token'] = token;
    data['login_count'] = loginCount;
    data['freeze_count'] = freezeCount;
    data['freeze_time'] = freezeTime;
    data['sms_freeze_time'] = smsFreezeTime;
    data['last_login_time'] = lastLoginTime;
    data['last_login_ip'] = lastLoginIp;
    data['role_ids'] = roleIds;
    if (identity != null) {
      data['identity'] = identity!.toJson();
    }
    if (userParents != null) {
      data['user_parents'] = userParents!.toJson();
    }
    if (userTeam != null) {
      data['user_team'] = userTeam!.toJson();
    }
    if (userAccount != null) {
      data['user_account'] = userAccount!.toJson();
    }
    if (userMerchant != null) {
      data['merchant'] = userMerchant!.toJson();
    }
    data['gender'] = gender;
    data['birthday'] = birthday;
    return data;
  }
}
