import 'dart:async';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:getx_xiaopa/r.dart';
import 'package:getx_xiaopa/storage/storage.dart';
import 'package:getx_xiaopa/values/values.dart';

class LoginState {
  String phoneStr = '';
  TextEditingController passwordController = TextEditingController();
  TextEditingController verifyController = TextEditingController();

  /// 1=账号密码登录  2=验证码登录
  var loginType = 1;

  /// 是否同意隐私协议
  var agree = false;

  var isObscureText = true;
  String iconData = R.login_eye_on_png;

  var verifyStr = 'login_verify_send'.tr;
  late Timer timer;
  late int seconds = 0;
  var inkWellStyle =
      StyleConfig.otherStyle(color: ColorConfig.mainColor, fontSize: 12);

  bool isSend = false;

  ///区号，默认中国
  String areaCode = "cn";
  String areaDialCode = "86";

  ///限制手机号码位数，根据不同国家来
  int phoneMaxLenght = 11;

  LoginState() {
    ///Initialize variables
    seconds = 60;
    loginType = int.parse(UserStore.to.isPwdLogin);
  }
}
