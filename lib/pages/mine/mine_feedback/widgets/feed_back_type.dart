import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:getx_xiaopa/extension/widget_extension.dart';
import 'package:getx_xiaopa/utils/utils.dart';
import 'package:getx_xiaopa/values/values.dart';

class FeedBackType extends StatefulWidget {
  final List<String> data;
  final Function(int index) onTap;

  const FeedBackType({super.key, required this.data, required this.onTap});

  @override
  State<FeedBackType> createState() => _FeedBackTypeState();
}

class _FeedBackTypeState extends State<FeedBackType> {
  int _index = 0;

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: 1.sw,
      child: Wrap(
        spacing: 10.w,
        runSpacing: 12.h,
        children: List.generate(widget.data.length, (index) {
          return Container(
            width: 96.w,
            height: 36.h,
            decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(18.r),
                color: index == _index
                    ? ColorConfig.searchTextColor
                    : const Color.fromRGBO(242, 242, 242, 1)),
            child: Center(
              child: Text(
                widget.data[index],
                textAlign: TextAlign.center,
                style: StyleConfig.otherStyle(
                  color: index == _index
                      ? Get.find<ThemeColorController>().gTextColor
                      : ColorConfig.searchTextColor,
                  fontSize: 12.sp,
                  fontWeight: FontWeight.w400,
                ),
              ),
            ),
          ).inkWell(() {
            setState(() {
              _index = index;
            });
            widget.onTap(_index);
          });
        }),
      ),
    );
  }
}
