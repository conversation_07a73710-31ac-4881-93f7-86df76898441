import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:getx_xiaopa/base/base.dart';
import 'package:getx_xiaopa/extension/images.dart';
import 'package:getx_xiaopa/extension/widget_extension.dart';
import 'package:getx_xiaopa/pages/mine/mine_feedback/widgets/feed_back_type.dart';
import 'package:getx_xiaopa/r.dart';
import 'package:getx_xiaopa/utils/utils.dart';
import 'package:getx_xiaopa/values/values.dart';
import 'package:getx_xiaopa/widget/widget.dart';

import 'index.dart';

class MineFeedbackPage extends BaseCommonView {
  MineFeedbackPage({super.key});

  final logic = Get.put(MineFeedbackController());

  @override
  Color? get navColor => Colors.transparent;

  @override
  String? get navTitle => "mine_item_six".tr;

  @override
  TextStyle? get navTitleStyle =>
      StyleConfig.blackStyle(fontSize: 18, fontWeight: FontWeight.w600);

  @override
  double? get leftWidth => 35.w;

  @override
  Widget? get leftButton => Padding(
        padding: EdgeInsets.only(left: 15.w),
        child: Images(
          path: R.back_png,
          width: 20.w,
          height: 20.h,
          scale: 2.8,
        ).inkWell(() => Get.back()),
      );

  @override
  Color? get contentColor => ColorConfig.shopDetailBackGroundColor;

  @override
  bool get isResize => true;

  // 主视图
  Widget _buildView() {
    return Container(
      margin: EdgeInsets.only(top: 20.h, left: 15.w, right: 15.w),
      child: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              padding: EdgeInsets.only(left: 15.w, right: 15.w),
              width: 375.w,
              height: 48.h,
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(10.r),
              ),
              child: Row(
                children: [
                  Text(
                    "mine_feedback_custom".tr,
                    style: StyleConfig.otherStyle(
                        color: ColorConfig.searchTextColor,
                        fontWeight: FontWeight.w600),
                  ),
                  const Expanded(child: SizedBox()),
                  Text(
                    "mine_feedback_custom_btn".tr,
                    style: StyleConfig.otherStyle(
                      color: ColorConfig.authenticationTextColor,
                    ),
                  ),
                  SizedBox(width: 6.w),
                  Images(
                    path: R.mine_right_png,
                    width: 9.w,
                    height: 16.h,
                  )
                ],
              ),
            ).inkWell(() => logic.toWXCustomer()),
            SizedBox(height: 10.h),
            Container(
              padding: EdgeInsets.all(15.r),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(10.r),
                color: Colors.white,
              ),
              child: Column(
                children: [
                  Row(
                    children: [
                      Text(
                        'mine_feedback_type'.tr,
                        style: StyleConfig.otherStyle(
                          color: ColorConfig.searchTextColor,
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      Text(
                        '*',
                        style: StyleConfig.otherStyle(
                          color: const Color.fromRGBO(255, 80, 110, 1),
                          fontSize: 16,
                          fontWeight: FontWeight.w400,
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: 14.h),
                  FeedBackType(
                    data: logic.typeList,
                    onTap: (index) {
                      logic.cIndex = index;
                    },
                  ),
                  SizedBox(height: 30.h),
                  Row(
                    children: [
                      Text(
                        'mine_feedback_detail'.tr,
                        style: StyleConfig.otherStyle(
                          color: ColorConfig.searchTextColor,
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      Text(
                        '*',
                        style: StyleConfig.otherStyle(
                          color: const Color.fromRGBO(255, 80, 110, 1),
                          fontSize: 16,
                          fontWeight: FontWeight.w400,
                        ),
                      ),
                    ],
                  ),
                  Container(
                    margin: EdgeInsets.only(top: 10.h),
                    width: 1.sw,
                    height: 308.h,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(10.w),
                      color: const Color.fromRGBO(252, 252, 252, 1),
                      border: Border.all(
                        color: const Color.fromRGBO(239, 239, 239, 1),
                        width: 1,
                      ),
                    ),
                    child: Column(
                      children: [
                        Expanded(
                          child: AppTextField(
                            style: StyleConfig.otherStyle(
                              color: ColorConfig.searchTextColor,
                              fontSize: 16,
                              fontWeight: FontWeight.w400,
                            ),
                            controller: logic.textEditingController,
                            maxLines: null,
                            maxLength: 200,
                            keyboardType: TextInputType.text,
                            onChanged: (value) => logic.textMaxLenght(value),
                            decoration: InputDecoration(
                              border: InputBorder.none,
                              enabledBorder: InputBorder.none,
                              focusedBorder: InputBorder.none,
                              counterText: '',
                              contentPadding: EdgeInsets.symmetric(
                                  horizontal: 10.w, vertical: 10.h),
                              hintText: 'mine_feedback_hint_text'.tr,
                              hintStyle: StyleConfig.otherStyle(
                                color: ColorConfig.authenticationTextColor,
                                fontSize: 14,
                                fontWeight: FontWeight.w400,
                              ),
                            ),
                          ),
                        ),
                        Container(
                          margin: EdgeInsets.only(right: 10.w, bottom: 10.h),
                          width: 1.sw,
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.end,
                            children: [
                              Text(
                                "${logic.textLength}",
                                style: StyleConfig.otherStyle(
                                  fontSize: 12,
                                  color: ColorConfig.authenticationTextColor,
                                ),
                              ),
                              Text(
                                "/200",
                                style: StyleConfig.otherStyle(
                                  fontSize: 12,
                                  color: ColorConfig.searchTextColor,
                                ),
                              ),
                            ],
                          ),
                        )
                      ],
                    ),
                  ),
                ],
              ),
            ),
            Container(
              margin: EdgeInsets.only(
                  top: 24.h,
                  bottom: ScreenUtil().bottomBarHeight + 20.h,
                  left: 46.w,
                  right: 46.w),
              width: 283.w,
              height: 48.h,
              decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(24.r),
                  color: ColorConfig.searchTextColor),
              child: Center(
                child: Text(
                  "mine_feedback_btn".tr,
                  style: StyleConfig.otherStyle(
                    fontSize: 16.sp,
                    fontWeight: FontWeight.w600,
                    color: Get.find<ThemeColorController>().gTextColor,
                  ),
                ),
              ),
            ).inkWell(() => logic.confirmAction())
          ],
        ),
      ),
    );
  }

  @override
  Widget buildContent() {
    return GetBuilder<MineFeedbackController>(
      init: MineFeedbackController(),
      id: "mine_feedback",
      builder: (_) {
        return _buildView();
      },
    );
  }
}
