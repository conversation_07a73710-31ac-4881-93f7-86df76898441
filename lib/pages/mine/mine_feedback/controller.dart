import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:getx_xiaopa/extension/widget_extension.dart';
import 'package:getx_xiaopa/network/http_client.dart';
import 'package:getx_xiaopa/storage/storage.dart';
import 'package:getx_xiaopa/utils/utils.dart';

class MineFeedbackController extends GetxController {
  MineFeedbackController();

  TextEditingController textEditingController = TextEditingController();
  int cIndex = 0;
  List<String> typeList = [
    "mine_feedback_type_one".tr,
    "mine_feedback_type_two".tr,
    "mine_feedback_type_three".tr,
    "mine_feedback_type_four".tr
  ];

  int textLength = 0;

  _initData() {
    update(["mine_feedback"]);
  }

  @override
  void onReady() {
    super.onReady();
    _initData();
  }

  textMaxLenght(value) {
    textLength = value.length;
    update(["mine_feedback"]);
  }

  toWXCustomer() async {
    bool flag =
        await WxUtil.launchCustomerService(WxConfig.corpId, WxConfig.url);
    if (!flag) {
      CToast.showToast("mine_feedback_custom_tips".tr);
    }
  }

  confirmAction() {
    if (textEditingController.text.isEmpty ||
        textEditingController.text.length < 6) {
      CToast.showToast("mine_feedback_hint_text".tr);
      return;
    }
    CToast.showLoading();
    _feedbackCreate();
  }

  ///====================网络请求====================
  _feedbackCreate() async {
    var map = {"content": textEditingController.text, "type": cIndex + 1};
    final result = await http.feedbackCreate(map);
    if (result.code == 0) {
      CToast.showToast("mine_feedback_complete".trArgs([result.msg!]));
    } else {
      CToast.showToast(result.msg!);
    }
  }
}
