import 'dart:async';
import 'package:get/get.dart';
import 'package:getx_xiaopa/values/values.dart';

class MineCancelState {
  /// 默认说明页面
  bool isExplain = true;

  String explainStr = 'mine_cancel_explan_tips'.tr;

  bool isAgree = false;

  bool isCancel = false;

  var verifyStr = 'mine_cancel_verify'.trArgs(['60']);

  /// 是否已发送
  bool isSend = false;
  late Timer timer;
  late int seconds;
  var inkWellStyle = StyleConfig.blackStyle(fontSize: 14, opacity: 0.6);

  String verifyCode = "";

  String phone = '';

  MineCancelState() {
    seconds = 60;

    ///Initialize variables
  }
}
