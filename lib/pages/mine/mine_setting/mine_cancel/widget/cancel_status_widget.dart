import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:getx_xiaopa/extension/images.dart';
import 'package:getx_xiaopa/extension/widget_extension.dart';
import 'package:getx_xiaopa/r.dart';
import 'package:getx_xiaopa/routes/router.dart';
import 'package:getx_xiaopa/utils/utils.dart';
import 'package:getx_xiaopa/values/values.dart';

class CancelStatusWidget extends StatelessWidget {
  const CancelStatusWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      onPopInvokedWithResult: (canPop, result) {},
      child: Container(
        width: 1.sw,
        height: 1.sh,
        padding: EdgeInsets.only(top: 192.h),
        color: Colors.white,
        child: Column(
          children: [
            Images(
              path: R.mine_image_02_png,
              width: 248.w,
              height: 203.h,
            ),
            SizedBox(height: 8.h),
            Text(
              "mine_cancel_success".tr,
              style: StyleConfig.otherStyle(
                  color: ColorConfig.searchTextColor, fontSize: 14),
            ),
            SizedBox(height: 8.h),
            Text(
              "mine_cancel_success_tips".tr,
              textAlign: TextAlign.center,
              style: StyleConfig.otherStyle(
                  color: ColorConfig.shopDetailTextColor, fontSize: 14),
            ),
            SizedBox(height: 22.h),
            Container(
              width: 200.w,
              height: 48.h,
              decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(24.r),
                  color: const Color.fromRGBO(40, 39, 46, 1)),
              child: Center(
                child: Text(
                  "console_sound_record_compose".tr,
                  style: StyleConfig.otherStyle(
                      color: Get.find<ThemeColorController>().gTextColor,
                      fontWeight: FontWeight.w500),
                ),
              ),
            ).inkWell(() async {
              Get.offAllNamed(AppRoutes.LOGIN);
            })
          ],
        ),
      ),
    );
  }
}
