import 'dart:async';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:getx_xiaopa/extension/widget_extension.dart';
import 'package:getx_xiaopa/network/http_client.dart';
import 'package:getx_xiaopa/network/model/result.dart';
import 'package:getx_xiaopa/pages/mine/mine_setting/mine_cancel/widget/cancel_status_widget.dart';
import 'package:getx_xiaopa/storage/storage.dart';
import 'package:getx_xiaopa/utils/utils.dart';
import 'package:getx_xiaopa/values/values.dart';
import 'package:getx_xiaopa/widget/widget.dart';

import 'mine_cancel_state.dart';

class MineCancelLogic extends GetxController {
  final MineCancelState state = MineCancelState();

  @override
  void onInit() {
    state.phone = StrUtil.maskPhone(UserStore.to.userInfo.mobileNumber ?? "");
    update(["mine_cancel_view"]);
    super.onInit();
  }

  ///同意协议
  agreeAction(flag) {
    state.isAgree = flag;
    update(["mine_cancel_view"]);
  }

  /// 协议下一步
  explainAction() async {
    if (!state.isAgree) return;

    ///用户还绑定着设备，提醒去解绑才能注销
    if (UserStore.to.deviceList.isNotEmpty) {
      Get.dialog(CommonDialog(
        mTitle: "mine_cancel_error_title".tr,
        mContent: "mine_cancel_error_tips".tr,
        confirmAction: () {
          Get.back();
        },
      ));
      return;
    }
    state.isExplain = false;

    bool flag = await _sendPhoneVerify();
    if (flag) {
      _startTimer();
    }

    update(["mine_cancel_view"]);
  }

  ///获取验证码
  verifySend() async {
    if (state.isSend) return;
    bool flag = await _sendPhoneVerify();
    if (flag) {
      _startTimer();
    }
  }

  /// 启动倒计时的计时器。
  void _startTimer() {
    if (state.isSend) return;
    state.isSend = true;
    state.inkWellStyle = StyleConfig.blackStyle(fontSize: 14, opacity: 0.6);
    // 计时器（`Timer`）组件的定期（`periodic`）构造函数，创建一个新的重复计时器。
    state.timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (state.seconds == 0) {
        _cancelTimer();
        state.seconds = 60;
        state.isSend = false;
        state.inkWellStyle =
            StyleConfig.otherStyle(color: Colors.black, fontSize: 14);
        state.verifyStr = 'login_verify_resend'.tr;
        update(["mine_cancel_view"]);
        return;
      }
      state.seconds--;
      state.verifyStr = 'mine_cancel_verify'.trArgs(['${state.seconds}']);

      update(["mine_cancel_view"]);
    });
  }

  /// 取消定时器
  void _cancelTimer() {
    state.timer.cancel();
  }

  ///验证码发送改变
  verifyChange(String value) {
    if (value.length == 6) {
      state.isCancel = true;
    } else {
      state.isCancel = false;
    }
    update(["mine_cancel_view"]);
  }

  ///验证码输入完成
  verifyCompleted(value) {
    state.verifyCode = "$value";
  }

  /// 注销账号
  cancelAction() async {
    if (!state.isCancel) return;
    CToast.showLoading();
    var map = {"verifyCode": state.verifyCode};
    Result result = await http.cancelUserInfo(map);
    if (result.code == 0) {
      await UserStore.to.removeUserInfo();
      await UserStore.to.removePassword();
      await UserStore.to.removeAccount();
      await ConfigStore.to.removeWifiConfig();
      Get.to(() => const CancelStatusWidget());
    }
    CToast.showToast(result.msg!);
  }

  @override
  void onClose() {
    if (state.isSend) {
      _cancelTimer();
    }
    super.onClose();
  }

  ///===================网络请求================================
  Future<bool> _sendPhoneVerify() async {
    var map = {
      "mobile_area": UserStore.to.userInfo.mobileArea,
      "mobile": UserStore.to.userInfo.mobile,
      "template": "SMS_228215199",
      "scene": "confirm"
    };
    Result result = await http.sendPhoneVerify(map);
    CToast.showToast(result.msg!);
    if (result.code == 0) {
      return true;
    }
    return false;
  }
}
