import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:getx_xiaopa/base/base.dart';
import 'package:getx_xiaopa/extension/images.dart';
import 'package:getx_xiaopa/extension/widget_extension.dart';
import 'package:getx_xiaopa/r.dart';
import 'package:getx_xiaopa/utils/utils.dart';
import 'package:getx_xiaopa/values/values.dart';
import 'package:getx_xiaopa/widget/widget.dart';
import 'package:pin_code_fields/pin_code_fields.dart';

import 'mine_cancel_logic.dart';
import 'mine_cancel_state.dart';

class MineCancelPage extends BaseCommonView {
  MineCancelPage({super.key});

  final MineCancelLogic logic = Get.put(MineCancelLogic());
  final MineCancelState state = Get.find<MineCancelLogic>().state;

  @override
  Color? get navColor => Colors.transparent;

  @override
  String? get navTitle => "mine_cancel_title".tr;

  @override
  TextStyle? get navTitleStyle =>
      StyleConfig.blackStyle(fontSize: 18, fontWeight: FontWeight.w500);

  @override
  double? get leftWidth => 35.w;

  @override
  Widget? get leftButton => Padding(
        padding: EdgeInsets.only(left: 15.w),
        child: Images(
          path: R.back_png,
          width: 20.w,
          height: 20.h,
          scale: 2.8,
        ).inkWell(() => Get.back()),
      );

  @override
  Color? get contentColor => Colors.white;

  Widget _bodyWidget() {
    return state.isExplain
        ? Column(
            children: [
              Expanded(
                child: Container(
                  margin: EdgeInsets.only(top: 17.h, left: 20.w, right: 20.w),
                  child: SingleChildScrollView(
                    child: Text(
                      state.explainStr,
                      style: StyleConfig.blackStyle(fontSize: 14),
                    ),
                  ),
                ),
              ),
              Container(
                margin: EdgeInsets.only(left: 17.w, top: 12.h),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Container(
                      margin: EdgeInsets.only(top: 4.h),
                      child: CheckBoxRounded(
                        size: 16.sp,
                        checkedColor: Get.find<ThemeColorController>()
                            .getColor('textColor'),
                        checkedBgColor: ColorConfig.searchTextColor,
                        uncheckedBgColor: Colors.white,
                        borderColor: const Color.fromRGBO(215, 215, 215, 1),
                        onTap: (value) => logic.agreeAction(value),
                      ),
                    ),
                    SizedBox(width: 5.w),
                    SizedBox(
                      width: 320.w,
                      child: Text(
                        "mine_cancel_tips".tr,
                        style: StyleConfig.blackStyle(fontSize: 14),
                      ),
                    )
                  ],
                ),
              ),
              Container(
                margin: EdgeInsets.only(
                    top: 36.h, bottom: ScreenUtil().bottomBarHeight + 27.h),
                decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(24.r),
                    color: state.isAgree
                        ? const Color.fromRGBO(40, 39, 46, 1)
                        : const Color.fromRGBO(215, 215, 215, 1)),
                width: 283.w,
                height: 48.h,
                child: Center(
                  child: ThemeText(
                    dataStr: "console_paring_wifi_next".tr,
                    keyName: 'textColor',
                    flag: state.isAgree,
                    subColor: ColorConfig.authenticationTextColor,
                    fontWeight: FontWeight.w500,
                  ),
                ).inkWell(() => logic.explainAction()),
              )
            ],
          )
        : Container(
            margin: EdgeInsets.only(top: 17.h, left: 15.w, right: 15.w),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  "mine_cancel_send_verify_tips".tr,
                  style: StyleConfig.blackStyle(fontSize: 14),
                ),
                SizedBox(height: 15.h),
                Text("${"mine_cancel_verify_tips".tr} ${state.phone}",
                    style: StyleConfig.blackStyle(fontSize: 14)),
                Container(
                  margin: EdgeInsets.only(top: 40.h, left: 15.w, right: 15.w),
                  child: PinCodeTextField(
                    appContext: Get.context!,
                    length: 6,
                    textStyle: StyleConfig.otherStyle(
                        color: const Color.fromRGBO(69, 47, 10, 1),
                        fontSize: 20),
                    obscureText: false,
                    animationType: AnimationType.fade,
                    keyboardType: TextInputType.number,
                    pinTheme: PinTheme(
                      shape: PinCodeFieldShape.box,
                      borderRadius: BorderRadius.circular(5),
                      fieldWidth: 40.w,
                      fieldHeight: 44.h,
                      inactiveBorderWidth: 0.r,
                      inactiveFillColor:
                          Get.find<ThemeColorController>().gCancelInactiveFill,
                      selectedFillColor:
                          Get.find<ThemeColorController>().gCancelSelectedFill,
                      selectedColor:
                          Get.find<ThemeColorController>().gCancelSelected,
                      activeFillColor:
                          Get.find<ThemeColorController>().gCancelActiveFill,
                      activeColor:
                          Get.find<ThemeColorController>().gCancelSelected,
                      selectedBorderWidth: 1.r,
                      activeBorderWidth: 1.r,
                    ),
                    animationDuration: const Duration(milliseconds: 300),
                    enableActiveFill: true,
                    onCompleted: (v) => logic.verifyCompleted(v),
                    onChanged: (v) => logic.verifyChange(v),
                  ),
                ),
                Container(
                  margin: EdgeInsets.only(top: 27.h),
                  alignment: Alignment.center,
                  child: Text(
                    state.verifyStr,
                    style: state.inkWellStyle,
                  ),
                ).inkWell(() => logic.verifySend()),
                const Expanded(child: SizedBox()),
                Container(
                  margin: EdgeInsets.only(
                      bottom: ScreenUtil().bottomBarHeight + 27.h,
                      left: 46.w,
                      right: 46.w),
                  height: 48.h,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(24.r),
                    color: state.isCancel
                        ? const Color.fromRGBO(40, 39, 46, 1)
                        : const Color.fromRGBO(215, 215, 215, 1),
                  ),
                  child: Center(
                    child: ThemeText(
                      dataStr: "mine_cancel_btn".tr,
                      keyName: 'textColor',
                      flag: state.isCancel,
                      subColor: ColorConfig.authenticationTextColor,
                      fontWeight: FontWeight.w500,
                    ),
                  ).inkWell(() => logic.cancelAction()),
                ),
              ],
            ),
          );
  }

  @override
  Widget buildContent() {
    return GetBuilder<MineCancelLogic>(
      id: "mine_cancel_view",
      builder: (_) {
        return _bodyWidget();
      },
    );
  }
}
