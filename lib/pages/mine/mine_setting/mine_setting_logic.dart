import 'dart:async';
import 'dart:io';

import 'package:get/get.dart';
import 'package:getx_xiaopa/extension/widget_extension.dart';
import 'package:getx_xiaopa/network/http_client.dart';
import 'package:getx_xiaopa/network/model/request_body.dart';
import 'package:getx_xiaopa/network/model/result.dart';
import 'package:getx_xiaopa/pages/mine/mine_setting/widget/api_bottom_sheet.dart';
import 'package:getx_xiaopa/pages/mine/widget/update_dialog.dart';
import 'package:getx_xiaopa/routes/router.dart';
import 'package:getx_xiaopa/storage/storage.dart';
import 'package:getx_xiaopa/utils/utils.dart';
import 'package:getx_xiaopa/widget/widget.dart';
import 'package:restart_app/restart_app.dart';

import 'mine_setting_state.dart';

class MineSettingLogic extends GetxController {
  final MineSettingState state = MineSettingState();

  ///接口地址切换
  apiChangeAction() {
    state.clickIndex++;

    // 重置计时器（比如 1.5 秒内算连续）
    state.resetTimer?.cancel();
    state.resetTimer = Timer(const Duration(seconds: 2), () {
      state.clickIndex = 0;
    });
    if (state.clickIndex >= 5) {
      state.resetTimer?.cancel();
      state.clickIndex = 0;
      _showBottomSheet();
    }
  }

  _showBottomSheet() {
    Get.bottomSheet(
        ApiBottomSheet(
          product: isProduction ? 0 : 1,
          apiAction: (index) {
            bool flag = index == 0 ? true : false;

            Get.dialog(CommonDialog(
                mContent: "mine_setting_reset_app".tr,
                confirmAction: () async {
                  CommonStore.to.setIsProduction(flag);
                  await SocketUtil.getInstance()?.disconnect();
                  if (Platform.isIOS) {
                    if (!await PermissionUtil().isNotification()) {
                      exit(0);
                    }
                  }
                  Restart.restartApp(
                      notificationTitle: "mine_setting_reset_title".tr,
                      notificationBody: "mine_setting_reset_tips".tr);
                }));
          },
        ),
        isDismissible: false);
  }

  ///语言
  languageAction() {
    Get.toNamed(AppRoutes.MINE_LANGUAGE);
  }

  /// 注销账号
  cancelAction() {
    Get.toNamed(AppRoutes.MINE_CANCEL);
  }

  ///账号安全
  pwdAction() {
    Get.toNamed(AppRoutes.MINE_PWD);
  }

  ///更新版本
  updateAction() {
    _versionAction();
  }

  /// 退出登录
  loginOutAction() {
    Get.dialog(CommonDialog(
      mTitle: "mine_setting_login_out_title".tr,
      mContent: "mine_setting_login_out_content".tr,
      mConfirm: "mine_setting_login_out_btn".tr,
      confirmAction: () async {
        Get.back();
        CToast.showLoading();
        Result result = await http.loginOut({});
        if (result.code == 0) {
          await UserStore.to.removeUserInfo();
          Get.offAndToNamed(AppRoutes.LOGIN);
        } else {
          CToast.showToast(result.msg!);
        }
      },
    ));
  }

  ///===================网络请求================================
  updateUserInfo({String? image, String? nick}) async {
    CToast.showLoading();
    var map = {
      "avatar": image,
      "nickname": nick,
    };
    Result result = await http.updateUserInfo(map);
    if (result.code == 0) {
      await UserStore.to.getUserInfo();
    }
    update(["mine_setting_view"]);
    CToast.showToast(result.msg!);
  }

  _versionAction() async {
    var map = RequestBody(filters: [
      Filter(name: 'type', expr: '=', value: Platform.isAndroid ? '1' : '2'),
      Filter(expr: "=", name: "is_publish", value: 1)
    ]).toJson();
    Result result = await http.getVersion(map);
    if (result.code == 0) {
      state.vModel = result.data;
      ConfigStore.to.downUrl = state.vModel.url ?? '';
      if (state.vModel.code != UserStore.to.version) {
        if (state.vModel.code!.isEmpty) return;
        List<String> sCode = state.vModel.code.toString().split('.');
        List<String> lCode = UserStore.to.version.split('.');

        if (int.parse(sCode[0]) > int.parse(lCode[0]) ||
            int.parse(sCode[1]) > int.parse(lCode[1])) {
          ///强制更新
          _updateDialog(false);
          return;
        } else if (int.parse(sCode[0]) == int.parse(lCode[0]) &&
            int.parse(sCode[1]) == int.parse(lCode[1])) {
          if (int.parse(sCode[2]) > int.parse(lCode[2])) {
            ///自愿更新
            _updateDialog(true);
          } else {
            CToast.showToast("mine_setting_update_tips".tr);
          }
        } else {
          CToast.showToast("mine_setting_update_tips".tr);
        }
      } else {
        CToast.showToast("mine_setting_update_tips".tr);
      }
    }
  }

  _updateDialog(isClose) {
    Get.dialog(UpdateDialog(
        upDateTitle: state.vModel.name,
        upDateContent: state.vModel.logs,
        mUpdateUrl: state.vModel.url,
        iosUrl: state.vModel.redirect,
        mUpdateType: state.vModel.type,
        isClose: isClose));
  }
}
