import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:getx_xiaopa/extension/widget_extension.dart';
import 'package:getx_xiaopa/utils/utils.dart';
import 'package:getx_xiaopa/values/values.dart';
import 'package:getx_xiaopa/widget/widget.dart';

class ApiBottomSheet extends StatefulWidget {
  /// 默认选中
  final int product;
  final Function(int) apiAction;

  const ApiBottomSheet(
      {super.key, required this.product, required this.apiAction});

  @override
  State<ApiBottomSheet> createState() => _ApiBottomSheetState();
}

class _ApiBottomSheetState extends State<ApiBottomSheet> {
  /// 0=正式  1=测试
  int _sign = 0;

  @override
  void initState() {
    super.initState();
    _sign = widget.product;
  }

  Widget _genderItem({String model = ''}) {
    return Container(
      margin: EdgeInsets.only(left: 15.w, right: 15.w),
      height: 58.h,
      child: Column(
        children: [
          Expanded(
            child: Row(
              children: [
                Text(model,
                    style: StyleConfig.otherStyle(
                        color: ColorConfig.searchTextColor)),
                const Expanded(child: SizedBox()),
                CheckBoxRounded(
                  isGroup: true,
                  value: model == "正式" ? 0 : 1,
                  groupValue: _sign,
                  checkedColor:
                      Get.find<ThemeColorController>().getColor('textColor'),
                  checkedBgColor: ColorConfig.searchTextColor,
                  uncheckedBgColor: Colors.white,
                  borderColor: const Color.fromRGBO(215, 215, 215, 1),
                  onGroupTap: (sign) {
                    setState(() {
                      _sign = sign!;
                    });
                  },
                )
              ],
            ),
          ),
          Container(
            width: 345.w,
            height: 1.h,
            color: const Color.fromRGBO(233, 232, 233, 1),
          )
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 1.sw,
      height: 280.h,
      decoration: BoxDecoration(
          borderRadius: BorderRadius.only(
              topLeft: Radius.circular(20.r), topRight: Radius.circular(20.r)),
          color: Colors.white),
      child: Column(
        children: [
          SizedBox(height: 20.h),
          Text(
            "修改接口模式",
            style: StyleConfig.otherStyle(
                color: ColorConfig.searchTextColor,
                fontSize: 20,
                fontWeight: FontWeight.w500),
          ),
          SizedBox(height: 20.h),
          Container(
            margin: EdgeInsets.only(left: 15.w, right: 15.w),
            child: Column(
              children: [
                _genderItem(model: "正式"),
                _genderItem(model: "测试"),
              ],
            ),
          ),
          Container(
            margin: EdgeInsets.only(top: 28.h),
            child: Row(
              children: [
                Container(
                  margin: EdgeInsets.only(left: 20.w),
                  width: 163.w,
                  height: 40.h,
                  decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(20.r),
                      border: Border.all(
                          width: 1.r,
                          color: const Color.fromRGBO(120, 120, 120, 1))),
                  child: Center(
                    child: Text(
                      '取消',
                      style: StyleConfig.otherStyle(
                          color: ColorConfig.searchTextColor),
                    ),
                  ),
                ).inkWell(() => Get.back()),
                const Expanded(child: SizedBox()),
                Container(
                  margin: EdgeInsets.only(right: 20.w),
                  width: 163.w,
                  height: 40.h,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(20.r),
                    color: ColorConfig.searchTextColor,
                  ),
                  child: const Center(
                    child: ThemeText(
                      dataStr: "保存",
                      keyName: 'textColor',
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ).inkWell(() {
                  Get.back();
                  widget.apiAction(_sign);
                }),
              ],
            ),
          )
        ],
      ),
    );
  }
}
