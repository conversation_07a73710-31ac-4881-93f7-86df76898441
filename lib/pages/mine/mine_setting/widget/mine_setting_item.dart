import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:getx_xiaopa/extension/images.dart';
import 'package:getx_xiaopa/extension/widget_extension.dart';
import 'package:getx_xiaopa/r.dart';

class MineSettingItem extends StatelessWidget {
  final Widget titleWidget;
  final Widget? subWidget;
  final bool isShowArrow;
  final double? mHeight;
  final Function() onClick;

  const MineSettingItem({
    super.key,
    required this.titleWidget,
    this.subWidget,
    this.isShowArrow = true,
    this.mHeight = 78,
    required this.onClick,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.only(left: 15.w, right: 15.w),
      width: 345.w,
      height: mHeight?.h,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12.r),
        color: Colors.white,
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Expanded(
            child: Container(
              margin: EdgeInsets.only(left: 5.w, right: 5.w),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  titleWidget,
                  const Expanded(child: SizedBox()),
                  Container(
                    margin: EdgeInsets.only(right: 10.w),
                    color: Colors.transparent,
                    child: subWidget,
                  ),
                  Offstage(
                    offstage: !isShowArrow,
                    child: Images(
                      path: R.mine_right_png,
                      width: 9.w,
                      height: 16.h,
                    ),
                  ),
                ],
              ),
            ),
          ),
          Container(
            width: 345.w,
            height: 1.h,
            color: const Color.fromRGBO(233, 232, 233, 1),
          )
        ],
      ),
    ).inkWell(() => onClick());
  }
}
