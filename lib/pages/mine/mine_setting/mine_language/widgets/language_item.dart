import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:getx_xiaopa/extension/widget_extension.dart';
import 'package:getx_xiaopa/values/values.dart';
import 'package:getx_xiaopa/widget/widget.dart';

class LanguageItem extends StatefulWidget {
  final int cIndex;
  final List<LanguageStruct> structList;
  final Function(int) onTap;

  const LanguageItem(
      {super.key,
      required this.cIndex,
      required this.onTap,
      required this.structList});

  @override
  State<LanguageItem> createState() => _LanguageItemState();
}

class _LanguageItemState extends State<LanguageItem> {
  int _cIndex = 0;

  @override
  void initState() {
    _cIndex = widget.cIndex;
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: List.generate(
        widget.structList.length,
        (index) {
          bool flag = _cIndex == index;
          return Container(
            padding: EdgeInsets.only(left: 10.w, right: 10.w),
            width: 345.w,
            height: 62.h,
            color: Colors.white,
            child: Column(
              children: [
                Expanded(
                  child: Container(
                    padding: EdgeInsets.only(left: 6.w, right: 6.w),
                    child: Row(
                      children: [
                        Text(
                          widget.structList[index].name,
                          style: StyleConfig.otherStyle(
                              color: ColorConfig.searchTextColor,
                              fontWeight: FontWeight.w600),
                        ),
                        const Expanded(child: SizedBox()),
                        CheckBoxRounded(
                          isChecked: flag,
                          isGroup: true,
                          value: index,
                          groupValue: _cIndex,
                          checkedBgColor: ColorConfig.searchTextColor,
                          uncheckedBgColor: Colors.white,
                          borderColor: const Color.fromRGBO(215, 215, 215, 1),
                          onGroupTap: (value) {
                            setState(() {
                              _cIndex = value!;
                            });
                            widget.onTap.call(value!);
                          },
                        ),
                      ],
                    ),
                  ),
                ),
                Offstage(
                  offstage: widget.structList.length - 1 <= index,
                  child: Container(
                    width: 325.w,
                    height: 1.h,
                    color: const Color.fromRGBO(233, 232, 233, 1),
                  ),
                )
              ],
            ),
          ).inkWell(() {
            setState(() {
              _cIndex = index;
            });
            widget.onTap.call(_cIndex);
          });
        },
      ),
    );
  }
}

class LanguageStruct {
  String name;
  String code;
  String country;

  LanguageStruct({this.name = "", this.code = "", this.country = ""});
}
