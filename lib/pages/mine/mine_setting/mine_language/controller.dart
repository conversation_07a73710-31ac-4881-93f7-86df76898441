import 'dart:io';
import 'dart:ui';
import 'package:get/get.dart';
import 'package:getx_xiaopa/extension/widget_extension.dart';
import 'package:getx_xiaopa/network/http_client.dart';
import 'package:getx_xiaopa/network/model/result.dart';
import 'package:getx_xiaopa/pages/mine/mine_setting/mine_language/widgets/language_item.dart';
import 'package:getx_xiaopa/storage/storage.dart';
import 'package:getx_xiaopa/utils/utils.dart';
import 'package:getx_xiaopa/widget/widget.dart';
import 'package:restart_app/restart_app.dart';

class MineLanguageController extends GetxController {
  MineLanguageController();

  ///当前使用的语言
  int cIndex = 0;

  final List _languages = [
    {"name": "简体中文", "code": "zh", "country": "CN"},
    {"name": "English", "code": "en", "country": "US"},
  ];

  List<LanguageStruct> languages = [];

  @override
  void onReady() {
    super.onReady();

    for (var i = 0; i < _languages.length; i++) {
      LanguageStruct struct = LanguageStruct();
      struct.name = _languages[i]["name"];
      struct.code = _languages[i]["code"];
      struct.country = _languages[i]["country"];
      if (ConfigStore.to.locale.languageCode == struct.code) {
        cIndex = i;
      }
      languages.add(struct);
    }

    update(["mine_language"]);
  }

  changeLanguage(int index) {
    if (index == cIndex) return;
    Get.dialog(
      CommonDialog(
          mContent: "mine_setting_reset_app".tr,
          confirmAction: () async {
            _updateUserLanguage(_languages[index]["code"], index);
          }),
    );
  }

  ///=================网络请求========================
  _updateUserLanguage(String lang, int index) async {
    var map = {"lang": lang};
    Result result = await http.updateUserInfo(map);
    if (result.code == 0) {
      CToast.showLoading();
      Locale value =
          Locale(_languages[index]["code"], _languages[index]["country"]);
      await ConfigStore.to.onLocaleUpdate(value);
      await SocketUtil.getInstance()?.disconnect();
      Future.delayed(const Duration(seconds: 1), () async {
        CToast.dismiss();
        if (Platform.isIOS) {
          if (!await PermissionUtil().isNotification()) {
            exit(0);
          }
        }
        Restart.restartApp(
            notificationTitle: "mine_setting_reset_title".tr,
            notificationBody: "mine_setting_reset_tips".tr);
      });
    }
  }
}
