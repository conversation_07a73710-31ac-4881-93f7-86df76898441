import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:getx_xiaopa/base/base.dart';
import 'package:getx_xiaopa/extension/images.dart';
import 'package:getx_xiaopa/extension/widget_extension.dart';
import 'package:getx_xiaopa/pages/mine/mine_setting/mine_language/widgets/language_item.dart';
import 'package:getx_xiaopa/r.dart';
import 'package:getx_xiaopa/values/style.dart';

import 'index.dart';

class MineLanguagePage extends BaseCommonView<MineLanguageController> {
  MineLanguagePage({super.key});

  @override
  Color? get navColor => Colors.transparent;

  @override
  String? get navTitle => "mine_setting_language".tr;

  @override
  TextStyle? get navTitleStyle =>
      StyleConfig.blackStyle(fontSize: 18, fontWeight: FontWeight.w600);

  @override
  double? get leftWidth => 35.w;

  @override
  Color? get contentColor => const Color.fromRGBO(249, 249, 249, 1);

  @override
  Widget? get leftButton => Padding(
        padding: EdgeInsets.only(left: 15.w),
        child: Images(
          path: R.back_png,
          width: 20.w,
          height: 20.h,
          scale: 2.8,
        ).inkWell(() => Get.back()),
      );

  // 主视图
  Widget _buildView() {
    return Container(
      margin: EdgeInsets.only(left: 15.w, right: 15.w),
      padding: EdgeInsets.all(0.r),
      width: 375.w,
      height: 125.h,
      constraints: BoxConstraints(minHeight: 125.h),
      clipBehavior: Clip.hardEdge,
      decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(10.r),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.06),
              // 阴影颜色和透明度
              spreadRadius: 0,
              // 阴影扩散范围
              blurRadius: 10,
              // 阴影模糊程度
              offset: const Offset(0, 2),
              // 阴影偏移量（水平，垂直）
            )
          ]),
      child: controller.languages.isEmpty
          ? const SizedBox()
          : LanguageItem(
              cIndex: controller.cIndex,
              structList: controller.languages,
              onTap: (cIndex) => controller.changeLanguage(cIndex),
            ),
    );
  }

  @override
  Widget buildContent() {
    return GetBuilder<MineLanguageController>(
      init: MineLanguageController(),
      id: "mine_language",
      builder: (_) {
        return _buildView();
      },
    );
  }
}
