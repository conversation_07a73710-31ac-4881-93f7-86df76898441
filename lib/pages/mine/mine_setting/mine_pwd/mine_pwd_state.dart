import 'dart:async';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:getx_xiaopa/utils/utils.dart';
import 'package:getx_xiaopa/values/values.dart';

class MinePwdState {
  final TextEditingController verifyController = TextEditingController();
  final TextEditingController pwdController = TextEditingController();
  final TextEditingController pwd2Controller = TextEditingController();

  var verifyStr = 'login_verify_send'.tr;

  /// 是否已发送
  bool isSend = false;
  Timer? timer;
  late int seconds;
  var inkWellStyle = StyleConfig.otherStyle(
      color: Get.find<ThemeColorController>().gTextColor, fontSize: 12);

  ///是否可以点击缺点
  bool isComfire = false;

  String phone = '';

  MinePwdState() {
    ///Initialize variables
    seconds = 60;
  }
}
