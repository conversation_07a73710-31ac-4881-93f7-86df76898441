import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:getx_xiaopa/base/base.dart';
import 'package:getx_xiaopa/extension/images.dart';
import 'package:getx_xiaopa/extension/widget_extension.dart';
import 'package:getx_xiaopa/r.dart';
import 'package:getx_xiaopa/values/values.dart';
import 'package:getx_xiaopa/widget/widget.dart';

import 'mine_pwd_logic.dart';

class MinePwdPage extends BaseCommonView {
  MinePwdPage({super.key});

  final logic = Get.put(MinePwdLogic());
  final state = Get.find<MinePwdLogic>().state;

  @override
  Color? get navColor => Colors.transparent;

  @override
  String? get navTitle => "mine_pwd_title".tr;

  @override
  TextStyle? get navTitleStyle =>
      StyleConfig.blackStyle(fontSize: 18, fontWeight: FontWeight.w500);

  @override
  double? get leftWidth => 40.w;

  @override
  Widget? get leftButton => Padding(
        padding: EdgeInsets.only(left: 15.w),
        child: Images(
          path: R.back_png,
          width: 20.w,
          height: 20.h,
          scale: 2.8,
        ).inkWell(() => Get.back()),
      );

  @override
  Color? get contentColor => Colors.white;

  @override
  Widget buildContent() {
    return GetBuilder<MinePwdLogic>(
      id: "mine_pwd_view",
      builder: (_) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            SizedBox(height: 20.h),
            Container(
              padding: EdgeInsets.only(left: 15.w, right: 15.w),
              width: 1.sw,
              height: 59.h,
              child: Column(
                children: [
                  Expanded(
                    child: Container(
                      margin: EdgeInsets.only(left: 5.w, right: 5.w),
                      child: Row(
                        children: [
                          Text(
                            "mine_pwd_phone".tr,
                            style: StyleConfig.otherStyle(
                                fontSize: 14.sp,
                                color: ColorConfig.authenticationTextColor),
                          ),
                          SizedBox(width: 35.w),
                          Expanded(
                            child: Text(
                              state.phone,
                              style: StyleConfig.otherStyle(
                                  color: ColorConfig.searchTextColor),
                            ),
                          )
                        ],
                      ),
                    ),
                  ),
                  Container(
                    width: 345.w,
                    height: 1.h,
                    color: const Color.fromRGBO(233, 232, 233, 1),
                  )
                ],
              ),
            ),
            Container(
              padding: EdgeInsets.only(left: 15.w, right: 15.w),
              width: 1.sw,
              height: 59.h,
              child: Column(
                children: [
                  Expanded(
                    child: Container(
                      margin: EdgeInsets.only(left: 5.w, right: 5.w),
                      child: Row(
                        children: [
                          Text(
                            "mine_pwd_new".tr,
                            style: StyleConfig.otherStyle(
                                fontSize: 14.sp,
                                color: ColorConfig.authenticationTextColor),
                          ),
                          SizedBox(width: 35.w),
                          Expanded(
                            child: AppTextField(
                              controller: state.pwdController,
                              style: StyleConfig.otherStyle(
                                  color: ColorConfig.searchTextColor,
                                  fontSize: 14.sp,
                                  fontWeight: FontWeight.w400,
                                  height: 1),
                              keyboardType: TextInputType.text,
                              maxLength: 16,
                              decoration: InputDecoration(
                                border: InputBorder.none,
                                enabledBorder: InputBorder.none,
                                focusedBorder: InputBorder.none,
                                counterText: '',
                                hintText: 'mine_pwd_new_hint_text'.tr,
                                hintStyle: StyleConfig.otherStyle(
                                    color: ColorConfig.shopDetailTextColor,
                                    fontSize: 14.sp),
                              ),
                              onChanged: (value) => logic.isComfire(),
                            ),
                          )
                        ],
                      ),
                    ),
                  ),
                  Container(
                    width: 345.w,
                    height: 1.h,
                    color: const Color.fromRGBO(233, 232, 233, 1),
                  )
                ],
              ),
            ),
            Container(
              padding: EdgeInsets.only(left: 15.w, right: 15.w),
              width: 1.sw,
              height: 59.h,
              child: Column(
                children: [
                  Expanded(
                    child: Container(
                      margin: EdgeInsets.only(left: 5.w, right: 5.w),
                      child: Row(
                        children: [
                          Text(
                            "mine_pwd_confirm".tr,
                            style: StyleConfig.otherStyle(
                                fontSize: 14.sp,
                                color: ColorConfig.authenticationTextColor),
                          ),
                          SizedBox(width: 20.w),
                          Expanded(
                            child: AppTextField(
                              controller: state.pwd2Controller,
                              style: StyleConfig.otherStyle(
                                  color: ColorConfig.searchTextColor,
                                  fontSize: 14.sp,
                                  fontWeight: FontWeight.w400,
                                  height: 1),
                              keyboardType: TextInputType.text,
                              maxLength: 16,
                              decoration: InputDecoration(
                                border: InputBorder.none,
                                enabledBorder: InputBorder.none,
                                focusedBorder: InputBorder.none,
                                counterText: '',
                                hintText: 'mine_pwd_confirm_hint_text'.tr,
                                hintStyle: StyleConfig.otherStyle(
                                    color: ColorConfig.shopDetailTextColor,
                                    fontSize: 14.sp),
                              ),
                              onChanged: (value) => logic.isComfire(),
                            ),
                          )
                        ],
                      ),
                    ),
                  ),
                  Container(
                    width: 345.w,
                    height: 1.h,
                    color: const Color.fromRGBO(233, 232, 233, 1),
                  )
                ],
              ),
            ),
            Container(
              margin: EdgeInsets.only(top: 10.h, left: 20.w),
              child: Text(
                "mine_pwd_new_tips".tr,
                style: StyleConfig.otherStyle(
                    color: ColorConfig.shopDetailTextColor, fontSize: 12),
              ),
            ),
            Container(
                padding: EdgeInsets.only(left: 15.w, right: 15.w),
                width: 1.sw,
                height: 59.h,
                child: Column(
                  children: [
                    Expanded(
                      child: Container(
                        margin: EdgeInsets.only(left: 5.w, right: 5.w),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Text(
                              "mine_pwd_verify".tr,
                              style: StyleConfig.otherStyle(
                                  fontSize: 14.sp,
                                  color: ColorConfig.authenticationTextColor),
                            ),
                            SizedBox(width: 10.w),
                            Expanded(
                              child: AppTextField(
                                controller: state.verifyController,
                                //控制键盘的功能键 指enter键，比如此处设置为next时，enter键
                                //显示的文字内容为 下一步
                                textInputAction: TextInputAction.next,
                                style: StyleConfig.otherStyle(
                                  color: ColorConfig.searchTextColor,
                                  height: 1,
                                  fontSize: 14.sp,
                                ),
                                keyboardType: TextInputType.number,
                                inputFormatters: [
                                  FilteringTextInputFormatter.digitsOnly
                                ],
                                decoration: InputDecoration(
                                  border: InputBorder.none,
                                  enabledBorder: InputBorder.none,
                                  focusedBorder: InputBorder.none,
                                  counterText: '',
                                  contentPadding: EdgeInsets.symmetric(
                                      horizontal: 5.w, vertical: 12.h),
                                  hintText: 'mine_pwd_verify_hint_text'.tr,
                                  hintStyle: StyleConfig.otherStyle(
                                    color: ColorConfig.shopDetailTextColor,
                                    height: 1,
                                    fontSize: 14.sp,
                                  ),
                                ),
                                onChanged: (value) => logic.isComfire(),
                              ),
                            ),
                            AnimatedPress(
                              isAuto: false,
                              seconds: 100,
                              scaleEnd: 0.95,
                              child: Container(
                                margin: EdgeInsets.only(right: 5.w),
                                width: 80.w,
                                height: 25.h,
                                decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(13.r),
                                    color: state.isSend
                                        ? const Color.fromRGBO(238, 238, 238, 1)
                                        : ColorConfig.searchTextColor),
                                child: Center(
                                  child: Text(
                                    state.verifyStr,
                                    style: state.inkWellStyle,
                                  ),
                                ),
                              ).inkWell(() => logic.sendPhoneVerifyAction()),
                            ),
                          ],
                        ),
                      ),
                    ),
                    Container(
                      width: 345.w,
                      height: 1.h,
                      color: const Color.fromRGBO(233, 232, 233, 1),
                    )
                  ],
                )),
            const Expanded(child: SizedBox()),
            Container(
              margin: EdgeInsets.only(
                  bottom: ScreenUtil().bottomBarHeight + 50.h,
                  left: 46.w,
                  right: 46.w),
              width: 283.w,
              height: 48.h,
              decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(24.r),
                  color: state.isComfire
                      ? const Color.fromRGBO(40, 39, 46, 1)
                      : const Color.fromRGBO(215, 215, 215, 1)),
              child: Center(
                child: ThemeText(
                  dataStr: "confirm".tr,
                  keyName: 'textColor',
                  flag: state.isComfire,
                  subColor: ColorConfig.authenticationTextColor,
                  fontWeight: FontWeight.w500,
                ),
              ).inkWell(() => logic.updatePwd()),
            ),
          ],
        );
      },
    );
  }
}
