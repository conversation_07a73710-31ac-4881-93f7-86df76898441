import 'dart:async';

import 'package:get/get.dart';
import 'package:getx_xiaopa/extension/widget_extension.dart';
import 'package:getx_xiaopa/network/http_client.dart';
import 'package:getx_xiaopa/network/model/result.dart';
import 'package:getx_xiaopa/storage/storage.dart';
import 'package:getx_xiaopa/utils/utils.dart';
import 'package:getx_xiaopa/values/values.dart';

import 'mine_pwd_state.dart';

class MinePwdLogic extends GetxController {
  final MinePwdState state = MinePwdState();

  @override
  void onInit() {
    state.phone = StrUtil.maskPhone(UserStore.to.userInfo.mobileNumber ?? "");
    update(["mine_pwd_view"]);
    super.onInit();
  }

  /// 发送验证码
  sendPhoneVerifyAction() async {
    if (state.isSend) return;
    bool flag = await _sendPhoneVerify();
    if (flag) {
      state.isSend = true;
      _startTimer();
    }
  }

  isComfire() {
    if (state.verifyController.text.isNotEmpty &&
        state.pwdController.text.isNotEmpty &&
        state.pwd2Controller.text.isNotEmpty) {
      state.isComfire = true;
    } else {
      state.isComfire = false;
    }

    update(["mine_pwd_view"]);
  }

  /// 启动倒计时的计时器。
  void _startTimer() {
    if (state.seconds != 60) return;
    state.inkWellStyle = StyleConfig.otherStyle(
        color: ColorConfig.shopDetailTextColor, fontSize: 12);
    // 计时器（`Timer`）组件的定期（`periodic`）构造函数，创建一个新的重复计时器。
    state.timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (state.seconds == 0) {
        _cancelTimer();
        state.seconds = 60;
        state.isSend = false;
        state.inkWellStyle = StyleConfig.otherStyle(
            color: Get.find<ThemeColorController>().gTextColor, fontSize: 12);
        state.verifyStr = 'login_verify_resend'.tr;
        update(["mine_pwd_view"]);
        return;
      }
      state.seconds--;
      state.verifyStr = '${state.seconds}s';

      update(["mine_pwd_view"]);
    });
  }

  /// 取消定时器
  void _cancelTimer() {
    state.timer?.cancel();
  }

  ///===================网络请求================================
  Future<bool> _sendPhoneVerify() async {
    var map = {
      "mobile_area": UserStore.to.userInfo.mobileArea,
      "mobile": UserStore.to.userInfo.mobile,
      "template": "SMS_228215199",
      "scene": "confirm"
    };
    Result result = await http.sendPhoneVerify(map);
    CToast.showToast(result.msg!);
    if (result.code == 0) {
      return true;
    }
    return false;
  }

  updatePwd() async {
    if (!state.isComfire) return;

    if (state.verifyController.text.isEmpty) {
      CToast.showToast("mine_pwd_verify_hint_text".tr);
      return;
    }

    if (state.pwdController.text.length < 8 ||
        PatternUtil.isLetterAndNumber(state.pwdController.text) == false) {
      CToast.showToast("mine_pwd_new_tips".tr);
      return;
    }

    if (state.pwdController.text != state.pwd2Controller.text) {
      CToast.showToast("mine_pwd_error_tips".tr);
      return;
    }

    CToast.showLoading();

    var map = {
      "mobile": UserStore.to.userInfo.mobile,
      "verifyCode": state.verifyController.text,
      "template": "SMS_228215199",
      "password": state.pwdController.text,
      "passwordConfirm": state.pwd2Controller.text
    };
    Result result = await http.retrieveUserInfo(map);

    if (result.code == 0) {
      UserStore.to.getUserInfo();
      Get.back();
    }
    CToast.showToast(result.msg!);
  }

  @override
  void onClose() {
    if (state.isSend) {
      _cancelTimer();
    }
    super.onClose();
  }
}
