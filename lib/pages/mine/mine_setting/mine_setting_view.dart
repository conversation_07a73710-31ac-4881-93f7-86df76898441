import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:getx_xiaopa/base/base.dart';
import 'package:getx_xiaopa/extension/images.dart';
import 'package:getx_xiaopa/extension/widget_extension.dart';
import 'package:getx_xiaopa/pages/mine/mine_setting/widget/mine_setting_item.dart';
import 'package:getx_xiaopa/r.dart';
import 'package:getx_xiaopa/storage/storage.dart';
import 'package:getx_xiaopa/values/values.dart';
import 'package:getx_xiaopa/widget/widget.dart';

import 'mine_setting_logic.dart';

class MineSettingPage extends BaseCommonView {
  MineSettingPage({super.key});

  final logic = Get.put(MineSettingLogic());
  final state = Get.find<MineSettingLogic>().state;

  @override
  Color? get navColor => Colors.transparent;

  @override
  String? get navTitle => "mine_item_two".tr;

  @override
  VoidCallback? get navTitleCall => logic.apiChangeAction();

  @override
  TextStyle? get navTitleStyle =>
      StyleConfig.blackStyle(fontSize: 18, fontWeight: FontWeight.w500);

  @override
  double? get leftWidth => 40.w;

  @override
  Widget? get leftButton => Padding(
        padding: EdgeInsets.only(left: 15.w),
        child: Images(
          path: R.back_png,
          width: 20.w,
          height: 20.h,
          scale: 2.8,
        ).inkWell(() => Get.back()),
      );

  @override
  Color? get contentColor => Colors.white;

  @override
  Widget buildContent() {
    return GetBuilder<MineSettingLogic>(
      id: "mine_setting_view",
      builder: (_) {
        return Container(
          margin: EdgeInsets.only(left: 15.w, right: 15.w, top: 15.h),
          child: Column(
            children: [
              MineSettingItem(
                  titleWidget: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        "mine_setting_language".tr,
                        style: StyleConfig.otherStyle(
                            color: ColorConfig.searchTextColor,
                            fontWeight: FontWeight.w500),
                      ),
                      Text(
                        "mine_setting_language_tips".tr,
                        style: StyleConfig.otherStyle(
                            color: ColorConfig.shopDetailTextColor,
                            fontSize: 14),
                      )
                    ],
                  ),
                  subWidget: ThemeText(
                      dataStr: "mine_setting_change".tr, keyName: 'iconColor'),
                  onClick: () => logic.languageAction()),
              MineSettingItem(
                  titleWidget: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        "mine_setting_cancel".tr,
                        style: StyleConfig.otherStyle(
                            color: ColorConfig.searchTextColor,
                            fontWeight: FontWeight.w500),
                      ),
                      SizedBox(
                        width: 200.w,
                        child: Text(
                          "mine_setting_cancel_tips".tr,
                          maxLines: 2,
                          style: StyleConfig.otherStyle(
                              color: ColorConfig.shopDetailTextColor,
                              fontSize: 14),
                        ),
                      )
                    ],
                  ),
                  subWidget: ThemeText(
                      dataStr: "mine_setting_cancel".tr, keyName: 'iconColor'),
                  onClick: () => logic.cancelAction()),
              MineSettingItem(
                  titleWidget: Text(
                    "mine_setting_pwd".tr,
                    style: StyleConfig.otherStyle(
                        color: ColorConfig.searchTextColor,
                        fontWeight: FontWeight.w500),
                  ),
                  onClick: () => logic.pwdAction()),
              MineSettingItem(
                  titleWidget: Text(
                    "mine_setting_version".tr,
                    style: StyleConfig.otherStyle(
                        color: ColorConfig.searchTextColor,
                        fontWeight: FontWeight.w500),
                  ),
                  subWidget: Text(
                    "v${UserStore.to.version}",
                    style: StyleConfig.otherStyle(
                        color: ColorConfig.shopDetailTextColor),
                  ),
                  onClick: () => logic.updateAction()),
              const Expanded(child: SizedBox()),
              Container(
                margin: EdgeInsets.only(
                    bottom: ScreenUtil().bottomBarHeight + 50.h),
                width: 283.w,
                height: 48.h,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(24.r),
                  color: const Color.fromRGBO(40, 39, 46, 1),
                ),
                child: Center(
                  child: ThemeText(
                    dataStr: "mine_setting_logout".tr,
                    keyName: 'textColor',
                    fontWeight: FontWeight.w500,
                  ),
                ).inkWell(() => logic.loginOutAction()),
              ),
            ],
          ),
        );
      },
    );
  }
}
