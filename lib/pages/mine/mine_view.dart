import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:getx_xiaopa/base/base.dart';
import 'package:getx_xiaopa/extension/images.dart';
import 'package:getx_xiaopa/extension/widget_extension.dart';
import 'package:getx_xiaopa/network/http_client.dart';
import 'package:getx_xiaopa/pages/mine/widget/mine_item.dart';
import 'package:getx_xiaopa/r.dart';
import 'package:getx_xiaopa/routes/router.dart';
import 'package:getx_xiaopa/storage/storage.dart';
import 'package:getx_xiaopa/values/values.dart';
import 'package:getx_xiaopa/widget/widget.dart';

import 'mine_logic.dart';

class MinePage extends BaseCommonView {
  MinePage({super.key});

  final logic = Get.put(MineLogic());
  final state = Get.find<MineLogic>().state;

  @override
  bool? get isHiddenNav => true;

  @override
  Widget buildContent() {
    return GetBuilder<MineLogic>(
      id: "mine_view",
      builder: (_) => Container(
        constraints: const BoxConstraints.expand(),
        color: const Color.fromRGBO(249, 249, 249, 1),
        child: Stack(
          children: [
            Positioned(
              width: 1.sw,
              height: 346.h,
              child: Container(
                decoration: const BoxDecoration(
                  image: DecorationImage(
                    image: AssetImage(R.mine_bg_png),
                    fit: BoxFit.fill,
                  ),
                ),
                child: Stack(
                  children: [
                    Positioned(
                      top: 78.h,
                      left: 25.w,
                      child: Container(
                        width: 60.r,
                        height: 60.r,
                        decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(30.r)),
                        clipBehavior: Clip.hardEdge,
                        child: state.avatar.isNotEmpty
                            ? Images(
                                path: fileUrl(state.avatar),
                                boxFit: BoxFit.cover,
                              )
                            : const ThemeImagePath(
                                fileName: 'mine_avator.png', fit: BoxFit.cover),
                      ),
                    ),
                    Positioned(
                      top: 82.h,
                      left: 95.w,
                      child: Row(
                        children: [
                          Container(
                            constraints: BoxConstraints(maxWidth: 200.w),
                            height: 25.h,
                            child: Text(
                              state.name,
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                              style: StyleConfig.otherStyle(
                                  color: const Color.fromRGBO(69, 47, 10, 1),
                                  fontWeight: FontWeight.w700,
                                  fontSize: 18),
                            ),
                          ),
                          SizedBox(width: 5.w),
                          Container(
                            margin: EdgeInsets.only(top: 4.h),
                            child: Images(
                              path: R.mine_right_png,
                              width: 20.r,
                              height: 16.r,
                            ),
                          )
                        ],
                      ).inkWell(() => logic.toPage(AppRoutes.MIME_INFO)),
                    ),
                    Positioned(
                      top: 114.h,
                      left: 95.w,
                      width: 99.w,
                      height: 20.h,
                      child: Container(
                        padding: EdgeInsets.only(left: 5.w),
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(4.r),
                          color: Colors.white,
                        ),
                        child: Text(
                          state.phone,
                          style: StyleConfig.otherStyle(
                              color: const Color.fromRGBO(102, 102, 102, 1),
                              fontSize: 12),
                        ),
                      ),
                    ),
                    Positioned(
                      top: 136.h,
                      width: 375.w,
                      height: 107.w,
                      child: ThemeContainerImage(
                        fileName:
                            "mine_item_bg_${ConfigStore.to.getLocaleCode()}.png",
                        padding: EdgeInsets.only(top: 45.h, left: 31.w),
                        child: Row(
                          children: [
                            ThemeText(
                              dataStr: 'mine_one'.tr,
                              keyName: 'floatingTextColor',
                              fontSize: 14,
                              fontWeight: FontWeight.w500,
                            ),
                            ThemeText(
                                dataStr: state.licenseExpireDate,
                                keyName: 'floatingTextColor',
                                fontSize: 14,
                                fontWeight: FontWeight.w700),
                          ],
                        ),
                      ),
                    ),
                    Positioned(
                      top: 240.h,
                      left: 15.w,
                      right: 15.w,
                      height: 77.h,
                      child: Container(
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.only(
                            bottomLeft: Radius.circular(10.r),
                            bottomRight: Radius.circular(10.r),
                          ),
                          color: Colors.white,
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withValues(alpha: 0.06),
                              // 阴影颜色和透明度
                              spreadRadius: 0,
                              // 阴影扩散范围
                              blurRadius: 10,
                              // 阴影模糊程度
                              offset: const Offset(0, -1),
                              // 阴影偏移量（水平，垂直）
                            )
                          ],
                        ),
                        clipBehavior: Clip.hardEdge,
                        child: Row(
                          children: [
                            Container(
                              padding: EdgeInsets.only(left: 10.w, right: 20.w),
                              width: 342.w,
                              height: 77.h,
                              child: Row(
                                children: [
                                  ThemeImagePath(
                                    fileName: 'mine_image_001.png',
                                    imgWidget: 40.r,
                                    imgHeight: 40.r,
                                  ),
                                  SizedBox(width: 4.w),
                                  Container(
                                    margin: EdgeInsets.only(top: 2.h),
                                    child: ThemeImagePath(
                                      fileName:
                                          'mine_image_003_${ConfigStore.to.getLocaleCode()}.png',
                                      imgWidget: 92.w,
                                      imgHeight: 20.h,
                                      fit: BoxFit.fill,
                                    ),
                                  ),
                                  Expanded(
                                    child: Row(
                                      mainAxisAlignment: MainAxisAlignment.end,
                                      children: [
                                        Container(
                                          margin: EdgeInsets.only(right: 5.w),
                                          child: ThemeText(
                                            dataStr: "${UserStore.to.balance}",
                                            keyName: 'floatingTextColor',
                                            fontSize: 20,
                                            fontWeight: FontWeight.w700,
                                          ),
                                        ),
                                        Container(
                                          margin: EdgeInsets.only(top: 5.h),
                                          child: Text(
                                            "mine_two".tr,
                                            style: StyleConfig.otherStyle(
                                                color: ColorConfig
                                                    .shopDetailTextColor,
                                                fontSize: 12,
                                                fontWeight: FontWeight.w600),
                                          ),
                                        )
                                      ],
                                    ),
                                  ),
                                ],
                              ),
                            ).inkWell(() => logic.toRechargePage()),
                          ],
                        ),
                      ),
                    )
                  ],
                ),
              ),
            ),
            Positioned(
              top: 330.h,
              left: 15.w,
              right: 15.w,
              child: Container(
                decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(10.r),
                    color: Colors.white,
                    boxShadow: [
                      BoxShadow(
                        // 阴影颜色和透明度
                        color: Colors.black.withValues(alpha: 0.03),
                        // 阴影扩散范围
                        spreadRadius: 0.1,
                        // 阴影模糊程度
                        blurRadius: 10,
                        // 阴影偏移量（水平，垂直）
                        offset: const Offset(0, 2),
                      )
                    ]),
                child: Column(
                  children: [
                    MineItem(
                        icon: "mine_xiaopa.png",
                        title: "mine_item_one".tr,
                        onClick: () => logic.toPage(AppRoutes.MINE_DEVICE)),
                    MineItem(
                        icon: "mine_setting.png",
                        title: "mine_item_two".tr,
                        onClick: () => logic.toPage(AppRoutes.MIME_SETTING)),
                    MineItem(
                        icon: "mine_user_protocol.png",
                        title: "mine_item_three".tr,
                        onClick: () => logic
                            .toAgreementPage(ConfigStore.to.userAgreement)),
                    MineItem(
                        icon: "mine_privacy_protocol.png",
                        title: "mine_item_four".tr,
                        onClick: () => logic
                            .toAgreementPage(ConfigStore.to.privacyPolicy)),
                    MineItem(
                        icon: "mine_explain.png",
                        title: "mine_item_five".tr,
                        onClick: () => logic
                            .toAgreementPage(ConfigStore.to.usageInstructions)),
                    MineItem(
                        icon: "mine_feedback.png",
                        title: "mine_item_six".tr,
                        isShowLine: false,
                        onClick: () => logic.toFeedbackPage()),
                  ],
                ),
              ),
            )
          ],
        ),
      ),
    );
  }
}
