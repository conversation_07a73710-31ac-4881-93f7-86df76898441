///
/// Code generated by jsonToDartModel https://ashamp.github.io/jsonToDartModel/
///
class VersionModel {
  String? id;
  String? createdAt;
  String? updatedAt;
  String? deletedAt;
  String? name;

  ///1=安卓 2=ios
  int? type;
  String? code;
  String? logs;
  String? url;
  String? redirect;

  ///1=发布 2=未发布
  int? isPublish;

  VersionModel(
      {this.id,
      this.createdAt,
      this.updatedAt,
      this.deletedAt,
      this.name,
      this.type,
      this.code,
      this.logs,
      this.url,
      this.redirect,
      this.isPublish});
  VersionModel.fromJson(Map<String, dynamic> json) {
    id = json['id']?.toString();
    createdAt = json['created_at']?.toString();
    updatedAt = json['updated_at']?.toString();
    deletedAt = json['deleted_at']?.toString();
    name = json['name']?.toString();
    type = json['type']?.toInt();
    code = json['code']?.toString();
    logs = json['logs']?.toString();
    url = json['url']?.toString();
    redirect = json['redirect']?.toString();
    isPublish = json['is_publish']?.toInt();
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['id'] = id;
    data['created_at'] = createdAt;
    data['updated_at'] = updatedAt;
    data['deleted_at'] = deletedAt;
    data['name'] = name;
    data['type'] = type;
    data['code'] = code;
    data['logs'] = logs;
    data['url'] = url;
    data['redirect'] = redirect;
    data['is_publish'] = isPublish;
    return data;
  }
}
