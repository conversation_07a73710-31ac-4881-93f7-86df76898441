///
/// Code generated by jsonToDartModel https://ashamp.github.io/jsonToDartModel/
///
class TaskSkinUserSkinModel {
/*
{
  "id": "1",
  "created_at": "0001-01-01T00:00:00Z",
  "updated_at": "0001-01-01T00:00:00Z",
  "deleted_at": null,
  "picture": "",
  "contour": "",
  "name": "小粑",
  "unlock_no": 1
} 
*/

  String? id;
  String? createdAt;
  String? updatedAt;
  String? deletedAt;
  String? picture;
  String? contour;
  String? name;
  int? unlockNo;

  TaskSkinUserSkinModel({
    this.id,
    this.createdAt,
    this.updatedAt,
    this.deletedAt,
    this.picture,
    this.contour,
    this.name,
    this.unlockNo,
  });
  TaskSkinUserSkinModel.fromJson(Map<String, dynamic> json) {
    id = json['id']?.toString();
    createdAt = json['created_at']?.toString();
    updatedAt = json['updated_at']?.toString();
    deletedAt = json['deleted_at']?.toString();
    picture = json['picture']?.toString();
    contour = json['contour']?.toString();
    name = json['name']?.toString();
    unlockNo = json['unlock_no']?.toInt();
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['id'] = id;
    data['created_at'] = createdAt;
    data['updated_at'] = updatedAt;
    data['deleted_at'] = deletedAt;
    data['picture'] = picture;
    data['contour'] = contour;
    data['name'] = name;
    data['unlock_no'] = unlockNo;
    return data;
  }
}

class TaskSkinUserModel {
/*
{
  "id": "4817895805681665",
  "created_at": "2025-05-24T10:12:58.529+08:00",
  "updated_at": "2025-05-24T10:12:58.529+08:00",
  "deleted_at": null,
  "user_id": "4470670919467008",
  "device_id": "4163364096835585",
  "skin_id": "1",
  "is_default": 0,
  "skin": {
    "id": "1",
    "created_at": "0001-01-01T00:00:00Z",
    "updated_at": "0001-01-01T00:00:00Z",
    "deleted_at": null,
    "picture": "",
    "contour": "",
    "name": "小粑",
    "unlock_no": 1
  }
} 
*/

  String? id;
  String? createdAt;
  String? updatedAt;
  String? deletedAt;
  String? userId;
  String? deviceId;
  String? skinId;
  int? isDefault;
  TaskSkinUserSkinModel? skin;

  TaskSkinUserModel({
    this.id,
    this.createdAt,
    this.updatedAt,
    this.deletedAt,
    this.userId,
    this.deviceId,
    this.skinId,
    this.isDefault,
    this.skin,
  });
  TaskSkinUserModel.fromJson(Map<String, dynamic> json) {
    id = json['id']?.toString();
    createdAt = json['created_at']?.toString();
    updatedAt = json['updated_at']?.toString();
    deletedAt = json['deleted_at']?.toString();
    userId = json['user_id']?.toString();
    deviceId = json['device_id']?.toString();
    skinId = json['skin_id']?.toString();
    isDefault = json['is_default']?.toInt();
    skin = (json['skin'] != null)
        ? TaskSkinUserSkinModel.fromJson(json['skin'])
        : null;
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['id'] = id;
    data['created_at'] = createdAt;
    data['updated_at'] = updatedAt;
    data['deleted_at'] = deletedAt;
    data['user_id'] = userId;
    data['device_id'] = deviceId;
    data['skin_id'] = skinId;
    data['is_default'] = isDefault;
    if (skin != null) {
      data['skin'] = skin!.toJson();
    }
    return data;
  }
}

class TaskSkinUserResult {
/*
{
  "count": 1,
  "pageSize": 10,
  "pageNum": 1,
  "data": [
    {
      "id": "4817895805681665",
      "created_at": "2025-05-24T10:12:58.529+08:00",
      "updated_at": "2025-05-24T10:12:58.529+08:00",
      "deleted_at": null,
      "user_id": "4470670919467008",
      "device_id": "4163364096835585",
      "skin_id": "1",
      "is_default": 0,
      "skin": {
        "id": "1",
        "created_at": "0001-01-01T00:00:00Z",
        "updated_at": "0001-01-01T00:00:00Z",
        "deleted_at": null,
        "picture": "",
        "contour": "",
        "name": "小粑",
        "unlock_no": 1
      }
    }
  ]
} 
*/

  int? count;
  int? pageSize;
  int? pageNum;
  List<TaskSkinUserModel?>? data;

  TaskSkinUserResult({
    this.count,
    this.pageSize,
    this.pageNum,
    this.data,
  });
  TaskSkinUserResult.fromJson(Map<String, dynamic> json) {
    count = json['count']?.toInt();
    pageSize = json['pageSize']?.toInt();
    pageNum = json['pageNum']?.toInt();
    if (json['data'] != null) {
      final v = json['data'];
      final arr0 = <TaskSkinUserModel>[];
      v.forEach((v) {
        arr0.add(TaskSkinUserModel.fromJson(v));
      });
      data = arr0;
    }
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['count'] = count;
    data['pageSize'] = pageSize;
    data['pageNum'] = pageNum;
    if (this.data != null) {
      final v = this.data;
      final arr0 = [];
      for (var v in v!) {
        arr0.add(v!.toJson());
      }
      data['data'] = arr0;
    }
    return data;
  }
}
