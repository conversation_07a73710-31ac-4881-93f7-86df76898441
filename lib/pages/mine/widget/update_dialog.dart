import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:getx_xiaopa/extension/images.dart';
import 'package:getx_xiaopa/extension/widget_extension.dart';
import 'package:getx_xiaopa/network/http_client.dart';
import 'package:getx_xiaopa/r.dart';
import 'package:getx_xiaopa/utils/utils.dart';
import 'package:getx_xiaopa/values/values.dart';
import 'package:getx_xiaopa/widget/theme_text.dart';
import 'package:percent_indicator/circular_percent_indicator.dart';
import 'package:url_launcher/url_launcher.dart';

class UpdateDialog extends StatefulWidget {
  final String? upDateTitle;
  final String? upDateContent;
  final String? mUpdateUrl;
  final String? iosUrl;
  final int? mUpdateType;
  final bool isClose;

  const UpdateDialog({
    super.key,
    this.upDateTitle,
    this.upDateContent,
    required this.isClose,
    this.mUpdateUrl,
    this.mUpdateType,
    this.iosUrl,
  });

  @override
  State<UpdateDialog> createState() => _UpdateDialogState();
}

class _UpdateDialogState extends State<UpdateDialog> {
  late var _downing = false;
  late var _progress = 0;

  String _taskId = '';

  bool _close = true;

  @override
  void initState() {
    _close = widget.isClose;
    DownloadUtil.addListener("update_version", (id, status, progress) {
      if (_taskId == id) {
        if (status == 2) {
          setState(() {
            _progress = progress;
          });
        }
        if (status == 3) {
          DownloadUtil.open(taskId: id);

          ///打开更新文件 2s后结束app进程
          Future.delayed(const Duration(seconds: 2), () {
            SystemNavigator.pop();
          });
        }
      }
    });
    super.initState();
  }

  @override
  void dispose() {
    DownloadUtil.removeListener("update_version");
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      onPopInvokedWithResult: (didPop, result) {
        if (didPop) return;
        if (_close) {
          Get.back();
        }
      },
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            SizedBox(
              width: 317.w,
              height: 366.h,
              child: Stack(
                clipBehavior: Clip.none,
                children: [
                  Container(
                    constraints: const BoxConstraints.expand(),
                    child: Images(
                      path: R.update_bg_png,
                      boxFit: BoxFit.fill,
                    ),
                  ),
                  Positioned(
                    left: 35.w,
                    top: 84.h,
                    child: Text(
                      "mine_update_title".tr,
                      style: StyleConfig.otherStyle(
                          color: ColorConfig.searchTextColor,
                          fontSize: 22,
                          fontWeight: FontWeight.w700),
                    ),
                  ),
                  Positioned(
                    left: 39.w,
                    top: 115.h,
                    child: Text(
                      widget.upDateTitle ?? "",
                      style: StyleConfig.otherStyle(
                          color: ColorConfig.searchTextColor,
                          fontSize: 20,
                          fontWeight: FontWeight.w600),
                    ),
                  ),
                  Positioned(
                    left: 39.w,
                    top: 150.h,
                    width: 200.w,
                    height: 120.h,
                    child: SingleChildScrollView(
                      child: Text(
                        widget.upDateContent ?? "",
                        style: StyleConfig.otherStyle(
                            color: ColorConfig.authenticationTextColor,
                            fontSize: 14),
                      ),
                    ),
                  ),
                  Positioned(
                      top: 295.h,
                      left: 58.w,
                      child: Offstage(
                        offstage: _downing,
                        child: Container(
                          width: 200.w,
                          height: 48.h,
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(24.r),
                            color: const Color.fromRGBO(40, 39, 46, 1),
                          ),
                          child: Center(
                            child: ThemeText(
                              dataStr: "mine_update_btn".tr,
                              keyName: 'textColor',
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ).inkWell(() async {
                          if (widget.mUpdateType == 1) {
                            ///获取需要的权限
                            PermissionUtil.storage(Get.context!,
                                action: () async {
                              setState(() {
                                _downing = true;
                                _close = false;
                              });

                              ///安卓更新
                              var file = await FileStore.to.getDownDirectory();
                              await FileStore.to.deleteFile("$file/xiaopa.apk");
                              _taskId = await DownloadUtil.startTask(
                                  fileUrl(widget.mUpdateUrl ?? ''), file,
                                  fileName: "xiaopa.apk");
                            });
                          } else {
                            ///ios更新
                            if (widget.iosUrl != null) {
                              await launchUrl(Uri.parse(widget.iosUrl!));
                            }
                          }
                        }),
                      )),
                  Positioned(
                    top: 295.h,
                    left: 40.w,
                    child: Offstage(
                      offstage: !_downing,
                      child: SizedBox(
                        width: 230.w,
                        height: 48.h,
                        child: CircularPercentIndicator(
                          radius: 20.w,
                          lineWidth: 4.0,
                          percent: _progress / 100,
                          center: Text(
                            "$_progress",
                            style: StyleConfig.blackStyle(
                              fontSize: 10,
                              fontWeight: FontWeight.w400,
                            ),
                          ),
                          progressColor: ColorConfig.mainColor,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
            SizedBox(height: 18.h),
            _close
                ? Images(
                    path: R.update_close_png,
                    width: 34.r,
                    height: 34.r,
                  ).inkWell(() => Get.back())
                : Container()
          ],
        ),
      ),
    );
  }
}
