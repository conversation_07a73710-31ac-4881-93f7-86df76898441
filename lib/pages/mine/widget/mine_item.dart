import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:getx_xiaopa/extension/images.dart';
import 'package:getx_xiaopa/extension/widget_extension.dart';
import 'package:getx_xiaopa/r.dart';
import 'package:getx_xiaopa/values/values.dart';
import 'package:getx_xiaopa/widget/theme_image.dart';

class MineItem extends StatelessWidget {
  final String icon;
  final String title;
  final Widget? subWidget;
  final bool isShowArrow;
  final bool isShowLine;
  final double? mHeight;
  final Function() onClick;

  const MineItem({
    super.key,
    this.icon = '',
    required this.title,
    this.subWidget,
    this.isShowArrow = true,
    this.isShowLine = true,
    this.mHeight = 58,
    required this.onClick,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.only(left: 20.w, top: 20.h, right: 20.w),
      width: 345.w,
      height: mHeight?.h,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(10.r),
        color: Colors.white,
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              icon.isEmpty
                  ? const SizedBox()
                  : ThemeImagePath(
                      fileName: icon,
                      imgWidget: 20.r,
                      imgHeight: 20.r,
                    ),
              SizedBox(width: 10.w),
              Text(
                title,
                style: StyleConfig.otherStyle(
                    color: ColorConfig.searchTextColor, fontSize: 14),
              ),
              const Expanded(child: SizedBox()),
              Container(
                margin: EdgeInsets.only(right: !isShowArrow ? 27.w : 4.w),
                child: subWidget,
              ),
              Offstage(
                offstage: !isShowArrow,
                child: Images(
                  path: R.mine_right_png,
                  width: 11.w,
                  height: 20.h,
                ),
              ),
            ],
          ),
          const Expanded(child: SizedBox()),
          Offstage(
            offstage: !isShowLine,
            child: Container(
              width: 305.w,
              height: 1.h,
              color: const Color.fromRGBO(233, 232, 233, 1),
            ),
          )
        ],
      ),
    ).inkWell(() => onClick());
  }
}
