import 'dart:io';

import 'package:get/get.dart';
import 'package:getx_xiaopa/extension/widget_extension.dart';
import 'package:getx_xiaopa/network/http_client.dart';
import 'package:getx_xiaopa/network/model/result.dart';
import 'package:getx_xiaopa/pages/application/application_logic.dart';
import 'package:getx_xiaopa/pages/console/model/device_items_result.dart';
import 'package:getx_xiaopa/pages/mine/mine_device/index.dart';
import 'package:getx_xiaopa/routes/router.dart';
import 'package:getx_xiaopa/storage/storage.dart';
import 'package:getx_xiaopa/utils/utils.dart';
import 'package:getx_xiaopa/widget/widget.dart';
// ignore: depend_on_referenced_packages
import 'package:in_app_purchase_storekit/in_app_purchase_storekit.dart';

import 'mine_state.dart';

class MineLogic extends GetxController {
  final MineState state = MineState();

  @override
  void onInit() async {
    state.phone = StrUtil.maskPhone(UserStore.to.userInfo.mobileNumber ?? "");

    state.name = UserStore.to.userInfo.nickname ?? "";
    state.avatar = UserStore.to.userInfo.avatar ?? "";

    state.isBirthday = _isBirthday(DateTime.now());

    updateLicenseExpirDate();

    Future.delayed(const Duration(milliseconds: 300), () {
      if (Platform.isIOS) {
        ///初始化iOS内购
        BuyEngine();
        bus.on("iosInAppPurchase", _buyResult);
      }
    });

    super.onInit();
  }

  updateLicenseExpirDate() {
    state.licenseExpireDate = UserStore.to.deviceList.isNotEmpty
        ? TimeUtil.getSimpleDate(
            UserStore.to.deviceList[0].licenseExpireDate ?? "",
            isHMS: false)
        : "";
    update(["mine_view"]);
  }

  ///iOS内购回调
  _buyResult(res) async {
    if (res == null) return;
    String receipt = await ReceiptHelper.getReceipt(true);
    logD("下单后：$receipt");
    if (receipt.isEmpty) {
      CToast.showToast("mine_recharge_tips".tr);
      return;
    }

    _orderAppleChenck(res, receipt);
  }

  ///判断当天是否用户的生日
  bool _isBirthday(DateTime date) {
    if (UserStore.to.userInfo.birthday!.isEmpty) return false;
    DateTime date =
        DateTime.parse(UserStore.to.userInfo.birthday?.split(" ")[0] ?? "");
    DateTime now = DateTime.now();
    if (date.month == now.month && date.day == now.day) {
      return true;
    }
    return false;
  }

  toPage(String name) {
    if (name == AppRoutes.MINE_DEVICE) {
      if (UserStore.to.deviceList.isEmpty) {
        Get.find<ApplicationLogic>().tChange(0, isShow: true);
        return;
      }
      if (Get.isRegistered<MineDeviceController>()) {
        Get.find<MineDeviceController>().reloadData();
      } else {
        Get.put(MineDeviceController());
        Get.find<MineDeviceController>().reloadData();
      }
    }
    Get.toNamed(name)?.then((value) {
      state.phone = StrUtil.maskPhone(UserStore.to.userInfo.mobileNumber ?? "");
      state.name = UserStore.to.userInfo.nickname ?? "";
      state.avatar = UserStore.to.userInfo.avatar ?? "";
      update(["mine_view"]);
    });
  }

  toAgreementPage(String url) {
    Get.to(const WebView(), arguments: url);
  }

  toFeedbackPage() {
    Get.toNamed(AppRoutes.MINE_FEEDBACK);
  }

  toRechargePage() {
    bus.off("iosInAppPurchase");
    Get.toNamed(AppRoutes.RECHARGE)?.then((value) {
      update(["mine_view"]);
    });
  }

  addDeviceAction() {
    Get.find<ApplicationLogic>().tChange(0, isShow: true);
  }

  updateStatus(DeviceItemsModel model) {
    state.socketModel = model;
    update(["mine_view"]);
  }

  _orderAppleChenck(SK2PurchaseDetails sk, String receipt) async {
    var map = {
      "product_id": sk.productID,
      "transaction_id": sk.purchaseID,
      "receipt": receipt
    };
    Result result = await http.orderApple(map);

    if (result.code == 0) {
      BuyEngine().checkApplePayInfo(sk);

      refreshAccountItems();

      bus.off("iosInAppPurchase");
    }
  }

  refreshAccountItems() async {
    ///重新获取账户余额
    await UserStore.to.getAccountItems();

    update(["mine_view"]);
  }
}
