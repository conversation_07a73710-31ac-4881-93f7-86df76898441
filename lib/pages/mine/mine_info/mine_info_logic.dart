import 'dart:io';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:getx_xiaopa/extension/widget_extension.dart';
import 'package:getx_xiaopa/network/dio_util.dart';
import 'package:getx_xiaopa/network/http_client.dart';
import 'package:getx_xiaopa/network/model/result.dart';
import 'package:getx_xiaopa/pages/mine/mine_info/widget/gender_select_widget.dart';
import 'package:getx_xiaopa/pages/mine/mine_info/widget/nick_name_widget.dart';
import 'package:getx_xiaopa/routes/router.dart';
import 'package:getx_xiaopa/storage/storage.dart';
import 'package:getx_xiaopa/utils/utils.dart';

import 'package:getx_xiaopa/widget/widget.dart';
import 'package:image_cropper/image_cropper.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:wechat_assets_picker/wechat_assets_picker.dart';

import 'mine_info_state.dart';

class MineInfoLogic extends GetxController {
  final MineInfoState state = MineInfoState();
  @override
  void onInit() {
    super.onInit();

    // 预先请求权限，避免首次保存时的卡顿
    if (Platform.isIOS) {
      Permission.photos.request();
    }

    state.avatar = UserStore.to.userInfo.avatar ?? "";
    state.name = UserStore.to.userInfo.nickname ?? "";
    state.phone = StrUtil.maskPhone(UserStore.to.userInfo.mobileNumber ?? "");
    state.gender = UserStore.to.userInfo.gender == 0
        ? ""
        : UserStore.to.userInfo.gender == 1
            ? "mine_info_gender_man".tr
            : "mine_info_gender_woman".tr;

    state.birthday = UserStore.to.userInfo.birthday ?? "";
  }

  ///修改头像
  avatarAction() async {
    PermissionUtil.media(Get.context!, action: () async {
      PermissionUtil.storage(Get.context!, action: () async {
        List<AssetEntity>? result = await AssetPicker.pickAssets(
          Get.context!,
          pickerConfig: AssetPickerConfig(
            maxAssets: 1,
            requestType: RequestType.image,
            dragToSelect: false,
            specialPickerType: SpecialPickerType.noPreview,
            pathNameBuilder: (AssetPathEntity path) => switch (path) {
              final p when p.isAll => "diary_edit_photo_title".tr,
              _ => path.name,
            },
          ),
        );

        if (result != null) {
          if (result.isEmpty) return;
          File? img = await result.first.file;
          if (img != null) {
            _editImage(img.path);
          }
        }
      });
    });
  }

  _editImage(String imagePath) async {
    CroppedFile? croppedFile = await ImageCropper().cropImage(
      sourcePath: imagePath,
      uiSettings: [
        AndroidUiSettings(
            toolbarTitle: 'diary_edit_photo_edit'.tr,
            hideBottomControls: true,
            cropStyle: CropStyle.circle,
            cropGridColumnCount: 0,
            cropGridRowCount: 0,
            cropFrameColor: Colors.transparent),
        IOSUiSettings(
            title: 'diary_edit_photo_edit'.tr,
            cropStyle: CropStyle.circle,
            aspectRatioPickerButtonHidden: true),
      ],
    );
    if (croppedFile != null) {
      final result = await DioUtil.getInstance()
          .uploadFile(croppedFile.path, access: "user-avatar");
      if (result.isNotEmpty) {
        updateUserInfo(image: result);
      } else {
        CToast.showToast("mine_photo_update_fail".tr);
      }
    }
  }

  ///修改昵称
  nickNameAction() {
    Get.bottomSheet(NickNameWidget(
      saveAction: (value) {
        if (value.isNotEmpty) {
          updateUserInfo(nick: value);
        }
      },
    ));
  }

  ///性别修改
  genderAction() {
    Get.bottomSheet(GenderSelectWidget(
      gender: state.gender == "男" ? 1 : 0,
      genderAction: (value) {
        int temp = value == 0 ? 2 : 1;
        state.gender = value == 1
            ? "mine_info_gender_man".tr
            : "mine_info_gender_woman".tr;
        updateUserInfo(gender: temp);
      },
    ));
  }

  ///修改手机号码
  phoneAction() {
    Get.toNamed(AppRoutes.MINE_PHONE)?.then((value) {
      state.phone = StrUtil.maskPhone(UserStore.to.userInfo.mobileNumber ?? "");
      update(["mine_info_view"]);
    });
  }

  ///修改生日
  birthdayAction() {
    if (state.birthday.isNotEmpty) {
      CToast.showToast("mine_info_birthday_edit_tips".tr);
      return;
    }
    Get.dialog(
      CommonDialog(
        mTitle: "mine_info_birthday_dialog_title".tr,
        mContent: "mine_info_birthday_dialog_content".tr,
        mConfirm: "mine_info_birthday_dialog_btn".tr,
        confirmAction: () async {
          String date = await _datePickAction();
          if (date.isNotEmpty) {
            updateUserInfo(birthday: date);
          }
        },
      ),
    );
  }

  Future<String> _datePickAction() async {
    DateTime? selectDateTime = await showDatePicker(
      context: Get.context!,
      locale: ConfigStore.to.locale,
      initialDate: DateTime.now(),
      firstDate: DateTime(1980),
      lastDate: DateTime(2100),
    );
    if (selectDateTime != null) {
      return '${selectDateTime.year}-${selectDateTime.month.toString().padLeft(2, '0')}-${selectDateTime.day.toString().padLeft(2, '0')}';
    }
    return "";
  }

  ///===================网络请求================================
  updateUserInfo(
      {String? image, String? nick, String? birthday, int? gender}) async {
    CToast.showLoading();
    var map = {
      "avatar": image,
      "nickname": nick,
      "birthday": birthday,
      "gender": gender,
    };
    Result result = await http.updateUserInfo(map);
    if (result.code == 0) {
      await UserStore.to.getUserInfo();

      state.avatar = UserStore.to.userInfo.avatar ?? "";
      state.name = UserStore.to.userInfo.nickname ?? "";
      state.phone = StrUtil.maskPhone(UserStore.to.userInfo.mobileNumber ?? "");
      state.gender = UserStore.to.userInfo.gender == 1
          ? "mine_info_gender_man".tr
          : UserStore.to.userInfo.gender == 2
              ? "mine_info_gender_woman".tr
              : "";
      state.birthday = UserStore.to.userInfo.birthday ?? "";
    }
    update(["mine_info_view"]);
    CToast.showToast(result.msg!);
  }
}
