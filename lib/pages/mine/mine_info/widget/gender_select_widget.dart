import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:getx_xiaopa/extension/widget_extension.dart';
import 'package:getx_xiaopa/utils/utils.dart';
import 'package:getx_xiaopa/values/values.dart';
import 'package:getx_xiaopa/widget/widget.dart';

class GenderSelectWidget extends StatefulWidget {
  /// 默认选中
  final int gender;
  final Function(int) genderAction;

  const GenderSelectWidget(
      {super.key, required this.gender, required this.genderAction});

  @override
  State<GenderSelectWidget> createState() => _GenderSelectWidgetState();
}

class _GenderSelectWidgetState extends State<GenderSelectWidget> {
  /// 0=女性  1=男性
  int _sign = 0;

  @override
  void initState() {
    super.initState();
    _sign = widget.gender;
  }

  Widget _genderItem({String gender = ''}) {
    return Container(
      margin: EdgeInsets.only(left: 15.w, right: 15.w),
      height: 58.h,
      child: Column(
        children: [
          Expanded(
            child: Row(
              children: [
                Text(gender,
                    style: StyleConfig.otherStyle(
                        color: ColorConfig.searchTextColor)),
                const Expanded(child: SizedBox()),
                CheckBoxRounded(
                  isGroup: true,
                  value: gender == "mine_info_gender_woman".tr ? 0 : 1,
                  groupValue: _sign,
                  checkedColor:
                      Get.find<ThemeColorController>().getColor('textColor'),
                  checkedBgColor: ColorConfig.searchTextColor,
                  uncheckedBgColor: Colors.white,
                  borderColor: const Color.fromRGBO(215, 215, 215, 1),
                  onGroupTap: (sign) {
                    setState(() {
                      _sign = sign!;
                    });
                  },
                )
              ],
            ),
          ),
          Container(
            width: 345.w,
            height: 1.h,
            color: const Color.fromRGBO(233, 232, 233, 1),
          )
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 1.sw,
      height: 280.h,
      decoration: BoxDecoration(
          borderRadius: BorderRadius.only(
              topLeft: Radius.circular(20.r), topRight: Radius.circular(20.r)),
          color: Colors.white),
      child: Column(
        children: [
          SizedBox(height: 20.h),
          Text(
            "mine_info_gender_edit".tr,
            style: StyleConfig.otherStyle(
                color: ColorConfig.searchTextColor,
                fontSize: 20,
                fontWeight: FontWeight.w500),
          ),
          SizedBox(height: 20.h),
          Container(
            margin: EdgeInsets.only(left: 15.w, right: 15.w),
            child: Column(
              children: [
                _genderItem(gender: "mine_info_gender_woman".tr),
                _genderItem(gender: "mine_info_gender_man".tr),
              ],
            ),
          ),
          Container(
            margin: EdgeInsets.only(top: 28.h),
            child: Row(
              children: [
                Container(
                  margin: EdgeInsets.only(left: 20.w),
                  width: 163.w,
                  height: 40.h,
                  decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(20.r),
                      border: Border.all(
                          width: 1.r,
                          color: const Color.fromRGBO(120, 120, 120, 1))),
                  child: Center(
                    child: Text(
                      'cancel'.tr,
                      style: StyleConfig.otherStyle(
                          color: ColorConfig.searchTextColor),
                    ),
                  ),
                ).inkWell(() => Get.back()),
                const Expanded(child: SizedBox()),
                Container(
                  margin: EdgeInsets.only(right: 20.w),
                  width: 163.w,
                  height: 40.h,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(20.r),
                    color: ColorConfig.searchTextColor,
                  ),
                  child: Center(
                    child: ThemeText(
                      dataStr: "mine_info_save".tr,
                      keyName: 'textColor',
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ).inkWell(() {
                  Get.back();
                  widget.genderAction(_sign);
                }),
              ],
            ),
          )
        ],
      ),
    );
  }
}
