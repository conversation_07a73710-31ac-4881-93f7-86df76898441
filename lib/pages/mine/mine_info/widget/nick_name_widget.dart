import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:getx_xiaopa/extension/widget_extension.dart';
import 'package:getx_xiaopa/storage/storage.dart';
import 'package:getx_xiaopa/values/values.dart';
import 'package:getx_xiaopa/widget/widget.dart';

class NickNameWidget extends StatefulWidget {
  final Function(String) saveAction;

  const NickNameWidget({super.key, required this.saveAction});

  @override
  State<NickNameWidget> createState() => _NickNameWidgetState();
}

class _NickNameWidgetState extends State<NickNameWidget> {
  int _count = 0;
  int _maxCount = 8;
  final TextEditingController _nickNameController = TextEditingController();

  @override
  void initState() {
    _maxCount = ConfigStore.to.getLocaleCode() == "zh" ? 8 : 16;
    super.initState();
  }

  _textChangeAction(String value) {
    setState(() {
      _count = value.length;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 1.sw,
      height: 245.h,
      decoration: BoxDecoration(
          borderRadius: BorderRadius.only(
              topLeft: Radius.circular(20.r), topRight: Radius.circular(20.r)),
          color: Colors.white),
      child: Column(
        children: [
          SizedBox(height: 20.h),
          Text(
            "mine_info_name_edit".tr,
            style: StyleConfig.otherStyle(
                color: ColorConfig.searchTextColor,
                fontSize: 20,
                fontWeight: FontWeight.w500),
          ),
          SizedBox(height: 20.h),
          Container(
            width: 335.w,
            height: 77.h,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(10.r),
              color: const Color.fromRGBO(252, 252, 252, 1),
              border: Border.all(
                width: 1.r,
                color: const Color.fromRGBO(239, 239, 239, 1),
              ),
            ),
            child: Column(
              children: [
                Container(
                  margin: EdgeInsets.only(left: 16.w, right: 16.w, top: 8.h),
                  height: 24.h,
                  child: AppTextField(
                    controller: _nickNameController,
                    style: StyleConfig.otherStyle(
                        color: ColorConfig.searchTextColor,
                        fontSize: 14,
                        fontWeight: FontWeight.w400),
                    keyboardType: TextInputType.text,
                    onChanged: (value) => _textChangeAction(value),
                    maxLength: _maxCount,
                    decoration: InputDecoration(
                      border: InputBorder.none,
                      enabledBorder: InputBorder.none,
                      focusedBorder: InputBorder.none,
                      hintText: 'mine_info_name_hint_text'.tr,
                      counterText: '',
                      contentPadding: EdgeInsets.symmetric(vertical: 10.h),
                      hintStyle: StyleConfig.otherStyle(
                          color: ColorConfig.shopDetailTextColor, fontSize: 12),
                    ),
                  ),
                ),
                const Expanded(child: SizedBox()),
                Container(
                  margin: EdgeInsets.only(right: 16.w, bottom: 8.h),
                  alignment: Alignment.bottomRight,
                  child: Text(
                    '$_count/$_maxCount',
                    style: StyleConfig.otherStyle(
                        color: ColorConfig.shopDetailTextColor, fontSize: 12),
                  ),
                )
              ],
            ),
          ),
          Container(
            margin: EdgeInsets.only(top: 28.h),
            child: Row(
              children: [
                Container(
                  margin: EdgeInsets.only(left: 20.w),
                  width: 163.w,
                  height: 40.h,
                  decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(20.r),
                      border: Border.all(
                          width: 1.r,
                          color: const Color.fromRGBO(120, 120, 120, 1))),
                  child: Center(
                    child: Text(
                      'cancel'.tr,
                      style: StyleConfig.otherStyle(
                          color: ColorConfig.searchTextColor),
                    ),
                  ),
                ).inkWell(() => Get.back()),
                const Expanded(child: SizedBox()),
                Container(
                  margin: EdgeInsets.only(right: 20.w),
                  width: 163.w,
                  height: 40.h,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(20.r),
                    color: ColorConfig.searchTextColor,
                  ),
                  child: Center(
                    child: ThemeText(
                      dataStr: "mine_info_save".tr,
                      keyName: 'textColor',
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ).inkWell(() {
                  Get.back();
                  widget.saveAction(_nickNameController.text);
                }),
              ],
            ),
          )
        ],
      ),
    );
  }
}
