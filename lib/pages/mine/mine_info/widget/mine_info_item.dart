import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:getx_xiaopa/extension/images.dart';
import 'package:getx_xiaopa/extension/widget_extension.dart';
import 'package:getx_xiaopa/r.dart';
import 'package:getx_xiaopa/values/values.dart';

class MineInfoItem extends StatelessWidget {
  final String title;
  final Widget? subWidget;
  final double subRight;
  final Widget? isShowWidget;
  final bool isShowArrow;
  final bool isShowLine;
  final double? mHeight;
  final Function() onClick;

  const MineInfoItem({
    super.key,
    required this.title,
    this.subWidget,
    this.subRight = 10,
    this.isShowWidget,
    this.isShowArrow = true,
    this.isShowLine = true,
    this.mHeight = 59,
    required this.onClick,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 375.w,
      height: mHeight?.h,
      color: Colors.white,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Expanded(
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                SizedBox(width: 20.w),
                Text(
                  title,
                  style: StyleConfig.otherStyle(
                      color: ColorConfig.shopDetailTextColor, fontSize: 16),
                ),
                const Expanded(child: SizedBox()),
                Container(child: subWidget),
                SizedBox(width: subRight.w),
                Offstage(
                  offstage: !isShowArrow,
                  child: isShowWidget ??
                      Images(
                        path: R.mine_right_png,
                        width: 9.w,
                        height: 16.h,
                      ),
                ),
                SizedBox(width: 30.w)
              ],
            ),
          ),
          Offstage(
            offstage: !isShowLine,
            child: Container(
              width: 345.w,
              height: 1.h,
              color: const Color.fromRGBO(233, 232, 233, 1),
            ),
          ),
        ],
      ),
    ).inkWell(() => onClick());
  }
}
