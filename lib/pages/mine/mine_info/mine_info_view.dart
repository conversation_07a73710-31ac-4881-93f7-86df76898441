import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:getx_xiaopa/base/base.dart';
import 'package:getx_xiaopa/extension/images.dart';
import 'package:getx_xiaopa/extension/widget_extension.dart';
import 'package:getx_xiaopa/network/http_client.dart';
import 'package:getx_xiaopa/pages/mine/mine_info/widget/mine_info_item.dart';
import 'package:getx_xiaopa/r.dart';
import 'package:getx_xiaopa/values/values.dart';
import 'package:getx_xiaopa/widget/widget.dart';

import 'mine_info_logic.dart';

class MineInfoPage extends BaseCommonView {
  MineInfoPage({super.key});

  final logic = Get.put(MineInfoLogic());
  final state = Get.find<MineInfoLogic>().state;

  @override
  Color? get navColor => Colors.transparent;

  @override
  String? get navTitle => "mine_info_title".tr;

  @override
  TextStyle? get navTitleStyle =>
      StyleConfig.blackStyle(fontSize: 18, fontWeight: FontWeight.w500);

  @override
  double? get leftWidth => 35.w;

  @override
  Widget? get leftButton => Padding(
        padding: EdgeInsets.only(left: 15.w),
        child: Images(
          path: R.back_png,
          width: 20.w,
          height: 20.h,
          scale: 2.8,
        ).inkWell(() => Get.back()),
      );

  @override
  Color? get contentColor => Colors.white;

  @override
  Widget buildContent() {
    return GetBuilder<MineInfoLogic>(
      id: "mine_info_view",
      builder: (_) {
        return SafeArea(
          child: Column(
            children: [
              Container(
                margin: EdgeInsets.only(top: 20.h),
                alignment: Alignment.center,
                width: 100.w,
                height: 100.h,
                child: Stack(
                  children: [
                    Container(
                      width: 100.r,
                      height: 100.r,
                      decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(50.r)),
                      clipBehavior: Clip.hardEdge,
                      child: state.avatar.isNotEmpty
                          ? Images(
                              path: fileUrl(state.avatar),
                              boxFit: BoxFit.cover,
                            )
                          : const ThemeImagePath(
                              fileName: 'mine_avator.png',
                              fit: BoxFit.cover,
                            ),
                    ),
                    Positioned(
                      right: 5.w,
                      bottom: 5.h,
                      width: 24.r,
                      height: 24.r,
                      child: const ThemeImagePath(fileName: 'mine_camera.png'),
                    )
                  ],
                ),
              ).inkWell(() => logic.avatarAction()),
              SizedBox(height: 40.h),
              MineInfoItem(
                  title: "mine_info_name".tr,
                  subWidget: Text(
                    state.name,
                    style: StyleConfig.otherStyle(
                        color: ColorConfig.searchTextColor),
                  ),
                  onClick: () => logic.nickNameAction()),
              MineInfoItem(
                  title: "mine_info_gender".tr,
                  subWidget: Text(
                    state.gender,
                    style: StyleConfig.otherStyle(
                        color: ColorConfig.searchTextColor),
                  ),
                  onClick: () => logic.genderAction()),
              MineInfoItem(
                  title: "mine_info_phone".tr,
                  subWidget: Text(
                    state.phone,
                    style: StyleConfig.otherStyle(
                        color: ColorConfig.searchTextColor),
                  ),
                  onClick: () => logic.phoneAction()),
              MineInfoItem(
                  title: "mine_info_birthday".tr,
                  subRight: 3,
                  subWidget: state.birthday.isEmpty
                      ? Container(
                          padding: EdgeInsets.only(left: 20.w, right: 20.w),
                          height: 24.h,
                          decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(12.r),
                              color: ColorConfig.searchTextColor),
                          child: Center(
                            child: Text(
                              "mine_info_birthday_tips".tr,
                              style: StyleConfig.otherStyle(
                                  color: const Color.fromRGBO(255, 232, 174, 1),
                                  fontSize: 12),
                            ),
                          ),
                        )
                      : Text(
                          state.birthday.split(" ")[0],
                          style: StyleConfig.otherStyle(
                              color: ColorConfig.searchTextColor),
                        ),
                  onClick: () => logic.birthdayAction()),
              // MineInfoItem(
              //     title: "小耙ID",
              //     subRight: 4,
              //     subWidget: Text(
              //       'XP88777777',
              //       style: StyleConfig.otherStyle(
              //           color: ColorConfig.searchTextColor),
              //     ),
              //     isShowLine: false,
              //     isShowWidget: Images(
              //       path: R.mine_copy_png,
              //       width: 16.r,
              //       height: 16.r,
              //     ),
              //     onClick: () => logic.copyAction())
            ],
          ),
        );
      },
    );
  }
}
