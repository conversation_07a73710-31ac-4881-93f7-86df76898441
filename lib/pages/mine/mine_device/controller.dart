import 'package:get/get.dart';
import 'package:getx_xiaopa/pages/console/model/device_items_result.dart';
import 'package:getx_xiaopa/storage/storage.dart';

class MineDeviceController extends GetxController {
  MineDeviceController();

  List<DeviceItemsModel> deviceItems = [];

  DeviceItemsModel model = DeviceItemsModel();

  reloadData() {
    deviceItems = UserStore.to.deviceList;
    update(["mine_device"]);
  }

  updateStatus(DeviceItemsModel vModel) {
    for (var i = 0; i < deviceItems.length; i++) {
      if (vModel.id == deviceItems[i].id) {
        deviceItems[i] = vModel;
        update(["mine_device"]);
      }
    }
  }
}
