import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/utils.dart';
import 'package:getx_xiaopa/pages/console/model/device_items_result.dart';
import 'package:getx_xiaopa/utils/utils.dart';
import 'package:getx_xiaopa/values/values.dart';
import 'package:getx_xiaopa/widget/widget.dart';

class DeviceItem extends StatelessWidget {
  final DeviceItemsModel model;

  const DeviceItem({super.key, required this.model});

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.only(top: 10.h, left: 15.w, right: 15.w),
      padding: EdgeInsets.only(left: 15.w),
      width: 345.w,
      height: 102.h,
      decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(10.r),
          color: Colors.white,
          boxShadow: [
            BoxShadow(
              // 阴影颜色和透明度
              color: Colors.black.withValues(alpha: 0.06),
              // 阴影扩散范围
              spreadRadius: 0,
              // 阴影模糊程度
              blurRadius: 10,
              // 阴影偏移量（水平，垂直）
              offset: const Offset(0, 2),
            )
          ]),
      child: Row(
        children: [
          Container(
            width: 70.r,
            height: 70.r,
            padding: EdgeInsets.only(
                top: 14.h, right: 14.w, left: 14.w, bottom: 12.h),
            decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(10.r),
                color: const Color.fromRGBO(242, 242, 242, 1)),
            child: const ThemeImagePath(fileName: 'console_bind_image_03.png'),
          ),
          SizedBox(width: 10.w),
          Column(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Text(
                    'console'.tr,
                    style: StyleConfig.otherStyle(
                        color: ColorConfig.searchTextColor, fontSize: 16),
                  ),
                  SizedBox(width: 6.w),
                  ThemeContainer(
                    flag: model.isOnline == 1,
                    subColor: const Color.fromRGBO(242, 241, 245, 1),
                    radius: 13,
                    tPandding: EdgeInsets.only(left: 5.w, right: 5.w),
                    // tWidget: 40.w,
                    tHeight: 17.h,
                    child: Center(
                      child: ThemeText(
                        dataStr: model.isOnline == 1
                            ? "console_online".tr
                            : "console_offline".tr,
                        keyName: 'mineOnline',
                        flag: model.isOnline == 1,
                        subColor: ColorConfig.shopDetailTextColor,
                        fontSize: 12,
                      ),
                    ),
                  )
                ],
              ),
              SizedBox(height: 4.h),
              Text(
                'ID：${model.id}',
                style: StyleConfig.otherStyle(
                    color: ColorConfig.authenticationTextColor, fontSize: 14),
              ),
              SizedBox(height: 4.h),
              Text(
                '${"mine_device_one".tr} ${TimeUtil.getSimpleDate(model.licenseExpireDate ?? "", isHMS: false)}',
                style: StyleConfig.otherStyle(
                    color: ColorConfig.shopDetailTextColor, fontSize: 12),
              ),
            ],
          )
        ],
      ),
    );
  }
}
