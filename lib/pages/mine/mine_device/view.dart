import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:getx_xiaopa/base/base.dart';
import 'package:getx_xiaopa/extension/images.dart';
import 'package:getx_xiaopa/extension/widget_extension.dart';
import 'package:getx_xiaopa/r.dart';
import 'package:getx_xiaopa/values/values.dart';
import 'package:getx_xiaopa/widget/empty_data_widget.dart';

import 'index.dart';

class MineDevicePage extends BaseCommonView {
  MineDevicePage({super.key});

  final logic = Get.put(MineDeviceController());

  @override
  Color? get navColor => Colors.transparent;

  @override
  String? get navTitle => "mine_item_one".tr;

  @override
  TextStyle? get navTitleStyle =>
      StyleConfig.blackStyle(fontSize: 18, fontWeight: FontWeight.w600);

  @override
  double? get leftWidth => 35.w;

  @override
  Color? get contentColor => const Color.fromRGBO(249, 249, 249, 1);

  @override
  Widget? get leftButton => Padding(
        padding: EdgeInsets.only(left: 15.w),
        child: Images(
          path: R.back_png,
          width: 20.w,
          height: 20.h,
          scale: 2.8,
        ).inkWell(() => Get.back()),
      );

  // 主视图
  Widget _buildView() {
    return logic.deviceItems.isEmpty
        ? EmptyDataWidget(
            title: "mine_device_empty_tips".tr,
            imagePath: R.no_data_diary_png,
          )
        : ListView.builder(
            itemCount: logic.deviceItems.length,
            itemBuilder: (_, index) {
              return DeviceItem(
                model: logic.deviceItems[index],
              );
            },
          );
  }

  @override
  Widget buildContent() {
    return GetBuilder<MineDeviceController>(
      init: MineDeviceController(),
      id: "mine_device",
      builder: (_) {
        return _buildView();
      },
    );
  }
}
