import 'dart:async';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:getx_xiaopa/utils/theme_util.dart';
import 'package:getx_xiaopa/values/values.dart';

class MinePhoneState {
  String newPhoneController = '';
  TextEditingController verifyController = TextEditingController();

  var verifyStr = 'login_verify_send'.tr;

  /// 是否已发送
  bool isSend = false;
  late Timer timer;
  late int seconds;
  var inkWellStyle = StyleConfig.otherStyle(
      color: Get.find<ThemeColorController>().gTextColor, fontSize: 12);

  String areaCode = "cn";
  String areaDialCode = "86";

  ///限制手机号码位数，根据不同国家来
  int phoneMaxLenght = 11;

  String phone = '';

  MinePhoneState() {
    seconds = 60;

    ///Initialize variables
  }
}
