import 'dart:async';

import 'package:get/get.dart';
import 'package:getx_xiaopa/extension/widget_extension.dart';
import 'package:getx_xiaopa/network/http_client.dart';
import 'package:getx_xiaopa/network/model/result.dart';
import 'package:getx_xiaopa/storage/storage.dart';
import 'package:getx_xiaopa/utils/utils.dart';
import 'package:getx_xiaopa/values/values.dart';

import 'mine_phone_state.dart';

class MinePhoneLogic extends GetxController {
  final MinePhoneState state = MinePhoneState();

  @override
  void onInit() {
    super.onInit();
    state.phone = StrUtil.maskPhone(UserStore.to.userInfo.mobileNumber ?? "");
    state.areaCode = ConfigStore.to.areaCode;
    state.areaDialCode = ConfigStore.to.areaDialCode;

    _phoneMaxLenght();
  }

  phoneIsComponse(String value) {
    state.newPhoneController = value;
  }

  selectCountryAction(String code, String dialCode) {
    state.areaCode = code;
    state.areaDialCode = dialCode;
    ConfigStore.to.setAreaCode(code);
    ConfigStore.to.setAreaDialCode(dialCode);
    _phoneMaxLenght();
  }

  _phoneMaxLenght() {
    if (state.areaDialCode == "86") {
      state.phoneMaxLenght = 11;
    } else if (state.areaDialCode == "852" || state.areaDialCode == "853") {
      state.phoneMaxLenght = 8;
    } else {
      state.phoneMaxLenght = 15;
    }
    update(["mine_phone_view"]);
  }

  /// 获取验证码
  verifyAction() async {
    if (state.isSend) return;
    if (state.newPhoneController.isEmpty) {
      CToast.showToast("login_error_tips_one".tr);
      return;
    }
    bool flag = await _sendPhoneVerify();
    if (flag) {
      _startTimer();
    }
  }

  /// 启动倒计时的计时器。
  void _startTimer() {
    if (state.isSend) return;
    state.isSend = true;
    state.inkWellStyle = StyleConfig.otherStyle(
        color: ColorConfig.shopDetailTextColor, fontSize: 12);
    // 计时器（`Timer`）组件的定期（`periodic`）构造函数，创建一个新的重复计时器。
    state.timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (state.seconds == 0) {
        _cancelTimer();
        state.seconds = 60;
        state.isSend = false;
        state.inkWellStyle = StyleConfig.otherStyle(
            color: Get.find<ThemeColorController>().gTextColor, fontSize: 12);
        state.verifyStr = 'login_verify_resend'.tr;
        update(["mine_phone_view"]);
        return;
      }
      state.seconds--;
      state.verifyStr = '${state.seconds}s';

      update(["mine_phone_view"]);
    });
  }

  /// 取消定时器
  void _cancelTimer() {
    state.timer.cancel();
  }

  ///更改手机号
  phoneAction() {
    if (state.newPhoneController.isEmpty) {
      CToast.showToast("login_error_tips_one".tr);
      return;
    }
    if (state.verifyController.text.isEmpty) {
      CToast.showToast("login_verify_hint_text".tr);
      return;
    }
    updateUserInfo();
  }

  ///===================网络请求================================
  Future<bool> _sendPhoneVerify() async {
    var map = {
      "mobile_area": state.areaDialCode,
      "mobile": "${state.areaDialCode}${state.newPhoneController}",
      "template": "SMS_228215199",
      "scene": "confirm"
    };
    Result result = await http.sendPhoneVerify(map);
    CToast.showToast(result.msg!);
    if (result.code == 0) {
      return true;
    }
    return false;
  }

  @override
  void onClose() {
    if (state.isSend) {
      _cancelTimer();
    }
    super.onClose();
  }

  ///===================网络请求================================
  updateUserInfo() async {
    CToast.showLoading();
    var map = {
      "mobile_area": state.areaDialCode,
      "mobile_number": state.newPhoneController,
      "mobile": "${state.areaDialCode}${state.newPhoneController}",
      "verifyCode": state.verifyController.text
    };
    Result result = await http.updateUserInfo(map);
    if (result.code == 0) {
      await UserStore.to.getUserInfo();
      Get.back();
    }
    CToast.showToast(result.msg!);
  }
}
