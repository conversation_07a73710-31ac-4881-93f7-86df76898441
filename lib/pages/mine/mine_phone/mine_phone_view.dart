import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:getx_xiaopa/base/base.dart';
import 'package:getx_xiaopa/extension/widget_extension.dart';
import 'package:getx_xiaopa/r.dart';
import 'package:getx_xiaopa/storage/storage.dart';
import 'package:getx_xiaopa/values/values.dart';
import 'package:getx_xiaopa/widget/widget.dart';

import '../../../extension/images.dart';
import 'mine_phone_logic.dart';
import 'mine_phone_state.dart';

class MinePhonePage extends BaseCommonView {
  MinePhonePage({super.key});

  final MinePhoneLogic logic = Get.put(MinePhoneLogic());
  final MinePhoneState state = Get.find<MinePhoneLogic>().state;

  @override
  Color? get navColor => Colors.transparent;

  @override
  double? get leftWidth => 35.w;

  @override
  Widget? get leftButton => Padding(
        padding: EdgeInsets.only(left: 15.w),
        child: Images(
          path: R.back_png,
          width: 20.w,
          height: 20.h,
          scale: 2.8,
        ).inkWell(() => Get.back()),
      );

  @override
  Color? get contentColor => Colors.white;

  @override
  Widget buildContent() {
    return GetBuilder<MinePhoneLogic>(
      id: "mine_phone_view",
      builder: (_) {
        return Container(
          margin: EdgeInsets.only(top: 6.h),
          child: Column(
            children: [
              Container(
                margin: EdgeInsets.only(left: 40.w),
                child: Row(
                  children: [
                    Container(
                      margin: EdgeInsets.only(top: 9.h),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            "mine_phone_Title".tr,
                            style: StyleConfig.otherStyle(
                                color: ColorConfig.searchTextColor,
                                fontSize: 26,
                                fontWeight: FontWeight.w500),
                          ),
                          SizedBox(height: 10.h),
                          SizedBox(
                            width: 169.w,
                            child: Text(
                              "mine_phone_tips".tr,
                              style: StyleConfig.otherStyle(
                                  color: ColorConfig.shopDetailTextColor,
                                  fontSize: 12,
                                  fontWeight: FontWeight.w400),
                            ),
                          ),
                        ],
                      ),
                    ),
                    SizedBox(width: 19.w),
                    Images(
                      path: R.mine_image_01_png,
                      width: 130.w,
                      height: 97.h,
                    )
                  ],
                ),
              ),
              Container(
                margin: EdgeInsets.only(top: 21.h),
                width: 345.w,
                height: 1.h,
                color: const Color.fromRGBO(233, 232, 233, 1),
              ),
              Container(
                margin: EdgeInsets.only(top: 10.h),
                padding: EdgeInsets.only(left: 20.w, right: 20.w),
                width: 1.sw,
                height: 58.h,
                child: Row(
                  children: [
                    Text(
                      "mine_phone_old".tr,
                      style: StyleConfig.otherStyle(
                          fontSize: 14.sp,
                          color: ColorConfig.shopDetailTextColor),
                    ),
                    SizedBox(width: 20.w),
                    Expanded(
                      child: Text(
                        state.phone,
                        style: StyleConfig.otherStyle(
                            color: ColorConfig.searchTextColor),
                      ),
                    )
                  ],
                ),
              ),
              Container(
                margin: EdgeInsets.only(top: 10.h),
                padding: EdgeInsets.only(left: 20.w, right: 20.w),
                width: 1.sw,
                height: 58.h,
                child: Row(
                  children: [
                    Text(
                      "mine_phone_new".tr,
                      style: StyleConfig.otherStyle(
                          fontSize: 14.sp,
                          color: ColorConfig.shopDetailTextColor),
                    ),
                    SizedBox(width: 5.w),
                    Expanded(
                      child: IntlPhoneField(
                        disableLengthCheck: true,
                        showDropdownIcon: false,
                        showCountryFlag: false,
                        languageCode: ConfigStore.to.locale.languageCode,
                        inputFormatters: [
                          FilteringTextInputFormatter.digitsOnly,
                          LengthLimitingTextInputFormatter(state.phoneMaxLenght)
                        ],
                        style: StyleConfig.otherStyle(
                            fontSize: 16,
                            height: 1,
                            color: ColorConfig.searchTextColor,
                            fontWeight: FontWeight.w400),
                        dropdownTextStyle: StyleConfig.otherStyle(
                            fontSize: 14.sp,
                            height: 1,
                            color: ColorConfig.searchTextColor,
                            fontWeight: FontWeight.w400),
                        keyboardType: TextInputType.number,
                        decoration: InputDecoration(
                            border: InputBorder.none,
                            enabledBorder: InputBorder.none,
                            focusedBorder: InputBorder.none,
                            contentPadding: EdgeInsets.symmetric(
                                horizontal: 18.w,
                                vertical: Platform.isIOS ? 11.h : 16.h),
                            hintText: 'login_phone_hint_text'.tr,
                            hintStyle: StyleConfig.otherStyle(
                                color: ColorConfig.shopDetailTextColor,
                                height: 1,
                                fontSize: 13.sp)),
                        initialCountryCode: state.areaCode,
                        pickerDialogStyle: PickerDialogStyle(
                            backgroundColor: Colors.white,
                            searchFieldInputDecoration: InputDecoration(
                                labelText: "login_select_country".tr)),
                        onChanged: (phone) =>
                            logic.phoneIsComponse(phone.number),
                        onCountryChanged: (country) {
                          logic.selectCountryAction(
                              country.code, country.dialCode);
                        },
                      ),
                      // child: TextField(
                      //   controller: state.newPhoneController,
                      //   style: StyleConfig.otherStyle(
                      //       color: ColorConfig.searchTextColor,
                      //       fontSize: 16,
                      //       fontWeight: FontWeight.w400,
                      //       height: 1),
                      //   keyboardType: TextInputType.phone,
                      //   inputFormatters: [
                      //     FilteringTextInputFormatter.digitsOnly,
                      //     LengthLimitingTextInputFormatter(11)
                      //   ],
                      //   decoration: InputDecoration(
                      //     border: InputBorder.none,
                      //     enabledBorder: InputBorder.none,
                      //     focusedBorder: InputBorder.none,
                      //     hintText: '请输入新手机号',
                      //     hintStyle: StyleConfig.otherStyle(
                      //         color: ColorConfig.authenticationTextColor,
                      //         fontSize: 16),
                      //   ),
                      // ),
                    )
                  ],
                ),
              ),
              Container(
                margin: EdgeInsets.only(top: 10.h),
                padding: EdgeInsets.only(left: 20.w, right: 20.w),
                width: 1.sw,
                height: 58.h,
                child: Row(
                  children: [
                    Text(
                      "login_verify_code".tr,
                      style: StyleConfig.otherStyle(
                          fontSize: 14.sp,
                          color: ColorConfig.shopDetailTextColor),
                    ),
                    SizedBox(width: 35.w),
                    Expanded(
                      child: AppTextField(
                        controller: state.verifyController,
                        style: StyleConfig.otherStyle(
                            color: ColorConfig.searchTextColor,
                            fontSize: 14.sp,
                            fontWeight: FontWeight.w400,
                            height: 1),
                        keyboardType: TextInputType.phone,
                        inputFormatters: [
                          FilteringTextInputFormatter.digitsOnly
                        ],
                        decoration: InputDecoration(
                          border: InputBorder.none,
                          enabledBorder: InputBorder.none,
                          focusedBorder: InputBorder.none,
                          counterText: '',
                          hintText: 'login_verify_hint_text'.tr,
                          hintStyle: StyleConfig.otherStyle(
                              color: ColorConfig.authenticationTextColor,
                              fontSize: 14.sp),
                        ),
                      ),
                    ),
                    AnimatedPress(
                      seconds: 100,
                      scaleEnd: 0.95,
                      isAuto: false,
                      child: Container(
                        margin: EdgeInsets.only(right: 5.w),
                        width: 80.w,
                        height: 25.h,
                        decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(13.r),
                            color: state.isSend
                                ? const Color.fromRGBO(238, 238, 238, 1)
                                : ColorConfig.searchTextColor),
                        child: Center(
                          child: Text(
                            state.verifyStr,
                            style: state.inkWellStyle,
                          ),
                        ),
                      ).inkWell(() => logic.verifyAction()),
                    ),
                  ],
                ),
              ),
              Container(
                margin: EdgeInsets.only(left: 102.w, right: 15.w),
                height: 1.h,
                color: const Color.fromRGBO(233, 232, 233, 1),
              ),
              const Expanded(child: SizedBox()),
              Container(
                margin: EdgeInsets.only(
                    bottom: ScreenUtil().bottomBarHeight + 50.h),
                width: 283.w,
                height: 48.h,
                decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(24.r),
                    color: const Color.fromRGBO(40, 39, 46, 1)),
                child: Center(
                  child: ThemeText(
                    dataStr: "mine_phone_btn".tr,
                    keyName: 'textColor',
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ).inkWell(() => logic.phoneAction())
            ],
          ),
        );
      },
    );
  }
}
