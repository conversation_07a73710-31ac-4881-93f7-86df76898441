///
/// Code generated by jsonToDartModel https://ashamp.github.io/jsonToDartModel/
///
class TaskRemindModel {
/*
{
  "id": "4783232299040769",
  "created_at": "2025-05-12T11:15:25.161+08:00",
  "updated_at": "2025-05-12T11:15:25.161+08:00",
  "deleted_at": null,
  "start_time": 8,
  "end_time": 22,
  "interval": 60,
  "is_active":1,
  "user_id": "4470670919467008",
  "device_id": "4163364096835585"
} 
*/

  String? id;
  String? createdAt;
  String? updatedAt;
  String? deletedAt;
  double? startTime;
  double? endTime;
  double? interval;
  int? isActive;
  String? userId;
  String? deviceId;

  TaskRemindModel({
    this.id,
    this.createdAt,
    this.updatedAt,
    this.deletedAt,
    this.startTime,
    this.endTime,
    this.interval,
    this.isActive,
    this.userId,
    this.deviceId,
  });
  TaskRemindModel.fromJson(Map<String, dynamic> json) {
    id = json['id']?.toString();
    createdAt = json['created_at']?.toString();
    updatedAt = json['updated_at']?.toString();
    deletedAt = json['deleted_at']?.toString();
    startTime = json['start_time']?.toDouble();
    endTime = json['end_time']?.toDouble();
    interval = json['interval']?.toDouble();
    isActive = json['is_active']?.toInt();
    userId = json['user_id']?.toString();
    deviceId = json['device_id']?.toString();
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['id'] = id;
    data['created_at'] = createdAt;
    data['updated_at'] = updatedAt;
    data['deleted_at'] = deletedAt;
    data['start_time'] = startTime;
    data['end_time'] = endTime;
    data['interval'] = interval;
    data['is_active'] = isActive;
    data['user_id'] = userId;
    data['device_id'] = deviceId;
    return data;
  }
}

class TaskRemindResult {
/*
{
  "count": 1,
  "pageSize": 10,
  "pageNum": 1,
  "data": [
    {
      "id": "4783232299040769",
      "created_at": "2025-05-12T11:15:25.161+08:00",
      "updated_at": "2025-05-12T11:15:25.161+08:00",
      "deleted_at": null,
      "start_time": 8,
      "end_time": 22,
      "interval": 60,
      "user_id": "4470670919467008",
      "device_id": "4163364096835585"
    }
  ]
} 
*/

  int? count;
  int? pageSize;
  int? pageNum;
  List<TaskRemindModel?>? data;

  TaskRemindResult({
    this.count,
    this.pageSize,
    this.pageNum,
    this.data,
  });
  TaskRemindResult.fromJson(Map<String, dynamic> json) {
    count = json['count']?.toInt();
    pageSize = json['pageSize']?.toInt();
    pageNum = json['pageNum']?.toInt();
    if (json['data'] != null) {
      final v = json['data'];
      final arr0 = <TaskRemindModel>[];
      v.forEach((v) {
        arr0.add(TaskRemindModel.fromJson(v));
      });
      data = arr0;
    }
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['count'] = count;
    data['pageSize'] = pageSize;
    data['pageNum'] = pageNum;
    if (this.data != null) {
      final v = this.data;
      final arr0 = [];
      for (var v in v!) {
        arr0.add(v!.toJson());
      }
      data['data'] = arr0;
    }
    return data;
  }
}
