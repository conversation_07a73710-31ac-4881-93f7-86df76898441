///
/// Code generated by jsonToDartModel https://ashamp.github.io/jsonToDartModel/
///
class TaskTypeModel {
/*
{
  "id": "4163364096835586",
  "created_at": "0001-01-01T00:00:00Z",
  "updated_at": "0001-01-01T00:00:00Z",
  "deleted_at": null,
  "picture": "",
  "name": "咖啡",
  "sort": 1
} 
*/

  String? id;
  String? createdAt;
  String? updatedAt;
  String? deletedAt;
  String? picture;
  String? name;
  String? enName;
  int? sort;

  TaskTypeModel({
    this.id,
    this.createdAt,
    this.updatedAt,
    this.deletedAt,
    this.picture,
    this.name,
    this.enName,
    this.sort,
  });
  TaskTypeModel.fromJson(Map<String, dynamic> json) {
    id = json['id']?.toString();
    createdAt = json['created_at']?.toString();
    updatedAt = json['updated_at']?.toString();
    deletedAt = json['deleted_at']?.toString();
    picture = json['picture']?.toString();
    name = json['name']?.toString();
    enName = json['en_name']?.toString();
    sort = json['sort']?.toInt();
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['id'] = id;
    data['created_at'] = createdAt;
    data['updated_at'] = updatedAt;
    data['deleted_at'] = deletedAt;
    data['picture'] = picture;
    data['name'] = name;
    data['en_name'] = enName;
    data['sort'] = sort;
    return data;
  }
}

class TaskTypeResult {
/*
{
  "count": 1,
  "pageSize": 10,
  "pageNum": 1,
  "data": [
    {
      "id": "4163364096835586",
      "created_at": "0001-01-01T00:00:00Z",
      "updated_at": "0001-01-01T00:00:00Z",
      "deleted_at": null,
      "picture": "",
      "name": "咖啡",
      "sort": 1
    }
  ]
} 
*/

  int? count;
  int? pageSize;
  int? pageNum;
  List<TaskTypeModel?>? data;

  TaskTypeResult({
    this.count,
    this.pageSize,
    this.pageNum,
    this.data,
  });
  TaskTypeResult.fromJson(Map<String, dynamic> json) {
    count = json['count']?.toInt();
    pageSize = json['pageSize']?.toInt();
    pageNum = json['pageNum']?.toInt();
    if (json['data'] != null) {
      final v = json['data'];
      final arr0 = <TaskTypeModel>[];
      v.forEach((v) {
        arr0.add(TaskTypeModel.fromJson(v));
      });
      data = arr0;
    }
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['count'] = count;
    data['pageSize'] = pageSize;
    data['pageNum'] = pageNum;
    if (this.data != null) {
      final v = this.data;
      final arr0 = [];
      for (var v in v!) {
        arr0.add(v!.toJson());
      }
      data['data'] = arr0;
    }
    return data;
  }
}
