///
/// Code generated by jsonToDartModel https://ashamp.github.io/jsonToDartModel/
///
class TaskTargetModel {
/*
{
  "id": "4783232299040768",
  "created_at": "2025-05-12T11:15:25.156+08:00",
  "updated_at": "2025-05-13T10:00:21.989+08:00",
  "deleted_at": null,
  "num": 7,
  "user_id": "4470670919467008",
  "device_id": "4163364096835585"
} 
*/

  String? id;
  String? createdAt;
  String? updatedAt;
  String? deletedAt;
  int? theNum;
  String? userId;
  String? deviceId;
  int? continuous;
  int? skinUnlock;

  TaskTargetModel({
    this.id,
    this.createdAt,
    this.updatedAt,
    this.deletedAt,
    this.theNum,
    this.userId,
    this.deviceId,
    this.continuous,
    this.skinUnlock,
  });
  TaskTargetModel.fromJson(Map<String, dynamic> json) {
    id = json['id']?.toString();
    createdAt = json['created_at']?.toString();
    updatedAt = json['updated_at']?.toString();
    deletedAt = json['deleted_at']?.toString();
    theNum = json['num']?.toInt();
    userId = json['user_id']?.toString();
    deviceId = json['device_id']?.toString();
    continuous = json['continuous']?.toInt();
    skinUnlock = json['skin_unlock']?.toInt();
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['id'] = id;
    data['created_at'] = createdAt;
    data['updated_at'] = updatedAt;
    data['deleted_at'] = deletedAt;
    data['num'] = theNum;
    data['user_id'] = userId;
    data['device_id'] = deviceId;
    data['continuous'] = continuous;
    data['skin_unlock'] = skinUnlock;
    return data;
  }
}

class TaskTargetResult {
/*
{
  "count": 1,
  "pageSize": 10,
  "pageNum": 1,
  "data": [
    {
      "id": "4783232299040768",
      "created_at": "2025-05-12T11:15:25.156+08:00",
      "updated_at": "2025-05-13T10:00:21.989+08:00",
      "deleted_at": null,
      "num": 7,
      "user_id": "4470670919467008",
      "device_id": "4163364096835585"
    }
  ]
} 
*/

  int? count;
  int? pageSize;
  int? pageNum;
  List<TaskTargetModel?>? data;

  TaskTargetResult({
    this.count,
    this.pageSize,
    this.pageNum,
    this.data,
  });
  TaskTargetResult.fromJson(Map<String, dynamic> json) {
    count = json['count']?.toInt();
    pageSize = json['pageSize']?.toInt();
    pageNum = json['pageNum']?.toInt();
    if (json['data'] != null) {
      final v = json['data'];
      final arr0 = <TaskTargetModel>[];
      v.forEach((v) {
        arr0.add(TaskTargetModel.fromJson(v));
      });
      data = arr0;
    }
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['count'] = count;
    data['pageSize'] = pageSize;
    data['pageNum'] = pageNum;
    if (this.data != null) {
      final v = this.data;
      final arr0 = [];
      for (var v in v!) {
        arr0.add(v!.toJson());
      }
      data['data'] = arr0;
    }
    return data;
  }
}
