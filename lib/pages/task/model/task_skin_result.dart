///
/// Code generated by jsonToDartModel https://ashamp.github.io/jsonToDartModel/
///
class TaskSkinModel {
/*
{
  "id": "1",
  "created_at": "0001-01-01T00:00:00Z",
  "updated_at": "0001-01-01T00:00:00Z",
  "deleted_at": null,
  "picture": "",
  "name": "小粑",
  "unlock": 1
} 
*/

  String? id;
  String? createdAt;
  String? updatedAt;
  String? deletedAt;
  String? picture;
  String? name;
  String? enName;
  int? unlockNo;
  String? contour;

  TaskSkinModel({
    this.id,
    this.createdAt,
    this.updatedAt,
    this.deletedAt,
    this.picture,
    this.name,
    this.enName,
    this.unlockNo,
    this.contour,
  });
  TaskSkinModel.fromJson(Map<String, dynamic> json) {
    id = json['id']?.toString();
    createdAt = json['created_at']?.toString();
    updatedAt = json['updated_at']?.toString();
    deletedAt = json['deleted_at']?.toString();
    picture = json['picture']?.toString();
    name = json['name']?.toString();
    enName = json['en_name']?.toString();
    unlockNo = json['unlock_no']?.toInt();
    contour = json['contour']?.toString();
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['id'] = id;
    data['created_at'] = createdAt;
    data['updated_at'] = updatedAt;
    data['deleted_at'] = deletedAt;
    data['picture'] = picture;
    data['name'] = name;
    data['en_name'] = enName;
    data['unlock_no'] = unlockNo;
    data['contour'] = contour;
    return data;
  }
}

class TaskSkinResult {
/*
{
  "count": 1,
  "pageSize": 10,
  "pageNum": 1,
  "data": [
    {
      "id": "1",
      "created_at": "0001-01-01T00:00:00Z",
      "updated_at": "0001-01-01T00:00:00Z",
      "deleted_at": null,
      "picture": "",
      "name": "小粑",
      "unlock": 1
    }
  ]
} 
*/

  int? count;
  int? pageSize;
  int? pageNum;
  List<TaskSkinModel?>? data;

  TaskSkinResult({
    this.count,
    this.pageSize,
    this.pageNum,
    this.data,
  });
  TaskSkinResult.fromJson(Map<String, dynamic> json) {
    count = json['count']?.toInt();
    pageSize = json['pageSize']?.toInt();
    pageNum = json['pageNum']?.toInt();
    if (json['data'] != null) {
      final v = json['data'];
      final arr0 = <TaskSkinModel>[];
      v.forEach((v) {
        arr0.add(TaskSkinModel.fromJson(v));
      });
      data = arr0;
    }
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['count'] = count;
    data['pageSize'] = pageSize;
    data['pageNum'] = pageNum;
    if (this.data != null) {
      final v = this.data;
      final arr0 = [];
      for (var v in v!) {
        arr0.add(v!.toJson());
      }
      data['data'] = arr0;
    }
    return data;
  }
}
