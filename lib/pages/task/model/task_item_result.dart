///
/// Code generated by jsonToDartModel https://ashamp.github.io/jsonToDartModel/
///
class TaskItemModel {
/*
{
  "id": "4785977252904960",
  "created_at": "2025-05-13T09:58:51.603+08:00",
  "updated_at": "2025-05-13T09:58:51.603+08:00",
  "deleted_at": null,
  "task_type_id": "4163364096835586",
  "user_id": "4470670919467008",
  "device_id": "4163364096835585"
} 
*/

  String? id;
  String? createdAt;
  String? updatedAt;
  String? deletedAt;
  String? taskTypeId;
  String? userId;
  String? deviceId;
  bool? isUnlock;
  String? skinId;

  TaskItemModel(
      {this.id,
      this.createdAt,
      this.updatedAt,
      this.deletedAt,
      this.taskTypeId,
      this.userId,
      this.deviceId,
      this.isUnlock,
      this.skinId});
  TaskItemModel.fromJson(Map<String, dynamic> json) {
    id = json['id']?.toString();
    createdAt = json['created_at']?.toString();
    updatedAt = json['updated_at']?.toString();
    deletedAt = json['deleted_at']?.toString();
    taskTypeId = json['task_type_id']?.toString();
    userId = json['user_id']?.toString();
    deviceId = json['device_id']?.toString();
    isUnlock = json['is_unlock'];
    skinId = json['skin_id']?.toString();
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['id'] = id;
    data['created_at'] = createdAt;
    data['updated_at'] = updatedAt;
    data['deleted_at'] = deletedAt;
    data['task_type_id'] = taskTypeId;
    data['user_id'] = userId;
    data['device_id'] = deviceId;
    data['is_unlock'] = isUnlock;
    data['skin_id'] = skinId;
    return data;
  }
}

class TaskItemResult {
/*
{
  "count": 3,
  "pageSize": 10,
  "pageNum": 1,
  "data": [
    {
      "id": "4785977252904960",
      "created_at": "2025-05-13T09:58:51.603+08:00",
      "updated_at": "2025-05-13T09:58:51.603+08:00",
      "deleted_at": null,
      "task_type_id": "4163364096835586",
      "user_id": "4470670919467008",
      "device_id": "4163364096835585"
    }
  ]
} 
*/

  int? count;
  int? pageSize;
  int? pageNum;
  List<TaskItemModel?>? data;

  TaskItemResult({
    this.count,
    this.pageSize,
    this.pageNum,
    this.data,
  });
  TaskItemResult.fromJson(Map<String, dynamic> json) {
    count = json['count']?.toInt();
    pageSize = json['pageSize']?.toInt();
    pageNum = json['pageNum']?.toInt();
    if (json['data'] != null) {
      final v = json['data'];
      final arr0 = <TaskItemModel>[];
      v.forEach((v) {
        arr0.add(TaskItemModel.fromJson(v));
      });
      data = arr0;
    }
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['count'] = count;
    data['pageSize'] = pageSize;
    data['pageNum'] = pageNum;
    if (this.data != null) {
      final v = this.data;
      final arr0 = [];
      for (var v in v!) {
        arr0.add(v!.toJson());
      }
      data['data'] = arr0;
    }
    return data;
  }
}
