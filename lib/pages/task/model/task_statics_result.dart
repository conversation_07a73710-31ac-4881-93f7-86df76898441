///
/// Code generated by jsonToDartModel https://ashamp.github.io/jsonToDartModel/
///
class TaskStaticsModel {
/*
{
  "id": "4783661225345024",
  "created_at": "2025-05-12T14:48:28.154+08:00",
  "updated_at": "2025-05-12T14:48:28.154+08:00",
  "deleted_at": null,
  "type": 1,
  "duration": "2025-05-12",
  "num": 0,
  "user_id": "4470670919467008",
  "device_id": "4163364096835585"
} 
*/

  String? id;
  String? createdAt;
  String? updatedAt;
  String? deletedAt;
  int? type;
  String? duration;
  int? theNum;
  String? userId;
  String? deviceId;

  TaskStaticsModel({
    this.id,
    this.createdAt,
    this.updatedAt,
    this.deletedAt,
    this.type,
    this.duration,
    this.theNum,
    this.userId,
    this.deviceId,
  });
  TaskStaticsModel.fromJson(Map<String, dynamic> json) {
    id = json['id']?.toString();
    createdAt = json['created_at']?.toString();
    updatedAt = json['updated_at']?.toString();
    deletedAt = json['deleted_at']?.toString();
    type = json['type']?.toInt();
    duration = json['duration']?.toString();
    theNum = json['num']?.toInt();
    userId = json['user_id']?.toString();
    deviceId = json['device_id']?.toString();
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['id'] = id;
    data['created_at'] = createdAt;
    data['updated_at'] = updatedAt;
    data['deleted_at'] = deletedAt;
    data['type'] = type;
    data['duration'] = duration;
    data['num'] = theNum;
    data['user_id'] = userId;
    data['device_id'] = deviceId;
    return data;
  }
}

class TaskStaticsResult {
/*
{
  "count": 1,
  "pageSize": 10,
  "pageNum": 1,
  "data": [
    {
      "id": "4783661225345024",
      "created_at": "2025-05-12T14:48:28.154+08:00",
      "updated_at": "2025-05-12T14:48:28.154+08:00",
      "deleted_at": null,
      "type": 1,
      "duration": "2025-05-12",
      "num": 0,
      "user_id": "4470670919467008",
      "device_id": "4163364096835585"
    }
  ]
} 
*/

  int? count;
  int? pageSize;
  int? pageNum;
  List<TaskStaticsModel?>? data;

  TaskStaticsResult({
    this.count,
    this.pageSize,
    this.pageNum,
    this.data,
  });
  TaskStaticsResult.fromJson(Map<String, dynamic> json) {
    count = json['count']?.toInt();
    pageSize = json['pageSize']?.toInt();
    pageNum = json['pageNum']?.toInt();
    if (json['data'] != null) {
      final v = json['data'];
      final arr0 = <TaskStaticsModel>[];
      v.forEach((v) {
        arr0.add(TaskStaticsModel.fromJson(v));
      });
      data = arr0;
    }
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['count'] = count;
    data['pageSize'] = pageSize;
    data['pageNum'] = pageNum;
    if (this.data != null) {
      final v = this.data;
      final arr0 = [];
      for (var v in v!) {
        arr0.add(v!.toJson());
      }
      data['data'] = arr0;
    }
    return data;
  }
}
