import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:getx_xiaopa/base/base.dart';
import 'package:getx_xiaopa/extension/images.dart';
import 'package:getx_xiaopa/extension/widget_extension.dart';
import 'package:getx_xiaopa/r.dart';
import 'package:getx_xiaopa/values/values.dart';
import 'package:getx_xiaopa/widget/theme_image.dart';

import 'tasl_explan_index.dart';

class TaskExplanPage extends BaseCommonView<TaskExplanController> {
  TaskExplanPage({super.key});

  // 主视图
  Widget _buildView() {
    return ThemeContainerImage(
      fileName: 'task_explan_bg.png',
      conWidget: 1.sw,
      conHeight: 1.sh,
      fit: BoxFit.fill,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            margin: EdgeInsets.only(left: 15.w, top: 61.h),
            child: Images(
              path: R.back_png,
              width: 20.w,
              height: 20.h,
              color: Colors.white,
            ).inkWell(() => Get.back()),
          ),
          Expanded(
            child: Container(
              width: 345.w,
              margin: EdgeInsets.only(
                  left: 15.w, top: 100.h, right: 15.w, bottom: 10.h),
              padding: EdgeInsets.only(bottom: 20.h),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(10.r),
                color: Colors.white,
                boxShadow: const [
                  BoxShadow(
                    color: Color.fromRGBO(180, 142, 78, 0.13),
                    // 阴影颜色和透明度
                    spreadRadius: 0,
                    // 阴影扩散范围
                    blurRadius: 10,
                    // 阴影模糊程度
                    offset: Offset(0, 2),
                    // 阴影偏移量（水平，垂直）
                  )
                ],
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  SizedBox(height: 20.h),
                  Container(
                    alignment: Alignment.center,
                    child: Text(
                      "task_explan_tips_one".tr,
                      style: StyleConfig.otherStyle(
                          color: ColorConfig.searchTextColor,
                          fontSize: 20,
                          fontWeight: FontWeight.w800),
                    ),
                  ),
                  SizedBox(height: 12.h),
                  Expanded(
                    child: SingleChildScrollView(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          ThemeContainerImage(
                            fileName: 'task_explan_image.png',
                            padding: EdgeInsets.only(top: 9.h, left: 15.w),
                            conWidget: 147.w,
                            conHeight: 33.h,
                            fit: BoxFit.fill,
                            child: Text(
                              "task_explan_tips_two".tr,
                              style: StyleConfig.otherStyle(
                                  color: ColorConfig.searchTextColor,
                                  fontWeight: FontWeight.w700,
                                  fontSize: 14),
                            ),
                          ),
                          Container(
                            margin: EdgeInsets.only(
                                left: 15.w, right: 15.w, top: 10.h),
                            child: Text(
                              "task_explan_content_tips_two".tr,
                              style: StyleConfig.otherStyle(
                                color: ColorConfig.authenticationTextColor,
                                fontSize: 14,
                                fontWeight: FontWeight.w400,
                                height: 1.8,
                              ),
                            ),
                          ),
                          SizedBox(height: 10.h),
                          ThemeContainerImage(
                            fileName: 'task_explan_image.png',
                            padding: EdgeInsets.only(top: 9.h, left: 15.w),
                            conWidget: 147.w,
                            conHeight: 33.h,
                            fit: BoxFit.fill,
                            child: Text(
                              "task_explan_tips_three".tr,
                              style: StyleConfig.otherStyle(
                                  color: ColorConfig.searchTextColor,
                                  fontWeight: FontWeight.w700,
                                  fontSize: 14),
                            ),
                          ),
                          Container(
                            margin: EdgeInsets.only(
                                left: 15.w, right: 15.w, top: 10.h),
                            child: Text(
                              "task_explan_content_tips_three".tr,
                              style: StyleConfig.otherStyle(
                                color: ColorConfig.authenticationTextColor,
                                fontSize: 14,
                                fontWeight: FontWeight.w400,
                                height: 1.8,
                              ),
                            ),
                          ),
                          SizedBox(height: 10.h),
                          ThemeContainerImage(
                            fileName: 'task_explan_image.png',
                            padding: EdgeInsets.only(top: 9.h, left: 15.w),
                            conWidget: 147.w,
                            conHeight: 33.h,
                            fit: BoxFit.fill,
                            child: Text(
                              "task_explan_tips_four".tr,
                              style: StyleConfig.otherStyle(
                                  color: ColorConfig.searchTextColor,
                                  fontWeight: FontWeight.w700,
                                  fontSize: 14),
                            ),
                          ),
                          Container(
                            margin: EdgeInsets.only(
                                left: 15.w, right: 15.w, top: 10.h),
                            child: Text(
                              "task_explan_content_tips_four".tr,
                              style: StyleConfig.otherStyle(
                                color: ColorConfig.authenticationTextColor,
                                fontSize: 14,
                                fontWeight: FontWeight.w400,
                                height: 1.8,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          )
        ],
      ),
    );
  }

  @override
  bool? get isHiddenNav => true;

  @override
  bool? get extendBodyBehindAppBar => true;

  @override
  Widget buildContent() {
    return GetBuilder<TaskExplanController>(
      init: TaskExplanController(),
      id: "task_explan",
      builder: (_) {
        return _buildView();
      },
    );
  }
}
