import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:getx_xiaopa/base/base.dart';
import 'package:getx_xiaopa/extension/images.dart';
import 'package:getx_xiaopa/extension/widget_extension.dart';
import 'package:getx_xiaopa/pages/task/task_achievement/widget/task_achievement_item.dart';
import 'package:getx_xiaopa/r.dart';
import 'package:getx_xiaopa/values/values.dart';
import 'package:getx_xiaopa/widget/widget.dart';

import 'task_achievement_logic.dart';
import 'task_achievement_state.dart';

class TaskAchievementPage extends BaseCommonView {
  TaskAchievementPage({super.key});

  final TaskAchievementLogic logic = Get.put(TaskAchievementLogic());
  final TaskAchievementState state = Get.find<TaskAchievementLogic>().state;

  @override
  String? get navTitle => "task_achievement_title".tr;

  @override
  Color? get navColor => Colors.transparent;

  @override
  TextStyle? get navTitleStyle =>
      StyleConfig.blackStyle(fontSize: 18, fontWeight: FontWeight.w500);

  @override
  double? get leftWidth => 35.w;

  @override
  Widget? get leftButton => Padding(
        padding: EdgeInsets.only(left: 15.w),
        child: Images(
          path: R.back_png,
          width: 20.w,
          height: 20.h,
          scale: 2.8,
        ).inkWell(() => Get.back(result: state.isSave)),
      );

  @override
  Color? get contentColor => const Color.fromRGBO(249, 249, 249, 1);

  @override
  bool? get extendBodyBehindAppBar => true;

  @override
  Widget buildContent() {
    return GetBuilder<TaskAchievementLogic>(
      id: "task_achievement_view",
      builder: (_) {
        return PopScope(
          canPop: false,
          onPopInvokedWithResult: (didPop, result) {
            if (!didPop) {
              Get.back(result: state.isSave);
            }
          },
          child: SizedBox(
            width: 1.sw,
            height: 1.sh,
            child: Stack(
              children: [
                Positioned(
                  child: ThemeContainerImage(
                    fileName: 'task_achievement_bg_01.png',
                    conWidget: 1.sw,
                    conHeight: 259.h,
                    fit: BoxFit.fill,
                    child: Column(
                      children: [
                        Container(
                          margin: EdgeInsets.only(top: 100.h, left: 35.w),
                          child: Row(
                            children: [
                              Text(
                                "task_achievement_title_one".tr,
                                style: StyleConfig.otherStyle(
                                    color: ColorConfig.searchTextColor,
                                    fontSize: 14,
                                    fontWeight: FontWeight.w500),
                              ),
                              SizedBox(width: 5.w),
                              Text(
                                "${state.continuous}${'task_achievement_unit'.tr}",
                                style: StyleConfig.otherStyle(
                                    color:
                                        const Color.fromRGBO(255, 80, 110, 1),
                                    fontWeight: FontWeight.w700,
                                    fontSize: 18),
                              ),

                              // const ThemeText(
                              //   dataStr: "坚持喝水",
                              //   keyName: 'textColor',
                              //   fontSize: 14,
                              //   fontWeight: FontWeight.w500,
                              // ),
                            ],
                          ),
                        ),
                        Container(
                          margin: EdgeInsets.only(top: 10.h, left: 35.w),
                          child: Row(
                            children: [
                              Text(
                                "task_achievement_title_two".tr,
                                style: StyleConfig.otherStyle(
                                    color: ColorConfig.searchTextColor,
                                    fontSize: 14,
                                    fontWeight: FontWeight.w500),
                              ),
                              SizedBox(width: 5.w),
                              Text(
                                "${state.total}${'task_unit'.tr}",
                                style: StyleConfig.otherStyle(
                                    color:
                                        const Color.fromRGBO(255, 80, 110, 1),
                                    fontWeight: FontWeight.w700,
                                    fontSize: 18),
                              ),

                              // const ThemeText(
                              //   dataStr: "喝水战绩",
                              //   keyName: 'textColor',
                              //   fontSize: 14,
                              //   fontWeight: FontWeight.w500,
                              // )
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                Positioned(
                  top: 200.h,
                  width: 1.sw,
                  bottom: 0.h,
                  child: ThemeContainerImage(
                    fileName: 'task_achievement_bg_02.png',
                    fit: BoxFit.fill,
                    child: Column(
                      children: [
                        Container(
                          margin: EdgeInsets.only(left: 22.w, top: 28.h),
                          child: Row(
                            children: [
                              ThemeImagePath(
                                fileName: 'mine_avator.png',
                                imgWidget: 60.r,
                                imgHeight: 60.r,
                              ),
                              SizedBox(width: 7.w),
                              Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Container(
                                    margin: EdgeInsets.only(left: 3.w),
                                    child: Text(
                                      "console".tr,
                                      style: StyleConfig.otherStyle(
                                          color: const Color.fromRGBO(
                                              69, 47, 10, 1),
                                          fontWeight: FontWeight.w500,
                                          fontSize: 18),
                                    ),
                                  ),
                                  SizedBox(height: 6.h),
                                  Row(
                                    children: [
                                      ThemeImagePath(
                                        fileName: 'task_achievement_image.png',
                                        imgWidget: 17.w,
                                        imgHeight: 18.h,
                                      ),
                                      SizedBox(width: 2.w),
                                      Text(
                                        "task_achievement_title_three".trArgs(
                                            ["${state.unlockSkinList.length}"]),
                                        style: StyleConfig.otherStyle(
                                            color: ColorConfig
                                                .authenticationTextColor,
                                            fontWeight: FontWeight.w400,
                                            fontSize: 12),
                                      ),
                                      SizedBox(width: 8.w),
                                      ThemeImagePath(
                                        fileName:
                                            'task_achievement_explain.png',
                                        imgWidget: 14.r,
                                        imgHeight: 14.r,
                                      ).inkWell(() => logic.explanAction()),
                                    ],
                                  )
                                ],
                              )
                            ],
                          ),
                        ),
                        Expanded(
                          child: Container(
                            margin: EdgeInsets.only(top: 26.h),
                            child: GridView.count(
                              padding: EdgeInsets.only(
                                  top: 10.h, left: 15.w, right: 15.w),
                              childAspectRatio: 0.77,
                              crossAxisCount: 4,
                              mainAxisSpacing: 20.h,
                              crossAxisSpacing: 12.w,
                              children: List.generate(
                                state.achievementItemList.length,
                                (index) {
                                  return TaskAchievementItem(
                                    cIndex: index,
                                    itemType: state.achievementItemList[index],
                                    onTap: (cIndex) => logic.itemAction(cIndex),
                                  );
                                },
                              ),
                            ),
                          ),
                        ),
                        Container(
                          margin: EdgeInsets.only(top: 10.h, bottom: 20.h),
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(24.r),
                            color: const Color.fromRGBO(40, 39, 46, 1),
                          ),
                          width: 283.w,
                          height: 48.h,
                          child: Center(
                            child: ThemeText(
                              dataStr: "task_achievement_btn".tr,
                              keyName: 'textColor',
                              fontWeight: FontWeight.w700,
                            ),
                          ),
                        ).inkWell(() => logic.saveSkin())
                      ],
                    ),
                  ),
                )
              ],
            ),
          ),
        );
      },
    );
  }
}
