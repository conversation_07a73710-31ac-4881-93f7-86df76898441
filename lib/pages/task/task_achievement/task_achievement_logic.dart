import 'package:get/get.dart';
import 'package:getx_xiaopa/extension/widget_extension.dart';
import 'package:getx_xiaopa/network/http_client.dart';
import 'package:getx_xiaopa/network/model/request_body.dart';
import 'package:getx_xiaopa/network/model/result.dart';
import 'package:getx_xiaopa/pages/task/model/task_skin_result.dart';
import 'package:getx_xiaopa/r.dart';
import 'package:getx_xiaopa/routes/router.dart';
import 'package:getx_xiaopa/storage/storage.dart';

import 'task_achievement_state.dart';

class TaskAchievementLogic extends GetxController {
  final TaskAchievementState state = TaskAchievementState();

  @override
  void onInit() async {
    super.onInit();

    state.continuous = Get.arguments["continuous"];

    if (UserStore.to.unlockSkinList.isEmpty) {
      await UserStore.to.getUnlockSkinList();
    }

    state.unlockSkinList = UserStore.to.unlockSkinList;

    if (UserStore.to.allSkinList.isEmpty) {
      await UserStore.to.getAllSkinList();
    }

    state.skinList = UserStore.to.allSkinList;

    _initData();

    _getTaskItems();
  }

  ///处理数据
  _initData() {
    for (TaskSkinModel e in state.skinList) {
      var temp = AchievementItemType();

      ///已经解锁的皮肤
      for (var u in state.unlockSkinList) {
        ///如果已解锁皮肤列表包含解锁皮肤，则解锁
        if (e.id == u.skinId) {
          temp.id = u.id!;

          ///解锁标记
          temp.isUnlock = true;

          ///如果皮肤列表包含默认皮肤，则选中
          temp.isSelected = u.isDefault == 1 ? true : false;
        }
      }

      ///解锁的皮肤图片
      temp.lockImage = fileUrl(e.picture ?? '');

      ///未解锁皮肤图片
      temp.unlockImage = R.task_achievement_item_unlock_png;

      ///添加到列表
      state.achievementItemList.add(temp);
    }
    update(["task_achievement_view"]);
  }

  explanAction() {
    Get.toNamed(AppRoutes.TASK_EXPLAN);
  }

  itemAction(int index) {
    if (!state.achievementItemList[index].isUnlock) return;

    for (int i = 0; i < state.achievementItemList.length; i++) {
      state.achievementItemList[i].isSelected = false;
    }

    state.achievementItemList[index].isSelected = true;
    state.cIndex = index;

    update(["task_achievement_view"]);
  }

  saveSkin() {
    String cId = state.achievementItemList[state.cIndex].id;
    _updateTaskSkin(id: cId, isDefault: 1);
  }

  /// ==========================网络请求==========================
  ///任务列表（获取喝水战绩）
  _getTaskItems() async {
    var map = RequestBody(pagination: true, filters: [
      Filter(
          expr: "=",
          name: "device_id",
          value: UserStore.to.deviceList[UserStore.to.selectDevice].id)
    ]).toJson();
    Result result = await http.taskItems(map);
    if (result.code == 0) {
      state.total = '${result.data.count ?? 0}';
    } else {
      CToast.showToast(result.msg!);
    }
    update(["task_achievement_view"]);
  }

  ///修改使用的皮肤
  _updateTaskSkin({String id = "", int isDefault = 2}) async {
    CToast.showLoading();
    var map = {
      "id": id,
      "is_default": isDefault,
    };
    Result result = await http.taskShinUserUpdate(map);
    if (result.code == 0) {
      state.isSave = true;
      await UserStore.to.getUnlockSkinList();
    }
    CToast.showToast(result.msg!);
  }
}
