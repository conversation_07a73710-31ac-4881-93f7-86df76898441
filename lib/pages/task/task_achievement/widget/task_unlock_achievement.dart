import 'dart:ui';

import 'package:flutter/widgets.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:getx_xiaopa/extension/images.dart';
import 'package:getx_xiaopa/extension/widget_extension.dart';
import 'package:getx_xiaopa/network/http_client.dart';
import 'package:getx_xiaopa/pages/task/model/task_skin_result.dart';
import 'package:getx_xiaopa/r.dart';
import 'package:getx_xiaopa/storage/storage.dart';
import 'package:getx_xiaopa/widget/widget.dart';

class TaskUnLockAchievement extends StatefulWidget {
  final String skidId;
  final Function()? onTap;
  const TaskUnLockAchievement({super.key, this.onTap, this.skidId = ''});

  @override
  State<TaskUnLockAchievement> createState() => _TaskUnLockAchievementState();
}

class _TaskUnLockAchievementState extends State<TaskUnLockAchievement>
    with SingleTickerProviderStateMixin {
  late AnimationController controller;

  String skinName = "";
  String skinImage = "";

  @override
  void initState() {
    super.initState();

    for (TaskSkinModel model in UserStore.to.allSkinList) {
      if (widget.skidId == model.id) {
        skinImage = model.picture ?? '';
        skinName = ConfigStore.to.getLocaleCode() == "zh"
            ? model.name ?? ''
            : model.enName ?? '';
      }
    }

    controller = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 500),
    );

    controller.forward();
  }

  @override
  Widget build(BuildContext context) {
    return ScaleTransition(
      scale: CurvedAnimation(
        parent: controller,
        curve: Curves.easeOutBack,
      ),
      child: BackdropFilter(
        filter: ImageFilter.blur(sigmaX: 5, sigmaY: 5),
        child: Column(
          children: [
            SizedBox(height: 110.h),
            Row(
              children: [
                SizedBox(width: 140.w),
                ThemeText(
                  dataStr: "task_achiebement_locked_title".tr,
                  keyName: 'textColor',
                  fontSize: 24,
                ),
                SizedBox(width: 65.w),
                ThemeImagePath(
                  fileName: 'task_unlock_star_01.png',
                  imgWidget: 17.r,
                  imgHeight: 17.r,
                )
              ],
            ),
            SizedBox(height: 10.h),
            ThemeImagePath(
              fileName: 'task_unlock_hang_01.png',
              imgWidget: 259.w,
              imgHeight: 11.h,
            ),
            SizedBox(height: 10.h),
            ThemeText(
              dataStr: "${'task_achiebement_locked_tips_one'.tr}-$skinName",
              keyName: 'textColor',
              fontSize: 24,
              fontWeight: FontWeight.w700,
            ),
            SizedBox(height: 10.h),
            Row(
              children: [
                SizedBox(width: 48.w),
                ThemeImagePath(
                  fileName: 'task_unlock_star_01.png',
                  imgWidget: 17.r,
                  imgHeight: 17.r,
                ),
                ThemeImagePath(
                  fileName: 'task_unlock_hang_02.png',
                  imgWidget: 259.w,
                  imgHeight: 8.h,
                ),
              ],
            ),
            SizedBox(height: 28.h),
            SizedBox(
              width: 375.w,
              height: 308.h,
              child: Stack(
                children: [
                  const AnimatedRotateWidget(
                      mSeconds: 7000,
                      child: ThemeImagePath(
                        fileName: 'task_unlock_bg.png',
                        fit: BoxFit.cover,
                      )),
                  Positioned(
                    top: 143.h,
                    left: 103.w,
                    child: AnimatedPress(
                      animateType: 1,
                      scaleEnd: 0,
                      seconds: 1500,
                      child: ThemeImagePath(
                        fileName: 'task_unlock_star_01.png',
                        imgWidget: 17.r,
                        imgHeight: 17.r,
                      ),
                    ),
                  ),
                  Positioned(
                    top: 264.h,
                    left: 114.w,
                    child: AnimatedPress(
                      animateType: 1,
                      scaleEnd: 0,
                      seconds: 1700,
                      child: ThemeImagePath(
                        fileName: 'task_unlock_star_02.png',
                        imgWidget: 17.r,
                        imgHeight: 17.r,
                      ),
                    ),
                  ),
                  Positioned(
                    top: 119.h,
                    right: 113.w,
                    child: AnimatedPress(
                      animateType: 1,
                      scaleEnd: 0,
                      seconds: 1900,
                      child: ThemeImagePath(
                        fileName: 'task_unlock_star_02.png',
                        imgWidget: 17.r,
                        imgHeight: 17.r,
                      ),
                    ),
                  ),
                  Positioned(
                    top: 235.h,
                    right: 99.w,
                    child: AnimatedPress(
                      animateType: 1,
                      scaleEnd: 0,
                      seconds: 2100,
                      child: ThemeImagePath(
                        fileName: 'task_unlock_star_02.png',
                        imgWidget: 17.r,
                        imgHeight: 17.r,
                      ),
                    ),
                  ),
                  Positioned(
                    top: 51.h,
                    right: 80.w,
                    child: AnimatedPress(
                      child: Opacity(
                        opacity: 0.5,
                        child: ThemeImagePath(
                          fileName: 'task_unlock_star_02.png',
                          imgWidget: 17.r,
                          imgHeight: 17.r,
                        ),
                      ),
                    ),
                  ),
                  Positioned(
                    top: 107.h,
                    left: 78.w,
                    child: AnimatedPress(
                      child: Opacity(
                        opacity: 0.5,
                        child: ThemeImagePath(
                          fileName: 'task_unlock_star_02.png',
                          imgWidget: 17.r,
                          imgHeight: 17.r,
                        ),
                      ),
                    ),
                  ),
                  Positioned(
                    top: 183.h,
                    right: 65.w,
                    child: AnimatedPress(
                      child: ThemeImagePath(
                        fileName: 'task_unlock_star_02.png',
                        imgWidget: 17.r,
                        imgHeight: 17.r,
                      ),
                    ),
                  ),
                  Positioned(
                    top: 178.h,
                    left: 66.w,
                    child: AnimatedPress(
                      child: ThemeImagePath(
                        fileName: 'task_unlock_star_02.png',
                        imgWidget: 17.r,
                        imgHeight: 17.r,
                      ),
                    ),
                  ),
                  Positioned(
                    top: 19.h,
                    left: 23.w,
                    child: Images(
                      path: skinImage.isEmpty
                          ? R.task_achievement_item_png
                          : fileUrl(skinImage),
                      width: 337.w,
                      height: 276.h,
                    ),
                  ),
                ],
              ),
            ),
            SizedBox(height: 13.h),
            Container(
              width: 200.w,
              height: 48.h,
              decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(24.r),
                  color: const Color.fromRGBO(40, 39, 46, 1)),
              child: Center(
                child: ThemeText(
                  dataStr: "task_achiebement_btn".tr,
                  keyName: 'textColor',
                  fontWeight: FontWeight.w500,
                ),
              ),
            ).inkWell(() {
              Get.back();
              widget.onTap?.call();
            }),
            SizedBox(height: 32.h),
            Images(path: R.task_close_png, width: 34.r, height: 34.r)
                .inkWell(() {
              Get.back();
            }),
          ],
        ),
      ),
    );
  }
}
