import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:getx_xiaopa/extension/images.dart';
import 'package:getx_xiaopa/extension/widget_extension.dart';
import 'package:getx_xiaopa/pages/task/task_achievement/task_achievement_state.dart';
import 'package:getx_xiaopa/utils/utils.dart';
import 'package:getx_xiaopa/widget/widget.dart';

class TaskAchievementItem extends StatefulWidget {
  final int cIndex;
  final AchievementItemType itemType;
  final Function(int cIndex)? onTap;

  const TaskAchievementItem(
      {super.key, required this.itemType, required this.cIndex, this.onTap});

  @override
  State<TaskAchievementItem> createState() => _TaskAchievementItemState();
}

class _TaskAchievementItemState extends State<TaskAchievementItem> {
  @override
  Widget build(BuildContext context) {
    return Container(
      width: 82.w,
      height: 108.h,
      decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(4.r),
          color: Colors.white,
          border: widget.itemType.isSelected
              ? Border.all(
                  color: Get.find<ThemeColorController>().gSkinBorderColor,
                  width: 1.r)
              : Border.all(color: Colors.white, width: 1.r),
          boxShadow: const [
            BoxShadow(
              color: Color.fromRGBO(180, 142, 78, 0.16),
              // 阴影颜色和透明度
              spreadRadius: 0,
              // 阴影扩散范围
              blurRadius: 6,
              // 阴影模糊程度
              offset: Offset(0, 2),
              // 阴影偏移量（水平，垂直）
            )
          ]),
      child: Stack(
        clipBehavior: Clip.none,
        children: [
          Positioned(
            top: 15.h,
            left: 0,
            right: 0,
            child: Images(
              path: widget.itemType.isUnlock
                  ? widget.itemType.lockImage
                  : widget.itemType.unlockImage,
              height: 70.h,
            ),
          ),
          widget.itemType.isUnlock
              ? const SizedBox()
              : Positioned.fill(
                  child: Container(
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(4.r),
                      color: Get.find<ThemeColorController>().gSkinFullBgColor,
                    ),
                  ),
                ),
          widget.itemType.isUnlock
              ? const SizedBox()
              : Positioned(
                  top: -8.h,
                  right: -5.w,
                  child: Container(
                    width: 40.w,
                    height: 16.h,
                    decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(18.r),
                        color: const Color.fromRGBO(40, 39, 46, 1)),
                    child: Center(
                      child: ThemeText(
                        dataStr: "task_achievement_unlock".tr,
                        keyName: 'textColor',
                        fontSize: 10,
                      ),
                    ),
                  ),
                ),
        ],
      ),
    ).inkWell(() => widget.onTap?.call(widget.cIndex));
  }
}
