import 'package:getx_xiaopa/pages/mine/model/task_skin_user_result.dart';
import 'package:getx_xiaopa/pages/task/model/task_skin_result.dart';

class TaskAchievementState {
  String continuous = "0";
  String total = "0";

  List<AchievementItemType> achievementItemList = [];

  List<TaskSkinModel> skinList = [];
  List<TaskSkinUserModel> unlockSkinList = [];

  int cIndex = 0;

  ///如果更好过皮肤，返回上一页的时候要重新加载视图
  bool isSave = false;

  TaskAchievementState() {
    ///Initialize variables
  }
}

class AchievementItemType {
  String id;
  bool isSelected;
  bool isUnlock;
  String lockImage;
  String unlockImage;

  AchievementItemType(
      {this.id = "",
      this.isSelected = false,
      this.isUnlock = false,
      this.lockImage = "",
      this.unlockImage = ""});
}
