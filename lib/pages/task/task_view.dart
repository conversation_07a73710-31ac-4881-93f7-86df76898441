import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:getx_xiaopa/base/base.dart';
import 'package:getx_xiaopa/extension/images.dart';
import 'package:getx_xiaopa/extension/widget_extension.dart';
import 'package:getx_xiaopa/network/http_client.dart';
import 'package:getx_xiaopa/pages/task/task_detail/widget/ribbon_firework.dart';
import 'package:getx_xiaopa/pages/task/widget/drink_item.dart';
import 'package:getx_xiaopa/pages/task/widget/drink_record_item.dart';
import 'package:getx_xiaopa/pages/task/widget/full_widget_track_shape.dart';
import 'package:getx_xiaopa/pages/task/widget/image_slider_thumb.dart';
import 'package:getx_xiaopa/pages/task/widget/water_fill_widget_v3.dart';
import 'package:getx_xiaopa/r.dart';
import 'package:getx_xiaopa/storage/storage.dart';
import 'package:getx_xiaopa/utils/utils.dart';
import 'package:getx_xiaopa/values/values.dart';
import 'package:getx_xiaopa/widget/widget.dart';

import 'task_logic.dart';
import 'task_state.dart';

class TaskPage extends BaseCommonView {
  TaskPage({super.key});

  final TaskLogic logic = Get.put(TaskLogic());
  final TaskState state = Get.find<TaskLogic>().state;

  @override
  bool get customWidget => true;

  @override
  Widget buildContent() {
    return GetBuilder<TaskLogic>(
      id: "task_view",
      builder: (_) {
        return createCommonView(logic, (_) {
          return Scaffold(
            backgroundColor: Colors.white,
            extendBodyBehindAppBar: true,
            appBar: AppBar(
              backgroundColor: state.navColor,
              centerTitle: true,
              title: Text(
                "task_title".tr,
                style: StyleConfig.blackStyle(
                    fontSize: 18, fontWeight: FontWeight.w500),
              ),
              systemOverlayStyle: const SystemUiOverlayStyle(
                statusBarColor: Colors.transparent,
                statusBarIconBrightness: Brightness.dark,
              ),
              elevation: 0,
              scrolledUnderElevation: 0,
              leadingWidth: 35.w,
              leading: Padding(
                padding: EdgeInsets.only(left: 15.w),
                child: Images(
                  path: R.back_png,
                  width: 20.w,
                  height: 20.h,
                  scale: 2.8,
                ).inkWell(() => Get.back()),
              ),
            ),
            body: Container(
              margin:
                  EdgeInsets.only(bottom: ScreenUtil().bottomBarHeight + 20.h),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    Get.find<ThemeColorController>().gGradientStar,
                    Get.find<ThemeColorController>().gGradientEnd,
                  ],
                ),
              ),
              child: CustomScrollView(
                controller: state.scrollController,
                slivers: <Widget>[
                  SliverToBoxAdapter(
                    child: Container(
                      margin:
                          EdgeInsets.only(top: 102.h, left: 24.w, right: 24.w),
                      child: Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          ThemeContainerImage(
                            fileName: 'task_calendar.png',
                            padding: EdgeInsets.only(top: 16.h, bottom: 8.h),
                            conWidget: 44.w,
                            conHeight: 46.h,
                            alignment: Alignment.center,
                            child: Text(
                              logic.getNowDate(isDay: true),
                              style: StyleConfig.otherStyle(
                                  color: const Color.fromRGBO(69, 79, 106, 1),
                                  fontSize: 14,
                                  fontWeight: FontWeight.w600),
                            ),
                          ).inkWell(() => logic.toDetailPage()),
                          const Expanded(child: SizedBox()),
                          Container(
                            margin: EdgeInsets.only(top: 18.h),
                            child: RichText(
                              text: TextSpan(children: [
                                TextSpan(
                                    text: '${state.cupSchedule.toInt()}',
                                    style: StyleConfig.otherStyle(
                                        color: Get.find<ThemeColorController>()
                                            .gCupSchedule,
                                        fontSize: 36,
                                        fontWeight: FontWeight.w700)),
                                TextSpan(
                                  text: 'task_unit'.tr,
                                  style: StyleConfig.otherStyle(
                                      color: Get.find<ThemeColorController>()
                                          .gCupSchedule,
                                      fontSize: 14),
                                ),
                              ]),
                            ),
                          ),
                          const Expanded(child: SizedBox()),
                          ThemeImagePath(
                            fileName: 'task_image_04.png',
                            imgWidget: 30.r,
                            imgHeight: 30.r,
                          ).inkWell(() => logic.toSettingpage())
                        ],
                      ),
                    ),
                  ),
                  SliverToBoxAdapter(
                    child: Row(
                      children: [
                        SizedBox(width: 56.w),
                        SizedBox(
                          width: 105.w,
                          child: Row(
                            children: [
                              Text(
                                "task_today_complete".tr,
                                style: StyleConfig.otherStyle(
                                    color: ColorConfig.shopDetailTextColor,
                                    fontSize: 14),
                              ),
                              SizedBox(width: 11.w),
                              Text(
                                "${state.completeSchedule}%",
                                style: StyleConfig.otherStyle(
                                    color: ColorConfig.searchTextColor,
                                    fontWeight: FontWeight.w600,
                                    fontSize: 14),
                              ),
                            ],
                          ),
                        ),
                        SizedBox(width: 32.w),
                        Container(
                            width: 1.w,
                            height: 18.h,
                            color: ColorConfig.shopDetailTextColor),
                        SizedBox(width: 40.w),
                        Text(
                          "task_target".tr,
                          style: StyleConfig.otherStyle(
                              color: ColorConfig.shopDetailTextColor,
                              fontSize: 14),
                        ),
                        SizedBox(width: 6.w),
                        Text(
                          "${state.maxCupSchedule.toInt()}${"task_unit".tr}",
                          style: StyleConfig.otherStyle(
                              color: ColorConfig.searchTextColor,
                              fontWeight: FontWeight.w600,
                              fontSize: 14),
                        ),
                        SizedBox(width: 3.w),
                        ThemeImagePath(
                          fileName: 'task_image_03.png',
                          imgWidget: 10.w,
                          imgHeight: 11.h,
                        )
                      ],
                    ).inkWell(() => logic.drinkCountSheet()),
                  ),
                  SliverToBoxAdapter(
                    child: Container(
                      margin:
                          EdgeInsets.only(top: 19.h, left: 39.w, right: 39.w),
                      padding: EdgeInsets.symmetric(horizontal: 10.w),
                      width: 297.w,
                      height: 46.h,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(100.r),
                        color: Colors.white,
                        boxShadow: [
                          BoxShadow(
                            color: Get.find<ThemeColorController>()
                                .gSliderBoxShadowColor,
                            // 阴影颜色和透明度
                            spreadRadius: 0,
                            // 阴影扩散范围
                            blurRadius: 3,
                            // 阴影模糊程度
                            offset: const Offset(0, 1),
                            // 阴影偏移量（水平，垂直）
                          )
                        ],
                      ),
                      child: SliderTheme(
                        data: SliderTheme.of(Get.context!).copyWith(
                            trackHeight: 26.h,
                            inactiveTrackColor: Get.find<ThemeColorController>()
                                .gSliderInactiveColor,
                            activeTrackColor: Get.find<ThemeColorController>()
                                .gSliderActiveColor,
                            trackShape: const FullWidthTrackShape(),
                            thumbShape: state.isInit
                                ? ImageSliderThumb(
                                    image: state.imageInfo,
                                    size: Size(49.w, 49.h))
                                : null),
                        child: Slider(
                          value: state.cupSliderSchedule,
                          min: 0,
                          max: state.maxCupSchedule.toDouble(),
                          onChanged: (double value) {},
                        ),
                      ),
                    ),
                  ),
                  SliverToBoxAdapter(
                    child: Container(
                      margin: EdgeInsets.only(top: 23.h),
                      alignment: Alignment.topCenter,
                      child: IntrinsicWidth(
                        child: ThemeContainerImage(
                          fileName: 'task_bubble_bg.png',
                          fit: BoxFit.fill,
                          padding: EdgeInsets.only(
                              left: 15.w, right: 15.w, top: 10.h, bottom: 25.h),
                          // conHeight: 47.h,
                          alignment: Alignment.center,
                          child: Text(
                            state.drinkTipStr,
                            style: StyleConfig.otherStyle(
                                color: ColorConfig.searchTextColor,
                                fontWeight: FontWeight.w500,
                                fontSize: 12),
                          ),
                        ),
                      ),
                    ),
                  ),
                  state.isDrinkSuccess
                      ? SliverToBoxAdapter(
                          child: SizedBox(
                            width: 1.sw,
                            height: 289.h,
                            child: Stack(
                              alignment: Alignment.center,
                              children: [
                                PersistentRibbonFirework(
                                  width: 1.sw,
                                  height: 289.h,
                                  controller: state.rController,
                                  particlesPerBurst: 40,
                                  imagePaths: const [
                                    R.confetti_1_png,
                                    R.confetti_2_png,
                                    R.confetti_3_png,
                                    R.confetti_4_png,
                                    R.confetti_5_png,
                                  ],
                                ),
                                Images(
                                  path: fileUrl(UserStore.to.skinPicture),
                                  width: 375.w,
                                  height: 260.h,
                                )
                              ],
                            ),
                          ),
                        )
                      : SliverToBoxAdapter(
                          child: Container(
                            margin: EdgeInsets.only(left: 72.w, right: 72.w),
                            width: 239.w,
                            height: 289.h,
                            child: state.isInit
                                ? WaterFillWidgetV3(
                                    currentCapacity: state.cupSchedule,
                                    maxCapacity:
                                        state.maxCupSchedule.toDouble(),
                                    imagePath: UserStore.to.contour,
                                  )
                                : const SizedBox(),
                          ),
                        ),
                  SliverToBoxAdapter(
                    child: Container(
                      margin:
                          EdgeInsets.only(top: 18.h, left: 15.w, right: 15.w),
                      height: 92.h,
                      child: Row(
                        children: [
                          Offstage(
                            offstage: !state.isDrinkSuccess,
                            child: ThemeContainerImage(
                              fileName: 'task_drink_success.png',
                              margin:
                                  EdgeInsets.only(right: 10.w, bottom: 10.h),
                              padding: EdgeInsets.only(
                                  top: 20.h, left: 75.w, right: 15.w),
                              conWidget: 143.w,
                              conHeight: 75.h,
                              child: ThemeText(
                                dataStr: "task_share_btn".tr,
                                keyName: "textColor",
                                fontSize: 12,
                                fontWeight: FontWeight.w500,
                                textAlign: TextAlign.center,
                              ),
                            ).inkWell(() => logic.toSharePage()),
                          ),
                          Expanded(
                            child: ListView.builder(
                              scrollDirection: Axis.horizontal,
                              itemCount: state.taskTypeList.length,
                              itemBuilder: (_, index) {
                                return DrinkItem(
                                  index: index,
                                  taskTypeModel: state.taskTypeList[index],
                                  onTap: (cIndex) {
                                    logic.drinkAction(cIndex);
                                  },
                                );
                              },
                            ),
                          )
                        ],
                      ),
                    ),
                  ),
                  SliverToBoxAdapter(
                    child: Container(
                      margin:
                          EdgeInsets.only(left: 21.w, top: 20.h, right: 21.w),
                      alignment: Alignment.centerLeft,
                      child: Text(
                        "task_drink_time".tr,
                        style: StyleConfig.otherStyle(
                            color: const Color.fromRGBO(69, 79, 106, 1),
                            fontWeight: FontWeight.w600),
                      ),
                    ),
                  ),
                  SliverList(
                    delegate: SliverChildBuilderDelegate(
                      (context, index) => DrinkRecordItem(
                        taskItem: state.taskItem[index],
                      ),
                      childCount: state.taskItem.length, // 控制列表数量
                    ),
                  ),
                ],
              ),
            ),
          );
        }, initBuilder: () => const LoadStatusWidget());
      },
    );
  }
}
