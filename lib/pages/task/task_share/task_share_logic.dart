import 'dart:io';
import 'dart:math';
import 'dart:ui' as ui;

import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:getx_xiaopa/extension/widget_extension.dart';
import 'package:getx_xiaopa/network/http_client.dart';
import 'package:getx_xiaopa/network/model/request_body.dart';
import 'package:getx_xiaopa/network/model/result.dart';
import 'package:getx_xiaopa/storage/storage.dart';
import 'package:getx_xiaopa/utils/utils.dart';
import 'package:image_gallery_saver/image_gallery_saver.dart';
import 'package:path_provider/path_provider.dart';
import 'package:permission_handler/permission_handler.dart';

import 'task_share_state.dart';

class TaskShareLogic extends GetxController {
  final TaskShareState state = TaskShareState();

  @override
  void onInit() async {
    super.onInit();
    state.shareType = Get.arguments["shareType"];
    state.continuous = Get.arguments["continuous"];
    // 预先请求权限，避免首次保存时的卡顿
    if (Platform.isIOS) {
      await Permission.photos.request();
    }

    WidgetsBinding.instance.addPostFrameCallback((_) async {
      await _getTaskItems();

      state.imgData = await _getImageData();
      state.imageUri = await uint8ListToUri(state.imgData!);
      state.imageUri = state.imageUri.substring(7, state.imageUri.length);

      state.isInit = true;

      update(["task_share_view"]);
    });
  }

  shareAction(index) async {
    /// 保存本地操作
    if (index == 0) {
      PermissionUtil.storage(Get.context!, action: () async {
        CToast.showLoading();
        bool flag = await _doSaveImage(state.imgData);
        CToast.dismiss();
        if (flag) {
          CToast.showToast("diary_share_save_success".tr);
        } else {
          CToast.showToast("diary_share_save_fail".tr);
        }
      });
    }

    /// 微信分享
    if (index == 1) {
      bool re = await WxUtil.isInstalledWx();
      if (re) {
        WxUtil.shareSession(state.imageUri);
      } else {
        CToast.showToast("diary_share_no_wechat".tr);
      }
    }

    ///小红书分享
    if (index == 2) {
      if (XhsUtil.isInstall) {
        bool re = await XhsUtil.isSupportShareNote();
        if (re) {
          XhsUtil.shareXhs("", "", state.imageUri);
        } else {
          CToast.showToast("diary_share_no_xhs".tr);
        }
      } else {
        CToast.showToast("diary_share_no_xhs_sdk".tr);
      }
    }

    /// 朋友圈分享
    if (index == 3) {
      bool re = await WxUtil.isInstalledWx();
      if (re) {
        WxUtil.shareTimeLine(state.imageUri);
      } else {
        CToast.showToast("diary_share_no_wechat".tr);
      }
    }
  }

  Future<String> uint8ListToUri(Uint8List data,
      {String fileName = "temp.png"}) async {
    final tempDir = await getTemporaryDirectory();
    final file = File('${tempDir.path}/$fileName');
    await file.writeAsBytes(data);
    return file.uri.toString();
  }

  Future<bool> _doSaveImage(imgData) async {
    bool flag = false;
    if (imgData != null) {
      final result = await ImageGallerySaver.saveImage(imgData,
          isReturnImagePathOfIOS: true);
      flag = result["isSuccess"];
    }
    return flag;
  }

  Future<Uint8List?> _getImageData() async {
    CToast.showLoading();
    try {
      BuildContext? buildContext = state.repaintKey.currentContext;

      if (buildContext == null) return null;

      final renderObject = buildContext.findRenderObject();
      if (renderObject is! RenderRepaintBoundary) return null;
      final boundary = renderObject;

      // 等待下一帧绘制完成，确保内容都已经渲染
      await Future.delayed(const Duration(milliseconds: 500));
      await WidgetsBinding.instance.endOfFrame;

      final size = boundary.size;

      // 获取设备像素比
      // ignore: deprecated_member_use
      double dpr = ui.window.devicePixelRatio;

      // 计算一个使宽高都是整数像素的 pixelRatio（向下取整）
      final pixelRatioX = (size.width * dpr).floorToDouble() / size.width;
      final pixelRatioY = (size.height * dpr).floorToDouble() / size.height;
      final fixedPixelRatio = min(pixelRatioX, pixelRatioY);

      // 截图
      final image = await boundary.toImage(pixelRatio: fixedPixelRatio);
      final byteData = await image.toByteData(format: ui.ImageByteFormat.png);

      return byteData?.buffer.asUint8List();
    } catch (e, stack) {
      logD('截图失败: $e\n$stack');
      return null;
    } finally {
      CToast.dismiss();
    }
  }

  /// ==========================网络请求==========================
  ///任务列表（获取喝水战绩）
  _getTaskItems() async {
    var map = RequestBody(pagination: true, filters: [
      Filter(
          expr: "=",
          name: "device_id",
          value: UserStore.to.deviceList[UserStore.to.selectDevice].id)
    ]).toJson();
    Result result = await http.taskItems(map);
    if (result.code == 0) {
      state.total = result.data.count.toString();
    } else {
      CToast.showToast(result.msg!);
    }
    update(["task_share_view"]);
  }
}
