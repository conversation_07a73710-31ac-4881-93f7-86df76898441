import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:getx_xiaopa/base/base.dart';
import 'package:getx_xiaopa/extension/images.dart';
import 'package:getx_xiaopa/extension/widget_extension.dart';
import 'package:getx_xiaopa/network/http_client.dart';
import 'package:getx_xiaopa/r.dart';
import 'package:getx_xiaopa/storage/storage.dart';
import 'package:getx_xiaopa/utils/utils.dart';
import 'package:getx_xiaopa/values/values.dart';
import 'package:getx_xiaopa/widget/widget.dart';
import 'package:qr_flutter/qr_flutter.dart';

import 'task_share_logic.dart';
import 'task_share_state.dart';

class TaskSharePage extends BaseCommonView {
  TaskSharePage({super.key});

  final TaskShareLogic logic = Get.put(TaskShareLogic());
  final TaskShareState state = Get.find<TaskShareLogic>().state;

  @override
  Color? get navColor => Colors.transparent;

  @override
  TextStyle? get navTitleStyle =>
      StyleConfig.blackStyle(fontSize: 18, fontWeight: FontWeight.w500);

  @override
  double? get leftWidth => 35.w;

  @override
  Widget? get leftButton => Padding(
        padding: EdgeInsets.only(left: 15.w),
        child: Images(
          path: R.back_png,
          width: 20.w,
          height: 20.h,
          scale: 2.8,
        ).inkWell(() {
          CToast.dismiss();
          Get.back();
        }),
      );

  @override
  bool? get extendBodyBehindAppBar => true;

  String _bgColorImage() {
    String imagePath = "";
    switch (state.shareType) {
      case 1:
        imagePath = Get.find<ThemeImageController>().taskShareBg01;
        break;
      case 2:
        imagePath = Get.find<ThemeImageController>().taskShareBg02;
        break;
    }
    return imagePath;
  }

  Widget? _buildShareWidget() {
    Widget tempWidget = Container();
    switch (state.shareType) {
      case 1:
        tempWidget = Container(
          width: 300.w,
          height: 400.h,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(state.isInit ? 14.r : 0.r),
            color: Colors.transparent,
            image: const DecorationImage(
                image: AssetImage(R.task_share_image_01_png), fit: BoxFit.fill),
          ),
          clipBehavior: Clip.hardEdge,
          child: Stack(
            children: [
              Positioned(
                top: 129.h,
                left: 28.w,
                child: Images(
                  path: fileUrl(UserStore.to.skinPicture.isEmpty
                      ? R.task_share_01_image_png
                      : UserStore.to.skinPicture),
                  width: 257.w,
                  height: 211.h,
                ),
              ),
              ConfigStore.to.getLocaleCode() == "zh"
                  ? Positioned(
                      bottom: 25.h,
                      left: 72.w,
                      child: Row(
                        children: [
                          Container(
                            margin: EdgeInsets.only(top: 3.h),
                            child: ThemeText(
                              dataStr: "task_share_one".tr,
                              keyName: 'textColor',
                              fontWeight: FontWeight.w700,
                              fontSize: 10,
                            ),
                          ),
                          SizedBox(width: 2.w),
                          ThemeText(
                            dataStr: state.continuous,
                            keyName: 'textColor',
                            fontWeight: FontWeight.w700,
                          ),
                          SizedBox(width: 2.w),
                          Container(
                            margin: EdgeInsets.only(top: 3.h),
                            child: ThemeText(
                              dataStr: "task_detail_day".tr,
                              keyName: 'textColor',
                              fontWeight: FontWeight.w700,
                              fontSize: 10,
                            ),
                          ),
                          SizedBox(width: 16.w),
                          Container(
                            margin: EdgeInsets.only(top: 3.h),
                            child: ThemeText(
                              dataStr: "task_share_one".tr,
                              keyName: 'textColor',
                              fontWeight: FontWeight.w400,
                              fontSize: 10,
                            ),
                          ),
                          SizedBox(width: 2.w),
                          ThemeText(
                            dataStr: state.total,
                            keyName: 'textColor',
                            fontWeight: FontWeight.w700,
                          ),
                          SizedBox(width: 2.w),
                          Container(
                            margin: EdgeInsets.only(top: 3.h),
                            child: ThemeText(
                              dataStr: "task_unit".tr,
                              keyName: 'textColor',
                              fontWeight: FontWeight.w400,
                              fontSize: 10,
                            ),
                          ),
                        ],
                      ),
                    )
                  : Positioned(
                      bottom: 25.h,
                      left: 86.w,
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            crossAxisAlignment: CrossAxisAlignment.end,
                            children: [
                              ThemeText(
                                dataStr: "task_share_one".tr,
                                keyName: 'textColor',
                                fontWeight: FontWeight.w700,
                                fontSize: 10,
                                height: 1,
                              ),
                              SizedBox(width: 3.w),
                              ThemeText(
                                dataStr: state.continuous,
                                keyName: 'textColor',
                                fontWeight: FontWeight.w700,
                                height: 1,
                              ),
                              SizedBox(width: 3.w),
                              ThemeText(
                                dataStr: "task_detail_day".tr,
                                keyName: 'textColor',
                                fontWeight: FontWeight.w700,
                                fontSize: 10,
                                height: 1,
                              ),
                            ],
                          ),
                          Row(
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                              Container(
                                margin: EdgeInsets.only(top: 5.h),
                                child: ThemeText(
                                  dataStr: "task_share_two".tr,
                                  keyName: 'textColor',
                                  fontWeight: FontWeight.w700,
                                  fontSize: 10,
                                  height: 1,
                                ),
                              ),
                              SizedBox(width: 2.w),
                              ThemeText(
                                dataStr: state.total,
                                keyName: 'textColor',
                                fontWeight: FontWeight.w700,
                                height: 1,
                              ),
                              SizedBox(width: 2.w),
                            ],
                          )
                        ],
                      ),
                    ),
              Positioned(
                top: 10.h,
                right: 10.w,
                child: Container(
                  decoration: BoxDecoration(
                      border: Border.all(width: 1.r, color: Colors.white),
                      borderRadius: BorderRadius.circular(4.r),
                      color: Colors.white),
                  padding: EdgeInsets.all(5.r),
                  child: QrImageView(
                    data: ConfigStore.to.officialDownUrl,
                    size: 27,
                    backgroundColor: Colors.white,
                    padding: EdgeInsets.zero,
                  ),
                ),
              )
            ],
          ),
        );
        break;
      case 2:
        tempWidget = ThemeContainerImage(
          fileName:
              'task_share_02_image_01_${ConfigStore.to.getLocaleCode()}.png',
          conWidget: 300.w,
          conHeight: 400.w,
          fit: BoxFit.fill,
          clipBehavior: Clip.hardEdge,
          borderRadius: BorderRadius.circular(state.isInit ? 14.r : 0.r),
          child: Stack(
            children: [
              ConfigStore.to.getLocaleCode() == "zh"
                  ? Positioned(
                      top: 22.h,
                      left: 31.w,
                      child: Row(
                        children: [
                          Container(
                            margin: EdgeInsets.only(top: 2.h),
                            child: Text(
                              "task_share_one".tr,
                              style: StyleConfig.otherStyle(
                                  color: Colors.white,
                                  fontWeight: FontWeight.w700,
                                  fontSize: 8),
                            ),
                          ),
                          SizedBox(width: 2.w),
                          Text(
                            state.continuous,
                            style: StyleConfig.otherStyle(
                                color: Colors.white,
                                fontWeight: FontWeight.w700,
                                fontSize: 14),
                          ),
                          SizedBox(width: 2.w),
                          Container(
                            margin: EdgeInsets.only(top: 2.h),
                            child: Text(
                              "task_detail_day".tr,
                              style: StyleConfig.otherStyle(
                                  color: Colors.white,
                                  fontWeight: FontWeight.w700,
                                  fontSize: 8),
                            ),
                          ),
                          SizedBox(width: 8.w),
                          Container(
                            margin: EdgeInsets.only(top: 2.h),
                            child: Text(
                              "task_share_one".tr,
                              style: StyleConfig.otherStyle(
                                  color: Colors.white,
                                  fontWeight: FontWeight.w700,
                                  fontSize: 8),
                            ),
                          ),
                          SizedBox(width: 2.w),
                          Text(
                            state.total,
                            style: StyleConfig.otherStyle(
                                color: Colors.white,
                                fontWeight: FontWeight.w700,
                                fontSize: 14),
                          ),
                          SizedBox(width: 2.w),
                          Container(
                            margin: EdgeInsets.only(top: 2.h),
                            child: Text(
                              "task_unit".tr,
                              style: StyleConfig.otherStyle(
                                  color: Colors.white,
                                  fontWeight: FontWeight.w400,
                                  fontSize: 8),
                            ),
                          ),
                        ],
                      ),
                    )
                  : Positioned(
                      top: 22.h,
                      left: 28.w,
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              SizedBox(width: 11.w),
                              Text(
                                "task_share_one".tr,
                                style: StyleConfig.otherStyle(
                                    color: Colors.white,
                                    fontWeight: FontWeight.w700,
                                    fontSize: 10,
                                    height: 1),
                              ),
                              SizedBox(width: 3.w),
                              Text(
                                state.continuous,
                                style: StyleConfig.otherStyle(
                                    color: Colors.white,
                                    fontWeight: FontWeight.w700,
                                    fontSize: 14,
                                    height: 1),
                              ),
                              SizedBox(width: 3.w),
                              Text(
                                "task_detail_day".tr,
                                style: StyleConfig.otherStyle(
                                    color: Colors.white,
                                    fontWeight: FontWeight.w700,
                                    fontSize: 10,
                                    height: 1),
                              ),
                            ],
                          ),
                          Row(
                            children: [
                              Container(
                                margin: EdgeInsets.only(top: 2.h),
                                child: Text(
                                  "task_share_two".tr,
                                  style: StyleConfig.otherStyle(
                                      color: Colors.white,
                                      fontWeight: FontWeight.w700,
                                      fontSize: 10,
                                      height: 1),
                                ),
                              ),
                              SizedBox(width: 2.w),
                              Text(
                                state.total,
                                style: StyleConfig.otherStyle(
                                    color: Colors.white,
                                    fontWeight: FontWeight.w700,
                                    fontSize: 14,
                                    height: 1),
                              ),
                            ],
                          )
                        ],
                      ),
                    ),
              Positioned(
                top: 128.h,
                left: 0.w,
                child: Images(
                  path: R.task_share_02_image_04_png,
                  width: 300.w,
                  height: 220.h,
                ),
              ),
              Positioned(
                  top: 115.h,
                  left: 207.w,
                  child: Images(
                    path: R.task_share_02_image_02_png,
                    width: 42.w,
                    height: 47.h,
                  )),
              Positioned(
                top: 248.h,
                left: ConfigStore.to.getLocaleCode() == "zh" ? 193.w : 170.w,
                child: ThemeImagePath(
                  fileName:
                      'task_share_02_image_03_${ConfigStore.to.getLocaleCode()}.png',
                  imgWidget:
                      ConfigStore.to.getLocaleCode() == "zh" ? 78.w : 116.w,
                  imgHeight: 80.h,
                ),
              ),
              Positioned(
                bottom: 13.h,
                left: 16.w,
                child: QrImageView(
                  data: ConfigStore.to.downUrl,
                  size: 36,
                  backgroundColor: Colors.white,
                  padding: EdgeInsets.zero,
                ),
              )
            ],
          ),
        );
        break;
      default:
    }
    return tempWidget;
  }

  @override
  Widget buildContent() {
    return GetBuilder<TaskShareLogic>(
      id: "task_share_view",
      builder: (_) {
        return Container(
          width: 1.sw,
          height: 1.sh,
          decoration: BoxDecoration(
            image: DecorationImage(
                image: AssetImage(_bgColorImage()), fit: BoxFit.fill),
          ),
          child: Column(
            children: [
              Container(
                width: 300.w,
                height: 400.h,
                margin: EdgeInsets.only(top: 132.h),
                child: Stack(
                  children: [
                    RepaintBoundary(
                      key: state.repaintKey,
                      child: _buildShareWidget(),
                    ),
                    Positioned(
                      width: 300.w,
                      height: 400.h,
                      child: state.isInit
                          ? const ThemeImagePath(
                              fileName: 'task_share_kuang.png',
                              fit: BoxFit.fill,
                            )
                          : const SizedBox(),
                    )
                  ],
                ),
              ),
              Container(
                margin: EdgeInsets.only(top: 55.h),
                width: 200.w,
                height: 48.h,
                decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(24.r),
                    color: const Color.fromRGBO(40, 39, 46, 1)),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    ThemeImagePath(
                      fileName: 'task_share_friends.png',
                      imgWidget: 20.r,
                      imgHeight: 20.r,
                    ),
                    SizedBox(width: 5.w),
                    ThemeText(
                      dataStr: "diary_share_btn".tr,
                      keyName: "textColor",
                      fontWeight: FontWeight.w500,
                    )
                  ],
                ),
              ).inkWell(() => logic.shareAction(3)),
              Container(
                margin: EdgeInsets.only(top: 23.h, left: 40.w, right: 40.w),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceAround,
                  children: List.generate(state.shareList.length, (index) {
                    return Column(
                      children: [
                        Images(
                          path: state.shareList[index].imagePath,
                          width: 40.r,
                          height: 40.r,
                        ),
                        SizedBox(height: 6.h),
                        Text(
                          state.shareList[index].tip,
                          style: StyleConfig.otherStyle(
                              color: ColorConfig.authenticationTextColor,
                              fontSize: 12),
                        )
                      ],
                    ).inkWell(() => logic.shareAction(index));
                  }),
                ),
              )
            ],
          ),
        );
      },
    );
  }
}
