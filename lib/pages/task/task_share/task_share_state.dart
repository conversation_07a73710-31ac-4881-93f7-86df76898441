import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:getx_xiaopa/r.dart';

class TaskShareState {
  ///分享的类型 1=听牌系列 2=打工系列
  int shareType = 1;

  String continuous = "0";
  String total = "0";

  GlobalKey repaintKey = GlobalKey();

  List<ShareType> shareList = [
    ShareType(tip: "diary_share_save".tr, imagePath: R.task_share_save_png),
    ShareType(tip: "diary_share_wechat".tr, imagePath: R.task_share_wx_png),
    // ShareType(tip: "QQ", imagePath: R.task_share_qq_png),
    // ShareType(tip: "微博", imagePath: R.task_share_wb_png),
    ShareType(tip: "diary_share_xhs".tr, imagePath: R.task_share_xhs_png),
  ];

  late Uint8List? imgData;
  late String imageUri;
  bool isInit = false;

  TaskShareState() {
    ///Initialize variables
  }
}

class ShareType {
  final String tip;
  final String imagePath;

  ShareType({this.tip = "", this.imagePath = ""});
}
