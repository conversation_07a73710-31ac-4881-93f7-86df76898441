import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:getx_xiaopa/base/base.dart';
import 'package:getx_xiaopa/extension/images.dart';
import 'package:getx_xiaopa/extension/widget_extension.dart';
import 'package:getx_xiaopa/network/http_client.dart';
import 'package:getx_xiaopa/pages/task/task_detail/widget/drink_chart_widget.dart';
import 'package:getx_xiaopa/pages/task/task_detail/widget/drink_calendar_widget.dart';
import 'package:getx_xiaopa/pages/task/task_detail/widget/ribbon_firework.dart';
import 'package:getx_xiaopa/pages/task/widget/water_fill_widget_v3.dart';
import 'package:getx_xiaopa/r.dart';
import 'package:getx_xiaopa/storage/storage.dart';
import 'package:getx_xiaopa/utils/utils.dart';
import 'package:getx_xiaopa/values/values.dart';
import 'package:getx_xiaopa/widget/widget.dart';

import 'task_detail_logic.dart';
import 'task_detail_state.dart';

class TaskDetailPage extends BaseCommonView {
  TaskDetailPage({super.key});

  final TaskDetailLogic logic = Get.put(TaskDetailLogic());
  final TaskDetailState state = Get.find<TaskDetailLogic>().state;

  @override
  bool get customWidget => true;

  @override
  Widget buildContent() {
    return GetBuilder<TaskDetailLogic>(
      id: "task_detail_view",
      builder: (_) {
        return PopScope(
          canPop: false,
          onPopInvokedWithResult: (didPop, result) {
            if (!didPop) {
              Get.back(result: state.isBackReLoad);
            }
          },
          child: Scaffold(
            backgroundColor: const Color.fromRGBO(249, 249, 249, 1),
            extendBodyBehindAppBar: true,
            appBar: AppBar(
              scrolledUnderElevation: 0,
              elevation: 0,
              backgroundColor: state.navColor,
              leadingWidth: 35.w,
              leading: Padding(
                padding: EdgeInsets.only(left: 15.w),
                child: Images(
                  path: R.back_png,
                  width: 20.w,
                  height: 20.h,
                  scale: 2.8,
                ).inkWell(() => Get.back(result: state.isBackReLoad)),
              ),
              systemOverlayStyle: const SystemUiOverlayStyle(
                statusBarColor: Colors.transparent,
                statusBarIconBrightness: Brightness.dark,
              ),
            ),
            body: SingleChildScrollView(
              physics: const ClampingScrollPhysics(),
              controller: state.sController,
              child: Column(
                children: [
                  ThemeContainerImage(
                    fileName: 'task_drink_detail_bg.png',
                    fit: BoxFit.fill,
                    padding: EdgeInsets.only(top: 87.h),
                    conWidget: 1.sw,
                    child: Column(
                      children: [
                        Row(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            ThemeContainerImage(
                              fileName: 'task_detail_bg.png',
                              margin: EdgeInsets.only(top: 12.h, left: 111.w),
                              conWidget: 154.w,
                              conHeight: 100.h,
                              child: Column(
                                children: [
                                  Row(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      Container(
                                        margin: EdgeInsets.only(
                                            top: 15.h, left: 10.w),
                                        child: Text(
                                          state.continuous,
                                          style: StyleConfig.witheStyle(
                                              fontSize: 30,
                                              fontWeight: FontWeight.w700),
                                        ),
                                      ),
                                      SizedBox(width: 3.w),
                                      Container(
                                        margin: EdgeInsets.only(top: 25.h),
                                        child: Text(
                                          "task_achievement_unit".tr,
                                          style: StyleConfig.witheStyle(
                                              fontSize: 12,
                                              fontWeight: FontWeight.w400),
                                        ),
                                      ),
                                    ],
                                  ),
                                  Container(
                                    margin: EdgeInsets.only(top: 0.h),
                                    child: Text(
                                      "task_detail_tips_one".tr,
                                      style: StyleConfig.witheStyle(
                                          fontSize: 12,
                                          fontWeight: FontWeight.w600),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            Container(
                              margin: EdgeInsets.only(left: 43.w),
                              child: ThemeImagePath(
                                fileName:
                                    'task_detail_image_${ConfigStore.to.getLocaleCode()}.png',
                                imgWidget: 50.w,
                                imgHeight: 54.h,
                              ),
                            ).inkWell(() => logic.toAchievementPage()),
                          ],
                        ),
                        Container(
                          margin: EdgeInsets.only(top: 32.h),
                          width: 335.w,
                          height: 222.h,
                          decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(10.r),
                              color: Colors.white),
                          child: DrinkChartWidget(
                            chartData: state.chartData,
                            controller: state.dController,
                            onTabChanged: (index) {
                              logic.onTabChanged(index);
                            },
                          ),
                        ),
                      ],
                    ),
                  ),
                  Container(
                    margin: EdgeInsets.only(top: 28.h, left: 34.w, right: 34.w),
                    child: DrinkCalendarWidget(
                      completedDates: state.completedDates,
                      onDaySelected: (date) {
                        logic.onDaySelected(date);
                      },
                    ),
                  ),
                  if (state.isDrinkSuccess) ...{
                    Container(
                      margin: EdgeInsets.only(top: 39.h),
                      width: 1.sw,
                      height: 340.h,
                      child: Stack(
                        alignment: Alignment.center,
                        children: [
                          PersistentRibbonFirework(
                            width: 1.sw,
                            height: 340.h,
                            controller: state.rController,
                            imagePaths: const [
                              R.confetti_1_png,
                              R.confetti_2_png,
                              R.confetti_3_png,
                              R.confetti_4_png,
                              R.confetti_5_png,
                            ],
                          ),
                          Column(
                            children: [
                              Text(
                                logic.getDateString(DateTime.now()),
                                style: StyleConfig.otherStyle(
                                    color: ColorConfig.searchTextColor,
                                    fontWeight: FontWeight.w500,
                                    fontSize: 16),
                              ),
                              SizedBox(height: 6.h),
                              Container(
                                margin:
                                    EdgeInsets.only(left: 15.w, right: 15.w),
                                child: Text(
                                  "${logic.getWeekdayString(DateTime.now())}：${state.drinkTip}",
                                  textAlign: TextAlign.center,
                                  style: StyleConfig.otherStyle(
                                      color: Get.find<ThemeColorController>()
                                          .gWaterTips,
                                      fontWeight: FontWeight.w500,
                                      fontSize: 12),
                                ),
                              ),
                              SizedBox(height: 6.h),
                              Images(
                                path: fileUrl(UserStore.to.skinPicture),
                                width: 375.w,
                                height: 260.h,
                              )
                            ],
                          ),
                        ],
                      ),
                    ),
                    Container(
                      margin: EdgeInsets.only(top: 0.h, bottom: 20.h),
                      width: 200.w,
                      height: 48.h,
                      decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(24.r),
                          color: const Color.fromRGBO(40, 39, 46, 1)),
                      child: Row(
                        children: [
                          SizedBox(width: 7.w),
                          ThemeImagePath(
                            fileName: 'task_drink_detail_image.png',
                            imgWidget: 67.w,
                            imgHeight: 29.h,
                          ),
                          SizedBox(width: 5.w),
                          ThemeText(
                            dataStr: 'task_detail_tips_two'.tr,
                            keyName: 'textColor',
                            fontWeight: FontWeight.w500,
                          )
                        ],
                      ),
                    ).inkWell(() => logic.toSharePage())
                  } else ...{
                    SizedBox(height: 10.h),
                    Text(
                      logic.getDateString(DateTime.now()),
                      style: StyleConfig.otherStyle(
                          color: ColorConfig.searchTextColor,
                          fontWeight: FontWeight.w500,
                          fontSize: 16),
                    ),
                    SizedBox(height: 6.h),
                    Container(
                      margin: EdgeInsets.only(left: 15.w, right: 0.w),
                      child: Text(
                        "${logic.getWeekdayString(DateTime.now())}：${state.drinkTip}",
                        textAlign: TextAlign.center,
                        style: StyleConfig.otherStyle(
                            color: Get.find<ThemeColorController>().gWaterTips,
                            fontWeight: FontWeight.w500,
                            fontSize: 12),
                      ),
                    ),
                    Container(
                      margin:
                          EdgeInsets.only(top: 20.h, left: 72.w, right: 72.w),
                      width: 239.w,
                      height: 289.h,
                      child: state.isReLoad
                          ? WaterFillWidgetV3(
                              currentCapacity:
                                  state.cupSliderSchedule.toDouble(),
                              maxCapacity: state.maxCupSchedule.toDouble(),
                              imagePath: UserStore.to.contour,
                            )
                          : const SizedBox(),
                    )
                  },
                  SizedBox(height: 10.h)
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}
