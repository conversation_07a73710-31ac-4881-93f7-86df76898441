import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:getx_xiaopa/extension/widget_extension.dart';
import 'package:getx_xiaopa/network/http_client.dart';
import 'package:getx_xiaopa/network/model/request_body.dart';
import 'package:getx_xiaopa/network/model/result.dart';
import 'package:getx_xiaopa/pages/task/model/task_statics_result.dart';
import 'package:getx_xiaopa/routes/router.dart';
import 'package:getx_xiaopa/storage/storage.dart';
import 'task_detail_state.dart';

class TaskDetailLogic extends GetxController {
  final TaskDetailState state = TaskDetailState();

  @override
  void onInit() async {
    super.onInit();

    state.continuous = Get.arguments["continuous"];
    state.isDrinkSuccess = Get.arguments["isDrinkSuccess"];
    state.maxCupSchedule = Get.arguments["maxCupSchedule"];
    state.cupSliderSchedule = Get.arguments["cupSliderSchedule"];
    state.drinkTip = Get.arguments["drinkTip"];

    state.sController.addListener(() {
      if (state.sController.offset > 170.h) {
        state.rController.start();
      } else {
        state.rController.stop();
      }

      ///监听滚动位置设置导航栏颜色
      double opacity;
      opacity = state.sController.offset / 100;

      if (opacity > 0.8) opacity = 1;

      state.navColor = state.sController.offset > 20.h
          ? Colors.white.withValues(alpha: opacity)
          : Colors.transparent;
      update(["task_detail_view"]);
    });

    state.days = _getLast7DaysWithWeekday();
    state.weeks = _getDatesBeforeFourWeeks();
    state.months = _getLast12Months();

    state.isReLoad = true;

    _setValues(isNetwork: false);
    _getCompletedTaskItems(DateTime.now());

    onTabChanged(0);
  }

  onTabChanged(int index) {
    state.sIndex = index;
    _getStaticsTaskCurrent();
  }

  onDaySelected(DateTime date) {
    _getCompletedTaskItems(date);
  }

  /// 获取本天开始的前7天的星期几
  List<Map> _getLast7DaysWithWeekday() {
    final inputDate = DateTime.now();
    final List<String> weekdays = [
      'diary_week_one'.tr,
      'diary_week_two'.tr,
      'diary_week_three'.tr,
      'diary_week_four'.tr,
      'diary_week_five'.tr,
      'diary_week_six'.tr,
      'diary_week_seven'.tr,
    ];

    return List.generate(7, (index) {
      final date = inputDate.subtract(Duration(days: index));
      final dateString = '${date.year.toString().padLeft(4, '0')}-'
          '${date.month.toString().padLeft(2, '0')}-'
          '${date.day.toString().padLeft(2, '0')}';
      final weekdayName = weekdays[date.weekday - 1];
      return {"sign": dateString, "value": weekdayName};
    }).reversed.toList();
  }

  /// 获取前4周日期
  List<Map> _getDatesBeforeFourWeeks() {
    DateTime today = DateTime.now();

    // 找到今天属于的这一周的结束日（假设周六为一周的最后一天）
    int weekday = today.weekday; // 星期一是1，星期天是7
    // 我们假设以周六（6）为一周的最后一天
    int daysToSaturday = (6 - weekday + 7) % 7; // 差几天到周六
    DateTime endOfWeek = today.add(Duration(days: daysToSaturday));

    List<Map> weeks = [];

    for (int i = 0; i < 4; i++) {
      DateTime weekEnd = endOfWeek.subtract(Duration(days: i * 7));
      DateTime weekStart = weekEnd.subtract(const Duration(days: 6));
      String range = '';
      if (weekStart.month == weekEnd.month) {
        if (ConfigStore.to.getLocaleCode() == "zh") {
          range = "${weekStart.month}月${weekStart.day}-${weekEnd.day}日";
        } else {
          range = "${weekStart.month}/${weekStart.day}-${weekEnd.day}";
        }
      } else {
        if (ConfigStore.to.getLocaleCode() == "zh") {
          range =
              "${weekStart.month}月${weekStart.day}-${weekEnd.month}月${weekEnd.day}日";
        } else {
          range =
              "${weekStart.month}/${weekStart.day}-${weekEnd.month}/${weekEnd.day}";
        }
      }

      // 计算本年第几周（从每年第1天开始，每周7天）
      int weekOfYear =
          ((weekEnd.difference(DateTime(weekEnd.year, 1, 1)).inDays) / 7)
                  .floor() +
              1;

      weeks.add({"sign": "${today.year}-$weekOfYear", "value": range});
    }
    return weeks.reversed.toList();
  }

  ///获取本月开始的前12个月
  List<Map> _getLast12Months() {
    DateTime now = DateTime.now();
    List<Map> months = [];

    for (int i = 0; i < 12; i++) {
      DateTime target = DateTime(now.year, now.month - i, 1);
      final dateString = '${target.year.toString().padLeft(4, '0')}-'
          '${target.month.toString().padLeft(2, '0')}';
      months.add(
          {"sign": dateString, "value": target.month.toString().padLeft(2)});
    }

    return months.reversed.toList();
  }

  String getDateString(DateTime date) {
    String temp = "";

    if (ConfigStore.to.getLocaleCode() == "zh") {
      temp = '${date.year.toString().padLeft(4, '0')}年'
          '${date.month.toString().padLeft(2, '0')}月'
          '${date.day.toString().padLeft(2, '0')}日';
    } else {
      temp = '${date.month.toString().padLeft(2, '0')}/'
          '${date.day.toString().padLeft(2, '0')}/'
          '${date.year.toString().padLeft(4, '0')}';
    }

    return temp;
  }

  String getWeekdayString(DateTime date) {
    final weekdays = [
      'diary_week_one'.tr,
      'diary_week_two'.tr,
      'diary_week_three'.tr,
      'diary_week_four'.tr,
      'diary_week_five'.tr,
      'diary_week_six'.tr,
      'diary_week_seven'.tr,
    ];
    return ConfigStore.to.getLocaleCode() == "zh"
        ? '${"task_detail_tisps_three".tr}${weekdays[date.weekday - 1]}'
        : weekdays[date.weekday - 1];
  }

  /// 获取指定月份的起始和结束日期
  Map<String, DateTime> _getMonthStartAndEndDate(int year, int month) {
    // 起始日期：当月1号
    DateTime start = DateTime(year, month, 2);

    // 结束日期：下个月的第0天（即上个月的最后一天）
    DateTime end = DateTime(year, month + 1, 1);

    return {'start': start.toLocal(), 'end': end.toLocal()};
  }

  ///设置初始化数据,防止网络出问题UI加载出错
  _setValues({bool isNetwork = false}) {
    if (state.sIndex == 0) {
      state.chartData = [
        {
          "key": state.days[0]["value"],
          "num": 0,
          "sign": state.days[0]["sign"]
        },
        {
          "key": state.days[1]["value"],
          "num": 0,
          "sign": state.days[1]["sign"]
        },
        {
          "key": state.days[2]["value"],
          "num": 0,
          "sign": state.days[2]["sign"]
        },
        {
          "key": state.days[3]["value"],
          "num": 0,
          "sign": state.days[3]["sign"]
        },
        {
          "key": state.days[4]["value"],
          "num": 0,
          "sign": state.days[4]["sign"]
        },
        {
          "key": state.days[5]["value"],
          "num": 0,
          "sign": state.days[5]["sign"]
        },
        {"key": state.days[6]["value"], "num": 0, "sign": state.days[6]["sign"]}
      ];
    }
    if (state.sIndex == 1) {
      state.chartData = [
        {
          "key": state.weeks[0]["value"],
          "num": 0,
          "sign": state.weeks[0]["sign"]
        },
        {
          "key": state.weeks[1]["value"],
          "num": 0,
          "sign": state.weeks[1]["sign"]
        },
        {
          "key": state.weeks[2]["value"],
          "num": 0,
          "sign": state.weeks[2]["sign"]
        },
        {
          "key": state.weeks[3]["value"],
          "num": 0,
          "sign": state.weeks[3]["sign"]
        }
      ];
    }
    if (state.sIndex == 2) {
      state.chartData = [
        {
          "key": state.months[0]["value"],
          "num": 0,
          "sign": state.months[0]["sign"]
        },
        {
          "key": state.months[1]["value"],
          "num": 0,
          "sign": state.months[1]["sign"]
        },
        {
          "key": state.months[2]["value"],
          "num": 0,
          "sign": state.months[2]["sign"]
        },
        {
          "key": state.months[3]["value"],
          "num": 0,
          "sign": state.months[3]["sign"]
        },
        {
          "key": state.months[4]["value"],
          "num": 0,
          "sign": state.months[4]["sign"]
        },
        {
          "key": state.months[5]["value"],
          "num": 0,
          "sign": state.months[5]["sign"]
        },
        {
          "key": state.months[6]["value"],
          "num": 0,
          "sign": state.months[6]["sign"]
        },
        {
          "key": state.months[7]["value"],
          "num": 0,
          "sign": state.months[7]["sign"]
        },
        {
          "key": state.months[8]["value"],
          "num": 0,
          "sign": state.months[8]["sign"]
        },
        {
          "key": state.months[9]["value"],
          "num": 0,
          "sign": state.months[9]["sign"]
        },
        {
          "key": state.months[10]["value"],
          "num": 0,
          "sign": state.months[10]["sign"]
        },
        {
          "key": state.months[11]["value"],
          "num": 0,
          "sign": state.months[11]["sign"]
        },
      ];
    }

    if (isNetwork) {
      if (state.sIndex == 0) {
        for (var model in state.taskStaticsModels) {
          for (var i = 0; i < state.chartData.length; i++) {
            if (state.chartData[i]["sign"] == model.duration) {
              state.chartData[i]["num"] = model.theNum;
              break;
            }
          }
        }
      }
      if (state.sIndex == 1) {
        for (var model in state.taskStaticsModels) {
          for (var i = 0; i < state.chartData.length; i++) {
            if (state.chartData[i]["sign"] == model.duration) {
              state.chartData[i]["num"] = model.theNum;
            }
          }
        }
      }
      if (state.sIndex == 2) {
        for (var model in state.taskStaticsModels) {
          for (var i = 0; i < state.chartData.length; i++) {
            if (state.chartData[i]["sign"] == model.duration) {
              state.chartData[i]["num"] = model.theNum;
            }
          }
        }
      }
    }

    state.dController.onTapAction(state.chartData);
    update(["task_detail_view"]);
  }

  toAchievementPage() {
    var map = {"continuous": state.continuous};
    Get.toNamed(AppRoutes.TASK_ACHIEVEMENT, arguments: map)?.then((value) {
      if (value) {
        state.isReLoad = false;
        update(["task_detail_view"]);
        Future.delayed(const Duration(milliseconds: 200), () {
          state.isReLoad = true;
          state.isBackReLoad = true;
          update(["task_detail_view"]);
        });
      }
    });
  }

  toSharePage() {
    state.rController.stop();
    int shareType = UserStore.to.isDefaultSkin ? 2 : 1;
    var map = {"shareType": shareType, "continuous": state.continuous};
    Get.toNamed(AppRoutes.TASK_SHARE, arguments: map)?.then((value) {
      state.rController.start();
    });
  }

  ///========================网络请求===============================
  ///任务统计列表(当天周月)
  _getStaticsTaskCurrent() async {
    var map = {
      "device_id": UserStore.to.deviceList[UserStore.to.selectDevice].id,
      "type": state.sIndex + 1
    };
    Result result = await http.taskStatisticCurrent(map);
    if (result.code == 0) {
      state.taskStaticsModels.clear();
      TaskStaticsModel model = TaskStaticsModel.fromJson(result.data);
      _getStaticsTaskItems(model);
    } else {
      CToast.showToast(result.msg!);
    }
  }

  ///任务统计列表
  _getStaticsTaskItems(TaskStaticsModel model) async {
    var map = RequestBody(filters: [
      Filter(
          expr: "=",
          name: "device_id",
          value: UserStore.to.deviceList[UserStore.to.selectDevice].id),
      Filter(expr: "=", name: "type", value: "${state.sIndex + 1}"),
    ]).toJson();
    Result result = await http.taskStatisticItems(map);

    if (result.code == 0) {
      state.taskStaticsModels
        ..add(model)
        ..addAll(result.data.data);
      _setValues(isNetwork: true);
    } else {
      CToast.showToast(result.msg!);
    }
    update(["task_detail_view"]);
  }

  ///获取已经完成的任务
  _getCompletedTaskItems(DateTime date) async {
    final monthStartAndEndDate =
        _getMonthStartAndEndDate(date.year, date.month);

    var map = RequestBody(filters: [
      Filter(
          expr: "=",
          name: "device_id",
          value: UserStore.to.deviceList[UserStore.to.selectDevice].id),
      Filter(expr: "=", name: "type", value: "1"),
      Filter(expr: "between", name: "created_at", value: [
        monthStartAndEndDate["start"].toString(),
        monthStartAndEndDate["end"].toString()
      ]),
    ]).toJson();

    Result result = await http.taskStatisticItems(map);
    if (result.code == 0) {
      state.taskStaticsCompletedModels = result.data.data;
      for (var model in state.taskStaticsCompletedModels) {
        if (model.theNum != 0) {
          state.completedDates.add(DateTime.parse(model.duration!));
        }
      }
    } else {
      CToast.showToast(result.msg!);
    }
    update(["task_detail_view"]);
  }
}
