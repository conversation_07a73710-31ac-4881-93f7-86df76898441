import 'package:flutter/material.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:getx_xiaopa/utils/utils.dart';
import 'package:getx_xiaopa/values/values.dart';
import 'package:getx_xiaopa/widget/widget.dart';

class DrinkChartController {
  Function(List<Map> chartData)? _onTapCallback;

  void _bind({required Function(List<Map> chartData) onTapCallback}) {
    _onTapCallback = onTapCallback;
  }

  void onTapAction(List<Map> chartData) {
    _onTapCallback?.call(chartData);
  }
}

class DrinkChartWidget extends StatefulWidget {
  final List<Map> chartData;
  final DrinkChartController controller;
  final Function(int index) onTabChanged;

  const DrinkChartWidget(
      {super.key,
      required this.chartData,
      required this.onTabChanged,
      required this.controller});

  @override
  State<DrinkChartWidget> createState() => _DrinkChartWidgetState();
}

class _DrinkChartWidgetState extends State<DrinkChartWidget> {
  int _selectedIndex = 0;
  final List<String> _tabs = [
    "task_detail_day".tr,
    "diary_date_week".tr,
    "diary_date_month".tr
  ];

  late String _currentTab;
  late List<Map> _dataList = [];
  late final List<String> _keys = [];
  late final List<int> _values = [];
  late double _maxValue;
  late double _topPadding;
  late double _maxY;

  @override
  void initState() {
    super.initState();

    _dataList = widget.chartData;
    _maxYAction();

    widget.controller._bind(onTapCallback: _onTapAction);
  }

  void _onTapAction(List<Map> chartData) {
    _dataList = chartData;
    _maxYAction();
  }

  _maxYAction() {
    /// 初始化数组
    _keys.clear();
    _values.clear();

    _currentTab = _tabs[_selectedIndex];

    for (var data in _dataList) {
      _keys.add(data["key"]);
      _values.add(data["num"]);
    }

    _maxValue = _values.reduce((a, b) => a > b ? a : b).toDouble();
    _topPadding = _maxValue * 0.2; // 留20%顶部空间
    _maxY = _maxValue + _topPadding;
    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(10.r),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: List.generate(_tabs.length, (index) {
              bool selected = index == _selectedIndex;
              return GestureDetector(
                onTap: () {
                  _selectedIndex = index;

                  widget.onTabChanged(index);
                },
                child: Container(
                  margin: EdgeInsets.only(top: 20.h, left: 14.w, right: 14.w),
                  width: 80.w,
                  height: 36.h,
                  decoration: BoxDecoration(
                    color: selected
                        ? const Color.fromRGBO(40, 39, 46, 1)
                        : const Color.fromRGBO(242, 241, 245, 1),
                    borderRadius: BorderRadius.circular(18.r),
                  ),
                  child: Center(
                      child: ThemeText(
                    dataStr: _tabs[index],
                    keyName: 'textColor',
                    flag: selected,
                    subColor: ColorConfig.shopDetailTextColor,
                    fontWeight: FontWeight.w500,
                  )),
                ),
              );
            }),
          ),
          SizedBox(height: 14.h),
          SizedBox(
            width: 200.w,
            child: Text(
              _currentTab == 'task_detail_day'.tr
                  ? 'task_detail_day_one'.tr
                  : _currentTab == 'diary_date_week'.tr
                      ? 'task_detail_week_one'.tr
                      : 'task_detail_year_one'.tr,
              textAlign: TextAlign.center,
              style: StyleConfig.otherStyle(
                  color: ColorConfig.searchTextColor,
                  fontWeight: FontWeight.w500),
            ),
          ),
          SizedBox(height: 5.h),
          Expanded(
            child: BarChart(
              ///不加这个有动画会报错。加这个没有动画不会报错
              duration: Duration.zero,
              BarChartData(
                maxY: _maxY,
                barGroups: _values.asMap().entries.map((entry) {
                  int index = entry.key;
                  int value = entry.value;
                  return BarChartGroupData(
                    x: index,
                    barRods: [
                      BarChartRodData(
                        toY: value.toDouble(),
                        width: _currentTab == "task_detail_day".tr
                            ? 20.w
                            : _currentTab == "diary_date_week".tr
                                ? 46.w
                                : 10.w,
                        borderRadius: BorderRadius.circular(2.r),
                        gradient: LinearGradient(
                          colors: [
                            Get.find<ThemeColorController>().gChartGradientStar,
                            Get.find<ThemeColorController>().gChartGradientEnd,
                          ],
                          begin: Alignment.topCenter,
                          end: Alignment.bottomCenter,
                        ),
                      ),
                    ],
                    showingTooltipIndicators: value > 0 ? [0] : [],
                  );
                }).toList(),
                titlesData: FlTitlesData(
                  bottomTitles: AxisTitles(
                    sideTitles: SideTitles(
                      showTitles: true,
                      getTitlesWidget: (value, _) {
                        return Text(
                          _keys[value.toInt()],
                          style: StyleConfig.otherStyle(
                              color: ColorConfig.authenticationTextColor,
                              fontSize: 12),
                        );
                      },
                    ),
                  ),
                  leftTitles: const AxisTitles(
                      sideTitles: SideTitles(showTitles: false)),
                  topTitles: const AxisTitles(
                      sideTitles: SideTitles(showTitles: false)),
                  rightTitles: const AxisTitles(
                      sideTitles: SideTitles(showTitles: false)),
                ),
                barTouchData: BarTouchData(
                  enabled: true,
                  touchTooltipData: BarTouchTooltipData(
                    tooltipPadding: EdgeInsets.zero,
                    tooltipMargin: 0.h,
                    getTooltipColor: (group) => Colors.transparent,
                    getTooltipItem: (group, groupIndex, rod, rodIndex) {
                      return BarTooltipItem(
                        '${rod.toY.toInt()}',
                        StyleConfig.otherStyle(
                            color:
                                Get.find<ThemeColorController>().gChartCupColor,
                            fontWeight: FontWeight.w500,
                            fontSize: 12),
                      );
                    },
                  ),
                ),
                gridData: const FlGridData(show: false),
                borderData: FlBorderData(show: false),
              ),
            ),
          ),
          SizedBox(height: 5.h),
        ],
      ),
    );
  }
}
