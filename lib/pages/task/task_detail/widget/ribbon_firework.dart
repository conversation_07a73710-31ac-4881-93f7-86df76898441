import 'dart:async';
import 'dart:math';
import 'dart:ui' as ui;

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class RibbonFireworkController {
  VoidCallback? _startCallback;
  VoidCallback? _stopCallback;

  void _bind({required VoidCallback start, required VoidCallback stop}) {
    _startCallback = start;
    _stopCallback = stop;
  }

  void start() {
    _startCallback?.call();
  }

  void stop() {
    _stopCallback?.call();
  }
}

class PersistentRibbonFirework extends StatefulWidget {
  final double width;
  final double height;
  final List<String> imagePaths;
  final int particlesPerBurst;
  final Duration burstInterval;
  final RibbonFireworkController controller;

  const PersistentRibbonFirework({
    super.key,
    required this.width,
    required this.height,
    required this.imagePaths,
    required this.controller,
    this.particlesPerBurst = 6,
    this.burstInterval = const Duration(milliseconds: 600),
  });

  @override
  State<PersistentRibbonFirework> createState() =>
      _PersistentRibbonFireworkState();
}

class _PersistentRibbonFireworkState extends State<PersistentRibbonFirework>
    with SingleTickerProviderStateMixin {
  final List<_Particle> _particles = [];
  final Random _random = Random();
  final List<ui.Image> _images = [];

  late AnimationController _controller;
  Timer? _burstTimer;
  bool _running = false;

  @override
  void initState() {
    super.initState();
    widget.controller._bind(start: _start, stop: _stop);
    _loadImages().then((_) {
      _controller = AnimationController(
        vsync: this,
        duration: const Duration(seconds: 300),
      )
        ..addListener(_tick)
        ..repeat();
    });
  }

  Future<void> _loadImages() async {
    for (var path in widget.imagePaths) {
      final data = await rootBundle.load(path);
      final codec = await ui.instantiateImageCodec(data.buffer.asUint8List());
      final frame = await codec.getNextFrame();
      _images.add(frame.image);
    }
    setState(() {});
  }

  void _start() {
    if (_running || _images.isEmpty) return;
    _running = true;

    _burstTimer =
        Timer.periodic(widget.burstInterval, (_) => _generateParticles());
  }

  void _stop() {
    if (!_running) return;
    _burstTimer?.cancel();
    _burstTimer = null;
    _running = false;
  }

  void _tick() {
    const dt = 1 / 60;

    _particles.removeWhere((p) => p.isOutOfBounds(widget.width, widget.height));

    for (final p in _particles) {
      p.update(dt);
    }

    setState(() {});
  }

  void _generateParticles() {
    final center = Offset(widget.width / 2, widget.height / 2);
    final newParticles = <_Particle>[];

    for (int i = 0; i < widget.particlesPerBurst ~/ 2; i++) {
      // Left Up
      newParticles
          .add(_createParticle(center, angleRange: [-3 * pi / 4, -pi / 4]));
      // Right Up
      newParticles.add(_createParticle(center, angleRange: [-pi / 4, pi / 8]));
    }

    _particles.addAll(newParticles);
  }

  _Particle _createParticle(Offset origin, {required List<double> angleRange}) {
    final angle =
        angleRange[0] + _random.nextDouble() * (angleRange[1] - angleRange[0]);
    final speed = 200.0 + _random.nextDouble() * 100;
    final velocity = Offset(cos(angle), sin(angle)) * speed;

    return _Particle(
      image: _images[_random.nextInt(_images.length)],
      position: origin,
      velocity: velocity,
      gravity: 200 + _random.nextDouble() * 100,
      rotation: _random.nextDouble() * pi * 2,
      rotationSpeed: (_random.nextDouble() - 0.5) * 4,
      scale: 0.3 + _random.nextDouble() * 0.5,
    );
  }

  @override
  void dispose() {
    _controller.dispose();
    _burstTimer?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (_images.isEmpty) return const SizedBox();
    return SizedBox(
      width: widget.width,
      height: widget.height,
      child: CustomPaint(
        painter: _FireworkPainter(_particles),
      ),
    );
  }
}

class _Particle {
  final ui.Image image;
  final double gravity;
  final double rotationSpeed;
  final double scale;

  Offset position;
  Offset velocity;
  double rotation;

  _Particle({
    required this.image,
    required this.position,
    required this.velocity,
    required this.gravity,
    required this.rotation,
    required this.rotationSpeed,
    required this.scale,
  });

  void update(double dt) {
    velocity = Offset(velocity.dx, velocity.dy + gravity * dt);
    position += velocity * dt;
    rotation += rotationSpeed * dt;
  }

  bool isOutOfBounds(double width, double height) {
    return position.dx < -100 ||
        position.dx > width + 100 ||
        position.dy > height + 100;
  }
}

class _FireworkPainter extends CustomPainter {
  final List<_Particle> particles;

  _FireworkPainter(this.particles);

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint();

    for (final p in particles) {
      final alpha =
          (255 * (1 - p.position.dy / size.height)).clamp(0, 255).toInt();
      paint.color = Color.fromARGB(alpha, 255, 255, 255);

      canvas.save();
      canvas.translate(p.position.dx, p.position.dy);
      canvas.rotate(p.rotation);
      canvas.scale(p.scale, p.scale);
      canvas.drawImage(
        p.image,
        Offset(-p.image.width / 2, -p.image.height / 2),
        paint,
      );
      canvas.restore();
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}
