import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:getx_xiaopa/storage/storage.dart';
import 'package:getx_xiaopa/utils/utils.dart';
import 'package:getx_xiaopa/values/values.dart';
import 'package:getx_xiaopa/widget/theme_text.dart';
import 'package:table_calendar/table_calendar.dart';

class DrinkCalendarWidget extends StatefulWidget {
  final List<DateTime> completedDates;
  final Function(DateTime) onDaySelected;
  const DrinkCalendarWidget(
      {super.key, required this.completedDates, required this.onDaySelected});

  @override
  State<DrinkCalendarWidget> createState() => _DrinkCalendarWidgetState();
}

class _DrinkCalendarWidgetState extends State<DrinkCalendarWidget> {
  DateTime _focusedDay = DateTime.now();
  final DateTime _today = DateTime.now();

  bool _isSameDay(DateTime a, DateTime b) {
    return a.year == b.year && a.month == b.month && a.day == b.day;
  }

  bool _isCompleted(DateTime date) {
    return widget.completedDates.any((d) => _isSameDay(d, date));
  }

  // bool _isStreakLeft(DateTime date) {
  //   final prev = date.subtract(const Duration(days: 1));
  //   return _isCompleted(prev);
  // }

  // bool _isStreakRight(DateTime date) {
  //   final next = date.add(const Duration(days: 1));
  //   return _isCompleted(next);
  // }

  @override
  Widget build(BuildContext context) {
    return TableCalendar(
      availableGestures: AvailableGestures.horizontalSwipe,
      firstDay: DateTime.utc(2020, 1, 1),
      lastDay: DateTime.utc(2099, 12, 31),
      focusedDay: _focusedDay,
      rowHeight: 34.h,
      daysOfWeekHeight: 20.h,
      locale: ConfigStore.to.locale.toString(),
      headerStyle: HeaderStyle(
          formatButtonVisible: false,
          titleCentered: true,
          leftChevronIcon: Container(
            width: 20.r,
            height: 20.r,
            decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(20.r),
                color: const Color.fromRGBO(242, 241, 245, 1)),
            child: Center(
              child: Icon(
                size: 20.r,
                Icons.chevron_left,
                color: const Color.fromRGBO(185, 185, 185, 1),
              ),
            ),
          ),
          rightChevronIcon: Container(
            width: 20.r,
            height: 20.r,
            decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(20.r),
                color: const Color.fromRGBO(242, 241, 245, 1)),
            child: Center(
              child: Icon(
                size: 20.r,
                Icons.chevron_right,
                color: const Color.fromRGBO(185, 185, 185, 1),
              ),
            ),
          ),
          headerPadding: EdgeInsets.zero,
          titleTextStyle: StyleConfig.otherStyle(
              color: ColorConfig.searchTextColor,
              fontWeight: FontWeight.w500,
              fontSize: 18)),
      calendarStyle: const CalendarStyle(
        outsideDaysVisible: false,
        isTodayHighlighted: false,
      ),
      calendarBuilders: CalendarBuilders(
        dowBuilder: (context, day) {
          final text = [
            'diary_week_seven'.tr,
            'diary_week_one'.tr,
            'diary_week_two'.tr,
            'diary_week_three'.tr,
            'diary_week_four'.tr,
            'diary_week_five'.tr,
            'diary_week_six'.tr
          ][day.weekday % 7];
          return Center(
            child: Text(
              text,
              style: StyleConfig.otherStyle(
                  color: ColorConfig.authenticationTextColor, fontSize: 14),
            ),
          );
        },
        defaultBuilder: (context, date, _) => _buildDayCell(date),
        todayBuilder: (context, date, _) => _buildDayCell(date),
        selectedBuilder: (context, date, _) => _buildDayCell(date),
      ),
      onPageChanged: (focusedDay) {
        setState(() {
          _focusedDay = focusedDay;
        });
        widget.onDaySelected(focusedDay);
      },
    );
  }

  Widget _buildDayCell(DateTime date) {
    final bool isToday = _isSameDay(date, _today);
    final bool isCompleted = _isCompleted(date);
    // final bool leftConnected = _isStreakLeft(date);
    // final bool rightConnected = _isStreakRight(date);

    // BorderRadius radius = BorderRadius.circular(20.r);
    // if (leftConnected && rightConnected) {
    //   radius = BorderRadius.zero;
    // } else if (leftConnected) {
    //   radius = BorderRadius.only(
    //     topRight: Radius.circular(20.r),
    //     bottomRight: Radius.circular(20.r),
    //   );
    // } else if (rightConnected) {
    //   radius = BorderRadius.only(
    //     topLeft: Radius.circular(20.r),
    //     bottomLeft: Radius.circular(20.r),
    //   );
    // }

    return Container(
      margin: EdgeInsets.only(top: 5.h, bottom: 5.h),
      decoration: isCompleted
          ? BoxDecoration(
              boxShadow: const [
                  BoxShadow(
                    color: Color.fromRGBO(255, 254, 245, 0.5),
                    // 阴影颜色和透明度
                    spreadRadius: 0,
                    // 阴影扩散范围
                    blurRadius: 3,
                    // 阴影模糊程度
                    offset: Offset(0, 1),
                    // 阴影偏移量（水平，垂直）
                  )
                ],
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [
                  Get.find<ThemeColorController>().gChartGradientStar,
                  Get.find<ThemeColorController>().gChartGradientEnd,
                  // Color.fromRGBO(97, 235, 252, 1),
                  // Color.fromRGBO(50, 211, 247, 1)
                ],
              ),
              // borderRadius: radius,
              shape: BoxShape.circle)
          : null,
      child: Container(
        padding: EdgeInsets.only(right: 1.r),
        decoration: isToday
            ? const BoxDecoration(color: Colors.black, shape: BoxShape.circle)
            : null,
        child: Center(
          child: ThemeText(
            dataStr: '${date.day}',
            keyName: 'textColor',
            flag: isToday,
            subColor:
                isCompleted ? Colors.white : ColorConfig.shopDetailTextColor,
            fontWeight:
                isToday || isCompleted ? FontWeight.w500 : FontWeight.w400,
            fontSize: 12,
          ),
        ),
      ),
    );
  }
}
