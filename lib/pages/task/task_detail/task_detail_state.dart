import 'package:flutter/material.dart';
import 'package:getx_xiaopa/pages/task/model/task_statics_result.dart';
import 'package:getx_xiaopa/pages/task/task_detail/widget/drink_chart_widget.dart';
import 'package:getx_xiaopa/pages/task/task_detail/widget/ribbon_firework.dart';

class TaskDetailState {
  List<Map> chartData = [];
  List<DateTime> completedDates = [];

  late List<Map> days;
  late List<Map> weeks;
  late List<Map> months;

  Color navColor = Colors.transparent;
  final ScrollController sController = ScrollController();

  final RibbonFireworkController rController = RibbonFireworkController();

  final DrinkChartController dController = DrinkChartController();

  String continuous = "";

  bool isDrinkSuccess = false;
  int maxCupSchedule = 0;
  double cupSliderSchedule = 0;
  String drinkTip = "";

  List<TaskStaticsModel> taskStaticsModels = [];
  List<TaskStaticsModel> taskStaticsCompletedModels = [];
  int sIndex = 0;

  ///重新加载轮廓图
  bool isReLoad = false;
  ///是否通知上一层页面重新加载
  bool isBackReLoad = false;

  TaskDetailState() {
    ///Initialize variables
  }
}
