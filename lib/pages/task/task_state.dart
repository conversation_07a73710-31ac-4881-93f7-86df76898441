import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:getx_xiaopa/pages/task/model/task_item_result.dart';
import 'package:getx_xiaopa/pages/task/model/task_target_result.dart';
import 'package:getx_xiaopa/pages/task/model/task_type_result.dart';
import 'dart:ui' as ui;

import 'package:getx_xiaopa/pages/task/task_detail/widget/ribbon_firework.dart';

class TaskState {
  /// 当前喝水进度（上面显示的数字）
  double cupSchedule = 0;

  /// 当前喝水进度（进度条的进度）
  double cupSliderSchedule = 0;

  ///完成进度
  int completeSchedule = 0;

  /// 每天目标
  int maxCupSchedule = 8;

  bool isInit = false;

  late ui.Image? imageInfo;

  /// 一天喝水目标完成
  bool isDrinkSuccess = false;

  Color navColor = Colors.transparent;
  ScrollController scrollController = ScrollController();

  List<int> itemList = [6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20];

  final RibbonFireworkController rController = RibbonFireworkController();

  String deveciId = "";

  List<TaskTypeModel> taskTypeList = [];
  TaskTargetModel taskTargetModel = TaskTargetModel();

  List<TaskItemModel> taskItemList = [];

  List<TaskItem> taskItem = [];

  String drinkTipStr = "task_tips_one".tr;

  List<String> drinkTip = [
    "task_tips_one".tr,
    "task_tips_two".tr,
    "task_tips_three".tr,
    "task_tips_four".tr,
    "task_tips_five".tr,
    "task_tips_six".tr,
    "task_tips_seven".tr,
    "task_tips_eight".tr,
    "task_tips_nine".tr,
    "task_tips_ten".tr,
    "task_tips_eleven".tr,
    "task_tips_tweleve".tr,
    "task_tips_thirteen".tr,
    "task_tips_fourteen".tr,
    "task_tips_fifteen".tr,
    "task_tips_sixteen".tr,
    "task_tips_seventeen".tr,
    "task_tips_eighteen".tr,
    "task_tips_nineteen".tr,
    "task_tips_twenty".tr
  ];

  List<String> drinkSuccessTip = [
    "task_success_tips_one".tr,
    "task_success_tips_two".tr,
    "task_success_tips_three".tr,
    "task_success_tips_four".tr,
    "task_success_tips_five".tr,
    "task_success_tips_six".tr,
    "task_success_tips_seven".tr,
    "task_success_tips_eight".tr,
    "task_success_tips_nine".tr,
    "task_success_tips_ten".tr,
    "task_success_tips_eleven".tr,
    "task_success_tips_tweleve".tr,
    "task_success_tips_thirteen".tr,
    "task_success_tips_fourteen".tr,
    "task_success_tips_fifteen".tr,
    "task_success_tips_sixteen".tr,
    "task_success_tips_seventeen".tr,
    "task_success_tips_eighteen".tr,
    "task_success_tips_nineteen".tr,
    "task_success_tips_twenty".tr,
    "task_success_tips_tweenty_one".tr
  ];

  ///如果解锁皮肤了记录解锁皮肤的id
  String unlockSkinId = "";

  TaskState() {
    ///
  }
}

class TaskItem {
  final String imgStr;
  final String timeStr;

  TaskItem({this.imgStr = "", this.timeStr = ""});
}
