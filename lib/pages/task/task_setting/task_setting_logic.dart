import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:getx_xiaopa/extension/widget_extension.dart';
import 'package:getx_xiaopa/network/http_client.dart';
import 'package:getx_xiaopa/network/model/request_body.dart';
import 'package:getx_xiaopa/network/model/result.dart';
import 'package:getx_xiaopa/pages/task/task_setting/widget/explain_dialog.dart';
import 'package:getx_xiaopa/pages/task/widget/drink_count_sheet.dart';
import 'package:getx_xiaopa/storage/storage.dart';
import 'package:getx_xiaopa/utils/utils.dart';
import 'package:getx_xiaopa/values/values.dart';

import 'task_setting_state.dart';

class TaskSettingLogic extends GetxController {
  final TaskSettingState state = TaskSettingState();

  @override
  void onInit() {
    super.onInit();

    state.maxCupSchedule = Get.arguments["max_cup_schedule"];
    state.targetId = Get.arguments["target_id"];

    ///是否可以修改喝水
    state.isChange = Get.arguments["isChange"];
    _taskRemindItems();
  }

  explainAction() {
    Get.dialog(const ExplainDialog());
  }

  drinkCountSheet() {
    ///一天中没喝水前才能修改喝水目标
    if (!state.isChange) {
      CToast.showToast("task_drink_error_tips".tr);
      return;
    }
    Get.bottomSheet(DrinkCountSheet(
      initialValue: state.maxCupSchedule,
      data: state.itemList,
      onTap: (index) {
        _changeTaskTarget(theNum: index);
      },
    ));
  }

  showAction(flag) {
    state.isShow = flag;
    int type = flag ? 1 : 2;
    _taskRemindUpdate2(type: type);
  }

  intervalAction(int index) {
    state.sIndex = index;
    double intervalTime = state.intervalList[index].intervalTime;
    state.intervalTime = intervalTime % 1 == 0
        ? intervalTime.toInt().toString()
        : intervalTime.toString();
    update(["task_setting_view"]);
  }

  startTimeAction() async {
    state.startTime = await _datePickAction(state.startTime);
    update(["task_setting_view"]);
  }

  endTimeAction() async {
    state.endTime = await _datePickAction(state.endTime);
    update(["task_setting_view"]);
  }

  Future<String> _datePickAction(String yStr) async {
    TimeOfDay initTime = TimeUtil.parseTimeString(yStr);

    TimeOfDay? picked = await showTimePicker(
      context: Get.context!,
      barrierDismissible: false,
      initialEntryMode: TimePickerEntryMode.inputOnly,
      initialTime: initTime,
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: Theme.of(context).colorScheme.copyWith(
                  error: const Color.fromRGBO(249, 56, 56, 1),
                ),
            timePickerTheme: TimePickerThemeData(
              backgroundColor: Colors.white,
              dayPeriodBorderSide: BorderSide.none,
              dayPeriodTextColor: WidgetStateColor.resolveWith((states) {
                if (states.contains(WidgetState.selected)) {
                  return Get.find<ThemeColorController>().gTextColor;
                }
                return ColorConfig.searchTextColor;
              }),
              confirmButtonStyle: ButtonStyle(
                foregroundColor:
                    WidgetStateProperty.all(ColorConfig.searchTextColor),
              ),
              cancelButtonStyle: ButtonStyle(
                foregroundColor:
                    WidgetStateProperty.all(ColorConfig.shopDetailTextColor),
              ),

              dayPeriodTextStyle: TextStyle(
                  fontSize: 14.sp,
                  fontWeight: FontWeight.w500,
                  fontFamily: "Alibaba"),
              dayPeriodColor: WidgetStateColor.resolveWith((states) {
                if (states.contains(WidgetState.selected)) {
                  return ColorConfig.searchTextColor; // 选中背景
                }
                return const Color.fromRGBO(242, 241, 245, 1); // 默认背景
              }),
              // 小时分钟背景色（控制选中态样式）
              hourMinuteColor: WidgetStateColor.resolveWith((states) {
                if (states.contains(WidgetState.selected)) {
                  return ColorConfig.searchTextColor; // 选中背景
                }
                return const Color.fromRGBO(242, 241, 245, 1); // 默认背景
              }),
              hourMinuteTextColor: WidgetStateColor.resolveWith((states) {
                if (states.contains(WidgetState.selected)) {
                  return Get.find<ThemeColorController>().gTextColor;
                }
                return ColorConfig.searchTextColor;
              }),
              hourMinuteTextStyle: TextStyle(
                  fontSize: 40.sp,
                  fontWeight: FontWeight.w500,
                  fontFamily: "Alibaba"),
            ),
          ),
          child: child!,
        );
      },
    );

    String timeStr = "";

    if (picked != null) {
      timeStr =
          '${picked.hour.toString().padLeft(2, '')}:${picked.minute.toString().padLeft(2, '0')}';
    } else {
      timeStr = yStr;
    }

    return timeStr;
  }

  saveAction() {
    _taskRemindUpdate();
  }

  ///十进制数字转成时间格式
  String _convertDoubleToTimeString(double time) {
    int hour = time.floor(); // 取整小时
    int minutes = ((time - hour) * 60).round(); // 将小数部分转换为分钟
    return '$hour:${minutes.toString().padLeft(2, '0')}';
  }

  /// 整数转成分钟的十进制表示
  String _formatDecimalMinutes(int seconds) {
    double minutes = seconds / 60;
    // 转成字符串并去掉不必要的末尾 0 和小数点
    String result = minutes.toStringAsFixed(2);
    result = result.replaceFirst(RegExp(r'\.?0+$'), '');
    return result;
  }

  /// 时间格式转十进制数字
  double _parseTimeStringToDecimalHours(String timeString) {
    List<String> parts = timeString.split(':');
    int hour = int.parse(parts[0]);
    int minute = int.parse(parts[1]);
    return hour + minute / 60;
  }

  /// ========================网络请求===============================
  /// 任务提醒
  _taskRemindItems() async {
    var map = RequestBody(filters: [
      Filter(
          expr: "=",
          name: "device_id",
          value: UserStore.to.deviceList[UserStore.to.selectDevice].id)
    ]).toJson();

    Result result = await http.taskRemindItems(map);
    if (result.code == 0) {
      state.taskRemindModel = result.data.data[0];

      state.deveciId = state.taskRemindModel.deviceId ?? "";
      state.isShow = state.taskRemindModel.isActive == 1 ? true : false;

      state.intervalTime =
          _formatDecimalMinutes(state.taskRemindModel.interval?.toInt() ?? 0);

      for (var i = 0; i < state.intervalList.length; i++) {
        if (double.parse(state.intervalTime) ==
            state.intervalList[i].intervalTime) {
          state.sIndex = i;
          break;
        }
      }

      state.startTime = _convertDoubleToTimeString(
          state.taskRemindModel.startTime?.toDouble() ?? 0);
      state.endTime = _convertDoubleToTimeString(
          state.taskRemindModel.endTime?.toDouble() ?? 0);

      update(["task_setting_view"]);
    } else {
      CToast.showToast(result.msg!);
    }
  }

  /// 任务提醒修改
  _taskRemindUpdate() async {
    CToast.showLoading();
    var map = {
      "id": state.taskRemindModel.id,
      "start_time": double.parse(
          _parseTimeStringToDecimalHours(state.startTime).toStringAsFixed(2)),
      "end_time": double.parse(
          _parseTimeStringToDecimalHours(state.endTime).toStringAsFixed(2)),
      "interval": (double.parse(state.intervalTime) * 60).round(),
      "is_active": state.isShow ? 1 : 0,
    };
    Result result = await http.taskRemindUpdate(map);
    CToast.showToast(result.msg!);
  }

  _taskRemindUpdate2({required int type}) async {
    CToast.showLoading();
    var map = {"id": state.taskRemindModel.id, "is_active": type};
    Result result = await http.taskRemindUpdate(map);
    if (result.code == 0) {
    } else {
      CToast.showToast(result.msg!);
    }
    update(["task_setting_view"]);
  }

  ///修改任务目标
  _changeTaskTarget({required int theNum}) async {
    if (state.targetId.isEmpty) return;
    var map = {
      "id": state.targetId,
      "num": theNum,
    };
    Result result = await http.taskTargetUpdate(map);
    if (result.code == 0) {
      state.maxCupSchedule = theNum;
      update(["task_setting_view"]);
    } else {
      CToast.showToast(result.msg!);
    }
  }
}
