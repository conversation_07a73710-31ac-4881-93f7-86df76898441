import 'package:get/get.dart';
import 'package:getx_xiaopa/pages/task/model/task_remind_result.dart';

class TaskSettingState {
  bool isShow = false;

  String intervalTime = "";
  String startTime = "";
  String endTime = "";

  int sIndex = 0;

  List<IntervalType> intervalList = [
    IntervalType(
        intervalTime: 0.25, intervalTips: "15${'task_setting_unit_secend'.tr}"),
    IntervalType(
        intervalTime: 0.5, intervalTips: "30${'task_setting_unit_secend'.tr}"),
    IntervalType(
        intervalTime: 1, intervalTips: "1${'task_setting_unit_hour'.tr}"),
    IntervalType(
        intervalTime: 1.5,
        intervalTips:
            "1${'task_setting_unit_hour'.tr}30${'task_setting_unit_secend'.tr}"),
    IntervalType(
        intervalTime: 2, intervalTips: "2${'task_setting_unit_hour'.tr}"),
    IntervalType(
        intervalTime: 2.5,
        intervalTips:
            "2${'task_setting_unit_hour'.tr}30${'task_setting_unit_secend'.tr}"),
  ];

  String deveciId = "";
  String targetId = "";

  /// 每天目标
  int maxCupSchedule = 8;

  bool isChange = true;

  List<int> itemList = [6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20];

  late TaskRemindModel taskRemindModel;

  TaskSettingState() {
    ///Initialize variables
  }
}

class IntervalType {
  final double intervalTime;
  final String intervalTips;

  IntervalType({this.intervalTime = 0, this.intervalTips = ""});
}
