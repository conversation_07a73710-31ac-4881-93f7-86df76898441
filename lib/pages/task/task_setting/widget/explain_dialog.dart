import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:getx_xiaopa/extension/images.dart';
import 'package:getx_xiaopa/extension/widget_extension.dart';
import 'package:getx_xiaopa/r.dart';
import 'package:getx_xiaopa/utils/utils.dart';
import 'package:getx_xiaopa/values/values.dart';

class ExplainDialog extends StatelessWidget {
  const ExplainDialog({super.key});

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            width: 296.w,
            height: 300.h,
            decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(10.r), color: Colors.white),
            child: Column(
              children: [
                SizedBox(height: 30.h),
                Text(
                  "task_setting_target".tr,
                  style: StyleConfig.otherStyle(
                      color: ColorConfig.searchTextColor,
                      fontWeight: FontWeight.w500,
                      fontSize: 20),
                ),
                Container(
                  margin: EdgeInsets.only(top: 20.h, left: 23.w, right: 23.w),
                  child: Text(
                    StrUtil.breakWord("task_setting_target_tips".tr),
                    style: StyleConfig.otherStyle(
                        color: ColorConfig.authenticationTextColor,
                        fontSize: 14),
                  ),
                )
              ],
            ),
          ),
          SizedBox(height: 30.h),
          Images(
            path: R.task_close_png,
            width: 34.r,
            height: 34.r,
          ).inkWell(() => Get.back())
        ],
      ),
    );
  }
}
