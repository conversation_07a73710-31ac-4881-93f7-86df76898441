import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:getx_xiaopa/base/base.dart';
import 'package:getx_xiaopa/extension/images.dart';
import 'package:getx_xiaopa/extension/widget_extension.dart';
import 'package:getx_xiaopa/r.dart';
import 'package:getx_xiaopa/utils/utils.dart';
import 'package:getx_xiaopa/values/values.dart';
import 'package:getx_xiaopa/widget/widget.dart';

import 'task_setting_logic.dart';
import 'task_setting_state.dart';

class TaskSettingPage extends BaseCommonView {
  TaskSettingPage({super.key});

  final TaskSettingLogic logic = Get.put(TaskSettingLogic());
  final TaskSettingState state = Get.find<TaskSettingLogic>().state;

  @override
  Color? get navColor => Colors.transparent;

  @override
  String? get navTitle => "task_setting_title".tr;

  @override
  TextStyle? get navTitleStyle =>
      StyleConfig.blackStyle(fontSize: 18, fontWeight: FontWeight.w500);

  @override
  double? get leftWidth => 35.w;

  @override
  Widget? get leftButton => Padding(
        padding: EdgeInsets.only(left: 15.w),
        child: Images(
          path: R.back_png,
          width: 20.w,
          height: 20.h,
          scale: 2.8,
        ).inkWell(() => Get.back()),
      );

  @override
  bool? get extendBodyBehindAppBar => true;

  @override
  Widget buildContent() {
    return GetBuilder<TaskSettingLogic>(
      id: "task_setting_view",
      builder: (_) {
        return Container(
          padding: EdgeInsets.only(top: 111.h),
          width: 1.sw,
          height: 1.sh,
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: [
                Get.find<ThemeColorController>().gGradientStar,
                Get.find<ThemeColorController>().gGradientEnd,
              ],
            ),
          ),
          child: SingleChildScrollView(
            child: Column(
              children: [
                Container(
                  margin: EdgeInsets.only(left: 30.w, right: 20.w),
                  child: Row(
                    children: [
                      Text(
                        "task_setting_target".tr,
                        style: StyleConfig.otherStyle(
                            color: ColorConfig.searchTextColor,
                            fontWeight: FontWeight.w500,
                            fontSize: 18),
                      ),
                      const Expanded(child: SizedBox()),
                      ThemeImagePath(
                        fileName: 'task_explain.png',
                        imgWidget: 20.r,
                        imgHeight: 20.r,
                      ).inkWell(() => logic.explainAction())
                    ],
                  ),
                ),
                Container(
                  margin: EdgeInsets.only(top: 10.h),
                  width: 345.w,
                  height: 130.h,
                  decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(10.r),
                      color: Colors.white,
                      boxShadow: const [
                        BoxShadow(
                          color: Color.fromRGBO(68, 78, 87, 0.06),
                          // 阴影颜色和透明度
                          spreadRadius: 0,
                          // 阴影扩散范围
                          blurRadius: 9,
                          // 阴影模糊程度
                          offset: Offset(0, 2),
                          // 阴影偏移量（水平，垂直）
                        )
                      ]),
                  child: Column(
                    children: [
                      SizedBox(height: 20.h),
                      ThemeImagePath(
                        fileName: "task_item.png",
                        imgWidget: 28.r,
                        imgHeight: 28.r,
                      ),
                      SizedBox(height: 10.h),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Text(
                            "${state.maxCupSchedule}",
                            style: StyleConfig.otherStyle(
                                color: ColorConfig.searchTextColor,
                                fontWeight: FontWeight.w500,
                                fontSize: 20),
                          ),
                          SizedBox(width: 2.w),
                          Container(
                            margin: EdgeInsets.only(top: 3.h),
                            child: Text(
                              "task_unit".tr,
                              style: StyleConfig.otherStyle(
                                  color: ColorConfig.searchTextColor,
                                  fontWeight: FontWeight.w400,
                                  fontSize: 12),
                            ),
                          ),
                          SizedBox(width: 6.w),
                          ThemeImagePath(
                            fileName: 'task_image_02.png',
                            imgWidget: 14.r,
                            imgHeight: 14.r,
                          )
                        ],
                      ).inkWell(() => logic.drinkCountSheet()),
                      SizedBox(height: 8.h),
                      Text(
                        "task_setting_num".tr,
                        style: StyleConfig.otherStyle(
                            color: ColorConfig.shopDetailTextColor,
                            fontWeight: FontWeight.w400,
                            fontSize: 12),
                      ),
                    ],
                  ),
                ),
                Container(
                  margin: EdgeInsets.only(top: 22.h, left: 30.w, right: 20.w),
                  child: Row(
                    children: [
                      Text(
                        "task_setting_remind".tr,
                        style: StyleConfig.otherStyle(
                            color: ColorConfig.searchTextColor,
                            fontWeight: FontWeight.w500,
                            fontSize: 18),
                      ),
                      const Expanded(child: SizedBox()),
                      CupertinoSwitch(
                          activeTrackColor: ColorConfig.searchTextColor,
                          value: state.isShow,
                          onChanged: (value) => logic.showAction(value))
                    ],
                  ),
                ),
                Offstage(
                  offstage: !state.isShow,
                  child: Column(
                    children: [
                      Container(
                        margin: EdgeInsets.only(top: 8.h),
                        width: 345.w,
                        height: 130.h,
                        decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(10.r),
                            color: Colors.white,
                            boxShadow: const [
                              BoxShadow(
                                color: Color.fromRGBO(68, 78, 87, 0.06),
                                // 阴影颜色和透明度
                                spreadRadius: 0,
                                // 阴影扩散范围
                                blurRadius: 9,
                                // 阴影模糊程度
                                offset: Offset(0, 2),
                                // 阴影偏移量（水平，垂直）
                              )
                            ]),
                        child: Row(
                          children: [
                            Container(
                              padding: EdgeInsets.only(top: 20.h, right: 40.w),
                              width: 345.w / 2,
                              child: Column(
                                children: [
                                  Images(
                                    path: R.task_notice_png,
                                    width: 28.r,
                                    height: 28.r,
                                  ),
                                  SizedBox(height: 10.h),
                                  Row(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      Text(
                                        state.intervalTime,
                                        style: StyleConfig.otherStyle(
                                            color: ColorConfig.searchTextColor,
                                            fontWeight: FontWeight.w500,
                                            fontSize: 20),
                                      ),
                                      Container(
                                        margin: EdgeInsets.only(
                                            left: 2.w, top: 3.h),
                                        child: Text(
                                          "task_setting_hour".tr,
                                          style: StyleConfig.otherStyle(
                                              color:
                                                  ColorConfig.searchTextColor,
                                              fontWeight: FontWeight.w400,
                                              fontSize: 12),
                                        ),
                                      )
                                    ],
                                  ),
                                  SizedBox(height: 12.h),
                                  Text(
                                    "task_setting_interval".tr,
                                    style: StyleConfig.otherStyle(
                                        color: ColorConfig.shopDetailTextColor,
                                        fontWeight: FontWeight.w500,
                                        fontSize: 12),
                                  ),
                                ],
                              ),
                            ),
                            Container(
                              margin: EdgeInsets.only(top: 20.h),
                              width: 345.w / 2,
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Images(
                                    path: R.task_clock_png,
                                    width: 28.r,
                                    height: 28.r,
                                  ),
                                  SizedBox(height: 10.h),
                                  Text(
                                    "${state.startTime}-${state.endTime}",
                                    style: StyleConfig.otherStyle(
                                        color: ColorConfig.searchTextColor,
                                        fontWeight: FontWeight.w500,
                                        fontSize: 20),
                                  ),
                                  SizedBox(height: 12.h),
                                  Text(
                                    "task_setting_time".tr,
                                    style: StyleConfig.otherStyle(
                                        color: ColorConfig.shopDetailTextColor,
                                        fontWeight: FontWeight.w500,
                                        fontSize: 12),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),
                      Container(
                        margin: EdgeInsets.only(
                            top: 8.h,
                            bottom: ScreenUtil().statusBarHeight + 20.h),
                        width: 345.w,
                        height: 420.h,
                        decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(10.r),
                            color: Colors.white,
                            boxShadow: const [
                              BoxShadow(
                                color: Color.fromRGBO(68, 78, 87, 0.06),
                                // 阴影颜色和透明度
                                spreadRadius: 0,
                                // 阴影扩散范围
                                blurRadius: 9,
                                // 阴影模糊程度
                                offset: Offset(0, 2),
                                // 阴影偏移量（水平，垂直）
                              )
                            ]),
                        child: Column(
                          children: [
                            Container(
                              margin: EdgeInsets.only(left: 19.w, top: 20.h),
                              child: Row(
                                children: [
                                  Images(
                                    path: R.task_notice_01_png,
                                    width: 20.r,
                                    height: 20.r,
                                  ),
                                  SizedBox(width: 5.w),
                                  Text(
                                    "task_setting_interval".tr,
                                    style: StyleConfig.otherStyle(
                                        color: ColorConfig.searchTextColor,
                                        fontWeight: FontWeight.w500,
                                        fontSize: 14),
                                  )
                                ],
                              ),
                            ),
                            Container(
                              margin: EdgeInsets.only(top: 22.h),
                              child: Wrap(
                                spacing: 12.w, // 主轴（水平）方向间距
                                runSpacing: 12.h, // 纵轴（垂直）方向间距
                                children: List.generate(
                                    state.intervalList.length, (index) {
                                  return Container(
                                    width: 94.w,
                                    height: 38.h,
                                    decoration: BoxDecoration(
                                        borderRadius:
                                            BorderRadius.circular(10.r),
                                        color: state.sIndex == index
                                            ? ColorConfig.searchTextColor
                                            : const Color.fromRGBO(
                                                248, 247, 249, 1)),
                                    child: Center(
                                        child: ThemeText(
                                      dataStr: state
                                          .intervalList[index].intervalTips,
                                      keyName: 'textColor',
                                      flag: state.sIndex == index,
                                      subColor: ColorConfig.searchTextColor,
                                      fontSize: 14,
                                      fontWeight: FontWeight.w500,
                                    )),
                                  ).inkWell(() => logic.intervalAction(index));
                                }),
                              ),
                            ),
                            Container(
                              margin: EdgeInsets.only(left: 19.w, top: 52.h),
                              child: Row(
                                children: [
                                  Images(
                                    path: R.task_clock_01_png,
                                    width: 20.r,
                                    height: 20.r,
                                  ),
                                  SizedBox(width: 5.w),
                                  Text(
                                    "task_setting_time".tr,
                                    style: StyleConfig.otherStyle(
                                        color: ColorConfig.searchTextColor,
                                        fontWeight: FontWeight.w500,
                                        fontSize: 14),
                                  )
                                ],
                              ),
                            ),
                            Container(
                              margin: EdgeInsets.only(top: 30.h),
                              child: Row(
                                children: [
                                  Expanded(
                                    flex: 1,
                                    child: Column(
                                      children: [
                                        Row(
                                          mainAxisAlignment:
                                              MainAxisAlignment.center,
                                          children: [
                                            Text(
                                              "task_setting_start_time".tr,
                                              style: StyleConfig.otherStyle(
                                                  color: ColorConfig
                                                      .searchTextColor,
                                                  fontWeight: FontWeight.w500),
                                            ),
                                            SizedBox(width: 5.w),
                                            ThemeImagePath(
                                              fileName: "task_image_02.png",
                                              imgWidget: 14.r,
                                              imgHeight: 14.r,
                                            )
                                          ],
                                        ),
                                        SizedBox(height: 8.h),
                                        Text(
                                          state.startTime,
                                          style: StyleConfig.otherStyle(
                                              color: ColorConfig
                                                  .shopDetailTextColor,
                                              fontSize: 14,
                                              fontWeight: FontWeight.w500),
                                        )
                                      ],
                                    ).inkWell(() => logic.startTimeAction()),
                                  ),
                                  Expanded(
                                    flex: 1,
                                    child: Column(
                                      children: [
                                        Row(
                                          mainAxisAlignment:
                                              MainAxisAlignment.center,
                                          children: [
                                            Text(
                                              "task_setting_end_time".tr,
                                              style: StyleConfig.otherStyle(
                                                  color: ColorConfig
                                                      .searchTextColor,
                                                  fontWeight: FontWeight.w500),
                                            ),
                                            SizedBox(width: 5.w),
                                            ThemeImagePath(
                                              fileName: 'task_image_02.png',
                                              imgWidget: 14.r,
                                              imgHeight: 14.r,
                                            )
                                          ],
                                        ),
                                        SizedBox(height: 8.h),
                                        Text(
                                          state.endTime,
                                          style: StyleConfig.otherStyle(
                                              color: ColorConfig
                                                  .shopDetailTextColor,
                                              fontSize: 14,
                                              fontWeight: FontWeight.w500),
                                        )
                                      ],
                                    ).inkWell(() => logic.endTimeAction()),
                                  )
                                ],
                              ),
                            ),
                            Container(
                              margin: EdgeInsets.only(top: 30.h),
                              width: 315.w,
                              height: 1.h,
                              color: const Color.fromRGBO(249, 249, 249, 1),
                            ),
                            Container(
                              margin: EdgeInsets.only(top: 19.h),
                              width: 200.w,
                              height: 48.h,
                              decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(24.r),
                                  color: const Color.fromRGBO(40, 39, 46, 1)),
                              child: Center(
                                child: ThemeText(
                                  dataStr: "task_setting_btn".tr,
                                  keyName: 'textColor',
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ).inkWell(() => logic.saveAction()),
                          ],
                        ),
                      )
                    ],
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}
