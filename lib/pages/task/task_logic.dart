import 'dart:math';
import 'dart:ui';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:getx_xiaopa/base/base.dart';
import 'package:getx_xiaopa/extension/widget_extension.dart';
import 'package:getx_xiaopa/network/http_client.dart';
import 'package:getx_xiaopa/network/model/request_body.dart';
import 'package:getx_xiaopa/network/model/result.dart';
import 'package:getx_xiaopa/pages/task/model/task_item_result.dart';
import 'package:getx_xiaopa/pages/task/widget/drink_count_sheet.dart';
import 'package:getx_xiaopa/routes/router.dart';
import 'package:getx_xiaopa/storage/storage.dart';
import 'package:getx_xiaopa/utils/utils.dart';

import 'task_achievement/widget/task_unlock_achievement.dart';
import 'task_state.dart';

class TaskLogic extends BaseCommonController {
  @override
  final TaskState state = TaskState();

  @override
  void initData() {
    state.deveciId = Get.arguments;

    state.scrollController.addListener(() {
      ///监听滚动位置设置导航栏颜色
      double opacity;
      opacity = state.scrollController.offset / 100;

      if (opacity > 0.8) opacity = 1;
      if (opacity > 1) opacity = 1;

      state.navColor = state.scrollController.offset > 20.h
          ? Colors.white.withValues(alpha: opacity)
          : Colors.transparent;

      update(["task_view"]);
    });

    _init();
  }

  @override
  void onHidden() {}

  _init() async {
    await _loadImage();

    getNowDate();

    await _getTaskTypeItems();
    await _getTaskTarget();
    await _getTaskItems();

    if (state.isDrinkSuccess) {
      int a = Random().nextInt(state.drinkSuccessTip.length);
      state.drinkTipStr = state.drinkSuccessTip[a];
    } else {
      int a = Random().nextInt(state.drinkTip.length);
      state.drinkTipStr = state.drinkTip[a];
    }

    state.isInit = true;

    update(["task_view"]);
  }

  /// 计算完成进度
  _completeScheduleAction() {
    state.completeSchedule =
        ((state.cupSliderSchedule / state.maxCupSchedule) * 100).round();
  }

  _loadImage() async {
    final ByteData data = await rootBundle
        .load(Get.find<ThemeImageController>().taskImage01); //图片路径
    final Codec codec = await instantiateImageCodec(data.buffer.asUint8List());
    final FrameInfo frameInfo = await codec.getNextFrame();
    state.imageInfo = frameInfo.image;
  }

  void drinkAction(int index) {
    HapticFeedback.lightImpact();
    _createTaskItem(taskTypeId: state.taskTypeList[index].id ?? "");
  }

  toDetailPage() {
    var map = {
      "continuous": "${state.taskTargetModel.continuous ?? 0}",
      "isDrinkSuccess": state.isDrinkSuccess,
      "maxCupSchedule": state.maxCupSchedule,
      "cupSliderSchedule": state.cupSliderSchedule,
      "drinkTip": state.drinkTipStr
    };
    Get.toNamed(AppRoutes.TASK_DETAIL, arguments: map)?.then((value) {
      if (value) {
        state.isInit = false;
        update(["task_view"]);
        Future.delayed(const Duration(milliseconds: 200), () {
          state.isInit = true;
          update(["task_view"]);
        });
      }
    });
  }

  toSharePage() {
    int shareType = UserStore.to.isDefaultSkin ? 2 : 1;
    var map = {
      "shareType": shareType,
      "continuous": "${state.taskTargetModel.continuous}",
    };
    Get.toNamed(AppRoutes.TASK_SHARE, arguments: map);
  }

  toSettingpage() {
    // Get.dialog(
    //   TaskUnLockAchievement(
    //     skidId: state.unlockSkinId,
    //     onTap: () {
    //       int shareType = UserStore.to.isDefaultSkin ? 2 : 1;
    //       var map = {
    //         "shareType": shareType,
    //         "continuous": '${state.taskTargetModel.continuous}'
    //       };
    //       Get.toNamed(AppRoutes.TASK_ACHIEVEMENT, arguments: map);
    //     },
    //   ),
    //   transitionDuration: const Duration(milliseconds: 300),
    // );

    var map = {
      "max_cup_schedule": state.maxCupSchedule,
      "isChange": state.taskItemList.isEmpty,
      "target_id": state.taskTargetModel.id
    };
    Get.toNamed(AppRoutes.TASK_SETTING, arguments: map)?.then((value) {
      _getTaskTarget();
    });
  }

  drinkCountSheet() {
    ///一天中没喝水前才能修改喝水目标
    if (state.taskItemList.isNotEmpty) {
      CToast.showToast("task_drink_error_tips".tr);
      return;
    }
    Get.bottomSheet(DrinkCountSheet(
      initialValue: state.maxCupSchedule,
      data: state.itemList,
      onTap: (index) {
        _changeTaskTarget(theNum: index);
      },
    ));
  }

  ///获取当前日期
  ///[isDay] 是否返回当天多少号
  String getNowDate({bool isDay = false}) {
    DateTime temp = DateTime.now();
    String dateStr = "";
    if (isDay) {
      dateStr = "${temp.day}";
    } else {
      dateStr = "${temp.year}-${temp.month}-${temp.day} 00:00:00";
    }
    return dateStr;
  }

  ///====================== 网络请求 =======================
  ///任务类型列表
  _getTaskTypeItems() async {
    var map = RequestBody(orders: [Order(expr: "asc", name: "sort")]).toJson();
    Result result = await http.taskTypeItems(map);
    if (result.code == 0) {
      state.taskTypeList = result.data.data;
    } else {
      CToast.showToast(result.msg!);
    }
  }

  ///任务目标
  _getTaskTarget() async {
    var map = RequestBody(filters: [
      Filter(expr: "=", name: "device_id", value: state.deveciId)
    ]).toJson();
    Result result = await http.taskTargetItems(map);
    if (result.code == 0) {
      if (result.data.data.isNotEmpty) {
        state.taskTargetModel = result.data.data[0];
        state.maxCupSchedule = state.taskTargetModel.theNum ?? 8;
      } else {
        state.maxCupSchedule = 8;
      }
      update(["task_view"]);
    } else {
      CToast.showToast(result.msg!);
    }
  }

  ///任务列表
  _getTaskItems() async {
    var map = RequestBody(pagination: false, filters: [
      Filter(expr: "=", name: "device_id", value: state.deveciId),
      Filter(expr: ">=", name: "created_at", value: getNowDate())
    ]).toJson();
    Result result = await http.taskItems(map);
    if (result.code == 0) {
      state.taskItemList.clear();

      state.taskItemList = result.data.data;

      state.cupSchedule = state.taskItemList.length.toDouble();

      if (state.cupSchedule == state.maxCupSchedule) {
        Future.delayed(const Duration(milliseconds: 100), () {
          state.rController.start();
          Future.delayed(const Duration(seconds: 1), () {
            state.rController.stop();
          });
        });
      }

      state.cupSliderSchedule = state.cupSchedule;
      if (state.cupSliderSchedule >= state.maxCupSchedule) {
        state.isDrinkSuccess = true;
        state.cupSliderSchedule = state.maxCupSchedule.toDouble();

        /// 如果是已完成目标，每次进来只散花一次
        if (!state.isInit) {
          Future.delayed(const Duration(milliseconds: 100), () {
            state.rController.start();
            Future.delayed(const Duration(seconds: 1), () {
              state.rController.stop();
            });
          });
        }
      } else {
        state.isDrinkSuccess = false;
      }

      _completeScheduleAction();

      ///处理下任务列表，只需要图片和时间即可
      state.taskItem.clear();
      for (var i in state.taskItemList) {
        for (var t in state.taskTypeList) {
          if (i.taskTypeId == t.id) {
            String temp = TimeUtil.getSimpleHM(i.createdAt);
            state.taskItem.add(TaskItem(
              imgStr: fileUrl(t.picture ?? ""),
              timeStr: temp,
            ));
          }
        }
      }
      netState = NetState.dataSuccessState;
      update(["task_view"]);
    } else {
      CToast.showToast(result.msg!);
    }
  }

  ///任务创建(喝水)
  _createTaskItem({required String taskTypeId}) async {
    CToast.showLoading();
    var map = {"device_id": state.deveciId, "task_type_id": taskTypeId};
    Result result = await http.taskCreate(map);
    if (result.code == 0) {
      EventBus().emit("addWater");
      _getTaskItems();
      TaskItemModel taskItemModel = result.data;
      if (taskItemModel.isUnlock ?? false) {
        ///有新皮肤解锁，重新请求已解锁皮肤列表数据
        await UserStore.to.getUnlockSkinList();

        state.unlockSkinId = taskItemModel.skinId ?? "";
        Get.dialog(
          TaskUnLockAchievement(
            skidId: state.unlockSkinId,
            onTap: () {
              int shareType = UserStore.to.isDefaultSkin ? 2 : 1;
              var map = {
                "shareType": shareType,
                "continuous": '${state.taskTargetModel.continuous}'
              };
              Get.toNamed(AppRoutes.TASK_ACHIEVEMENT, arguments: map);
            },
          ),
          transitionDuration: const Duration(milliseconds: 300),
        );
      }
    } else {
      CToast.showToast(result.msg!);
    }
  }

  ///修改任务目标
  _changeTaskTarget({required int theNum}) async {
    var map = {
      "id": state.taskTargetModel.id,
      "num": theNum,
    };
    Result result = await http.taskTargetUpdate(map);
    if (result.code == 0) {
      state.maxCupSchedule = theNum;
      _getTaskItems();
    } else {
      CToast.showToast(result.msg!);
    }
  }
}
