import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:getx_xiaopa/values/values.dart';

class InfinitePicker<T> extends StatefulWidget {
  final List<T> data;
  final T initialValue;
  final Widget Function(BuildContext, T, bool) itemBuilder;
  final ValueChanged<T>? onSelected;
  final double itemExtent;
  final double height;

  const InfinitePicker({
    super.key,
    required this.data,
    required this.initialValue,
    required this.itemBuilder,
    this.onSelected,
    this.itemExtent = 50,
    this.height = 250,
  });

  @override
  State<InfinitePicker<T>> createState() => _InfinitePickerState<T>();
}

class _InfinitePickerState<T> extends State<InfinitePicker<T>> {
  static const int loopMultiplier = 1000;

  late final FixedExtentScrollController _controller;
  late final List<T> _loopedData;

  int get _dataLength => widget.data.length;

  int get _middleIndex => _dataLength * loopMultiplier ~/ 2;

  int get _selectedIndex => _controller.selectedItem % _dataLength;

  T get selectedValue => widget.data[_selectedIndex];

  @override
  void initState() {
    super.initState();

    // 构造无限数据
    _loopedData = List.generate(_dataLength * loopMultiplier, (index) {
      return widget.data[index % _dataLength];
    });

    // 找到初始值的偏移
    int initialOffset = widget.data.indexOf(widget.initialValue);
    if (initialOffset < 0) initialOffset = 0;

    _controller = FixedExtentScrollController(
      initialItem: _middleIndex + initialOffset,
    );
  }

  void _onScrollEnd() {
    // 防止滚动太远造成跳跃或性能问题
    final currentIndex = _controller.selectedItem;
    if ((currentIndex - _middleIndex).abs() > _dataLength * 10) {
      final resetIndex = _middleIndex + _selectedIndex;
      _controller.jumpToItem(resetIndex);
    }
    setState(() {});

    widget.onSelected?.call(selectedValue);
  }

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: widget.height.h,
      child: Stack(
        children: [
          Align(
            alignment: Alignment.center,
            child: Container(
              width: 314.w,
              height: widget.itemExtent.h,
              decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(6.r),
                  color: ColorConfig.searchTextColor),
            ),
          ),
          NotificationListener<ScrollEndNotification>(
            onNotification: (notification) {
              _onScrollEnd();
              return false;
            },
            child: ListWheelScrollView.useDelegate(
              controller: _controller,
              itemExtent: widget.itemExtent.h,
              physics: const FixedExtentScrollPhysics(),
              overAndUnderCenterOpacity: 0.5,
              perspective: 0.002,
              childDelegate: ListWheelChildBuilderDelegate(
                childCount: _loopedData.length,
                builder: (context, index) {
                  if (index < 0 || index >= _loopedData.length) return null;
                  final T item = _loopedData[index];
                  final bool isSelected = index == _controller.selectedItem;
                  return Center(
                      child: widget.itemBuilder(context, item, isSelected));
                },
              ),
            ),
          ),
        ],
      ),
    );
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }
}
