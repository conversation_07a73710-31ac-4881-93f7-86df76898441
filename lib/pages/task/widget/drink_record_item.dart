import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:getx_xiaopa/extension/images.dart';
import 'package:getx_xiaopa/network/http_client.dart';
import 'package:getx_xiaopa/pages/task/task_state.dart';
import 'package:getx_xiaopa/values/values.dart';
import 'package:getx_xiaopa/widget/theme_text.dart';

class DrinkRecordItem extends StatelessWidget {
  final TaskItem taskItem;

  const DrinkRecordItem({super.key, required this.taskItem});

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.only(top: 10.h, left: 20.w, right: 20.w),
      padding: EdgeInsets.only(left: 17.w, right: 17.w),
      height: 59.h,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(10.r),
        color: Colors.white,
        boxShadow: const [
          BoxShadow(
            color: Color.fromRGBO(68, 78, 87, 0.06),
            // 阴影颜色和透明度
            spreadRadius: 0,
            // 阴影扩散范围
            blurRadius: 9,
            // 阴影模糊程度
            offset: Offset(0, 2),
            // 阴影偏移量（水平，垂直）
          )
        ],
      ),
      child: Row(
        children: [
          Images(
            path: fileUrl(taskItem.imgStr),
            width: 30.r,
            height: 30.r,
          ),
          SizedBox(width: 7.w),
          Text(
            taskItem.timeStr,
            style: StyleConfig.otherStyle(
                color: ColorConfig.authenticationTextColor,
                fontWeight: FontWeight.w500),
          ),
          const Expanded(child: SizedBox()),
          const ThemeText(
            dataStr: "+1",
            keyName: 'drinkRecord',
            fontWeight: FontWeight.w500,
            fontSize: 20,
          ),
          SizedBox(width: 2.w),
          Container(
            margin: EdgeInsets.only(top: 4.h),
            child: ThemeText(
                dataStr: "task_record_item_unit".tr,
                keyName: 'drinkRecord',
                fontSize: 14),
          )
        ],
      ),
    );
  }
}
