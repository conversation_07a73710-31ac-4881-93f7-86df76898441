import 'dart:async';
import 'dart:math';
import 'dart:ui' as ui;

import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:getx_xiaopa/network/dio_util.dart';
import 'package:getx_xiaopa/network/http_client.dart';
import 'package:getx_xiaopa/r.dart';
import 'package:getx_xiaopa/storage/storage.dart';
import 'package:getx_xiaopa/utils/utils.dart';
import 'package:sensors_plus/sensors_plus.dart';

class WaterFillWidgetV3 extends StatefulWidget {
  final double currentCapacity;
  final double maxCapacity;
  final String imagePath;
  final bool isOut;

  const WaterFillWidgetV3({
    super.key,
    required this.currentCapacity,
    required this.maxCapacity,
    required this.imagePath,
    this.isOut = false,
  });

  @override
  State<WaterFillWidgetV3> createState() => _WaterFillWidgetV3State();
}

class _WaterFillWidgetV3State extends State<WaterFillWidgetV3>
    with TickerProviderStateMixin {
  ui.Image? _maskImage;

  late StreamSubscription _sensorSub;
  late AnimationController _waveController;

  Offset _gravity = const Offset(0, 1);
  Offset _targetGravity = const Offset(0, 1);

  late final Ticker _gravityTicker;

  @override
  void initState() {
    super.initState();

    _loadMask();

    _waveController = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 2),
    )
      ..addListener(() {
        setState(() {});
      })
      ..repeat();

    _gravityTicker = Ticker((_) {
      final delta = (_targetGravity - _gravity).distance;
      if (delta > 0.001) {
        _gravity = Offset.lerp(_gravity, _targetGravity, 0.05)!;
        setState(() {}); // 只更新 painter
      }
    })
      ..start();

    _sensorSub = accelerometerEventStream().listen((event) {
      final dir = Offset(event.x, event.y);
      if (dir.distance > 0.1) {
        _targetGravity = dir / dir.distance;
      } else {
        _targetGravity = const Offset(0, 1);
      }
    });
  }

  Future<void> _loadMask() async {
    if (widget.imagePath.isEmpty) {
      ///数据为空则使用本地图片
      _maskImage = await _loadUiImage(R.task_drink_bg_png);
      return;
    }
    var img = widget.imagePath.startsWith("assets")
        ? await _loadUiImage(widget.imagePath)
        : await _loadUiImageFromNetworkWithDio(fileUrl(widget.imagePath));
    if (!mounted) return;
    setState(() => _maskImage = img);
  }

  Future<ui.Image> _loadUiImage(String assetPath) async {
    final data = await rootBundle.load(assetPath);
    final codec = await ui.instantiateImageCodec(data.buffer.asUint8List());
    final frame = await codec.getNextFrame();
    return frame.image;
  }

  Future<ui.Image> _loadUiImageFromNetworkWithDio(String imageUrl) async {
    try {
      final imageData = await DioUtil.getInstance().getImageBytes(imageUrl);
      if (imageData.isEmpty) {
        logE("Failed to load image from network: $imageUrl");
      }
      final codec =
          await ui.instantiateImageCodec(Uint8List.fromList(imageData));
      final frame = await codec.getNextFrame();
      return frame.image;
    } catch (e) {
      logD("图片请求错误");
    }
    return _loadUiImage(R.task_drink_bg_png);
  }

  @override
  void dispose() {
    _waveController.dispose();
    _gravityTicker.dispose();
    _sensorSub.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (_maskImage == null) {
      return const Center(child: CircularProgressIndicator());
    }
    final width = _maskImage!.width.toDouble();
    final height = _maskImage!.height.toDouble();
    return FittedBox(
      fit: BoxFit.contain,
      child: SizedBox(
        width: width,
        height: height,
        child: CustomPaint(
          painter: _OptimizedWaterPainter(
              image: _maskImage!,
              gravity: _gravity,
              progress: widget.currentCapacity / widget.maxCapacity,
              wavePhase: _waveController.value * 2 * pi,
              isOut: widget.isOut),
        ),
      ),
    );
  }
}

class _OptimizedWaterPainter extends CustomPainter {
  final ui.Image image;
  final Offset gravity;
  final double progress;
  final double wavePhase;
  final bool isOut;

  _OptimizedWaterPainter({
    required this.image,
    required this.gravity,
    required this.progress,
    required this.wavePhase,
    required this.isOut,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final rect = Offset.zero & size;
    if (progress <= 0) {
      // 2️⃣ 再绘制瓶子轮廓图像
      final paint = Paint()
        ..filterQuality = FilterQuality.high
        ..colorFilter = ColorFilter.mode(
            Get.find<ThemeColorController>().gWaterBgColor, BlendMode.srcIn);
      // 2. 再绘制原图，遮住阴影内部，只保留外发散的阴影
      canvas.drawImageRect(
        image,
        Rect.fromLTWH(
            -3, -2, image.width.toDouble() + 6, image.height.toDouble() + 4),
        rect,
        paint,
      );
    } else {
      // 3️⃣ 正常绘制水波 + 裁剪遮罩
      final gravityNorm = correctGravity(gravity);
      final center = Offset(size.width / 2, size.height / 2);
      final tangent = Offset(-gravityNorm.dy, gravityNorm.dx);
      final clampedProgress = progress.clamp(0.0, 1.0);
      Offset base =
          center + gravityNorm * size.shortestSide * (0.5 - clampedProgress);

      final path = Path();
      const segments = 80;
      double waveHeight = 5;

      final dynamicHeight = 20 * (1 + gravity.dx.abs()); // 屏幕倾斜更大波动

      for (int i = 0; i <= segments; i++) {
        final t = i / segments;
        final offsetX = (t - 0.5) * size.width * 1.5;
        final waveOffset =
            waveHeight * sin(offsetX / dynamicHeight + wavePhase);
        final p = base + tangent * offsetX + gravityNorm * waveOffset;
        if (i == 0) {
          path.moveTo(p.dx, p.dy);
        } else {
          path.lineTo(p.dx, p.dy);
        }
      }

      final Offset p0 = base + tangent * (-size.width); // 波浪起点
      final Offset pN = base + tangent * (size.width); // 波浪终点

      final Offset end1 = pN + gravityNorm * size.longestSide;
      final Offset end2 = p0 + gravityNorm * size.longestSide;

      path.lineTo(end1.dx, end1.dy);
      path.lineTo(end2.dx, end2.dy);
      path.close();

      canvas.saveLayer(rect, Paint());

      // 1️⃣ 背景色（涂轮廓内的颜色）
      canvas.drawRect(
          rect,
          Paint()
            ..color = Get.find<ThemeColorController>().gWaterBgColor); // 💧背景色

      //水的颜色
      canvas.drawPath(path,
          Paint()..color = Get.find<ThemeColorController>().gWaterFullColor);

      // 2. 用轮廓 alpha 遮罩裁剪水波（遮住超出部分）
      final maskPaint = Paint()
        ..blendMode = BlendMode.dstIn
        ..filterQuality = FilterQuality.high;

      canvas.drawImageRect(
        image,
        Rect.fromLTWH(
            -2, -2, image.width.toDouble() + 4, image.height.toDouble() + 4),
        Rect.fromLTWH(-4, -4, size.width + 8, size.height + 8),
        maskPaint,
      );
      canvas.restore();
    }

    if (UserStore.to.deviceTheme == 2) {
      _drawOuterShadow(canvas, rect);
    }
  }

  void _drawOuterShadow(Canvas canvas, Rect rect) {
    final src =
        Rect.fromLTWH(0, 0, image.width.toDouble(), image.height.toDouble());
    const Offset shadowOffset = Offset(0, 0); // 阴影偏移（可调）
    const double blurSigma = 58; // 模糊半径（可调）
    final shadowBounds = rect.shift(shadowOffset).inflate(blurSigma * 2);

    // 单独图层做阴影，避免污染其它内容
    canvas.saveLayer(shadowBounds, Paint());

    // 1) 画一张“偏移 + 模糊 + 着色”的瓶子（作为阴影）
    final shadowPaint = Paint()
      ..imageFilter = ui.ImageFilter.blur(sigmaX: blurSigma, sigmaY: blurSigma)
      ..colorFilter = ColorFilter.mode(
          Color.fromRGBO(254, 139, 172, isOut ? 0.4 : 0.1),
          BlendMode.srcIn); //0.1
    canvas.drawImageRect(image, src, rect.shift(shadowOffset), shadowPaint);

    // 2) 把瓶子本体区域扣掉（只保留外侧的阴影环）
    final cutoutPaint = Paint()..blendMode = BlendMode.dstOut;
    canvas.drawImageRect(image, src, rect, cutoutPaint);

    canvas.restore();
  }

  Offset correctGravity(Offset g) {
    if (g.distance == 0) return const Offset(0, 1);

    return Offset(-g.dx, g.dy) / g.distance;
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}
