import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:getx_xiaopa/extension/images.dart';
import 'package:getx_xiaopa/extension/widget_extension.dart';
import 'package:getx_xiaopa/network/http_client.dart';
import 'package:getx_xiaopa/pages/task/model/task_type_result.dart';
import 'package:getx_xiaopa/r.dart';
import 'package:getx_xiaopa/storage/storage.dart';
import 'package:getx_xiaopa/values/values.dart';

class DrinkItem extends StatelessWidget {
  final int index;
  final TaskTypeModel taskTypeModel;
  final Function(int) onTap;

  const DrinkItem(
      {super.key,
      required this.index,
      required this.taskTypeModel,
      required this.onTap});

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.only(left: index == 0 ? 0 : 10.w),
      width: 68.w,
      height: 92.h,
      child: Column(
        children: [
          SizedBox(
            width: 68.r,
            height: 68.r,
            child: Stack(
              children: [
                Images(
                  path: fileUrl(taskTypeModel.picture ?? ""),
                  width: 68.r,
                  height: 68.r,
                ),
                Positioned(
                  left: 26.w,
                  right: 25.w,
                  bottom: 3.h,
                  child: Images(
                    path: R.task_drink_image_png,
                    width: 17.r,
                    height: 17.r,
                  ),
                )
              ],
            ),
          ),
          Text(
            ConfigStore.to.getLocaleCode() == "zh"
                ? taskTypeModel.name ?? ""
                : taskTypeModel.enName ?? "",
            style: StyleConfig.otherStyle(
                color: const Color.fromRGBO(69, 79, 106, 1),
                fontSize: ConfigStore.to.getLocaleCode() == "zh" ? 14 : 12),
          ),
        ],
      ),
    ).inkWell(() => onTap(index));
  }
}
