import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:getx_xiaopa/base/base.dart';
import 'package:getx_xiaopa/extension/images.dart';
import 'package:getx_xiaopa/extension/widget_extension.dart';
import 'package:getx_xiaopa/pages/ai_chat/widget/phone_call_widget.dart';
import 'package:getx_xiaopa/pages/ai_chat/widget/speak_widget.dart';
import 'package:getx_xiaopa/pages/ai_chat/widget/text_widget.dart';
import 'package:getx_xiaopa/utils/utils.dart';
import 'package:getx_xiaopa/values/values.dart';
import 'package:getx_xiaopa/widget/widget.dart';

import 'ai_chat_logic.dart';

class AiChatPage extends BaseCommonView {
  AiChatPage({super.key});

  final logic = Get.put(AiChatLogic());
  final state = Get.find<AiChatLogic>().state;

  @override
  bool? get isHiddenNav => true;

  @override
  bool get customWidget => true;

  @override
  Widget buildContent() {
    return GetBuilder<AiChatLogic>(
      id: "ai_chat_view",
      builder: (_) => createCommonView(
        logic,
        (_) => Scaffold(
          body: Container(
            width: 1.sw,
            height: 1.sh,
            color: Colors.transparent,
            child: Stack(
              alignment: AlignmentDirectional.topCenter,
              children: [
                Container(
                  width: 1.sw,
                  height: 1.sh,
                  constraints: const BoxConstraints.expand(),
                  child: Images(
                    path: state.imagePath,
                    boxFit: BoxFit.cover,
                  ),
                ).inkWell(() => logic.clickScreen()),
                // Positioned(
                //   right: 20.w,
                //   top: ScreenUtil().statusBarHeight + 5.h,
                //   child: Images(
                //     path: R.brain_switch_png,
                //     width: 36.w,
                //     height: 36.h,
                //   ).inkWell(() => logic.switchBrain()),
                // ),
                // Positioned(
                //   right: 12.w,
                //   top: ScreenUtil().statusBarHeight + 20.h,
                //   child: Images(
                //     path: R.ai_chat_town_png,
                //     width: 53.w,
                //     height: 56.h,
                //   ).inkWell(() => logic.toTownAction()),
                // ),
                Positioned(
                  bottom: 0.h,
                  width: 1.sw,
                  child: Column(
                    children: [
                      SizedBox(
                        height: 250.h,
                        child: GestureDetector(
                          onTap: () {
                            logic.cancelSpeakAction();
                          },
                          child: ListView.builder(
                            shrinkWrap: true,
                            controller: state.scrollController,
                            itemCount: state.messageList.length,
                            itemBuilder: (context, index) {
                              return state.messageList[index];
                            },
                          ),
                        ),
                      ),
                      _switchWidget(state.widgetType)
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
        initBuilder: () => const LoadStatusWidget(),
      ),
    );
  }

  Widget _switchWidget(String type) {
    switch (type) {
      case "speak":
        return SpeakWidget(
          statusWidget: state.isSpeakTalk
              ? Container(
                  width: 255.w,
                  height: 40.h,
                  margin: EdgeInsets.only(left: 9.w, right: 9.w),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(10.r),
                    color: const Color.fromRGBO(40, 39, 46, 1),
                    border: Border.all(
                        width: 1.r,
                        color: Get.find<ThemeColorController>().gConBrodeColor),
                  ),
                  child: Center(
                    child: ThemeText(
                        dataStr: "chat_btn_release".tr,
                        keyName: "textColor",
                        fontSize: 16,
                        fontWeight: FontWeight.w500),
                  ),
                )
              : Container(
                  width: 255.w,
                  height: 40.h,
                  margin: EdgeInsets.only(left: 9.w, right: 9.w),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(10.r),
                    color: Colors.white,
                    border: Border.all(
                        width: 1.r,
                        color: Get.find<ThemeColorController>().gConBrodeColor),
                  ),
                  child: Center(
                    child: Text(
                      "chat_btn_press".tr,
                      style: StyleConfig.otherStyle(
                          color: ColorConfig.searchTextColor,
                          fontSize: 16,
                          fontWeight: FontWeight.w500),
                    ),
                  ),
                ),
          languageAction: () {
            logic.switchTypeAction("text", isStopAudio: false);
          },
          phoneAction: () {
            logic.switchTypeAction("phone");
          },
          speakStartAction: (details) {
            logic.speakStartAction();
          },
          speakMoveAction: (details) {
            logic.speakMoveAction();
          },
          speakEndAction: () {
            logic.speakEndAction();
          },
          speakEndTap: (details) {
            logic.speakEndAction();
          },
          cancelSpeak: () {
            logic.cancelSpeakAction();
          },
        );
      case "text":
        return TextWidget(
          focusNode: state.focusNode,
          speakAction: () {
            logic.switchTypeAction("speak", isStopAudio: false);
          },
          phoneAction: () {
            logic.switchTypeAction("phone");
          },
          sendMessage: (String content) {
            logic.sendTextFlied(content);
          },
          textEditingController: state.textEditingController,
        );
      case "phone":
        return PhoneCallWidget(
          playOrPauseAction: () {
            logic.switchCallPause();
          },
          closePhoneCallAction: () {
            logic.switchTypeAction("speak");
          },
        );
      default:
        return const SizedBox();
    }
  }
}
