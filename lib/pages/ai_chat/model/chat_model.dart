class ChatModel {
  int? code;
  int? type;
  String? assistant;
  String? tool;
  String? error;
  String? extra;
  String? msg;

  ChatModel({
    this.code,
    this.type,
    this.assistant,
    this.tool,
    this.error,
    this.extra,
    this.msg
  });
  ChatModel.fromJson(Map<String, dynamic> json) {
    code = json['code']?.toInt();
    type = json['type']?.toInt();
    assistant = json['assistant']?.toString();
    tool = json['tool']?.toString();
    error = json['error']?.toString();
    extra = json['extra']?.toString();
    msg = json['msg']?.toString();
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['code'] = code;
    data['type'] = type;
    data['assistant'] = assistant;
    data['tool'] = tool;
    data['error'] = error;
    data['extra'] = extra;
    data['msg'] = msg;
    return data;
  }
}
