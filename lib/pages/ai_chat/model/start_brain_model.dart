///
/// Code generated by jsonToDartModel https://ashamp.github.io/jsonToDartModel/
///
class StartBrainModel {
/*
{
  "room_id": "4163364096835584",
  "token": "001670504547476c001835c30abUAAYGGAFx6oHZwAAAAAQADQxNjMzNjQwOTY4MzU1ODQQADQxNjM0MDgxODczNTkyMzIFAAAAAAAAAAEAAAAAAAIAAAAAAAMAAAAAAAQAAAAAACAAtDWkzuVts8a0jiV4LvjRqFM29Vm1xCWkJHrSztFg7hc=",
  "user_id": "4163408187359232"
}
*/

  String? appId;
  String? roomId;
  String? token;
  String? userId;

  StartBrainModel({this.roomId, this.token, this.userId, this.appId});

  StartBrainModel.fromJson(Map<String, dynamic> json) {
    roomId = json['room_id']?.toString();
    token = json['token']?.toString();
    userId = json['user_id']?.toString();
    appId = json['app_id']?.toString();
  }

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['room_id'] = roomId;
    data['token'] = token;
    data['user_id'] = userId;
    data['app_id'] = appId;
    return data;
  }
}
