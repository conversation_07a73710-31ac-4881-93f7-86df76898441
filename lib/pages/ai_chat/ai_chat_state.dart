import 'package:flutter/cupertino.dart';
import 'package:get/get.dart';
import 'package:getx_xiaopa/pages/ai_chat/model/start_brain_model.dart';
import 'package:getx_xiaopa/pages/ai_chat/widget/chat_bubble/chat_bubble.dart';
import 'package:getx_xiaopa/pages/ai_chat/widget/chat_bubble/live_chat_text_renderer.dart';

class AiChatState {
  ///按住说话控制按钮变化
  var isSpeakTalk = false;

  var frequency = 2.0;
  var tipString = "chat_listening".tr;

  //speak,phone,text
  var widgetType = 'speak';

  String staticUrl = '';
  String dynamicUrl = '';

  ///标记视频的下载task
  String staticTask = '';
  String dynamicTask = '';

  ///标记视频下载情况
  bool staticDownload = false;
  bool dynamicDownload = false;

  ///心情类型
  String moodType = "normal";

  FocusNode focusNode = FocusNode();
  final TextEditingController textEditingController = TextEditingController();

  ///音色id
  String cSpeakId = "";

  ///数字人id
  String cBrainId = "";

  ///数字人名称
  String cBrainName = "";

  ///数字人欢迎语
  String cWelcome = "";

  ///数字人声音id(用于在map中寻找音色id)
  String cVoiceId = "";

  ScrollController scrollController = ScrollController();

  bool isInit = false;

  //phone模式是否在暂停对话
  var isPauseOnPhoneCall = false;

  StartBrainModel startBrainModel = StartBrainModel();

  String tempStr = '';

  String cMac = '';
  String imagePath = "";

  ///是否正在说话
  bool isSpeaking = false;

  Map<String, RxList<ChatBubble>> messageHistory = {};
  RxList<ChatBubble> messageList = RxList();
  Map<String, ChatTextController> activeControllers = {};

  ///重试下载次数
  int retryCount = 0;

  ///打断了就不要继续输出小耙说话了 默认为输出=true
  bool isLog = true;

  bool isLeaveRoom = false;

  AiChatState() {
    ///Initialize variables
  }
}
