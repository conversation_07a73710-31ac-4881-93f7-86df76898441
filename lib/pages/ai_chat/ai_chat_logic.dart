import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:getx_xiaopa/base/base.dart';
import 'package:getx_xiaopa/extension/widget_extension.dart';
import 'package:getx_xiaopa/network/http_client.dart';
import 'package:getx_xiaopa/network/model/result.dart';
import 'package:getx_xiaopa/pages/ai_chat/widget/chat_bubble/chat_bubble.dart';
import 'package:getx_xiaopa/pages/ai_chat/widget/chat_bubble/live_chat_text_renderer.dart';
import 'package:getx_xiaopa/pages/diary/utils/diary_store.dart';
import 'package:getx_xiaopa/routes/router.dart';
import 'package:getx_xiaopa/storage/storage.dart';
import 'package:getx_xiaopa/utils/utils.dart';
import 'package:getx_xiaopa/values/values.dart';
import 'package:volc_engine_rtc/volc_engine_rtc.dart';
import 'package:wakelock_plus/wakelock_plus.dart';

import 'ai_chat_state.dart';

class AiChatLogic extends BaseCommonController {
  @override
  final AiChatState state = AiChatState();

  @override
  void initData() async {
    if (UserStore.to.deviceList.isNotEmpty) {
      CommonStore.to.getBrainDetail(
          UserStore.to.deviceList[UserStore.to.selectDevice].botId ?? '');
    }

    WidgetsBinding.instance.addPostFrameCallback((_) async {
      DownloadUtil.addListener('download_webp', (id, status, progress) {
        logD(
            "下载状态：${state.staticTask} == ${state.dynamicTask} == $id == $status == $progress");
        if (status == 4) {
          state.retryCount++;

          ///重试3次不行就放弃
          if (state.retryCount < 3) {
            DownloadUtil.retryTask(taskId: id);
          }

          if (state.retryCount == 4) {
            DownloadUtil.cancelTask(isAll: true);
          }
        }
        if (state.staticTask == id) {
          ///不说话视频下载情况
          if (status == 3) {
            state.staticDownload = true;
          }
        }
        if (state.dynamicTask == id) {
          ///不说话视频下载情况
          if (status == 3) {
            state.dynamicDownload = true;
          }
        }

        ///两个视频都下载完成
        if (state.staticDownload && state.dynamicDownload) {
          CommonStore.to.setVideoUrl(state.cBrainId, state.staticUrl,
              state.dynamicUrl, state.moodType);

          ///下载完成重新调用下视频播放
          _playChange(true,
              startBrain: state.isInit ? true : false, mood: state.moodType);
        }
      });

      ///延迟1s执行
      Future.delayed(const Duration(milliseconds: 800), () {
        loadBrainData(isDownload: true);
      });
    });
  }

  @override
  void onHidden() {}

  ///加载数字人数据
  loadBrainData({bool startBrain = false, bool isDownload = false}) async {
    if (UserStore.to.deviceList.isEmpty) return;

    state.cBrainId = await _waitForBrainId() ?? "";
    state.cBrainName = CommonStore.to.brainDetail.name ?? "";
    state.cWelcome = CommonStore.to.brainDetail.welcome ?? "";
    state.cVoiceId = CommonStore.to.brainDetail.speakerId ?? "";
    state.staticUrl = CommonStore.to.brainDetail.aigcMotionUrl ?? "";
    state.dynamicUrl = CommonStore.to.brainDetail.aigcTalkUrl ?? "";

    state.cMac = UserStore.to.deviceList[UserStore.to.selectDevice].mac!;

    state.messageHistory[state.cBrainId] = RxList();

    await _onlyStopBrain();

    logD("数字人数据：${state.cBrainId} == ${state.cWelcome}  == ${state.cVoiceId}");

    state.moodType = DiaryStore.to.getMoodType();
    logD("心情类型：${state.moodType}");

    if (DiaryStore.to.motionUrl.isNotEmpty &&
        DiaryStore.to.talkUrl.isNotEmpty) {
      state.staticUrl = DiaryStore.to.motionUrl;
      state.dynamicUrl = DiaryStore.to.talkUrl;
    }

    _playChange(true,
        staticUrl: state.staticUrl,
        dynamicUrl: state.dynamicUrl,
        startBrain: startBrain,
        mood: state.moodType,
        isDownload: isDownload);
  }

  /// 等待 brainDetail.id 有值，或者超时 3 秒
  Future<String?> _waitForBrainId(
      {Duration timeout = const Duration(seconds: 3)}) async {
    final start = DateTime.now();

    while (true) {
      final id = CommonStore.to.brainDetail.id;

      if (id != null) {
        return id; // 有值，立即返回
      }

      if (DateTime.now().difference(start) > timeout) {
        return null; // 超时返回
      }

      await Future.delayed(const Duration(milliseconds: 100)); // 轮询间隔
    }
  }

  ///切换设备,重新加载房间
  changeBrain() async {
    if (state.cMac.isEmpty) return;

    await _stopBrain();

    state.cMac = "";

    state.isInit = false;

    state.messageList.clear();

    netState = NetState.initializeState;

    update(["ai_chat_view", "speak_widget", "text_widget"]);
  }

  ///当解绑最后一个数字人时重置数据
  resetBrain() async {
    if (state.cMac.isEmpty) return;

    await _stopBrain();

    state.cMac = "";

    state.isInit = false;

    state.messageList.clear();

    netState = NetState.initializeState;

    update(["ai_chat_view", "speak_widget", "text_widget"]);
  }

  ///修改心情变化视频
  changeDiary() async {
    String tempMood = DiaryStore.to.getMoodType();
    if (state.moodType == tempMood) {
      ///心情一样直接return
      return;
    }

    state.moodType = tempMood;

    if (DiaryStore.to.motionUrl.isNotEmpty &&
        DiaryStore.to.talkUrl.isNotEmpty) {
      state.moodType = DiaryStore.to.getMoodType();
      logD("心情类型：${state.moodType}");
      state.staticUrl = DiaryStore.to.motionUrl;
      state.dynamicUrl = DiaryStore.to.talkUrl;
    }

    _playChange(true,
        staticUrl: state.staticUrl,
        dynamicUrl: state.dynamicUrl,
        startBrain: false,
        mood: state.moodType,
        isDownload: true);
  }

  Future<void> _addChatMessage(
      {bool isMe = false, bool isClear = false, bool isScroll = true}) async {
    if (isClear) {
      state.messageList.clear();
    }

    final ChatTextController controller = ChatTextController();

    ChatBubble bubble = ChatBubble(
      isUser: isMe,
      content: LiveChatTextRenderer(
        controller: controller,
        style: StyleConfig.witheStyle(fontSize: 14),
      ),
    );

    state.messageList.add(bubble);

    state.activeControllers[state.cBrainId] = controller;

    state.messageHistory.putIfAbsent(state.cBrainId, () => RxList());
    state.messageHistory[state.cBrainId] = state.messageList;

    if (isScroll) {
      _scrollBottom();
    } else {
      update(["ai_chat_view", "speak_widget", "text_widget"]);
    }
  }

  ///上传界面
  void switchBrain() async {
    stopPlay();
    if (state.widgetType == "phone") {
      state.widgetType = "speak";
      update(["ai_chat_view"]);
    }

    //跳转后的页面，返回时获取回传数据
    Get.toNamed(AppRoutes.SWITCH_BRAIN)?.then((args) {
      getBackData(args);
    });
  }

  void toTownAction() async {
    // String path = await FileStore.to.saveFile("rtc_files_wav");
    // Map<String, dynamic> jsonConfig = {
    //   "rtc.audio_dump": {"enable": false, "mode": "all", "path": path}
    // };
    // ByteRtcUtil.to.rtcVideo?.setRuntimeParameters(jsonConfig);

    // CToast.showToast("停止录制");
    // stopPlay();
    // Get.toNamed(AppRoutes.GAME)?.then((value) {
    //   startPlay();
    // });
  }

  //获取上一个页面的回传数据
  void getBackData(args) async {
    if (args != null) {
      ///如果选择同一个数字人，直接播放return
      if (state.cBrainId == args['id']) {
        startPlay();
        return;
      }

      await _stopBrain();

      state.cBrainId = args['id'];

      state.cBrainName = args['name'];
      state.cWelcome = args['welcome'];
      state.cVoiceId = args['voiceId'];
      state.staticUrl = args['staticUrl'];
      state.dynamicUrl = args['dynamicUrl'];

      state.messageList = state.messageHistory[state.cBrainId]!;

      _playChange(true,
          staticUrl: state.staticUrl,
          dynamicUrl: state.dynamicUrl,
          startBrain: true,
          mood: state.moodType);

      _scrollBottom();
    } else {
      startPlay();
    }
  }

  ///isStopAudio：是否停止播放
  switchTypeAction(String type, {bool isStopAudio = true}) async {
    state.widgetType = type;

    if (isStopAudio) _stopSpeaking();

    if (type == 'speak' || type == 'text') {
      ///取消屏幕常亮
      await WakelockPlus.disable();

      if (type == 'text') {
        state.focusNode.requestFocus();
      }

      state.isPauseOnPhoneCall = false;

      await ByteRtcUtil.to.rtcVideo?.stopAudioCapture();

      _scrollBottom();
    }
    if (type == 'phone') {
      await PermissionUtil.microPhone(Get.context!, action: () async {
        ///设置屏幕常亮
        await WakelockPlus.enable();

        await ByteRtcUtil.to.rtcVideo?.startAudioCapture();

        update(["ai_chat_view"]);
      });
    }
  }

  _initRTCAndJoinRoom() async {
    ///提示词清空
    state.tipString = '';

    _initRTCEventHandler();

    bool flag = await ByteRtcUtil.to.createEngine(
        state.startBrainModel.appId!, state.startBrainModel.userId!);
    if (!flag) {
      CToast.showToast("RTC Engine Create Error!");
      return;
    }

    ///创建房间
    await ByteRtcUtil.to.createRoom(state.startBrainModel.roomId!);

    ///加入房间之前先离开房间
    await ByteRtcUtil.to.rtcRoom?.leaveRoom();

    ///加入房间
    await ByteRtcUtil.to
        .joinRoom(state.startBrainModel.token!, state.startBrainModel.userId!);

    update(["ai_chat_view", "phone_call_widget", "text_widget"]);
  }

  ///设置RTC监听事件
  _initRTCEventHandler() {
    // ByteRtcUtil.to.roomHandler.onRoomStateChanged =
    //     (String roomId, String uid, int state, String extraInfo) async {
    //   if (state == 0) {
    //     String path = await FileStore.to.saveFile("rtc_files_wav");
    //     await FileStore.to.deleteAll(path);
    //     Map<String, dynamic> jsonConfig = {
    //       "rtc.audio_dump": {"enable": true, "mode": "all", "path": path}
    //     };
    //     ByteRtcUtil.to.rtcVideo?.setRuntimeParameters(jsonConfig);
    //   }
    // };

    ByteRtcUtil.to.roomHandler.onUserLeave =
        (String uid, UserOfflineReason reason) {
      logD("离开房间的ID：$uid   离开房间原因：$reason");
    };

    ByteRtcUtil.to.videoHandler.onLocalAudioPropertiesReport =
        (List<LocalAudioPropertiesInfo> audioPropertiesInfos) {
      state.frequency = NumUtil.limitNumber(
          audioPropertiesInfos[0].audioPropertiesInfo!.voicePitch!);
      update(["phone_call_widget"]);
    };

    ByteRtcUtil.to.roomHandler.onRoomBinaryMessageReceived =
        (String uId, Uint8List message) async {
      // logD("rtc字幕数据Uint8List：$message");
      // String tempMessage = StrUtil.extractJsonFromUint8List(message) ?? '';
      String tempMessage = StrUtil.unpack(message) ?? '';

      if (tempMessage.isEmpty) return;
      if (!tempMessage.contains("subtitle")) {
        Map<String, dynamic> jsonStatusMap = jsonDecode(tempMessage);
        String status = jsonStatusMap['Stage']['Description'];

        logD("rtc播放状态：$status");
        if (status == 'interrupted') {
          ///被打断
          state.isLog = false;
          state.tempStr = '';
          state.tipString = 'chat_listening'.tr;
          update(["ai_chat_view"]);
        } else if (status == 'listening') {
          ///正在聆听
          state.tipString = "chat_listening".tr;
          await _addChatMessage(isMe: true);
          update(["ai_chat_view"]);
        } else if (status == 'thinking') {
          ///正在思考
          state.tipString = "chat_thinking".tr;
          update(["ai_chat_view"]);
        } else if (status == 'answering') {
          ///正在回答
          state.tipString = "chat_answering".tr;
          await _addChatMessage(isMe: false);
          state.isSpeaking = true;
          state.isLog = true;
          _playChange(false, mood: state.moodType);
        } else if (status == 'answerFinish') {
          ///回答完成
          _scrollBottom();
          state.tempStr = '';
          state.tipString = "chat_listening".tr;
          state.isSpeaking = false;
          _playChange(true, mood: state.moodType);
        }
      } else {
        // logD("rtc字幕数据Uint8List：$message");
        // logD("rtc字幕解包数据：$tempMessage");
        Map<String, dynamic> jsonMessageMap = jsonDecode(tempMessage);

        String uId = jsonMessageMap['data'][0]['userId'];
        String textValue =
            jsonMessageMap['data'][0]['text'].toString().replaceAll(r'\"', '"');

        ///如果是用户说话，不解析文字
        if (uId == state.startBrainModel.userId) {
          bool flag = jsonMessageMap['data'][0]['definite'];
          if (flag) {
            logD("用户说话：$textValue");

            ///completer已经准备好
            await state.activeControllers[state.cBrainId]?.ready;
            state.activeControllers[state.cBrainId]?.safeAppend(textValue);
            _scrollBottom();
          }
        } else {
          if (state.isLog) {
            logD("非用户说话：$textValue");
            String decodedString = '';
            if (state.tempStr.isEmpty) {
              decodedString = textValue;
            } else {
              String diff = StrUtil.compareStrings(state.tempStr, textValue);
              decodedString = diff;

              _scrollBottom();
            }

            state.tempStr = textValue;

            Future.delayed(const Duration(milliseconds: 100), () async {
              ///completer已经准备好
              await state.activeControllers[state.cBrainId]?.ready;
              state.activeControllers[state.cBrainId]
                  ?.safeAppend(decodedString);
            });
          }
        }
      }
    };
  }

  switchCallPause() async {
    if (state.isPauseOnPhoneCall) {
      ///继续操作，继续监听
      state.isPauseOnPhoneCall = false;
      await ByteRtcUtil.to.rtcVideo?.startAudioCapture();
      state.tipString = 'chat_listening'.tr;
    } else {
      ///暂停操作，并且关闭语音监听
      state.isPauseOnPhoneCall = true;
      await ByteRtcUtil.to.rtcVideo?.stopAudioCapture();
      _stopSpeaking();
      state.tipString = 'chat_pasued'.tr;
    }
    update(["ai_chat_view", "phone_call_widget"]);
  }

  ///回到页面播放视频
  startPlay() async {
    ByteRtcUtil.to.rtcVideo?.setPlaybackVolume(100);
    if (state.isInit) {
      if (state.isSpeaking) {
        ///停止播放
        state.isSpeaking = false;
        _stopSpeaking();
      }
      await _preloadLocalFileImage(
          "${FileStore.to.filePath}static_${state.moodType}_${state.cBrainId}.webp");
      await _preloadLocalFileImage(
          "${FileStore.to.filePath}dynamic_${state.moodType}_${state.cBrainId}.webp");
      update(["ai_chat_view"]);
      if (state.widgetType == "phone") {
        ///设置屏幕常亮
        await WakelockPlus.enable();
        await ByteRtcUtil.to.rtcVideo?.startAudioCapture();
      }
      if (state.isLeaveRoom) {
        loadBrainData(startBrain: true, isDownload: false);
      }
      state.isLeaveRoom = false;
    } else {
      state.isInit = true;
      loadBrainData(startBrain: true, isDownload: true);
    }
  }

  ///切换界面停止播放音频 暂停视频
  ///退出房间
  stopPlay({bool isLeaveRoom = false}) async {
    ByteRtcUtil.to.rtcVideo?.setPlaybackVolume(0);
    if (state.isSpeaking) {
      ///停止播放
      state.isSpeaking = false;
      _stopSpeaking();
    }
    if (state.widgetType == "phone") {
      ///取消屏幕常亮
      await WakelockPlus.disable();
      await ByteRtcUtil.to.rtcVideo?.stopAudioCapture();
    }

    if (state.isLeaveRoom) return;
    state.isLeaveRoom = isLeaveRoom;
    if (state.isLeaveRoom) {
      _stopBrain();
    }
  }

  void sendTextFlied(String result) async {
    state.focusNode.unfocus();

    _interruptBrain("ExternalTextToLLM", message: result);
  }

  void _scrollBottom() {
    //延迟1s让对话列表滚动到底部
    Future.delayed(const Duration(milliseconds: 300), () {
      if (!state.scrollController.hasClients) return;
      state.scrollController.animateTo(
        state.scrollController.position.maxScrollExtent,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeOut,
      );
      update(["ai_chat_view"]);
    });
  }

  /// 视频播放
  /// isStatic：是否加载不说话视频
  /// staticUrl：静态视频接口
  /// dynamicUrl：动态视频接口
  /// startBrain：是否开启数字人请求
  /// mood：心情类型
  _playChange(bool isStatic,
      {String staticUrl = '',
      String dynamicUrl = '',
      bool startBrain = false,
      String mood = "",
      bool isDownload = false}) async {
    if (isDownload) {
      ///接口不为空  说明要判断资源是否存在，不存在需要下载
      if (staticUrl.isNotEmpty || dynamicUrl.isNotEmpty) {
        if ((CommonStore.to.videoUrl[state.cBrainId]![mood][0] != staticUrl) &&
            (CommonStore.to.videoUrl[state.cBrainId]![mood][1] != dynamicUrl)) {
          await FileStore.to.deleteFile(
              "${FileStore.to.filePath}static_${mood}_${state.cBrainId}.webp");
          await FileStore.to.deleteFile(
              "${FileStore.to.filePath}dynamic_${mood}_${state.cBrainId}.webp");

          ///两个视频都不一样，则下载或更新
          var path = await FileStore.to.saveFile("videos", isPreferences: true);
          if (path.isEmpty) return;
          state.staticDownload = false;
          state.dynamicDownload = false;
          state.staticTask = await DownloadUtil.startTask(staticUrl, path,
              fileName: "static_${mood}_${state.cBrainId}.webp");
          state.dynamicTask = await DownloadUtil.startTask(dynamicUrl, path,
              fileName: "dynamic_${mood}_${state.cBrainId}.webp");
        } else if ((CommonStore.to.videoUrl[state.cBrainId]![mood][0] !=
                staticUrl) &&
            (CommonStore.to.videoUrl[state.cBrainId]![mood][1] == dynamicUrl)) {
          ///静态视频不一样，则下载或更新
          await FileStore.to.deleteFile(
              "${FileStore.to.filePath}static_${mood}_${state.cBrainId}.webp");
          var path = await FileStore.to.saveFile("videos", isPreferences: true);
          if (path.isEmpty) return;
          state.staticDownload = false;
          state.dynamicDownload = true;
          state.staticTask = await DownloadUtil.startTask(staticUrl, path,
              fileName: "static_${mood}_${state.cBrainId}.webp");
        } else if ((CommonStore.to.videoUrl[state.cBrainId]![mood][0] ==
                staticUrl) &&
            (CommonStore.to.videoUrl[state.cBrainId]![mood][1] != dynamicUrl)) {
          ///动态视频不一样，则下载或更新
          await FileStore.to.deleteFile(
              "${FileStore.to.filePath}dynamic_${mood}_${state.cBrainId}.webp");
          var path = await FileStore.to.saveFile("videos", isPreferences: true);
          if (path.isEmpty) return;
          state.staticDownload = true;
          state.dynamicDownload = false;
          state.dynamicTask = await DownloadUtil.startTask(dynamicUrl, path,
              fileName: "dynamic_${mood}_${state.cBrainId}.webp");
        } else {
          ///存在则重新调用方法显示视频
          state.staticDownload = true;
          state.dynamicDownload = true;
          _playChange(true, startBrain: startBrain, mood: state.moodType);
        }
      }
    } else {
      if (isStatic) {
        state.imagePath =
            "${FileStore.to.filePath}static_${mood}_${state.cBrainId}.webp";
      } else {
        state.imagePath =
            "${FileStore.to.filePath}dynamic_${mood}_${state.cBrainId}.webp";
      }
      if (startBrain && state.staticDownload && state.dynamicDownload) {
        _startBrain(mood: mood);
      }
    }
    update(["ai_chat_view", "speak_widget", "text_widget"]);
  }

  ///预加载图片，不然切换会出现闪白屏
  Future<void> _preloadLocalFileImage(String filePath) async {
    if (!File(filePath).existsSync()) return;
    final fileImage = FileImage(File(filePath));
    await precacheImage(fileImage, Get.context!);
  }

  ///==================UI交互======================
  ///长按按钮事件
  speakStartAction() async {
    if (await PermissionUtil().isMicroPhone()) {
      _stopSpeaking();
      state.isSpeakTalk = true;
      _startListeningXF();
    } else {
      PermissionUtil.microPhone(Get.context!, action: () async {});
    }
  }

  ///松开取消事件
  speakMoveAction() {
    _stopListeningXF(isChange: true);
    update(['ai_chat_view', "speak_widget"]);
  }

  ///松开按钮事件
  speakEndAction() async {
    state.tipString = "";
    state.isSpeakTalk = false;
    _stopListeningXF(isChange: true);
    update(["ai_chat_view", "speak_widget"]);
  }

  cancelSpeakAction() {
    clickScreen();
  }

  ///开始监听语音
  ///isChange:是否改变UI
  ///isHold:是否按住 0=按住
  _startListeningXF() async {
    await ByteRtcUtil.to.rtcVideo?.startAudioCapture();
    update(["ai_chat_view", "speak_widget"]);
  }

  ///停止监听语音
  ///isChange:是否改变UI
  _stopListeningXF({bool isChange = false}) async {
    if (isChange) {
      state.isSpeakTalk = false;
    }
    await ByteRtcUtil.to.rtcVideo?.stopAudioCapture();
    update(["ai_chat_view", "speak_widget"]);
  }

  ///停止播放（本界面使用）
  _stopSpeaking() async {
    _interruptBrain("interrupt");
  }

  clickScreen() async {
    _stopSpeaking();
  }

  ///============================GPT对话======================================================///
  _startBrain({String mood = ''}) async {
    if (UserStore.to.deviceList.isEmpty) return;
    var map = {"mac": state.cMac, "is_app": true};
    Result result = await http.startBrain(map);
    if (result.code == 0) {
      state.startBrainModel = result.data;

      await _preloadLocalFileImage(
          "${FileStore.to.filePath}static_${mood}_${state.cBrainId}.webp");
      await _preloadLocalFileImage(
          "${FileStore.to.filePath}dynamic_${mood}_${state.cBrainId}.webp");

      netState = NetState.dataSuccessState;
      update(["ai_chat_view"]);

      ///使用RTC
      _initRTCAndJoinRoom();
    } else {
      CToast.showToast(result.msg!);
    }
  }

  _stopBrain() async {
    if (UserStore.to.deviceList.isEmpty) return;
    var map = {"mac": state.cMac, "is_app": true};
    Result result = await http.stopBrain(map);
    if (result.code == 0) {
      state.tipString = '';
      if (ByteRtcUtil.to.rtcRoom != null) {
        ByteRtcUtil.to.rtcRoom?.leaveRoom();
      }
    }
  }

  ///单纯关闭智能体，每次启动智能体前发送一次，避免不正常退出房间再次开启智能体出现问题
  Future _onlyStopBrain() async {
    if (UserStore.to.deviceList.isEmpty) return;
    var map = {"mac": state.cMac, "is_app": true};
    Result result = await http.stopBrain(map);
    if (result.code != 0) {
      CToast.showToast(result.msg!);
    }
  }

  ///command
  ///interrupt=打断智能体
  ///ExternalTextToLLM :传入外部问题送入LLM
  ///ExternalTextToSpeech：直接播放文字
  _interruptBrain(String command, {String message = ''}) async {
    if (UserStore.to.deviceList.isEmpty) return;
    var map = {
      "mac": state.cMac,
      "command": command,
      "message": message,
      "interrupt_mode": 1,
      "is_app": true
    };
    Result result = await http.interruptBrain(map);
    if (result.code == 0) {
      if (message.isNotEmpty) {
        state.isLog = false;
        Future.delayed(const Duration(milliseconds: 200), () async {
          await _addChatMessage(isMe: true);
          Future.delayed(const Duration(milliseconds: 100), () async {
            ///completer已经准备好
            await state.activeControllers[state.cBrainId]?.ready;
            state.activeControllers[state.cBrainId]?.safeAppend(message);
            _scrollBottom();
          });
        });
      }
    } else {
      CToast.showToast(result.msg!);
    }
  }

  @override
  void onClose() {
    state.scrollController.dispose();
    DownloadUtil.removeListener("download_webp");
    ByteRtcUtil.to.destroyEngine();
    super.onClose();
  }
}
