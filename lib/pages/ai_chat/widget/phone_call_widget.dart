import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:getx_xiaopa/extension/images.dart';
import 'package:getx_xiaopa/extension/widget_extension.dart';
import 'package:getx_xiaopa/pages/ai_chat/ai_chat_logic.dart';
import 'package:getx_xiaopa/r.dart';
import 'package:getx_xiaopa/values/values.dart';
import 'package:getx_xiaopa/widget/bar/bar_pulse_loading.dart';

class PhoneCallWidget extends GetView<AiChatLogic> {
  final Function() playOrPauseAction;
  final Function() closePhoneCallAction;

  const PhoneCallWidget(
      {super.key,
      required this.playOrPauseAction,
      required this.closePhoneCallAction});

  @override
  Widget build(BuildContext context) {
    return GetBuilder<AiChatLogic>(
      id: "phone_call_widget",
      builder: (_) => Container(
        color: Colors.white,
        width: 1.sw,
        height: 70.h,
        child: Row(
          children: [
            Container(
              padding: EdgeInsets.only(left: 15.w),
              child: Images(
                path: controller.state.isPauseOnPhoneCall
                    ? R.play_media_icon_png
                    : R.pause_media_icon_png,
                width: 40.w,
                height: 40.h,
              ).inkWell(() => playOrPauseAction()),
            ),
            Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                SizedBox(
                  width: 44.w,
                  height: 15.h,
                  child: Center(
                    child: BarPulseLoading(
                      width: 1.w,
                      height: controller.state.frequency.h,
                      color: ColorConfig.searchTextColor,
                    ),
                  ),
                ),
                SizedBox(
                  width: 245.w,
                  height: 44.h,
                  child: Center(
                    child: Text(
                      controller.state.tipString,
                      style: StyleConfig.otherStyle(
                          color: ColorConfig.searchTextColor, fontSize: 14),
                    ),
                  ),
                )
              ],
            ),
            Container(
              padding: EdgeInsets.only(right: 15.w),
              child: Images(
                path: R.close_phone_call_png,
                width: 40.w,
                height: 40.h,
              ).inkWell(() => closePhoneCallAction()),
            ),
          ],
        ),
      ),
    );
  }
}
