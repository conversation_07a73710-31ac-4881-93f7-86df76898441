import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:getx_xiaopa/extension/images.dart';
import 'package:getx_xiaopa/extension/widget_extension.dart';
import 'package:getx_xiaopa/pages/ai_chat/ai_chat_logic.dart';
import 'package:getx_xiaopa/r.dart';
import 'package:getx_xiaopa/utils/utils.dart';
import 'package:getx_xiaopa/values/values.dart';
import 'package:getx_xiaopa/widget/widget.dart';

class TextWidget extends GetView<AiChatLogic> {
  final Function() speakAction;
  final Function() phoneAction;
  final TextEditingController textEditingController;
  final Function(String content) sendMessage;
  final FocusNode focusNode;

  const TextWidget({
    super.key,
    required this.speakAction,
    required this.phoneAction,
    required this.sendMessage,
    required this.textEditingController,
    required this.focusNode,
  });

  @override
  Widget build(BuildContext context) {
    return GetBuilder<AiChatLogic>(
      id: "text_widget",
      builder: (_) => ClipRect(
        child: BackdropFilter(
          filter: ImageFilter.blur(sigmaX: 6.0, sigmaY: 6.0),
          child: Container(
            color: Colors.black.withValues(alpha: 0.075), // 必须加个透明容器
            child: Container(
              width: 1.sw,
              // height: 70.h,
              constraints: BoxConstraints(minHeight: 70.h),
              child: Row(
                children: [
                  Container(
                    margin: EdgeInsets.only(left: 15.w),
                    child: Images(
                      path: R.switch_speak_png,
                      width: 36.w,
                      height: 36.h,
                    ).inkWell(() => speakAction()),
                  ),
                  Container(
                    width: 255.w,
                    // height: 40.h,
                    constraints: BoxConstraints(minHeight: 40.h),
                    margin: EdgeInsets.only(
                        top: 5.h, left: 9.w, right: 9.w, bottom: 5.h),
                    padding: EdgeInsets.only(left: 15.w),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      border: Border.all(
                        color: Get.find<ThemeColorController>().gConBrodeColor,
                        width: 1,
                      ),
                      borderRadius: BorderRadius.circular(10),
                    ),
                    child: Row(
                      children: [
                        Expanded(
                          child: AppTextField(
                            keyboardType: TextInputType.text,
                            focusNode: focusNode,
                            textInputAction: TextInputAction.done,
                            maxLines: null,
                            controller: textEditingController,
                            onSubmitted: (text) {
                              if (textEditingController.text.isNotEmpty) {
                                controller
                                    .sendTextFlied(textEditingController.text);
                                textEditingController.clear();
                              }
                            },
                            style: StyleConfig.otherStyle(
                                color: const Color.fromRGBO(17, 17, 17, 1),
                                fontSize: 14),
                            decoration: InputDecoration(
                              isCollapsed: true,
                              hintText: "chat_hint_text".tr,
                              hintStyle: StyleConfig.otherStyle(
                                  color: ColorConfig.shopDetailTextColor,
                                  fontSize: 14),
                              border: const OutlineInputBorder(
                                  borderSide: BorderSide(
                                      width: 0, color: Colors.transparent)),
                              enabledBorder: const OutlineInputBorder(
                                  borderSide: BorderSide(
                                width: 0,
                                color: Colors.transparent,
                              )),
                              focusedBorder: const OutlineInputBorder(
                                  borderSide: BorderSide(
                                width: 0,
                                color: Colors.transparent,
                              )),
                              contentPadding: EdgeInsets.symmetric(
                                vertical: 0.h,
                              ),
                            ),
                          ),
                        ),
                        Images(
                          path: R.chat_send_png,
                          width: 30.w,
                          height: 30.h,
                        ).inkWell(() {
                          if (textEditingController.text.isNotEmpty) {
                            controller
                                .sendTextFlied(textEditingController.text);
                            textEditingController.clear();
                          }
                        }),
                        SizedBox(width: 5.w)
                      ],
                    ),
                  ),
                  Container(
                    margin: EdgeInsets.only(right: 15.w),
                    child: Images(
                      path: R.switch_phone_png,
                      width: 36.w,
                      height: 36.h,
                    ).inkWell(() => phoneAction()),
                  )
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
