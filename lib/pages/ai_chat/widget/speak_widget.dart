import 'dart:ui';

import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:getx_xiaopa/extension/images.dart';
import 'package:getx_xiaopa/extension/widget_extension.dart';
import 'package:getx_xiaopa/pages/ai_chat/ai_chat_logic.dart';
import 'package:getx_xiaopa/r.dart';

class SpeakWidget extends GetView<AiChatLogic> {
  final Widget statusWidget;
  final Function() languageAction;
  final Function() phoneAction;
  final GestureLongPressDownCallback? speakStartAction;
  final Function(DragEndDetails)? speakMoveAction;
  final GestureLongPressUpCallback? speakEndAction;
  final GestureTapUpCallback? speakEndTap;
  final Function()? cancelSpeak;

  const SpeakWidget(
      {super.key,
      required this.statusWidget,
      required this.languageAction,
      required this.phoneAction,
      this.speakStartAction,
      this.speakMoveAction,
      this.speakEndAction,
      this.speakEndTap,
      this.cancelSpeak});

  @override
  Widget build(BuildContext context) {
    return GetBuilder<AiChatLogic>(
      id: "speak_widget",
      builder: (_) => ClipRect(
        child: BackdropFilter(
          filter: ImageFilter.blur(sigmaX: 6.0, sigmaY: 6.0),
          child: Container(
            color: Colors.black.withValues(alpha: 0.075), // 必须加个透明容器
            child: SizedBox(
              width: 1.sw,
              height: 70.h,
              // decoration: const BoxDecoration(
              //   color: Colors.transparent,
              //   image: DecorationImage(
              //       image: AssetImage(R.ai_chat_bg_png), fit: BoxFit.fill),
              // ),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Container(
                    padding: EdgeInsets.only(left: 15.w),
                    child: Images(
                      path: R.switch_keyboard_png,
                      width: 36.w,
                      height: 36.h,
                    ).inkWell(() => languageAction()),
                  ),
                  GestureDetector(
                    behavior: HitTestBehavior.opaque,
                    dragStartBehavior: DragStartBehavior.start,
                    onLongPressDown: (details) =>
                        speakStartAction?.call(details),
                    onLongPressUp: () => speakEndAction?.call(),
                    onTapUp: (details) => speakEndTap?.call(details),
                    onVerticalDragEnd: (details) =>
                        speakMoveAction?.call(details),
                    onHorizontalDragEnd: (details) =>
                        speakMoveAction?.call(details),
                    child: statusWidget,
                  ),
                  Container(
                    padding: EdgeInsets.only(right: 15.w),
                    child: Images(
                      path: R.switch_phone_png,
                      width: 36.w,
                      height: 36.h,
                    ).inkWell(() => phoneAction()),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
