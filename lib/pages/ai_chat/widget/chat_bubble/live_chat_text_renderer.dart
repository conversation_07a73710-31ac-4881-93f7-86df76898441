import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:getx_xiaopa/extension/images.dart';

class ChatTextController {
  final Completer<void> _readyCompleter = Completer<void>();
  void Function(String text)? appendText;

  void registerAppendFunction(void Function(String text) onAppend) {
    appendText = onAppend;
    if (!_readyCompleter.isCompleted) {
      _readyCompleter.complete();
    }
  }

  Future<void> get ready => _readyCompleter.future;

  void safeAppend(String text) {
    if (appendText != null) {
      appendText!(text);
    }
  }
}

class LiveChatTextRenderer extends StatefulWidget {
  final ChatTextController controller;
  final TextStyle style;

  const LiveChatTextRenderer({
    super.key,
    required this.controller,
    this.style = const TextStyle(fontSize: 16),
  });

  @override
  State<LiveChatTextRenderer> createState() => LiveChatTextRendererState();
}

class LiveChatTextRendererState extends State<LiveChatTextRenderer>
    with AutomaticKeepAliveClientMixin {
  final _spans = ValueNotifier<List<InlineSpan>>([]);
  final StringBuffer _buffer = StringBuffer();

  @override
  bool get wantKeepAlive => true;

  final Set<String> _emojiBlacklist = {
    '提醒喝水',
    '恢复音量',
    '静音',
    '音量:增大',
    '音量:减少',
    '音量:最大化',
    '音量:最小化'
  };

  final _emojiList = [
    "委屈",
    "开心",
    "难过",
    "生气",
    "无聊",
    "打瞌睡",
    "失落",
    "卖萌",
    "惊吓",
    "惊喜",
    "头晕",
    "喜爱",
    "疑惑",
    "思考",
    "点头",
    "常规",
    "无奈",
    "哭",
    "偷看",
    "激动",
    "严肃",
    "律动",
    "感动",
    "故障",
    "混乱",
    "悲伤",
    "无法理解",
    "惊讶",
    "尴尬",
    "期待",
    "燃",
    "音乐",
    "兴奋",
    "害羞",
    "好奇"
  ];

  @override
  void initState() {
    super.initState();
    widget.controller.registerAppendFunction(_handleText);
  }

  void _handleText(String text) {
    _buffer.write(text);
    _flushBufferOnce();
  }

  void _flushBufferOnce() {
    final text = _buffer.toString();
    int i = 0;
    final newSpans = <InlineSpan>[];

    while (i < text.length) {
      if (text[i] == '(') {
        final close = text.indexOf(')', i);
        if (close != -1) {
          String emojiName = text.substring(i + 1, close);

          // ✅ 判断是否是需要跳过的特殊关键词
          if (_emojiBlacklist.contains(emojiName)) {
            // 跳过显示，也可以选择插入空白或其他占位
            i = close + 1;
            continue;
          }

          if (!_emojiList.contains(emojiName)) {
            emojiName = "其他";
          }

          newSpans.add(WidgetSpan(
            child: Images(
              path: 'assets/emojis/$emojiName.png',
              width: 20.r,
              height: 20.r,
              gaplessPlayback: true,
            ),
          ));
          i = close + 1;
        } else {
          // 不完整，等待后续数据
          break;
        }
      } else {
        final nextEmoji = text.indexOf('(', i);
        final end = nextEmoji == -1 ? text.length : nextEmoji;
        newSpans.add(TextSpan(
          text: text.substring(i, end),
          style: widget.style,
        ));
        i = end;
      }
    }

    // 保留未完成部分
    _buffer.clear();
    if (i < text.length) {
      _buffer.write(text.substring(i));
    }

    _spans.value = [..._spans.value, ...newSpans]; // ✅ 只更新一次
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return ValueListenableBuilder<List<InlineSpan>>(
      key: ValueKey(_spans),
      valueListenable: _spans,
      builder: (context, spans, _) {
        return RichText(
          text: TextSpan(children: spans),
        );
      },
    );
  }
}
