import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class ChatBubble extends StatelessWidget {
  final bool isUser;
  final Widget content;

  const ChatBubble({
    required this.isUser,
    required this.content,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Align(
      alignment: isUser ? Alignment.centerRight : Alignment.centerLeft,
      child: Container(
        padding:
            EdgeInsets.only(left: 12.w, right: 12.w, top: 8.h, bottom: 8.h),
        margin: EdgeInsets.only(left: 15.w, right: 15.w, bottom: 10.h),
        decoration: BoxDecoration(
          //设置4个角的圆角0px 16px, 16px, 16px;
          borderRadius: isUser
              ? const BorderRadius.only(
                  topLeft: Radius.circular(12),
                  topRight: Radius.circular(12),
                  bottomLeft: Radius.circular(12),
                  bottomRight: Radius.circular(0))
              : const BorderRadius.only(
                  topLeft: Radius.circular(16),
                  topRight: Radius.circular(16),
                  bottomLeft: Radius.circular(0),
                  bottomRight: Radius.circular(16)),
          color: isUser
              ? const Color.fromRGBO(21, 214, 175, 0.5)
              : const Color.fromRGBO(0, 0, 0, 0.2),
        ),
        child: content,
      ),
    );
  }
}
