import 'package:get/get.dart';
import 'package:shared_preferences/shared_preferences.dart';

class SharedPreferencesUtil extends GetxService {
  static SharedPreferencesUtil get to => Get.find();
  late final SharedPreferences _prefs;

  Future<SharedPreferencesUtil> init() async {
    _prefs = await SharedPreferences.getInstance();
    return this;
  }

  Future<bool> setString(String key, String value) async {
    return await _prefs.setString(key, value);
  }

  Future<bool> setBool(String key, bool value) async {
    return await _prefs.setBool(key, value);
  }

  Future<bool> setList(String key, List<String> value) async {
    return await _prefs.setStringList(key, value);
  }

  Future<bool> setInt(String key, int value) async {
    return await _prefs.setInt(key, value);
  }

  Future<bool> setDouble(String key, double value) async {
    return await _prefs.setDouble(key, value);
  }

  String getString(String key, {String? defaultStr}) {
    return _prefs.getString(key) ?? defaultStr ?? '';
  }

  bool getBool(String key, {bool? defaultBool}) {
    return _prefs.getBool(key) ?? defaultBool ?? false;
  }

  List<String> getList(String key) {
    return _prefs.getStringList(key) ?? [];
  }

  int getInt(String key) {
    return _prefs.getInt(key) ?? 0;
  }

  double getDouble(String key) {
    return _prefs.getDouble(key) ?? 0.0;
  }

  Future<bool> remove(String key) async {
    return await _prefs.remove(key);
  }
}
