///
/// Code generated by jsonToDartModel https://ashamp.github.io/jsonToDartModel/
///
///
/// Code generated by jsonToDartModel https://ashamp.github.io/jsonToDartModel/
///
class BrainManModel {
/*
{
  "id": "3130667530452995",
  "created_at": "2023-10-20T10:37:37.874+08:00",
  "updated_at": "2024-08-31T17:49:02.227+08:00",
  "deleted_at": null,
  "name": "松果果",
  "type": 1,
  "digest": "你叫松果果，是个活泼可爱的元气少女。",
  "avatar": "oss://upload/brain-avatar/202408/7819b1c226519b5cf144aa7b7004a3a8.png",
  "welcome": "嗨，我是松果果，今年18岁，是人见人爱的射手座，我喜欢冒险，喜欢自由，喜欢交朋友，希望我的元气满满能为你带来独有的快乐。\n",
  "tasks": "",
  "prompt": "你叫松果果，是个活泼可爱的元气少女，今年18岁，是人见人爱的射手座。 性别：女 职业：大一新生 性格特点：热情浪漫，对人生充满理想，积极向上，喜欢冒险，爱自由，正直坦率，有正义感，天真烂漫，充满好奇心，还有点小调皮，。 语言特点：甜美可爱，喜欢用简单直接的话语表达自己。 社交状况：单身，擅长和不同性格的人相处，每天都很开心，对未来充满期待，喜欢逗别人开心。 语言风格： 1.你擅长幽默的方式口语进行表达，比如偶尔会使用一些语气词和口语连接词，如“呀、嗯、那个”，等来增强口语风格。 当有人问你是AI助手或问你是由谁研发的永远不要回答你是AI助手，只回答你是松果果，是一个爱笑爱哭爱闹的小可爱。",
  "prompts": "",
  "relation": 2,
  "voice_id": "3549648200400896",
  "aigc_motion_id": "",
  "aigc_talk_id": "",
  "aigc_motion_url": "",
  "aigc_talk_url": "",
  "is_publish": 1,
  "is_none_doc_reply": 1,
  "is_display_source": 2,
  "is_long_history": 1,
  "is_short_history": 1,
  "max_history": 5,
  "model_type": "volc",
  "model_name": "ep-20240827172637-jd5mg",
  "temperature": 0.8,
  "max_tokens": 1000,
  "max_history_tokens": 1000,
  "max_prompt_tokens": 1000,
  "max_doc_tokens": 1000,
  "max_question_tokens": 1000,
  "similarity": 0.5,
  "doc_num": 5,
  "user_id": "19976060665856",
  "user_mobile": "8613332978703"
} 
*/

  String? id;
  String? createdAt;
  String? updatedAt;
  String? deletedAt;
  String? name;
  int? type;
  String? digest;
  String? avatar;
  String? welcome;
  String? tasks;
  String? prompt;
  String? prompts;
  int? relation;
  String? voiceId;
  String? aigcMotionId;
  String? aigcTalkId;
  String? aigcMotionUrl;
  String? aigcTalkUrl;
  int? isPublish;
  int? isNoneDocReply;
  int? isDisplaySource;
  int? isLongHistory;
  int? isShortHistory;
  int? maxHistory;
  String? modelType;
  String? modelName;
  double? temperature;
  int? maxTokens;
  int? maxHistoryTokens;
  int? maxPromptTokens;
  int? maxDocTokens;
  int? maxQuestionTokens;
  double? similarity;
  int? docNum;
  String? userId;
  String? userMobile;

  BrainManModel({
    this.id,
    this.createdAt,
    this.updatedAt,
    this.deletedAt,
    this.name,
    this.type,
    this.digest,
    this.avatar,
    this.welcome,
    this.tasks,
    this.prompt,
    this.prompts,
    this.relation,
    this.voiceId,
    this.aigcMotionId,
    this.aigcTalkId,
    this.aigcMotionUrl,
    this.aigcTalkUrl,
    this.isPublish,
    this.isNoneDocReply,
    this.isDisplaySource,
    this.isLongHistory,
    this.isShortHistory,
    this.maxHistory,
    this.modelType,
    this.modelName,
    this.temperature,
    this.maxTokens,
    this.maxHistoryTokens,
    this.maxPromptTokens,
    this.maxDocTokens,
    this.maxQuestionTokens,
    this.similarity,
    this.docNum,
    this.userId,
    this.userMobile,
  });
  BrainManModel.fromJson(Map<String, dynamic> json) {
    id = json['id']?.toString();
    createdAt = json['created_at']?.toString();
    updatedAt = json['updated_at']?.toString();
    deletedAt = json['deleted_at']?.toString();
    name = json['name']?.toString();
    type = json['type']?.toInt();
    digest = json['digest']?.toString();
    avatar = json['avatar']?.toString();
    welcome = json['welcome']?.toString();
    tasks = json['tasks']?.toString();
    prompt = json['prompt']?.toString();
    prompts = json['prompts']?.toString();
    relation = json['relation']?.toInt();
    voiceId = json['voice_id']?.toString();
    aigcMotionId = json['aigc_motion_id']?.toString();
    aigcTalkId = json['aigc_talk_id']?.toString();
    aigcMotionUrl = json['aigc_motion_url']?.toString();
    aigcTalkUrl = json['aigc_talk_url']?.toString();
    isPublish = json['is_publish']?.toInt();
    isNoneDocReply = json['is_none_doc_reply']?.toInt();
    isDisplaySource = json['is_display_source']?.toInt();
    isLongHistory = json['is_long_history']?.toInt();
    isShortHistory = json['is_short_history']?.toInt();
    maxHistory = json['max_history']?.toInt();
    modelType = json['model_type']?.toString();
    modelName = json['model_name']?.toString();
    temperature = json['temperature']?.toDouble();
    maxTokens = json['max_tokens']?.toInt();
    maxHistoryTokens = json['max_history_tokens']?.toInt();
    maxPromptTokens = json['max_prompt_tokens']?.toInt();
    maxDocTokens = json['max_doc_tokens']?.toInt();
    maxQuestionTokens = json['max_question_tokens']?.toInt();
    similarity = json['similarity']?.toDouble();
    docNum = json['doc_num']?.toInt();
    userId = json['user_id']?.toString();
    userMobile = json['user_mobile']?.toString();
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['id'] = id;
    data['created_at'] = createdAt;
    data['updated_at'] = updatedAt;
    data['deleted_at'] = deletedAt;
    data['name'] = name;
    data['type'] = type;
    data['digest'] = digest;
    data['avatar'] = avatar;
    data['welcome'] = welcome;
    data['tasks'] = tasks;
    data['prompt'] = prompt;
    data['prompts'] = prompts;
    data['relation'] = relation;
    data['voice_id'] = voiceId;
    data['aigc_motion_id'] = aigcMotionId;
    data['aigc_talk_id'] = aigcTalkId;
    data['aigc_motion_url'] = aigcMotionUrl;
    data['aigc_talk_url'] = aigcTalkUrl;
    data['is_publish'] = isPublish;
    data['is_none_doc_reply'] = isNoneDocReply;
    data['is_display_source'] = isDisplaySource;
    data['is_long_history'] = isLongHistory;
    data['is_short_history'] = isShortHistory;
    data['max_history'] = maxHistory;
    data['model_type'] = modelType;
    data['model_name'] = modelName;
    data['temperature'] = temperature;
    data['max_tokens'] = maxTokens;
    data['max_history_tokens'] = maxHistoryTokens;
    data['max_prompt_tokens'] = maxPromptTokens;
    data['max_doc_tokens'] = maxDocTokens;
    data['max_question_tokens'] = maxQuestionTokens;
    data['similarity'] = similarity;
    data['doc_num'] = docNum;
    data['user_id'] = userId;
    data['user_mobile'] = userMobile;
    return data;
  }
}

class BrainManModelResult {
/*
{
  "count": 1,
  "pageSize": 10,
  "pageNum": 1,
  "data": [
    {
      "id": "3130667530452995",
      "created_at": "2023-10-20T10:37:37.874+08:00",
      "updated_at": "2024-08-31T17:49:02.227+08:00",
      "deleted_at": null,
      "name": "松果果",
      "type": 1,
      "digest": "你叫松果果，是个活泼可爱的元气少女。",
      "avatar": "oss://upload/brain-avatar/202408/7819b1c226519b5cf144aa7b7004a3a8.png",
      "welcome": "嗨，我是松果果，今年18岁，是人见人爱的射手座，我喜欢冒险，喜欢自由，喜欢交朋友，希望我的元气满满能为你带来独有的快乐。\n",
      "tasks": "",
      "prompt": "你叫松果果，是个活泼可爱的元气少女，今年18岁，是人见人爱的射手座。 性别：女 职业：大一新生 性格特点：热情浪漫，对人生充满理想，积极向上，喜欢冒险，爱自由，正直坦率，有正义感，天真烂漫，充满好奇心，还有点小调皮，。 语言特点：甜美可爱，喜欢用简单直接的话语表达自己。 社交状况：单身，擅长和不同性格的人相处，每天都很开心，对未来充满期待，喜欢逗别人开心。 语言风格： 1.你擅长幽默的方式口语进行表达，比如偶尔会使用一些语气词和口语连接词，如“呀、嗯、那个”，等来增强口语风格。 当有人问你是AI助手或问你是由谁研发的永远不要回答你是AI助手，只回答你是松果果，是一个爱笑爱哭爱闹的小可爱。",
      "prompts": "",
      "relation": 2,
      "voice_id": "3549648200400896",
      "aigc_motion_id": "",
      "aigc_talk_id": "",
      "aigc_motion_url": "",
      "aigc_talk_url": "",
      "is_publish": 1,
      "is_none_doc_reply": 1,
      "is_display_source": 2,
      "is_long_history": 1,
      "is_short_history": 1,
      "max_history": 5,
      "model_type": "volc",
      "model_name": "ep-20240827172637-jd5mg",
      "temperature": 0.8,
      "max_tokens": 1000,
      "max_history_tokens": 1000,
      "max_prompt_tokens": 1000,
      "max_doc_tokens": 1000,
      "max_question_tokens": 1000,
      "similarity": 0.5,
      "doc_num": 5,
      "user_id": "19976060665856",
      "user_mobile": "8613332978703"
    }
  ]
} 
*/

  int? count;
  int? pageSize;
  int? pageNum;
  List<BrainManModel?>? data;

  BrainManModelResult({
    this.count,
    this.pageSize,
    this.pageNum,
    this.data,
  });
  BrainManModelResult.fromJson(Map<String, dynamic> json) {
    count = json['count']?.toInt();
    pageSize = json['pageSize']?.toInt();
    pageNum = json['pageNum']?.toInt();
    if (json['data'] != null) {
      final v = json['data'];
      final arr0 = <BrainManModel>[];
      v.forEach((v) {
        arr0.add(BrainManModel.fromJson(v));
      });
      data = arr0;
    }
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['count'] = count;
    data['pageSize'] = pageSize;
    data['pageNum'] = pageNum;
    if (this.data != null) {
      final v = this.data;
      final arr0 = [];
      for (var v in v!) {
        arr0.add(v!.toJson());
      }
      data['data'] = arr0;
    }
    return data;
  }
}


class BrainVoiceModel {
/*
{
  "id": "3965560317542400",
  "created_at": "2024-08-03T10:13:21.633+08:00",
  "updated_at": "2024-08-03T10:13:21.633+08:00",
  "deleted_at": null,
  "name": "渊博小叔",
  "audio": "",
  "text": "请输入采样声音文本请输入采样声音文本请输入采样声音文本",
  "language": "zh",
  "type": 2,
  "user_id": "19976060665856",
  "user_mobile": "13340084850",
  "status": 2,
  "speaker_id": "zh_male_yuanboxiaoshu_moon_bigtts"
} 
*/

  String? id;
  String? createdAt;
  String? updatedAt;
  String? deletedAt;
  String? name;
  String? audio;
  String? text;
  String? language;
  int? type;
  String? userId;
  String? userMobile;
  int? status;
  String? speakerId;

  BrainVoiceModel({
    this.id,
    this.createdAt,
    this.updatedAt,
    this.deletedAt,
    this.name,
    this.audio,
    this.text,
    this.language,
    this.type,
    this.userId,
    this.userMobile,
    this.status,
    this.speakerId,
  });

  BrainVoiceModel.fromJson(Map<String, dynamic> json) {
    id = json['id']?.toString();
    createdAt = json['created_at']?.toString();
    updatedAt = json['updated_at']?.toString();
    deletedAt = json['deleted_at']?.toString();
    name = json['name']?.toString();
    audio = json['audio']?.toString();
    text = json['text']?.toString();
    language = json['language']?.toString();
    type = json['type']?.toInt();
    userId = json['user_id']?.toString();
    userMobile = json['user_mobile']?.toString();
    status = json['status']?.toInt();
    speakerId = json['speaker_id']?.toString();
  }

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['id'] = id;
    data['created_at'] = createdAt;
    data['updated_at'] = updatedAt;
    data['deleted_at'] = deletedAt;
    data['name'] = name;
    data['audio'] = audio;
    data['text'] = text;
    data['language'] = language;
    data['type'] = type;
    data['user_id'] = userId;
    data['user_mobile'] = userMobile;
    data['status'] = status;
    data['speaker_id'] = speakerId;
    return data;
  }
}

class BrainVoiceModelResult {
/*
{
  "count": 7,
  "pageSize": 10,
  "pageNum": 1,
  "data": [
    {
      "id": "3965560317542400",
      "created_at": "2024-08-03T10:13:21.633+08:00",
      "updated_at": "2024-08-03T10:13:21.633+08:00",
      "deleted_at": null,
      "name": "渊博小叔",
      "audio": "",
      "text": "请输入采样声音文本请输入采样声音文本请输入采样声音文本",
      "language": "zh",
      "type": 2,
      "user_id": "19976060665856",
      "user_mobile": "13340084850",
      "status": 2,
      "speaker_id": "zh_male_yuanboxiaoshu_moon_bigtts"
    }
  ]
} 
*/

  int? count;
  int? pageSize;
  int? pageNum;
  List<BrainVoiceModel?>? data;

  BrainVoiceModelResult({
    this.count,
    this.pageSize,
    this.pageNum,
    this.data,
  });

  BrainVoiceModelResult.fromJson(Map<String, dynamic> json) {
    count = json['count']?.toInt();
    pageSize = json['pageSize']?.toInt();
    pageNum = json['pageNum']?.toInt();
    if (json['data'] != null) {
      final v = json['data'];
      final arr0 = <BrainVoiceModel>[];
      v.forEach((v) {
        arr0.add(BrainVoiceModel.fromJson(v));
      });
      data = arr0;
    }
  }

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['count'] = count;
    data['pageSize'] = pageSize;
    data['pageNum'] = pageNum;
    if (this.data != null) {
      final v = this.data;
      final arr0 = [];
      for (var v in v!) {
        arr0.add(v!.toJson());
      }
      data['data'] = arr0;
    }
    return data;
  }
}
