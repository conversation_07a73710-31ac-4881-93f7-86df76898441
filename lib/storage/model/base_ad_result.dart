///
/// Code generated by jsonToDartModel https://ashamp.github.io/jsonToDartModel/
///
class BaseAdModel {
/*
{
  "id": "1",
  "created_at": "0001-01-01T00:00:00Z",
  "updated_at": "0001-01-01T00:00:00Z",
  "deleted_at": null,
  "name": "启动图",
  "picture": "",
  "url": "",
  "position": "",
  "content": "",
  "remark": "",
  "pv": "",
  "sort": 0
} 
*/

  String? id;
  String? createdAt;
  String? updatedAt;
  String? deletedAt;
  String? name;
  String? picture;
  String? url;
  String? position;
  String? content;
  String? remark;
  String? pv;
  int? sort;

  BaseAdModel({
    this.id,
    this.createdAt,
    this.updatedAt,
    this.deletedAt,
    this.name,
    this.picture,
    this.url,
    this.position,
    this.content,
    this.remark,
    this.pv,
    this.sort,
  });
  BaseAdModel.fromJson(Map<String, dynamic> json) {
    id = json['id']?.toString();
    createdAt = json['created_at']?.toString();
    updatedAt = json['updated_at']?.toString();
    deletedAt = json['deleted_at']?.toString();
    name = json['name']?.toString();
    picture = json['picture']?.toString();
    url = json['url']?.toString();
    position = json['position']?.toString();
    content = json['content']?.toString();
    remark = json['remark']?.toString();
    pv = json['pv']?.toString();
    sort = json['sort']?.toInt();
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['id'] = id;
    data['created_at'] = createdAt;
    data['updated_at'] = updatedAt;
    data['deleted_at'] = deletedAt;
    data['name'] = name;
    data['picture'] = picture;
    data['url'] = url;
    data['position'] = position;
    data['content'] = content;
    data['remark'] = remark;
    data['pv'] = pv;
    data['sort'] = sort;
    return data;
  }
}

class BaseAdResult {
/*
{
  "count": 4,
  "pageSize": 10,
  "pageNum": 1,
  "data": [
    {
      "id": "1",
      "created_at": "0001-01-01T00:00:00Z",
      "updated_at": "0001-01-01T00:00:00Z",
      "deleted_at": null,
      "name": "启动图",
      "picture": "",
      "url": "",
      "position": "",
      "content": "",
      "remark": "",
      "pv": "",
      "sort": 0
    }
  ]
} 
*/

  int? count;
  int? pageSize;
  int? pageNum;
  List<BaseAdModel?>? data;

  BaseAdResult({
    this.count,
    this.pageSize,
    this.pageNum,
    this.data,
  });
  BaseAdResult.fromJson(Map<String, dynamic> json) {
    count = json['count']?.toInt();
    pageSize = json['pageSize']?.toInt();
    pageNum = json['pageNum']?.toInt();
    if (json['data'] != null) {
      final v = json['data'];
      final arr0 = <BaseAdModel>[];
      v.forEach((v) {
        arr0.add(BaseAdModel.fromJson(v));
      });
      data = arr0;
    }
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['count'] = count;
    data['pageSize'] = pageSize;
    data['pageNum'] = pageNum;
    if (this.data != null) {
      final v = this.data;
      final arr0 = [];
      for (var v in v!) {
        arr0.add(v!.toJson());
      }
      data['data'] = arr0;
    }
    return data;
  }
}
