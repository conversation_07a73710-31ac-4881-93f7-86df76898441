///
/// Code generated by jsonToDartModel https://ashamp.github.io/jsonToDartModel/
///
class AnnounceModel {
/*
{
  "id": "5134435432267776",
  "created_at": "2025-09-10T14:39:54.369+08:00",
  "updated_at": "2025-09-10T14:39:54.369+08:00",
  "deleted_at": null,
  "title": "伤心公告",
  "content": "定时提醒不了了~~",
  "status": 2,
  "os": 2
} 
*/

  String? id;
  String? createdAt;
  String? updatedAt;
  String? deletedAt;
  String? title;
  String? content;
  int? status;
  int? os;

  AnnounceModel({
    this.id,
    this.createdAt,
    this.updatedAt,
    this.deletedAt,
    this.title,
    this.content,
    this.status,
    this.os,
  });
  AnnounceModel.fromJson(Map<String, dynamic> json) {
    id = json['id']?.toString();
    createdAt = json['created_at']?.toString();
    updatedAt = json['updated_at']?.toString();
    deletedAt = json['deleted_at']?.toString();
    title = json['title']?.toString();
    content = json['content']?.toString();
    status = json['status']?.toInt();
    os = json['os']?.toInt();
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['id'] = id;
    data['created_at'] = createdAt;
    data['updated_at'] = updatedAt;
    data['deleted_at'] = deletedAt;
    data['title'] = title;
    data['content'] = content;
    data['status'] = status;
    data['os'] = os;
    return data;
  }
}

class AnnounceModelResult {
/*
{
  "count": 2,
  "pageSize": 10,
  "pageNum": 1,
  "data": [
    {
      "id": "5134435432267776",
      "created_at": "2025-09-10T14:39:54.369+08:00",
      "updated_at": "2025-09-10T14:39:54.369+08:00",
      "deleted_at": null,
      "title": "伤心公告",
      "content": "定时提醒不了了~~",
      "status": 2,
      "os": 2
    }
  ]
} 
*/

  int? count;
  int? pageSize;
  int? pageNum;
  List<AnnounceModel?>? data;

  AnnounceModelResult({
    this.count,
    this.pageSize,
    this.pageNum,
    this.data,
  });
  AnnounceModelResult.fromJson(Map<String, dynamic> json) {
    count = json['count']?.toInt();
    pageSize = json['pageSize']?.toInt();
    pageNum = json['pageNum']?.toInt();
  if (json['data'] != null) {
  final v = json['data'];
  final arr0 = <AnnounceModel>[];
  v.forEach((v) {
  arr0.add(AnnounceModel.fromJson(v));
  });
    data = arr0;
    }
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['count'] = count;
    data['pageSize'] = pageSize;
    data['pageNum'] = pageNum;
    if (this.data != null) {
      final v = this.data;
      final arr0 = [];
  for (var v in v!) {
  arr0.add(v!.toJson());
  }
      data['data'] = arr0;
    }
    return data;
  }
}
