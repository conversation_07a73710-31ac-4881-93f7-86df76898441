///
/// Code generated by jsonToDartModel https://ashamp.github.io/jsonToDartModel/
///
class SetuPublicModel {
/*
{
  "id": "4644750507376641",
  "created_at": "2025-08-31T23:14:49.472+08:00",
  "updated_at": "2025-09-10T10:54:38.062+08:00",
  "deleted_at": null,
  "name": "音色价格",
  "code": "voicePrice",
  "type": 1,
  "is_public": 1,
  "groups": 3,
  "service_id": "3108746352918529",
  "remark": "",
  "value": "3500",
  "sort": 0
} 
*/

  String? id;
  String? createdAt;
  String? updatedAt;
  String? deletedAt;
  String? name;
  String? code;
  int? type;
  int? isPublic;
  int? groups;
  String? serviceId;
  String? remark;
  String? value;
  int? sort;

  SetuPublicModel({
    this.id,
    this.createdAt,
    this.updatedAt,
    this.deletedAt,
    this.name,
    this.code,
    this.type,
    this.isPublic,
    this.groups,
    this.serviceId,
    this.remark,
    this.value,
    this.sort,
  });
  SetuPublicModel.fromJson(Map<String, dynamic> json) {
    id = json['id']?.toString();
    createdAt = json['created_at']?.toString();
    updatedAt = json['updated_at']?.toString();
    deletedAt = json['deleted_at']?.toString();
    name = json['name']?.toString();
    code = json['code']?.toString();
    type = json['type']?.toInt();
    isPublic = json['is_public']?.toInt();
    groups = json['groups']?.toInt();
    serviceId = json['service_id']?.toString();
    remark = json['remark']?.toString();
    value = json['value']?.toString();
    sort = json['sort']?.toInt();
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['id'] = id;
    data['created_at'] = createdAt;
    data['updated_at'] = updatedAt;
    data['deleted_at'] = deletedAt;
    data['name'] = name;
    data['code'] = code;
    data['type'] = type;
    data['is_public'] = isPublic;
    data['groups'] = groups;
    data['service_id'] = serviceId;
    data['remark'] = remark;
    data['value'] = value;
    data['sort'] = sort;
    return data;
  }
}

class SetuPublicResult {
/*
{
  "count": 16,
  "pageSize": 10,
  "pageNum": 1,
  "data": [
    {
      "id": "4644750507376641",
      "created_at": "2025-08-31T23:14:49.472+08:00",
      "updated_at": "2025-09-10T10:54:38.062+08:00",
      "deleted_at": null,
      "name": "音色价格",
      "code": "voicePrice",
      "type": 1,
      "is_public": 1,
      "groups": 3,
      "service_id": "3108746352918529",
      "remark": "",
      "value": "3500",
      "sort": 0
    }
  ]
} 
*/

  int? count;
  int? pageSize;
  int? pageNum;
  List<SetuPublicModel?>? data;

  SetuPublicResult({
    this.count,
    this.pageSize,
    this.pageNum,
    this.data,
  });
  SetuPublicResult.fromJson(Map<String, dynamic> json) {
    count = json['count']?.toInt();
    pageSize = json['pageSize']?.toInt();
    pageNum = json['pageNum']?.toInt();
    if (json['data'] != null) {
      final v = json['data'];
      final arr0 = <SetuPublicModel>[];
      v.forEach((v) {
        arr0.add(SetuPublicModel.fromJson(v));
      });
      data = arr0;
    }
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['count'] = count;
    data['pageSize'] = pageSize;
    data['pageNum'] = pageNum;
    if (this.data != null) {
      final v = this.data;
      final arr0 = [];
      for (var v in v!) {
        arr0.add(v!.toJson());
      }
      data['data'] = arr0;
    }
    return data;
  }
}
