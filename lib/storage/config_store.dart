import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:getx_xiaopa/storage/preferences_store.dart';
import 'package:getx_xiaopa/values/storage.dart';

class ConfigStore extends GetxService {
  static ConfigStore get to => Get.find();

  Locale locale = const Locale('zh', "CN");

  List<Locale> languages = const [Locale('zh', "CN"), Locale('en', "US")];

  String downUrl = '';

  ///第一次打开app弹出协议政策
  bool _isFirst = true;

  bool get isFirst => _isFirst;

  ///固定下载地址
  String officialDownUrl = "https://marspt.cn/download";
  String _userAgreement = "";
  String get userAgreement => _userAgreement;
  String _privacyPolicy = "";
  String get privacyPolicy => _privacyPolicy;
  String _usageInstructions = "";
  String get usageInstructions => _usageInstructions;

  ///保存已经连接过的wifi
  List<String> _wifiConfig = [];

  List<String> get wifiConfig => _wifiConfig;

  String _areaCode = "CN";
  String get areaCode => _areaCode;

  String _areaDialCode = "86";
  String get areaDialCode => _areaDialCode;

  bool isToask = true;

  Future<ConfigStore> init() async {
    _isFirst =
        SharedPreferencesUtil.to.getBool(STORAGE_IS_FIRST, defaultBool: true);

    _wifiConfig = SharedPreferencesUtil.to.getList(WIFI_CONFIG);

    _areaCode =
        SharedPreferencesUtil.to.getString(STORAGE_AREA_CODE, defaultStr: "CN");
    _areaDialCode = SharedPreferencesUtil.to
        .getString(STORAGE_AREA_DIAL_CODE, defaultStr: "86");

    var langCode = SharedPreferencesUtil.to.getString(STORAGE_LANGUAGE_CODE);
    if (langCode.isNotEmpty) {
      var index = languages.indexWhere((element) {
        return "${element.languageCode}_${element.countryCode}" == langCode;
      });
      if (index >= 0) {
        locale = languages[index];
      }
    } else {
      locale = onLocaleSystem();
    }

    _userAgreement =
        "https://marspt.cn/app/agreement.html?sc_lang=${getLocaleCode()}";
    _privacyPolicy =
        "https://marspt.cn/app/privacy.html?sc_lang=${getLocaleCode()}";
    _usageInstructions =
        "https://marspt.cn/app/support.html?sc_lang=${getLocaleCode()}";

    getIsToask();

    return this;
  }

  /// 判断系统语言返回对应的语言
  Locale onLocaleSystem() {
    final localeDevice = Get.deviceLocale;
    if (localeDevice?.languageCode == 'zh') {
      return const Locale('zh', 'CN');
    } else {
      return const Locale('en', 'US');
    }
  }

  Future<void> onLocaleUpdate(Locale value) async {
    locale = value;
    await Get.updateLocale(value);
    await SharedPreferencesUtil.to.setString(
        STORAGE_LANGUAGE_CODE, "${value.languageCode}_${value.countryCode}");
  }

  ///获取语言的code
  String getLocaleCode() {
    return locale.languageCode;
  }

  getIsToask() {
    final lastDateStr = SharedPreferencesUtil.to.getString(STORAGE_IS_TOASK);

    final now = DateTime.now();
    final todayStr = "${now.year}-${now.month}-${now.day}";

    if (lastDateStr == todayStr) {
      isToask = false;
    }
  }

  setIsToask() {
    isToask = false;
    final now = DateTime.now();
    final todayStr = "${now.year}-${now.month}-${now.day}";
    SharedPreferencesUtil.to.setString(STORAGE_IS_TOASK, todayStr);
  }

  Future<void> setIsFirst(bool value) async {
    await SharedPreferencesUtil.to.setBool(STORAGE_IS_FIRST, value);
    _isFirst = value;
  }

  Future<void> setWifiConfig(Map value) async {
    List<String> temp = SharedPreferencesUtil.to.getList(WIFI_CONFIG);
    if (temp.isEmpty) {
      var json = jsonEncode(value);
      temp.add(json);
    } else {
      List tempList = [];

      ///保存过的不需要再保存
      bool flag = temp.every((e) {
        Map temp = jsonDecode(e);
        if (temp.keys.first.toString() == value.keys.first) {
          return false;
        }
        return true;
      });

      if (flag) {
        var json = jsonEncode(value);
        tempList.add(json);
      }

      if (tempList.isNotEmpty) {
        temp.addAll(tempList.cast<String>());
      }
    }

    _wifiConfig = temp;

    await SharedPreferencesUtil.to.setList(WIFI_CONFIG, temp);
  }

  Future<void> deleteWifiConfig(String wifiName) async {
    List<String> temp = SharedPreferencesUtil.to.getList(WIFI_CONFIG);
    List<String> tempList = [];
    for (var e in temp) {
      Map map = jsonDecode(e);
      if (!map.keys.first.toString().contains(wifiName)) {
        var json = jsonEncode(map);
        tempList.add(json);
      }
    }
    _wifiConfig = tempList;
    await SharedPreferencesUtil.to.setList(WIFI_CONFIG, tempList);
  }

  Future<void> removeWifiConfig() async {
    await SharedPreferencesUtil.to.remove(WIFI_CONFIG);
    _wifiConfig.clear();
  }

  ///区号持久化  字母
  Future<void> setAreaCode(String value) async {
    _areaCode = value;
    await SharedPreferencesUtil.to.setString(STORAGE_AREA_CODE, value);
  }

  ///区号持久化  数字
  Future<void> setAreaDialCode(String value) async {
    _areaDialCode = value;
    await SharedPreferencesUtil.to.setString(STORAGE_AREA_DIAL_CODE, value);
  }
}
