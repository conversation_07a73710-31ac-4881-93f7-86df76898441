import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:getx_xiaopa/extension/widget_extension.dart';
import 'package:getx_xiaopa/network/http_client.dart';
import 'package:getx_xiaopa/network/model/request_body.dart';
import 'package:getx_xiaopa/network/model/result.dart';
import 'package:getx_xiaopa/pages/console/model/device_items_result.dart';
import 'package:getx_xiaopa/pages/login/model/user_info_model.dart';
import 'package:getx_xiaopa/pages/mine/model/task_skin_user_result.dart';
import 'package:getx_xiaopa/pages/recharge/index.dart';
import 'package:getx_xiaopa/pages/task/model/task_skin_result.dart';
import 'package:getx_xiaopa/storage/storage.dart';
import 'package:getx_xiaopa/utils/utils.dart';
import 'package:getx_xiaopa/values/storage.dart';
import 'package:package_info_plus/package_info_plus.dart';

class UserStore extends GetxService {
  static UserStore get to => Get.find();

  ///是否登录
  bool _isLogin = false;

  bool get isLogin => _isLogin;

  ///限制网络重新登录只能一次
  bool _isLoggingOut = true;

  bool get isLoggingOut => _isLoggingOut;

  String _account = '';

  String get account => _account;
  String _password = '';

  String get password => _password;

  String _authorization = '';

  String get authorization => _authorization;

  ///最后一次是否密码登录 1=验证码登录 2=密码登录
  String _isPwdLogin = '1';

  String get isPwdLogin => _isPwdLogin;

  final _userInfo = UserInfoModel().obs;

  UserInfoModel get userInfo => _userInfo.value;

  int _balance = 0;

  int get balance => _balance;

  int _selectDevice = 0;

  ///选择的小耙
  int get selectDevice => _selectDevice;

  ///绑定过的设备
  final RxList<DeviceItemsModel> _deviceList = <DeviceItemsModel>[].obs;

  List<DeviceItemsModel> get deviceList => _deviceList;

  ///设备主题颜色 目前 1=本体色 2=草莓色(粉色)  默认本体色
  int _deviceTheme = 1;

  int get deviceTheme => _deviceTheme;

  ///全部皮肤
  final RxList<TaskSkinModel> _allSkinList = <TaskSkinModel>[].obs;

  List<TaskSkinModel> get allSkinList => _allSkinList;

  ///已解锁的皮肤
  final RxList<TaskSkinUserModel> _unlockSkinList = <TaskSkinUserModel>[].obs;

  List<TaskSkinUserModel> get unlockSkinList => _unlockSkinList;

  ///喝水轮廓图
  String _contour = "";

  String get contour => _contour;

  ///喝水皮肤图
  String _skinPicture = "";

  String get skinPicture => _skinPicture;

  ///是否使用默认皮肤
  bool isDefaultSkin = false;

  ///读取yaml文件里面的
  String _version = '';

  String get version => _version;

  String _jpushRegistrationId = '';

  Future<UserStore> init() async {
    _account = SharedPreferencesUtil.to.getString(STORAGE_USER_ACCOUNT_KEY);
    _password = SharedPreferencesUtil.to.getString(STORAGE_USER_PASSWORD_KEY);
    _authorization =
        SharedPreferencesUtil.to.getString(STORAGE_USER_AUTHORIZATION_KEY);
    _isPwdLogin = SharedPreferencesUtil.to
        .getString(STORAGE_USER_LOGIN_PWD_KEY, defaultStr: "1");

    PackageInfo packageInfo = await PackageInfo.fromPlatform();
    _version = packageInfo.version;

    // showHide();

    if (_authorization.isNotEmpty) {
      var useInfo = SharedPreferencesUtil.to.getString(STORAGE_USER_INFO_KEY);
      if (useInfo.isNotEmpty) {
        _userInfo(UserInfoModel.fromJson(jsonDecode(useInfo)));
        _isLogin = true;
      }

      if (kReleaseMode) {
        ///登录的情况下，每次进来上传报错文件 release模式下执行
        Future.delayed(const Duration(seconds: 10), () {
          ///防止一些数据没初始化，10秒后再上传
          FileStore.to.uploadCrashLog();
        });
      }
    }
    return this;
  }

  //获取用户信息
  Future<bool> getUserInfo(
      {String token = "", bool isSaveToken = false}) async {
    if (token.isNotEmpty) {
      setAuthorization(token);
    } else {
      if (_authorization.isEmpty) return false;
    }
    Result result = await http.getUserInfo({});
    if (result.code == 0) {
      UserInfoModel model = result.data!;
      if (isSaveToken && token.isEmpty) {
        setAuthorization(model.token!);
      }
      saveUserInfo(model);
    }
    return true;
  }

  // 保存用户信息
  Future<void> saveUserInfo(UserInfoModel info) async {
    _isLogin = true;

    ///给新用户设置默认名字
    if (info.nickname?.isEmpty ?? true) {
      info.nickname = "PaPa_${_getLast4(info.mobile)}";
    }
    _userInfo.value = info;
    await SharedPreferencesUtil.to
        .setString(STORAGE_USER_INFO_KEY, jsonEncode(info));
  }

  String _getLast4(String? mobile) {
    if (mobile == null || mobile.isEmpty) return "";
    if (mobile.length <= 4) return mobile;
    return mobile.substring(mobile.length - 4);
  }

  Future<void> setLoggingOut(bool value) async {
    _isLoggingOut = value;
  }

  Future<void> setAccount(String value) async {
    _account = value;
    await SharedPreferencesUtil.to.setString(STORAGE_USER_ACCOUNT_KEY, value);
  }

  Future<void> removeAccount() async {
    await SharedPreferencesUtil.to.remove(STORAGE_USER_ACCOUNT_KEY);
    _account = '';
  }

  Future<void> setPassword(String value) async {
    _password = value;
    await SharedPreferencesUtil.to.setString(STORAGE_USER_PASSWORD_KEY, value);
  }

  Future<void> removePassword() async {
    await SharedPreferencesUtil.to.remove(STORAGE_USER_PASSWORD_KEY);
    _password = '';
  }

  Future<void> setAuthorization(String value) async {
    _authorization = value;
    await SharedPreferencesUtil.to
        .setString(STORAGE_USER_AUTHORIZATION_KEY, value);
  }

  Future<void> setPwdLogin(String value) async {
    _isPwdLogin = value;
    await SharedPreferencesUtil.to.setString(STORAGE_USER_LOGIN_PWD_KEY, value);
  }

  // 注销
  Future<void> removeUserInfo() async {
    await SharedPreferencesUtil.to.remove(STORAGE_USER_AUTHORIZATION_KEY);
    await SharedPreferencesUtil.to.remove(STORAGE_USER_INFO_KEY);
    _authorization = '';
    _isLogin = false;
  }

  setSelectDevice(int cIndex) {
    _selectDevice = cIndex;
  }

  ///设置极光RegistrationId
  setJpushRegistrationId(String value) {
    _jpushRegistrationId = value;
    logD("极光注册ID：$_jpushRegistrationId");
  }

  ///绑定过的设备
  Future<void> deviceItems(
      {bool isInit = false, bool isChangeTheme = false}) async {
    if (_authorization.isEmpty) return;
    Result result = await http.itemsDevice({});
    if (result.code == 0) {
      _deviceList.clear();
      for (DeviceItemsModel e in result.data?.data ?? []) {
        _deviceList.add(e);

        ///绑定更新推送rid
        if (e.registrationId != _jpushRegistrationId) {
          _updateDeviceInfo(deviceId: e.id ?? '');
        }
      }
      if (isInit) {
        _selectDevice = 0;
      }
      if (_deviceList.isNotEmpty) {
        _deviceTheme = _deviceList[selectDevice].theme ?? 1;
        if (isChangeTheme) {
          Get.find<ThemeColorController>().switchTheme(_deviceTheme);
          Get.find<ThemeImageController>().switchTheme(_deviceTheme);
        }
        if (_allSkinList.isEmpty) {
          UserStore.to.getAllSkinList();
        }
        if (_unlockSkinList.isEmpty) {
          UserStore.to.getUnlockSkinList();
        }
      }
    }
  }

  _updateDeviceInfo({required String deviceId}) async {
    if (_jpushRegistrationId.isEmpty || deviceId.isEmpty) return;
    var map = {
      "id": deviceId,
      "registration_id": _jpushRegistrationId,
    };
    Result result = await http.updateDevicePreference(map);
    if (result.code != 0) {
      CToast.showToast(result.msg!);
    }
  }

  ///全部皮肤
  Future<void> getAllSkinList() async {
    if (_authorization.isEmpty || _deviceList.isEmpty) return;

    var map = RequestBody(pagination: false, filters: [
      Filter(
          expr: "=",
          name: "device_id",
          value: UserStore.to.deviceList[UserStore.to.selectDevice].id)
    ], orders: [
      Order(expr: "asr", name: "unlock_no")
    ]).toJson();

    final result = await http.taskShinItems(map);
    if (result.code == 0) {
      _allSkinList.clear();
      for (TaskSkinModel? model in result.data?.data ?? []) {
        _allSkinList.add(model!);
      }
    }
  }

  ///已解锁的皮肤
  Future<void> getUnlockSkinList() async {
    if (_authorization.isEmpty || _deviceList.isEmpty) return;

    var map = RequestBody(pagination: false, filters: [
      Filter(
          expr: "=",
          name: "device_id",
          value: _deviceList[UserStore.to.selectDevice].id)
    ]).toJson();

    final result = await http.taskShinUserItems(map);
    if (result.code == 0) {
      _unlockSkinList.clear();
      TaskSkinUserModel temp = TaskSkinUserModel();
      for (TaskSkinUserModel? e in result.data?.data ?? []) {
        _unlockSkinList.add(e!);

        if (e.isDefault == 1) {
          temp = e;
          _contour = e.skin?.contour ?? "";
          _skinPicture = e.skin?.picture ?? "";
        }
      }

      isDefaultSkin = _unlockSkinList
          .every((e) => int.parse(temp.skinId!) <= int.parse(e.skinId!));
    }
  }

  ///获取账户余额
  getAccountItems() async {
    var map = RequestBody(filters: [
      Filter(expr: "=", name: "category_code", value: "points")
    ]).toJson();
    Result result = await http.accountQueryItems(map);
    if (result.code == 0) {
      if (result.data.data.isNotEmpty) {
        AccoundItemModelResiltData model = result.data.data[0];
        _balance = model.balance ?? 0;
      }
    }
  }

  ///请求是否显示完整功能
  // Future showHide() async {
  //   var map = RequestBody(filters: [
  //     Filter(name: 'type', expr: '=', value: Platform.isAndroid ? '1' : '2'),
  //     Filter(name: 'code', expr: '=', value: _version),
  //     Filter(name: 'is_publish', expr: 'in', value: [1, 2])
  //   ]).toJson();
  //   Result result = await http.getVersion(map);
  //   if (result.code == 0) {
  //     VersionModel vModel = result.data;
  //     isHideUI = vModel.isPublish ?? 2;
  //   }
  // }
}
