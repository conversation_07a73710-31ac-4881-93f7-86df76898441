import 'dart:io';

import 'package:device_info_plus/device_info_plus.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:getx_xiaopa/extension/widget_extension.dart';
import 'package:getx_xiaopa/network/dio_util.dart';
import 'package:getx_xiaopa/network/http_client.dart';
import 'package:getx_xiaopa/network/model/result.dart';
import 'package:getx_xiaopa/storage/storage.dart';
import 'package:getx_xiaopa/utils/utils.dart';
import 'package:getx_xiaopa/values/storage.dart';
import 'package:path_provider/path_provider.dart';
import 'package:permission_handler/permission_handler.dart';

class FileStore extends GetxService {
  static FileStore get to => Get.find();

  String _filePath = '';

  String get filePath => _filePath;

  Future<FileStore> init() async {
    _filePath = SharedPreferencesUtil.to.getString(STATIC_VIDEO_PATH);
    if (Platform.isIOS || _filePath.isEmpty) {
      // 获取应用程序目录(ios沙盒会动态变化，每次要重新获取)
      Directory appDocumentDirectory = await getApplicationDocumentsDirectory();
      // 路径
      _filePath = '${appDocumentDirectory.path}/videos/';
    }
    return this;
  }

  ///获取下载文件夹路径(仅安卓使用)
  Future<String> getDownDirectory() async {
    var dir = await getDownloadsDirectory();
    if (dir!.path.isNotEmpty) {
      return dir.path;
    }
    return "";
  }

  ///删除指定文件(FlutterDownloader下载如果存在同名字的文件不会覆盖,所以要提前删除)
  Future<void> deleteFile(String nameFile) async {
    try {
      final file = File(nameFile);
      if (await file.exists()) {
        await file.delete();
        logD("删除文件成功");
      } else {
        logD("文件不存在");
      }
    } catch (e) {
      logD("删除文件时出错：$e");
    }
  }

  ///删除文件夹下的所有文件和文件夹
  Future<void> deleteAll(String directoryPath) async {
    Directory directory = Directory(directoryPath);
    if (await directory.exists()) {
      await directory.list().forEach((FileSystemEntity entity) async {
        if (entity is File) {
          await entity.delete();
        } else if (entity is Directory) {
          ///递归删除子目录
          deleteAll(entity.path);

          ///最后删除空目录本身
          await entity.delete();
        }
      });
    }
  }

  Future<String> saveFile(String fileName, {bool isPreferences = false}) async {
    if (Platform.isAndroid) {
      ///  安卓判断是否存在存储权限
      DeviceInfoPlugin deviceInfo = DeviceInfoPlugin();
      AndroidDeviceInfo info = await deviceInfo.androidInfo;
      String androidVersion = info.version.release;
      if (int.parse(androidVersion) >= 13) {
        return _createDirectory(fileName, isPreferences: isPreferences);
      } else {
        var hasStoragePermission = await Permission.storage.isGranted;
        if (!hasStoragePermission) {
          final status = await Permission.storage.request();
          hasStoragePermission = status.isGranted;
        }
        if (!hasStoragePermission) {
          CToast.showToast("file_store_tips".tr);
          return '';
        }
        return _createDirectory(fileName, isPreferences: isPreferences);
      }
    } else {
      ///iOS直接存储
      return _createDirectory(fileName, isPreferences: isPreferences);
    }
  }

  bool isFileExists(String fileName) {
    String filePath = '$_filePath$fileName';
    File file = File(filePath);
    return file.existsSync();
  }

  Future<String> _createDirectory(String fileName,
      {bool isPreferences = false}) async {
    // 获取应用程序目录
    Directory appDocumentDirectory = await getApplicationDocumentsDirectory();
    // 路径
    String path = '${appDocumentDirectory.path}/$fileName/';
    // 读取对应路径下的文件夹
    var dir = Directory(path);
    if (!dir.existsSync()) {
      // 创建文件,可选参数recursive：true表示可以创建嵌套文件夹，false表示只能创建最后一级文件夹（上一级文件不存在会报错），默认false
      var result = await dir.create(recursive: true);
      logD("文件创建成功：$result");
      if (isPreferences) {
        _filePath = path;
        SharedPreferencesUtil.to.setString(STATIC_VIDEO_PATH, _filePath);
      }
    }
    return path;
  }

  ///保存图片(aigc编辑图片使用)
  Future<String> saveEditImage(Uint8List imageBytes) async {
    final directory = await getApplicationDocumentsDirectory();
    final filePath = '${directory.path}/image.png';
    final file = File(filePath);
    await file.writeAsBytes(imageBytes);
    return filePath;
  }

  /// 保存崩溃日志
  Future<void> saveCrashLog(String log) async {
    final directory = await getApplicationDocumentsDirectory();
    final filePath = '${directory.path}/crash_log.txt';
    final file = File(filePath);
    // 追加模式写入（如果文件不存在会自动创建）
    //flush: true 立即写入
    await file.writeAsString('\n\n$log', mode: FileMode.append, flush: true);
  }

  ///导出崩溃日志
  Future<void> exportCrashLog() async {
    final docDir = await getApplicationDocumentsDirectory();
    final logFile = File('${docDir.path}/crash_log.txt');
    if (!await logFile.exists()) {
      // CToast.showToast("崩溃日志不存在");
      return;
    }

    if (Platform.isAndroid) {
      // 获取外部下载目录
      final downloadsDir = Directory('/storage/emulated/0/Download');
      final targetFile = File('${downloadsDir.path}/crash_log.txt');

      await logFile.copy(targetFile.path);

      // CToast.showToast("Android日志已导出到下载目录");
    }
  }

  ///上传崩溃日志
  Future<void> uploadCrashLog() async {
    final docDir = await getApplicationDocumentsDirectory();
    final logFile = File('${docDir.path}/crash_log.txt');
    if (!logFile.existsSync()) {
      // CToast.showToast("崩溃日志不存在");
      return;
    }

    final result = await DioUtil.getInstance().uploadFile(logFile.path,
        access: "log-exception", filename: "exception.txt");
    if (result.isNotEmpty) {
      var map = {"logfile": result};
      Result resultUpload = await http.uploadCrashLog(map);
      if (resultUpload.code == 0) {
        deleteFile(logFile.path);
        // CToast.showToast("上传成功");
      }
    }
  }
}
