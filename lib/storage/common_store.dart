import 'dart:convert';
import 'dart:io';
import 'package:get/get.dart';
import 'package:getx_xiaopa/network/http_client.dart';
import 'package:getx_xiaopa/network/model/request_body.dart';
import 'package:getx_xiaopa/network/model/result.dart';
import 'package:getx_xiaopa/pages/diary/model/diary_feeling_model_result.dart.dart';
import 'package:getx_xiaopa/pages/diary/model/diary_mood_items_result.dart';
import 'package:getx_xiaopa/pages/diary/model/diary_thing_items_result.dart';
import 'package:getx_xiaopa/storage/model/announce_model_result.dart';
import 'package:getx_xiaopa/storage/model/base_ad_result.dart';
import 'package:getx_xiaopa/storage/model/brain_detail_model.dart';
import 'package:getx_xiaopa/storage/model/setu_public_result.dart';
import 'package:getx_xiaopa/storage/storage.dart';
import 'package:getx_xiaopa/values/storage.dart';

///一些公共的请求可以先获取数据
class CommonStore extends GetxService {
  static CommonStore get to => Get.find();

  ///使用<string,map>储存时候转换会有问题，暂时使用列表 0=静态 1=动态
  Map<String, dynamic> _videoUrl = {};

  Map<String, dynamic> get videoUrl => _videoUrl;

  ///是否初始化videoUrl
  bool _isInit = true;

  BrainDetailModel _brainDetail = BrainDetailModel();

  BrainDetailModel get brainDetail => _brainDetail;

  ///心情列表
  List<DiaryMoodItemsModel> _diaryMoodItems = [];

  List<DiaryMoodItemsModel> get diaryMoodItems => _diaryMoodItems;

  //事件列表
  List<DiaryThingItemsModel> _diaryThingItems = [];

  List<DiaryThingItemsModel> get diaryThingItems => _diaryThingItems;

  //Feeling列表
  List<DiaryFeelingModel> _diaryFeelingItems = [];

  List<DiaryFeelingModel> get diaryFeelingItems => _diaryFeelingItems;

  ///图片
  final List<BaseAdModel> _baseAd = [];
  List<BaseAdModel> get baseAd => _baseAd;

  ///特殊节日图片
  final List<BaseAdModel> _baseSpecialAd = [];
  List<BaseAdModel> get baseSpecialAd => _baseSpecialAd;

  ///生日图片
  final List<BaseAdModel> _baseBrithdayAd = [];
  List<BaseAdModel> get baseBrithdayAd => _baseBrithdayAd;

  int _voicePrice = 0;
  int get voicePrice => _voicePrice;

  //是否显示完整的app功能 1=显示 2=不显示；默认不显示
  int _showAichat = 2;
  int get showAichat => _showAichat;

  String announceTitle = '';
  String announceContent = '';

  Future<CommonStore> init() async {
    if (UserStore.to.authorization.isNotEmpty) {
      getDiaryMoodItems();
      getDiaryThingItems();
      getDiaryFeelingItems();
    }

    getSetupPublic();
    getAnnounceList();

    var videoUrl =
        SharedPreferencesUtil.to.getString(VIDEO_URL_NEW, defaultStr: '');
    if (videoUrl.isNotEmpty) {
      _videoUrl = jsonDecode(videoUrl);
      _isInit = false;
    }
    return this;
  }

  Future<void> setIsProduction(bool value) async {
    await SharedPreferencesUtil.to.setBool(STORAGE_IS_PRODUCTION, value);
  }

  ///获取数字人详情
  getBrainDetail(String botId) async {
    if (botId.isEmpty) return;

    var map = {"bot_code": botId};

    Result result = await http.getBrainDetail(map);
    if (result.code == 0) {
      _brainDetail = result.data;
      if (_isInit) {
        _videoUrl[_brainDetail.id!] = {};
        _videoUrl[_brainDetail.id!]["very_happy"] = ["", ""];
        _videoUrl[_brainDetail.id!]["happy"] = ["", ""];
        _videoUrl[_brainDetail.id!]["normal"] = ["", ""];
        _videoUrl[_brainDetail.id!]["unhappy"] = ["", ""];
        _videoUrl[_brainDetail.id!]["very_unhappy"] = ["", ""];
      } else {
        if (!_videoUrl.keys.contains(_brainDetail.id!)) {
          _videoUrl[_brainDetail.id!] = {};
          _videoUrl[_brainDetail.id!]["very_happy"] = ["", ""];
          _videoUrl[_brainDetail.id!]["happy"] = ["", ""];
          _videoUrl[_brainDetail.id!]["normal"] = ["", ""];
          _videoUrl[_brainDetail.id!]["unhappy"] = ["", ""];
          _videoUrl[_brainDetail.id!]["very_unhappy"] = ["", ""];
        }
      }
      _isInit = false;
    }
  }

  Future<void> setVideoUrl(
      String brainId, String staticUrl, String dynamicUrl, String mood) async {
    _videoUrl[brainId]![mood][0] = staticUrl;
    _videoUrl[brainId]![mood][1] = dynamicUrl;
    await SharedPreferencesUtil.to
        .setString(VIDEO_URL_NEW, jsonEncode(_videoUrl));
  }

  ///心情列表
  getDiaryMoodItems() async {
    var map = RequestBody(pagination: false, filters: [
      Filter(expr: "=", name: "lang", value: ConfigStore.to.locale.languageCode)
    ], orders: [
      Order(expr: "asr", name: "sort")
    ]).toJson();
    Result result = await http.diaryMoodItems(map);
    if (result.code == 0) {
      _diaryMoodItems = result.data.data;
    }
  }

  ///根据mood的code返回对应的name
  String getMoodName(String mood) {
    String temp = '';
    for (var model in _diaryMoodItems) {
      if (model.code == mood) {
        temp = model.name ?? '';
        break;
      }
    }
    return temp.isEmpty ? mood : temp;
  }

  ///事件列表
  getDiaryThingItems() async {
    var map = RequestBody(pagination: false, filters: [
      //不筛选请求全部数据
      // Filter(expr: "=", name: "lang", value: ConfigStore.to.locale.languageCode)
    ], orders: [
      Order(expr: "asr", name: "sort")
    ]).toJson();
    Result result = await http.diaryThingItems(map);
    if (result.code == 0) {
      _diaryThingItems = result.data.data;
    }
  }

  ///传入事件返回对应的图片路径
  String getDiaryThingImage(String thing) {
    String temp = '';
    for (DiaryThingItemsModel tModel in _diaryThingItems) {
      if (tModel.name == thing) {
        temp = tModel.picture ?? '';
        break;
      }
    }
    return temp;
  }

  ///Feeling列表
  getDiaryFeelingItems() async {
    var map = RequestBody(pagination: false, filters: [
      Filter(expr: "=", name: "lang", value: ConfigStore.to.locale.languageCode)
    ], orders: [
      Order(expr: "asr", name: "sort")
    ]).toJson();
    Result result = await http.diaryFeelingItems(map);
    if (result.code == 0) {
      _diaryFeelingItems = result.data.data;
    }
  }

  ///获取(特殊节日)启动图
  getBaseAd() async {
    _baseAd.clear();
    _baseSpecialAd.clear();
    _baseBrithdayAd.clear();
    var map = RequestBody(pagination: false, filters: [
      Filter(expr: "in", name: "position", value: [
        (ConfigStore.to.getLocaleCode() == "zh" ? "start" : "enstart"),
        (ConfigStore.to.getLocaleCode() == "zh" ? "special" : "enspecial"),
        (ConfigStore.to.getLocaleCode() == "zh" ? "birthday" : "enbirthday")
      ])
    ]).toJson();
    Result result = await http.getBaseAd(map);
    if (result.code == 0) {
      for (BaseAdModel model in result.data.data) {
        if (model.position == "start" || model.position == "enstart") {
          _baseAd.add(model);
        }
        if (model.position == "special" || model.position == "enspecial") {
          _baseSpecialAd.add(model);
        }
        if (model.position == "birthday" || model.position == "enbirthday") {
          _baseBrithdayAd.add(model);
        }
      }
    }
  }

  ///获取配置项
  getSetupPublic() async {
    var map = RequestBody(filters: [
      Filter(
          expr: "in",
          name: "code",
          value: ["voicePrice", "isAndriodShowAichat", "isIosShowAichat"])
    ], pagination: false)
        .toJson();
    Result result = await http.setupPublic(map);
    if (result.code == 0) {
      SetuPublicResult sResult = result.data;
      for (SetuPublicModel model
          in (sResult.data ?? []).whereType<SetuPublicModel>()) {
        if (model.code == 'voicePrice') {
          _voicePrice = int.parse(model.value ?? "0");
        }
        if (Platform.isAndroid) {
          if (model.code == 'isAndriodShowAichat') {
            _showAichat = int.parse(model.value ?? "2");
          }
        }
        if (Platform.isIOS) {
          if (model.code == 'isIosShowAichat') {
            _showAichat = int.parse(model.value ?? "2");
          }
        }
      }
    }
  }

  ///获取公告列表
  getAnnounceList() async {
    var map = RequestBody(filters: [
      Filter(expr: "=", name: "status", value: 2),
      Filter(
          expr: "in", name: "os", value: [Platform.isAndroid ? "1" : "2", "3"])
    ], pagination: false)
        .toJson();
    Result result = await http.announceList(map);
    if (result.code == 0) {
      AnnounceModelResult aResult = result.data;
      for (AnnounceModel model
          in (aResult.data ?? []).whereType<AnnounceModel>()) {
        announceTitle = model.title ?? '';
        announceContent = model.content ?? '';
      }
    }
  }
}
