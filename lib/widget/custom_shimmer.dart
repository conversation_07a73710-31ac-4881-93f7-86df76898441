import 'package:flutter/material.dart';
import 'package:shimmer/shimmer.dart';

class CustomShimmer extends StatelessWidget {
  final double height;
  final double width;
  final double borderRadius;
  final double margin;
  final Color? bColor;

  const CustomShimmer({
    super.key,
    this.height = 32.0,
    this.width = 32.0,
    this.borderRadius = 4,
    this.margin = 0,
    this.bColor,
  });

  @override
  Widget build(BuildContext context) {
    return Shimmer.fromColors(
      baseColor: bColor ?? Colors.grey.shade300,
      highlightColor: Colors.grey.shade100,
      child: Container(
        margin: EdgeInsets.all(margin),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(borderRadius),
        ),
        height: height,
        width: width,
      ),
    );
  }
}
