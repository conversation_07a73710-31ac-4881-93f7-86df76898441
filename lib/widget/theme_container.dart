import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:getx_xiaopa/utils/utils.dart';

class ThemeContainer extends StatelessWidget {
  final double? tWidget;
  final double? tHeight;
  final EdgeInsetsGeometry? tMargin;
  final EdgeInsetsGeometry? tPandding;
  final bool flag;
  final Color? subColor;
  final double radius;
  final List<BoxShadow>? boxShadow;
  final Widget child;
  final String keyName;

  const ThemeContainer(
      {super.key,
      required this.child,
      this.flag = true,
      this.radius = 8,
      this.subColor,
      this.boxShadow,
      this.tWidget,
      this.tHeight,
      this.tPandding,
      this.tMargin,
      this.keyName = 'textColor'});

  @override
  Widget build(BuildContext context) {
    return GetBuilder<ThemeColorController>(
      id: "theme",
      builder: (themeCtl) {
        return Container(
          margin: tMargin,
          padding: tPandding,
          width: tWidget,
          height: tHeight,
          decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(radius),
              color: flag ? themeCtl.getColor(keyName) : subColor,
              boxShadow: boxShadow),
          child: child,
        );
      },
    );
  }
}
