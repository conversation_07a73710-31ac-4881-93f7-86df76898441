import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:getx_xiaopa/extension/widget_extension.dart';
import 'package:getx_xiaopa/utils/utils.dart';
import 'package:getx_xiaopa/values/values.dart';
import 'package:getx_xiaopa/widget/widget.dart';

class CommonInputDialog extends StatelessWidget {
  /// 是否输入模式 即带输入框 默认否
  final bool isInput;

  final String title;
  final TextStyle? titleStyle;
  final String content;
  final TextStyle? contextStyle;

  final double mHeight;

  ///禁止键盘返回
  final bool isPop;

  /// 是否只显示2个按钮
  final bool isShowTwoBtn;

  final String? mConfirm;
  final TextStyle? mConfirmStyle;
  final String? mCancel;
  final TextStyle? mCancelStyle;

  final Function()? confirmAction;
  final Function()? cancelAction;

  final TextEditingController? pwdController;

  const CommonInputDialog(
      {super.key,
      this.isInput = false,
      required this.title,
      this.titleStyle,
      this.content = '',
      this.contextStyle,
      this.mHeight = 176,
      this.isPop = true,
      this.isShowTwoBtn = false,
      this.mConfirm = "",
      this.mConfirmStyle,
      this.mCancel = "",
      this.mCancelStyle,
      this.confirmAction,
      this.cancelAction,
      this.pwdController});

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      onPopInvokedWithResult: (didPop, result) {
        if (didPop) return;
        if (isPop) {
          Get.back();
        }
      },
      child: Center(
        child: Container(
          width: 261.w,
          height: mHeight.h,
          clipBehavior: Clip.hardEdge,
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(14.r),
          ),
          child: _bodyWidget(),
        ),
      ),
    );
  }

  Widget _bodyWidget() {
    if (isInput) {
      return Column(
        children: [
          SizedBox(height: 20.h),
          Text(
            title,
            style: titleStyle ??
                StyleConfig.otherStyle(
                    color: ColorConfig.searchTextColor,
                    fontWeight: FontWeight.w500),
          ),
          SizedBox(height: 20.h),
          Container(
            margin: EdgeInsets.only(left: 10.w, right: 10.w),
            width: 241.w,
            height: 48.h,
            decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(4.r),
                color: const Color.fromRGBO(249, 249, 249, 1)),
            child: AppTextField(
              controller: pwdController,
              style: StyleConfig.otherStyle(
                  color: ColorConfig.searchTextColor,
                  fontSize: 16,
                  fontWeight: FontWeight.w500),
              keyboardType: TextInputType.text,
              decoration: InputDecoration(
                border: InputBorder.none,
                enabledBorder: InputBorder.none,
                focusedBorder: InputBorder.none,
                counterText: '',
                contentPadding: EdgeInsets.symmetric(horizontal: 20.w),
                hintText: 'console_paring_wifi_pwd_hint_text'.tr,
                hintStyle: StyleConfig.otherStyle(
                    color: ColorConfig.shopDetailTextColor, fontSize: 14),
              ),
            ),
          ),
          const Expanded(child: SizedBox()),
          Container(
            width: 261.w,
            height: 1.h,
            color: const Color.fromRGBO(215, 215, 215, 1),
          ),
          _btnWidget()
        ],
      );
    } else {
      return Column(
        children: [
          SizedBox(height: 20.h),
          Text(
            title,
            style: titleStyle ??
                StyleConfig.otherStyle(
                    color: ColorConfig.searchTextColor,
                    fontWeight: FontWeight.w500),
          ),
          Text(
            content,
            style: contextStyle ??
                StyleConfig.otherStyle(
                    color: ColorConfig.searchTextColor,
                    fontWeight: FontWeight.w500),
          ),
          const Expanded(child: SizedBox()),
          Container(
            width: 261.w,
            height: 1.h,
            color: const Color.fromRGBO(215, 215, 215, 1),
          ),
          _btnWidget()
        ],
      );
    }
  }

  Widget _btnWidget() {
    return isShowTwoBtn
        ? SizedBox(
            width: 216.w,
            height: 45.h,
            child: Row(
              children: [
                Expanded(
                  child: Center(
                    child: Text(
                      mCancel ?? "common_dialog_cancel".tr,
                      style: mCancelStyle ??
                          StyleConfig.otherStyle(
                              color: const Color.fromRGBO(102, 102, 102, 1),
                              fontWeight: FontWeight.w400),
                    ),
                  ).inkWell(() {
                    Get.back();
                    cancelAction?.call();
                  }),
                ),
                Container(
                  width: 1.w,
                  height: 45.h,
                  color: const Color.fromRGBO(215, 215, 215, 1),
                ),
                Expanded(
                  child: Center(
                    child: Text(
                      mConfirm ?? "common_dialog_confirm".tr,
                      style: mConfirmStyle ??
                          StyleConfig.otherStyle(
                              color: Get.find<ThemeColorController>()
                                  .gFloatingTextColor,
                              fontWeight: FontWeight.w500),
                    ),
                  ).inkWell(() {
                    Get.back();
                    confirmAction?.call();
                  }),
                ),
              ],
            ),
          )
        : SizedBox(
            width: 216.w,
            height: 45.h,
            child: Center(
              child: Text(
                mCancel ?? "common_dialog_cancel".tr,
                style: mCancelStyle ??
                    StyleConfig.otherStyle(
                        color: const Color.fromRGBO(180, 142, 78, 1),
                        fontWeight: FontWeight.w500),
              ),
            ),
          ).inkWell(
            () {
              Get.back();
              cancelAction?.call();
            },
          );
  }
}
