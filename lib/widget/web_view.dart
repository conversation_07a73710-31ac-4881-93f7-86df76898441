import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:getx_xiaopa/extension/images.dart';
import 'package:getx_xiaopa/extension/widget_extension.dart';
import 'package:getx_xiaopa/r.dart';
import 'package:webview_flutter/webview_flutter.dart';

class WebView extends StatelessWidget {
  const WebView({super.key});

  @override
  Widget build(BuildContext context) {
    late final PlatformWebViewControllerCreationParams params;
    params = const PlatformWebViewControllerCreationParams();

    final WebViewController wvController =
        WebViewController.fromPlatformCreationParams(params,
            onPermissionRequest: (WebViewPermissionRequest request) {
      request.grant();
    });

    wvController.setJavaScriptMode(JavaScriptMode.unrestricted);
    wvController.setBackgroundColor(Colors.white);
    wvController.loadRequest(Uri.parse(Get.arguments));
    wvController.setNavigationDelegate(NavigationDelegate(
        onPageStarted: (url) {
          CToast.showLoading();
        },
        onPageFinished: (url) {
          CToast.dismiss();
          //改变网页背景颜色和文字颜色
          const jsCode = """
            document.querySelector('body').setAttribute('data-weui-theme', 'dark');
            const sections = document.querySelectorAll('section');
            sections.forEach(function(section) {
              section.style.backgroundColor = 'white';
              section.style.color = 'white'; // 设置文字颜色为白色以便能看清内容
            });
            caches.delete("web-view-cache"); //清除缓存
            """;
          wvController.runJavaScript(jsCode);
        }));
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        leadingWidth: 40.w,
        //设置返回按钮的图标为白色
        leading: Padding(
          padding: EdgeInsets.only(left: 20.w),
          child: Images(
            path: R.back_png,
            width: 20.w,
            height: 20.h,
          ),
        ).inkWell(() async {
          //如果浏览器中有历史记录
          var canBack = await wvController.canGoBack();
          if (canBack) {
            // 当网页还有历史记录时，返回webview上一页
            await wvController.goBack();
          } else {
            // 返回原生页面上一页
            Navigator.of(Get.context!).pop();
          }
        }),
      ),
      body: WebViewWidget(controller: wvController),
    );
  }
}
