import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:getx_xiaopa/extension/images.dart';
import 'package:getx_xiaopa/values/values.dart';

class EmptyDataWidget extends StatelessWidget {
  final String title;
  final String imagePath;
  const EmptyDataWidget({
    super.key,
    this.title = '',
    this.imagePath = '',
  });

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Images(
            path: imagePath,
            width: 200.w,
            height: 160.h,
          ),
          SizedBox(height: 10.h),
          Text(
            title,
            style: StyleConfig.otherStyle(
                color: ColorConfig.shopDetailTextColor, fontSize: 14),
          )
        ],
      ),
    );
  }
}
