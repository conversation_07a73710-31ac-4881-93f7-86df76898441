import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_swiper_null_safety_flutter3/flutter_swiper_null_safety_flutter3.dart';
import 'package:getx_xiaopa/extension/images.dart';
import 'package:getx_xiaopa/network/http_client.dart';

import './custom_pagination_fraction_widget.dart';
import './custom_swiper_pagination_widget.dart';

class SwiperWidget extends StatefulWidget {
  /// 图片数组
  final List<String> imagesList;

  /// 宽度
  final double? width;

  /// 高度
  final double? height;

  /// 指示器对其方式
  final Alignment? alignment;

  /// 指示器颜色
  final Color? color;

  /// 指示器选中颜色
  final Color? activeColor;

  /// 事件
  final Function? onTap;

  /// 数字背景颜色
  final Color? backgroundColor;

  /// 字体大小
  final double? fontSize;

  /// 字体选中大小
  final double? activeFontSize;

  /// 类型 1:圆点 2:数字
  final int type;

  final bool isAuto;
  final bool isLoop;

  final SwiperController? controller;

  const SwiperWidget(
      {super.key,
      required this.imagesList,
      required this.width,
      required this.height,
      this.alignment,
      this.color,
      this.activeColor,
      this.onTap,
      this.backgroundColor,
      this.fontSize,
      this.activeFontSize,
      required this.type,
      this.isAuto = true,
      this.isLoop = true,
      this.controller});

  @override
  State<SwiperWidget> createState() => _SwiperWidgetState();
}

class _SwiperWidgetState extends State<SwiperWidget> {
  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: widget.width,
      height: widget.height,
      child: Swiper(
        controller: widget.controller,
        // scale: 0.5,
        itemBuilder: (BuildContext context, int index) {
          return Images(
            path: fileUrl(widget.imagesList[index]),
            boxFit: BoxFit.contain,
          );
        },
        itemCount: widget.imagesList.length,
        onTap: (int index) {
          if (widget.onTap != null) {
            widget.onTap!(index);
          }
        },
        autoplay: widget.imagesList.length == 1 ? false : widget.isAuto,
        loop: widget.imagesList.length == 1 ? false : widget.isLoop,

        /// 自定义指示器
        pagination: widget.type == 1
            ? SwiperPagination(
                builder: CustomSwiperPaginationBuilder(
                    color: widget.color ?? Colors.white38,
                    activeColor: widget.activeColor ?? Colors.white,
                    alignment: widget.alignment ?? Alignment.bottomCenter),
              )
            : SwiperPagination(
                alignment: Alignment.bottomRight,
                builder: CustomFractionPaginationBuilder(
                    backgroundColor: widget.backgroundColor,
                    fontSize: widget.fontSize ?? 12.sp,
                    activeFontSize: widget.activeFontSize ?? 14.sp,
                    color: widget.color ?? Colors.white70,
                    activeColor: widget.activeColor ?? Colors.white)),
      ),
    );
  }
}
