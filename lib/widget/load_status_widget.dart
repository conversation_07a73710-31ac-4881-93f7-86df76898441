import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:getx_xiaopa/extension/images.dart';
import 'package:getx_xiaopa/r.dart';

class LoadStatusWidget extends StatelessWidget {
  const LoadStatusWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return AnnotatedRegion<SystemUiOverlayStyle>(
      value: const SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,
        statusBarIconBrightness: Brightness.dark,
      ),
      child: Container(
        color: Colors.white,
        child: Center(
          child: Images(path: R.loading_gif, width: 200.w, height: 160.h),
        ),
      ),
    );
  }
}
