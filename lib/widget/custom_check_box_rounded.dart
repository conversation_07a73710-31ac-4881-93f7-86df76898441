import 'package:flutter/material.dart';

class CheckBoxRounded extends StatefulWidget {
  const CheckBoxRounded(
      {this.onTap,
      this.size,
      this.borderWidth = 1.0,
      this.borderColor,
      this.checkedColor,
      this.checkedBgColor,
      this.uncheckedBgColor,
      this.checkedWidget,
      this.uncheckedWidget,
      this.animationDuration,
      this.isChecked,
      this.disable = false,
      super.key,
      this.isGroup = false,
      this.value,
      this.groupValue,
      this.onGroupTap});

  /// Define wether the checkbox is marked or not
  final bool? isChecked;

  /// Defines interactive changing [isChecked] value on tap gesture
  final bool disable;

  /// Define the size of the checkbox
  final double? size;

  /// Define the width of the border checkbox
  final double borderWidth;

  /// Define the border of the widget
  final Color? borderColor;

  final Color? checkedColor;

  /// Define the color that is shown when Widgets is checked
  final Color? checkedBgColor;

  /// Define the color that is shown when Widgets is unchecked
  final Color? uncheckedBgColor;

  /// Define the widget that is shown when Widgets is checked
  final Widget? checkedWidget;

  /// Define the widget that is shown when Widgets is unchecked
  final Widget? uncheckedWidget;

  /// Define Function that os executed when user tap on checkbox
  // ignore: avoid_positional_boolean_parameters
  final void Function(bool? value)? onTap;

  final void Function(int? value)? onGroupTap;

  /// Define the duration of the animation. If any
  final Duration? animationDuration;

  /// 是否是组
  final bool? isGroup;

  /// 当前的值
  final int? value;

  /// 组的值
  final int? groupValue;

  @override
  State<CheckBoxRounded> createState() => _CheckBoxRoundedState();
}

class _CheckBoxRoundedState extends State<CheckBoxRounded> {
  bool _isChecked = false;
  int _value = 0;

  @override
  void initState() {
    super.initState();

    if (widget.isChecked != null) {
      _isChecked = widget.isChecked ?? false;
    }
    if (widget.isGroup != null) {
      _value = widget.value ?? 0;
    }
  }

  @override
  void didUpdateWidget(covariant CheckBoxRounded oldWidget) {
    if (widget.isChecked != null && oldWidget.isChecked != widget.isChecked) {
      _isChecked = widget.isChecked!;
    }
    super.didUpdateWidget(oldWidget);
  }

  @override
  Widget build(BuildContext context) {
    assert(debugCheckHasMaterial(context));

    final ThemeData themeData = Theme.of(context);

    final double effectiveIconSize = widget.size ?? 24.0;

    final Color effectiveBorderColor =
        widget.borderColor ?? themeData.dividerColor;

    final Color effectiveCheckedColor =
        widget.checkedBgColor ?? themeData.colorScheme.secondary;

    final Color effectiveUncheckedColor =
        widget.uncheckedBgColor ?? themeData.scaffoldBackgroundColor;

    final effectiveAnimationDuration =
        widget.animationDuration ?? const Duration(milliseconds: 100);

    final Widget checkedWidget = widget.checkedWidget ??
        Icon(
          Icons.check_rounded,
          color: widget.checkedColor ?? Colors.white,
          size: effectiveIconSize / 1.2,
        );

    final Widget uncheckedWidget = widget.uncheckedWidget != null
        ? SizedBox(child: widget.uncheckedWidget)
        : const SizedBox.shrink();

    Widget child = ClipRRect(
      borderRadius: const BorderRadius.all(Radius.circular(100)),
      child: AnimatedContainer(
        duration: effectiveAnimationDuration,
        width: effectiveIconSize,
        height: effectiveIconSize,
        decoration: BoxDecoration(
          border: Border.all(
            width: widget.borderWidth,
            color: _isChecked ? Colors.transparent : effectiveBorderColor,
          ),
          borderRadius: const BorderRadius.all(Radius.circular(100)),
          color: _isChecked ? effectiveCheckedColor : effectiveUncheckedColor,
        ),
        child: _isChecked ? checkedWidget : uncheckedWidget,
      ),
    );

    if (widget.isGroup ?? false) {
      child = ClipRRect(
        borderRadius: const BorderRadius.all(Radius.circular(100)),
        child: AnimatedContainer(
          duration: effectiveAnimationDuration,
          width: effectiveIconSize,
          height: effectiveIconSize,
          decoration: BoxDecoration(
            border: Border.all(
              width: widget.borderWidth,
              color: _value == widget.groupValue
                  ? Colors.transparent
                  : effectiveBorderColor,
            ),
            borderRadius: const BorderRadius.all(Radius.circular(100)),
            color: _value == widget.groupValue
                ? effectiveCheckedColor
                : effectiveUncheckedColor,
          ),
          child: _value == widget.groupValue ? checkedWidget : uncheckedWidget,
        ),
      );
    }

    return widget.disable
        ? child
        : GestureDetector(
            onTap: () {
              if (widget.isGroup ?? false) {
                widget.onGroupTap?.call(_value);
                setState(() => _value = widget.value!);
                return;
              }
              widget.onTap?.call(!_isChecked);
              setState(() => _isChecked = !_isChecked);
            },
            child: child,
          );
  }
}
