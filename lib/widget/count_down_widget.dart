import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

class CountDownWidget extends StatefulWidget {
  const CountDownWidget(
      {this.countDownTime = 5, required this.onTop, super.key});

  final num countDownTime;

  final Function() onTop;

  @override
  State<StatefulWidget> createState() => _CountDownWidgetState();
}

class _CountDownWidgetState extends State<CountDownWidget> {
  ///声明变量
  late Timer _timer;

  ///记录当前的时间
  num currentTimer = 0;

  ///防止点击多次
  bool _isBusy = false;

  @override
  void initState() {
    super.initState();

    ///循环执行
    ///间隔1秒
    currentTimer = widget.countDownTime;

    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      ///
      currentTimer -= 1;
      setState(() {});

      ///到5秒后停止
      if (currentTimer <= 0) {
        _timer.cancel();

        ///如果点击了就不执行下面操作
        if (_isBusy) return;
        widget.onTop();
      }
    });
  }

  @override
  void dispose() {
    ///取消计时器
    _timer.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        if (_isBusy) return;

        _isBusy = true;

        _timer.cancel();
        if (currentTimer <= 0) return;
        widget.onTop();
      },
      child: Container(
        margin: EdgeInsets.only(top: 50.h, right: 20.w),
        width: 70.w,
        height: 30.h,
        decoration: ShapeDecoration(
          color: Colors.black12,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.all(
              Radius.circular(32.0.w),
            ),
          ),
        ),
        child: Stack(
          alignment: Alignment.centerLeft,
          children: [
            Positioned(
                left: 10.w,
                child: Text(
                  '$currentTimer',
                  style: TextStyle(
                      color: Colors.white,
                      fontSize: 12.sp,
                      fontWeight: FontWeight.w400),
                )),
            Positioned(
              left: 20.w,
              child: Text(
                "|",
                style: TextStyle(
                    color: Colors.white,
                    fontSize: 14.sp,
                    fontWeight: FontWeight.w400),
              ),
            ),
            Positioned(
              left: 30.w,
              child: Text(
                "count_down_btn".tr,
                style: TextStyle(
                    color: Colors.white,
                    fontSize: 12.sp,
                    fontWeight: FontWeight.w400),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
