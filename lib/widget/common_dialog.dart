import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:getx_xiaopa/extension/widget_extension.dart';
import 'package:getx_xiaopa/values/values.dart';
import 'package:getx_xiaopa/widget/widget.dart';

class CommonDialog extends StatelessWidget {
  final double mHeight;
  final String mContent;
  final TextAlign? mContextAlign;
  final String? mTitle;
  final FontWeight? titleFontWeight;
  final String? mCancel;
  final String? mConfirm;
  final bool isCustonWidget;
  final double mContextSize;
  final FontWeight? contextFontWeight;
  final Color? mContextTextColor;
  final Color? mCancelTextColor;
  final Color? mConfirmTextColor;
  final Color? mCancelColor;
  final Color? mConfirmColor;
  final FontWeight? confirmFontWeight;

  /// 是否只显示一个确定按钮
  final bool isShowConfirm;
  final Function()? cancelAction;
  final Function()? closeAction;
  final Function() confirmAction;

  ///禁止键盘返回
  final bool isPop;

  final List<String>? keysList;
  final Function(String str)? onTapClick;

  final EdgeInsetsGeometry? exPadding;

  const CommonDialog(
      {super.key,
      this.mHeight = 203,
      this.mTitle,
      this.titleFontWeight,
      this.mCancel,
      this.mConfirm,
      this.isShowConfirm = false,
      this.mContextSize = 14,
      this.contextFontWeight,
      required this.mContent,
      this.mContextAlign,
      required this.confirmAction,
      this.cancelAction,
      this.closeAction,
      this.mContextTextColor,
      this.mCancelColor,
      this.mConfirmColor,
      this.isCustonWidget = false,
      this.mCancelTextColor,
      this.mConfirmTextColor,
      this.confirmFontWeight,
      this.isPop = true,
      this.keysList,
      this.onTapClick,
      this.exPadding});

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      onPopInvokedWithResult: (didPop, result) {
        if (didPop) return;
        if (isPop) {
          Get.back();
        }
      },
      child: Center(
        child: Container(
          width: 295.w,
          height: mHeight.h,
          padding:
              EdgeInsets.only(top: 38.h, bottom: 26.h, left: 19.w, right: 20.w),
          decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(10.w), color: Colors.white),
          child: Column(
            children: [
              Offstage(
                offstage: mTitle == null || mTitle!.isEmpty,
                child: Text(
                  mTitle ?? "common_dialog_title".tr,
                  style: StyleConfig.otherStyle(
                      color: ColorConfig.searchTextColor,
                      fontWeight: titleFontWeight ?? FontWeight.w500,
                      fontSize: 18),
                ),
              ),
              Expanded(
                child: SingleChildScrollView(
                  child: Container(
                    margin: EdgeInsets.only(top: 10.h),
                    padding: exPadding,
                    child: PrivacyToastWidget(
                      data: mContent,
                      keys: keysList ??
                          ["login_protocol_one".tr, "login_protocol_two".tr],
                      mContextAlign: mContextAlign ?? TextAlign.start,
                      style: StyleConfig.otherStyle(
                          color: mContextTextColor ??
                              ColorConfig.shopDetailTextColor,
                          fontSize: mContextSize,
                          fontWeight: contextFontWeight ?? FontWeight.w400),
                      keyStyle: StyleConfig.otherStyle(
                          color: const Color.fromRGBO(180, 142, 78, 1),
                          fontSize: mContextSize,
                          fontWeight: contextFontWeight ?? FontWeight.w500),
                      onTapCallback: (string) {
                        onTapClick?.call(string);
                      },
                    ),
                  ),
                ),
              ),
              Container(
                margin: EdgeInsets.only(top: 10.h),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Visibility(
                        visible: !isShowConfirm,
                        child: Container(
                          width: 105.w,
                          height: 48.h,
                          decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(24.r),
                              color: mCancelColor ??
                                  const Color.fromRGBO(215, 215, 215, 1)),
                          child: Center(
                            child: Text(
                              mCancel ?? "common_dialog_cancel".tr,
                              style: StyleConfig.otherStyle(
                                color: mCancelTextColor ??
                                    ColorConfig.authenticationTextColor,
                              ),
                            ),
                          ),
                        ).inkWell(() {
                          cancelAction != null
                              ? cancelAction?.call()
                              : Get.back();
                        })),
                    Visibility(
                        visible: !isShowConfirm, child: SizedBox(width: 16.w)),
                    Container(
                      width: isCustonWidget ? null : 135.w,
                      padding: isCustonWidget
                          ? EdgeInsets.only(left: 28.w, right: 28.w)
                          : null,
                      height: 48.h,
                      decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(24.r),
                          color: mConfirmColor ??
                              const Color.fromRGBO(40, 39, 46, 1)),
                      child: Center(
                        child: ThemeText(
                          dataStr: mConfirm ?? "common_dialog_confirm".tr,
                          keyName: 'textColor',
                          fontWeight: confirmFontWeight ?? FontWeight.w500,
                          height: 1,
                        ),
                      ),
                    ).inkWell(() {
                      Get.back();
                      confirmAction();
                    })
                  ],
                ),
              )
            ],
          ),
        ),
      ),
    );
  }
}
