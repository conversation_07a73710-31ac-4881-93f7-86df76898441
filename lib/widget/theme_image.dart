import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:getx_xiaopa/extension/images.dart';
import 'package:getx_xiaopa/utils/theme_util.dart';

///根据主题切换图片颜色
class ThemeImage extends StatelessWidget {
  final String imgPath;
  final String keyName;
  final double? imgWidget;
  final double? imgHeight;
  final BoxFit? fit;
  const ThemeImage(
      {super.key,
      required this.imgPath,
      required this.keyName,
      this.imgWidget,
      this.imgHeight,
      this.fit});

  @override
  Widget build(BuildContext context) {
    return GetBuilder<ThemeColorController>(
      id: "theme",
      builder: (themeCtr) {
        return Images(
          path: imgPath,
          width: imgWidget,
          height: imgHeight,
          boxFit: fit,
          color: themeCtr.getColor(keyName),
        );
      },
    );
  }
}

///根据主题切换图片
class ThemeImagePath extends StatelessWidget {
  final String fileName;
  final double? imgWidget;
  final double? imgHeight;
  final Color? imgColor;
  final BoxFit? fit;

  const ThemeImagePath({
    super.key,
    required this.fileName,
    this.imgWidget,
    this.imgHeight,
    this.fit,
    this.imgColor,
  });

  @override
  Widget build(BuildContext context) {
    return GetBuilder<ThemeImageController>(
      id: "theme",
      builder: (themeCtr) {
        return Images(
          path: themeCtr.getImagePath(fileName),
          width: imgWidget,
          height: imgHeight,
          boxFit: fit,
          color: imgColor,
        );
      },
    );
  }
}

///根据主题切换背景图片
class ThemeContainerImage extends StatelessWidget {
  final String fileName;
  final Widget child;
  final double? conWidget;
  final double? conHeight;
  final Color? imgColor;
  final BoxFit? fit;
  final EdgeInsetsGeometry? padding;
  final EdgeInsetsGeometry? margin;
  final AlignmentGeometry? alignment;
  final BorderRadiusGeometry? borderRadius;
  final Clip clipBehavior;

  const ThemeContainerImage(
      {super.key,
      required this.fileName,
      required this.child,
      this.conWidget,
      this.conHeight,
      this.fit,
      this.imgColor,
      this.margin,
      this.padding,
      this.alignment,
      this.borderRadius,
      this.clipBehavior = Clip.none});

  @override
  Widget build(BuildContext context) {
    return GetBuilder<ThemeImageController>(
      id: "theme",
      builder: (themeCtr) {
        return Container(
          margin: margin,
          padding: padding,
          width: conWidget,
          height: conHeight,
          alignment: alignment,
          decoration: BoxDecoration(
            borderRadius: borderRadius,
            image: DecorationImage(
                image: AssetImage(
                  themeCtr.getImagePath(fileName),
                ),
                fit: fit),
          ),
          clipBehavior: clipBehavior,
          child: child,
        );
      },
    );
  }
}
