import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:getx_xiaopa/utils/utils.dart';
import 'package:getx_xiaopa/values/values.dart';

class ThemeText extends StatelessWidget {
  final String dataStr;
  final bool flag;
  final Color subColor;
  final String keyName;
  final double fontSize;
  final FontWeight fontWeight;
  final TextAlign? textAlign;
  final double? height;
  final double? skewX;
  final double? wordSpacing;

  const ThemeText(
      {super.key,
      required this.dataStr,
      required this.keyName,
      this.flag = true,
      this.subColor = Colors.transparent,
      this.fontSize = 16,
      this.fontWeight = FontWeight.w400,
      this.textAlign,
      this.height,
      this.skewX,
      this.wordSpacing});

  @override
  Widget build(BuildContext context) {
    return GetBuilder<ThemeColorController>(
      id: "theme",
      builder: (themeCtr) {
        return Transform(
          transform: Matrix4.skewX(skewX ?? 0),
          child: Text(
            dataStr,
            textAlign: textAlign,
            style: StyleConfig.otherStyle(
                color: flag ? themeCtr.getColor(keyName) : subColor,
                fontSize: fontSize.sp,
                height: height,
                fontWeight: fontWeight,
                wordSpacing: wordSpacing),
          ),
        );
      },
    );
  }
}
