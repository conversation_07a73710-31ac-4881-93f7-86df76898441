import 'package:flutter/material.dart';

/// 动画

/// 动画-按下
class AnimatedPress extends StatefulWidget {
  final Widget child;

  /// 按下结束后缩放的比例，最大[1.0]
  final double scaleEnd;

  /// 动画时间
  final int seconds;

  /// x位移幅度 正数向右 负数向左
  final double xRange;

  /// y位移幅度 正数向下 负数向上
  final double yRange;

  /// 动画类型 0缩放 1透明度 默认0
  final int animateType;

  final Function? animatedStart;
  final Function? animatedFinish;

  ///是否自动动画
  final bool isAuto;

  const AnimatedPress({
    super.key,
    required this.child,
    this.scaleEnd = 0.8,
    this.seconds = 500,
    this.xRange = 0,
    this.yRange = 0,
    this.animateType = 0,
    this.animatedStart,
    this.animatedFinish,
    this.isAuto = true,
  });

  @override
  State<AnimatedPress> createState() => _AnimatedPressState();
}

class _AnimatedPressState extends State<AnimatedPress>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scale;
  late Animation<Offset> _move;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: Duration(milliseconds: widget.seconds),
    );

    _scale = Tween(begin: 1.0, end: widget.scaleEnd).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.decelerate,
      reverseCurve: Curves.easeIn,
    ));

    _move = Tween<Offset>(
            begin: const Offset(0, 0),
            end: Offset(widget.xRange, widget.yRange))
        .animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.decelerate,
      reverseCurve: Curves.easeIn,
    ));

    // _animationController.addListener(() {
    //   if (mounted) {
    //     setState(() {});
    //   }
    // });

    if (widget.isAuto) {
      _animationController.addStatusListener((status) {
        if (status == AnimationStatus.completed) {
          _animationController.reverse();
          widget.animatedFinish?.call();
        } else if (status == AnimationStatus.dismissed) {
          widget.animatedStart?.call();
          _animationController.forward();
        }
      });
      // controllerForward();
      _animationController.forward();
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  // /// 开始动画
  // void controllerForward() {
  //   final AnimationStatus status = _animationController.status;
  //   if (status != AnimationStatus.forward &&
  //       status != AnimationStatus.completed) {
  //     _animationController.forward();
  //   }
  // }

  // /// 结束动画
  // void controllerReverse() {
  //   _animationController.reverse();
  // }

  void triggerClickAnimation() {
    // 每次都先停止再重启动画，确保触发
    _animationController.stop();
    // _animationController.reset();
    _animationController.forward().then((_) {
      _animationController.reverse().then((_) {});
    });
  }

  @override
  Widget build(BuildContext context) {
    final animated = AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return widget.animateType == 0
            ? Transform.scale(
                scale: _scale.value,
                child: Transform.translate(
                  offset: _move.value,
                  child: child,
                ),
              )
            : FadeTransition(
                opacity: _scale,
                child: child,
              );
      },
      child: widget.child,
    );

    if (widget.isAuto) return animated;

    return Listener(
      onPointerDown: (_) => triggerClickAnimation(),
      onPointerHover: (_) => triggerClickAnimation(),
      child: animated,
    );
  }
}
