import 'package:flutter/material.dart';

///左右来回动画
class AnimatedMoveWidget extends StatefulWidget {
  final Widget child;

  /// x位移幅度 正数向右 负数向左
  final double xRange;

  /// y位移幅度 正数向下 负数向上
  final double yRange;

  /// z位移幅度 正数向上 负数向下
  final double zRange;

  /// 动画时间
  final int seconds;

  const AnimatedMoveWidget(
      {required this.child,
      super.key,
      this.xRange = 0,
      this.yRange = 0,
      this.zRange = 0,
      this.seconds = 500});

  @override
  State createState() => _AnimatedMoveWidgetState();
}

class _AnimatedMoveWidgetState extends State<AnimatedMoveWidget>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();

    _animationController = AnimationController(
        vsync: this, duration: Duration(milliseconds: widget.seconds));

    //使用弹性曲线
    _animation =
        CurvedAnimation(parent: _animationController, curve: Curves.easeOut);
    _animation = Tween(begin: 0.0, end: 1.0).animate(_animation);

    _animationController.addStatusListener((status) {
      if (status == AnimationStatus.completed) {
        _animationController.reverse();
      } else if (status == AnimationStatus.dismissed) {
        _animationController.forward();
      }
    });

    _animationController.forward();
  }

  void animationDispose() {
    _animationController.dispose();
  }

  @override
  void dispose() {
    animationDispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Transform(
      ///构建Matrix4
      transform: buildMatrix4(),

      ///中心对齐
      alignment: Alignment.center,
      child: widget.child,
    );
  }

  Matrix4 buildMatrix4() {
    double dx = 0;
    double dy = 0;
    double dz = 0;

    if (widget.xRange != 0) {
      dx = _animation.value * widget.xRange;
    }

    if (widget.yRange != 0) {
      dy = _animation.value * widget.yRange;
    }

    if (widget.zRange != 0) {
      dz = _animation.value * widget.zRange;
    }

    return Matrix4.translationValues(dx, dy, dz);
  }
}
