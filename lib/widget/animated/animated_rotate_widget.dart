import 'package:flutter/material.dart';

class AnimatedRotateWidget extends StatefulWidget {
  final int mSeconds;
  final Widget child;

  const AnimatedRotateWidget(
      {super.key, required this.child, this.mSeconds = 12000});

  @override
  State createState() => _AnimatedRotateWidgetState();
}

class _AnimatedRotateWidgetState extends State<AnimatedRotateWidget>
    with SingleTickerProviderStateMixin {
  late final Animation<double> _animation;
  late final AnimationController _animationController;

  @override
  void initState() {
    super.initState();

    _animationController = AnimationController(
      duration: Duration(milliseconds: widget.mSeconds),
      vsync: this,
    )..repeat();

    // 创建一个从0到1的补间动画   end: 1 * pi   转速
    _animation = Tween<double>(begin: 0, end: 1).animate(_animationController);
  }

  @override
  Widget build(BuildContext context) {
    return Center(
      child: AnimatedSwitcher(
        duration: const Duration(milliseconds: 500),
        transitionBuilder: (child, animation) {
          return RotationTransition(
            turns: _animation,
            child: child,
          );
        },
        child: widget.child,
      ),
    );
  }

  @override
  void dispose() {
    _animationController.dispose(); // 不要忘记在dispose中释放资源
    super.dispose();
  }
}
