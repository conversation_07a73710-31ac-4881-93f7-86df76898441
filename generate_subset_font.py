import os
import re
import subprocess
import sys

# === 配置部分 ===
SOURCE_FONT = "assets/fonts/AlibabaPuHuiTi.ttf"  # 原字体路径
OUTPUT_FONT = "assets/fonts/AlibabaPuHuiTi_subset.ttf"           # 输出字体路径
CHAR_FILE = "assets/char.txt"                              # 中间字符文件

# === Step 1: 扫描 lib/ 目录提取中文字符 ===
def extract_chinese_characters():
    chinese_pattern = re.compile(r'[\u4e00-\u9fff]')
    all_chars = set()

    for root, _, files in os.walk("lib"):
        for file in files:
            if file.endswith(".dart"):
                path = os.path.join(root, file)
                try:
                    with open(path, "r", encoding="utf-8") as f:
                        content = f.read()
                        chars = chinese_pattern.findall(content)
                        all_chars.update(chars)
                except Exception as e:
                    print(f"读取文件失败：{path}，原因：{e}")

    sorted_chars = sorted(all_chars)
    with open(CHAR_FILE, "w", encoding="utf-8") as f:
        f.write("".join(sorted_chars))
    
    print(f"✅ 提取完成，共 {len(sorted_chars)} 个中文字符，写入 {CHAR_FILE}")

# === Step 2: 执行 pyftsubset 生成子集字体 ===
def subset_font():
    if not os.path.exists(SOURCE_FONT):
        print(f"❌ 找不到原字体文件：{SOURCE_FONT}")
        sys.exit(1)

    cmd = [
        sys.executable,  # 使用当前 python 路径
        "-m", "fontTools.subset",
        SOURCE_FONT,
        f"--text-file={CHAR_FILE}",
        f"--output-file={OUTPUT_FONT}",
        "--layout-features='*'",
        # "--flavor=truetype", 
        "--with-zopfli", 
        "--no-hinting",
    ]

    try:
        subprocess.run(" ".join(cmd), shell=True, check=True)
        print(f"✅ 子集字体生成成功：{OUTPUT_FONT}")
    except subprocess.CalledProcessError:
        print("❌ 执行 pyftsubset 失败，请确认你已安装 fonttools，并配置了环境变量")

# === 主流程 ===
if __name__ == "__main__":
    print("🚀 开始生成子集字体...")
    extract_chinese_characters()
    subset_font()