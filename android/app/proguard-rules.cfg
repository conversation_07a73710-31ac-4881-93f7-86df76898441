######################################
# ByteDance Speech Engine
######################################
-keep class com.bytedance.speech.speechengine.SpeechEngineImpl {*;}
######################################
# JNA Support
######################################
-keep class com.sun.jna.* { *; }
-keepclassmembers class * extends com.sun.jna.* { public *; }

######################################
# Retrofit 2 Core (official)
######################################
-keepattributes *Annotation*, InnerClasses, EnclosingMethod

# Retrofit 注解
-keep interface retrofit2.http.* { *; }

# Retrofit 本体
-keep class retrofit2.** { *; }
-dontwarn retrofit2.**

# 保留字节跳动魔改 Retrofit（com.bytedance.retrofit2）相关
-keep class com.bytedance.retrofit2.** { *; }
-dontwarn com.bytedance.retrofit2.**

######################################
# Gson（如果你使用了 Retrofit + Gson）
######################################
-keep class com.google.gson.** { *; }
-keep class sun.misc.Unsafe { *; }
-dontwarn com.google.gson.**

######################################
# OkHttp（如果 Retrofit 内部用了 OkHttp）
######################################
-keep class okhttp3.** { *; }
-dontwarn okhttp3.**

-dontwarn javax.xml.stream.**
-keep class javax.xml.stream.** { *; }

-keep class com.tencent.mm.opensdk.** { *; }
-keep class com.tencent.wxop.** { *; }
-keep class com.tencent.mm.sdk.** { *; }
-keep class com.marspt.xiaopa.wxapi.WXPayEntryActivity { *; }

# -dontwarn com.vivo.push.**
# -keep class com.vivo.push.**{*; }
# -keep class com.vivo.vms.**{*; }