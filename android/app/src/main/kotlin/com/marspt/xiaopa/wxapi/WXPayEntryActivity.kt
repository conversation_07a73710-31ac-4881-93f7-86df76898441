package com.marspt.xiaopa.wxapi

import android.app.Activity
import android.content.Intent
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.util.Log
import android.widget.Toast
import com.marspt.xiaopa.WxUtil.Companion.get
import com.tencent.mm.opensdk.modelbase.BaseReq
import com.tencent.mm.opensdk.modelbase.BaseResp
import com.tencent.mm.opensdk.openapi.IWXAPIEventHandler

class WXPayEntryActivity : Activity(), IWXAPIEventHandler{

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
//        Toast.makeText(this, "onCreate", Toast.LENGTH_LONG).show()
        get().api.handleIntent(intent, this)
    }

    override fun onNewIntent(intent: Intent?) {
        super.onNewIntent(intent)
//        Toast.makeText(this, "onNewIntent", Toast.LENGTH_LONG).show()
        setIntent(intent)
        get().api.handleIntent(intent, this)
    }

    override fun onReq(p0: BaseReq?) {
//        Toast.makeText(this, "微信结果回调1", Toast.LENGTH_LONG).show()
        get().onReq(p0)
        finish()
    }

    override fun onResp(p0: BaseResp?) {
//        Toast.makeText(this, "微信结果回调2", Toast.LENGTH_LONG).show()
        get().onResp(p0)

        // 延时一下避免跳转过快
        Handler(Looper.getMainLooper()).postDelayed({
            val intent = Intent(this, get().context::class.java).apply {
                addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP or Intent.FLAG_ACTIVITY_SINGLE_TOP)
            }
            startActivity(intent)
            // 销毁中转页
            finish()
        }, 300)

    }
}