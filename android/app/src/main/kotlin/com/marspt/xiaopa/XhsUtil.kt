package com.marspt.xiaopa

import android.content.Context
import android.net.Uri
import android.util.Log
import com.crazecoder.openfile.FileProvider
import com.xingin.xhssharesdk.XhsShareSdkTools
import com.xingin.xhssharesdk.callback.XhsShareCallback
import com.xingin.xhssharesdk.callback.XhsShareRegisterCallback
import com.xingin.xhssharesdk.core.XhsShareSdk
import com.xingin.xhssharesdk.model.config.XhsShareGlobalConfig

import com.xingin.xhssharesdk.model.sharedata.XhsImageInfo
import com.xingin.xhssharesdk.model.sharedata.XhsImageResourceBean
import com.xingin.xhssharesdk.model.sharedata.XhsNote
import java.io.File


class XhsUtil private constructor(context: Context) {

    private val _context: MainActivity = context as MainActivity

    companion object {
        @Volatile
        private var instance: XhsUtil? = null

        fun getInstance(context: Context): XhsUtil {
            return instance ?: synchronized(this) {
                instance ?: XhsUtil(context).also { instance = it }
            }
        }

        @Synchronized
        fun get(): XhsUtil {
            return instance!!
        }
    }


    /**
     * 初始化 SDK
     */
    fun registerXhs(appKey: String?): Boolean {
        var flag = false;
        XhsShareSdk.registerApp(
            _context, appKey,
            XhsShareGlobalConfig().setEnableLog(true).setClearCacheWhenShareComplete(true)
                .setFileProviderAuthority("com.marspt.xiaopa.fileprovider")
                .setNeedRegisterReceiverWithOutsideActivity(true),
            object : XhsShareRegisterCallback {
                override fun onSuccess() {
                    Log.i("xhs", "onSuccess: 小红书SDK注册成功！")
                    flag = true
                }

                override fun onError(
                    errorCode: Int,
                    errorMessage: String,
                    exception: Exception?
                ) {
                    Log.i(
                        "xhs",
                        "onError: 小红书SDK注册失败！errorCode: $errorCode errorMessage: $errorMessage exception: $exception"
                    )
                    flag = false
                }
            })
        return flag
    }

    /*
    *  判断是否支持分享笔记到小红书
    */

    fun isSupportShareNote(): Boolean{
        val flag  = XhsShareSdkTools.isXhsInstalled(_context)
        return flag
    }

    /*
    * 分享图文笔记(图片)
    */
    fun shareNote(titleStr: String?, contentStr: String?, url: String?) {
        val uri = getContentUriFromPath(url.toString())
        var xhsNote = XhsNote().apply {
            title = titleStr
            content = contentStr
            imageInfo = XhsImageInfo(
                listOf(
                    XhsImageResourceBean(uri),
                )
            )

        }
        val sessionId = XhsShareSdk.shareNote(_context, xhsNote)
        Log.i("xhs", "shareNote: $sessionId");
        XhsShareSdk.setShareCallback(object : XhsShareCallback {
            override fun onSuccess(p0: String?) {
                Log.i("xhs", "onSuccess: 分享成功！！！")
                ///设置分享回调时，移除分享回调,注意内存泄露
                XhsShareSdk.setShareCallback(null)
            }

            override fun onError(
                p0: String,
                p1: Int,
                p2: String,
                p3: Throwable?
            ) {
                ///设置分享回调时，移除分享回调,注意内存泄露
                XhsShareSdk.setShareCallback(null)
            }

            override fun onError2(p0: String, p1: Int, p2: Int, p3: String, p4: Throwable?) {
                Log.i(
                    "xhs",
                    "onError: 分享失败!! $sessionId $p1 $p2 $p3 $p4"
                )
                ///设置分享回调时，移除分享回调,注意内存泄露
                XhsShareSdk.setShareCallback(null)
            }
        })

    }

    fun getContentUriFromPath(filePath: String): Uri {
        val file = File(filePath)
        return FileProvider.getUriForFile(_context, "com.marspt.xiaopa.fileprovider", file)
    }
}