package com.marspt.xiaopa

import android.content.Context
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.util.Log
import android.widget.Toast
import androidx.core.graphics.scale
import com.tencent.mm.opensdk.constants.Build
import com.tencent.mm.opensdk.constants.ConstantsAPI
import com.tencent.mm.opensdk.modelbase.BaseReq
import com.tencent.mm.opensdk.modelbase.BaseResp
import com.tencent.mm.opensdk.modelbiz.WXOpenCustomerServiceChat
import com.tencent.mm.opensdk.modelmsg.SendMessageToWX
import com.tencent.mm.opensdk.modelmsg.SendMessageToWX.Req.WXSceneSession
import com.tencent.mm.opensdk.modelmsg.SendMessageToWX.Req.WXSceneTimeline
import com.tencent.mm.opensdk.modelmsg.WXImageObject
import com.tencent.mm.opensdk.modelmsg.WXMediaMessage
import com.tencent.mm.opensdk.modelpay.PayReq
import com.tencent.mm.opensdk.modelpay.PayResp
import com.tencent.mm.opensdk.openapi.IWXAPI
import com.tencent.mm.opensdk.openapi.WXAPIFactory
import io.flutter.plugin.common.EventChannel.EventSink
import java.io.ByteArrayOutputStream

class WxUtil private constructor(context: Context,appKey: String ) {

     val context:MainActivity = context as MainActivity

    val api:IWXAPI = WXAPIFactory.createWXAPI(context,appKey, true)

    var eventSink: EventSink? = null

    companion object {
        @Volatile
        private var instance: WxUtil? = null

        fun getInstance(context: Context,appKey:String? ): WxUtil {
            return instance ?: synchronized(this) {
                instance ?: WxUtil(context, appKey.toString()).also { instance = it }
            }
        }

        @Synchronized
        fun get(): WxUtil {
            return instance!!
        }

    }

    fun setStreamHandle(eventSink: EventSink?) {
        this.eventSink = eventSink
    }

    /**
     * 初始化 SDK
     */
    fun registerWx(appKey: String?): Boolean {
        var flag = false;
        flag = api.registerApp(appKey)
        return flag
    }

    ///是否安装微信
    fun isInstalledWx(): Boolean{
        return api.isWXAppInstalled
    }

    val THUMB_SIZE = 150 // 微信建议缩略图150x150以内

    ///分享到会话
    fun shareWxWithSession(imagePath: String?){
        // 从本地路径加载图片
        val bmp = BitmapFactory.decodeFile(imagePath)
        if (bmp == null) {
            Log.e("WeChatShare", "图片加载失败: $imagePath")
            return
        }

        // 构造图片对象
        val imgObj = WXImageObject(bmp)
        val msg = WXMediaMessage().apply {
            mediaObject = imgObj

            // 构造缩略图（微信要求 thumbData < 32KB）
            val thumbBmp = bmp.scale(THUMB_SIZE, THUMB_SIZE)
            bmp.recycle()
            thumbData = bmpToByteArray(thumbBmp, true)
        }

        // 构造请求对象
        val req = SendMessageToWX.Req().apply {
            transaction = buildTransaction("img")
            message = msg
            scene = WXSceneSession // 例如 SendMessageToWX.Req.WXSceneTimeline
        }

        // 发送分享请求
        api.sendReq(req)
    }

    ///分享到朋友圈
    fun shareWxWithTimeLine(imagePath: String?){
        // 从本地路径加载图片
        val bmp = BitmapFactory.decodeFile(imagePath)
        if (bmp == null) {
            Log.e("WeChatShare", "图片加载失败: $imagePath")
            return
        }

        // 构造图片对象
        val imgObj = WXImageObject(bmp)
        val msg = WXMediaMessage().apply {
            mediaObject = imgObj

            // 构造缩略图（微信要求 thumbData < 32KB）
            val thumbBmp = bmp.scale(THUMB_SIZE, THUMB_SIZE)
            bmp.recycle()
            thumbData = bmpToByteArray(thumbBmp, true)
        }

        // 构造请求对象
        val req = SendMessageToWX.Req().apply {
            transaction = buildTransaction("img")
            message = msg
            scene = WXSceneTimeline // 例如 SendMessageToWX.Req.WXSceneTimeline
        }

        // 发送分享请求
        api.sendReq(req)
    }

    fun bmpToByteArray(bmp: Bitmap, needRecycle: Boolean): ByteArray {
        val output = ByteArrayOutputStream()
        bmp.compress(Bitmap.CompressFormat.PNG, 100, output)
        if (needRecycle) {
            bmp.recycle()
        }
        return output.toByteArray()
    }

    fun buildTransaction(type: String): String {
        return type + System.currentTimeMillis()
    }

    ///微信客服
    fun launchCustomerService(corpIdStr:String? ,urlStr:String? ): Boolean{
        if(api.wxAppSupportAPI >= Build.SUPPORT_OPEN_CUSTOMER_SERVICE_CHAT){
            val req =  WXOpenCustomerServiceChat.Req().apply{
                corpId = corpIdStr
                this.url = urlStr
            }
            api.sendReq(req)
            return true
        }
        return false
    }

    ///微信支付
    fun launchWxPay(appId: String?,partnerId: String?,prepayId: String?,sign: String?, packageValue:String?,nonceStr:String?,timeStamp:String?){

        var request = PayReq().apply {
            this.appId = appId
            this.partnerId = partnerId
            this.prepayId = prepayId
            this.packageValue = packageValue
            this.nonceStr = nonceStr
            this.timeStamp = timeStamp
            this.sign = sign
        }
        api.sendReq(request)
    }

    fun onReq(p0: BaseReq?) {
        // 处理微信请求（一般用于分享）
        // Log.d("WxUtil", "onReq: $p0")
    }

     fun onResp(p0: BaseResp?) {
        // 处理微信响应（用于支付、分享结果等）
        // Toast.makeText(_context, "${p0?.type} == ${p0?.errCode} == ${p0?.errStr}", Toast.LENGTH_LONG).show()

        when (p0?.type) {
            ConstantsAPI.COMMAND_PAY_BY_WX -> {
                // 支付回调
                val resp = p0 as? PayResp
                val code = resp?.errCode
                val msg = resp?.errStr
                // Log.d("WxUtil", "支付结果：$code $msg")

                val result = "{ \"code\":$code,\"data\":$msg}"
                eventSink?.success(result);
            }
            else -> {
                // Log.d("WxUtil", "其他类型回调: ${p0?.type}")
            }
        }

    }
}
