package com.marspt.xiaopa.espblufi;

import static android.content.Context.WIFI_SERVICE;

import android.bluetooth.BluetoothAdapter;
import android.bluetooth.BluetoothDevice;
import android.bluetooth.BluetoothGatt;
import android.bluetooth.BluetoothGattCallback;
import android.bluetooth.BluetoothGattCharacteristic;
import android.bluetooth.BluetoothGattDescriptor;
import android.bluetooth.BluetoothGattService;
import android.bluetooth.BluetoothProfile;
import android.bluetooth.le.BluetoothLeScanner;
import android.bluetooth.le.ScanResult;
import android.bluetooth.le.ScanSettings;
import android.location.LocationManager;
import android.net.wifi.WifiInfo;
import android.net.wifi.WifiManager;
import android.os.Build;
import android.os.SystemClock;
import android.text.TextUtils;
import android.util.Log;

import androidx.core.content.ContextCompat;
import androidx.core.location.LocationManagerCompat;

import android.content.Context;
import android.Manifest;
import android.content.pm.PackageManager;

import androidx.core.app.ActivityCompat;

import com.marspt.xiaopa.MainActivity;
import com.marspt.xiaopa.espblufi.app.BlufiApp;
import com.marspt.xiaopa.espblufi.constants.BlufiConstants;
import com.marspt.xiaopa.espblufi.constants.SettingsConstants;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;

import blufi.espressif.BlufiCallback;
import blufi.espressif.BlufiClient;
import blufi.espressif.params.BlufiConfigureParams;
import blufi.espressif.params.BlufiParameter;
import blufi.espressif.response.BlufiScanResult;
import blufi.espressif.response.BlufiStatusResponse;
import io.flutter.plugin.common.EventChannel;
import io.reactivex.rxjava3.android.schedulers.AndroidSchedulers;
import io.reactivex.rxjava3.core.Observable;
import io.reactivex.rxjava3.schedulers.Schedulers;


public class EspBlufiUtil {

    String TAG = "EspBlufiUtil";

    private static final long TIMEOUT_SCAN = 3 * 1000L;

    private MainActivity _context;
    private static EspBlufiUtil instance;

    private String mBlufiFilter;
    private volatile long mScanStartTime;

    private ScanCallback mScanCallback;
    private ExecutorService mThreadPool;
    private Future<Boolean> mUpdateFuture;

    static EventChannel.EventSink eventSink;

    private boolean _isStop = false;

    private BluetoothDevice _device;

    private Map<String, BluetoothDevice> mDeviceMap;

    private BlufiClient mBlufiClient;
    private volatile boolean mConnected;

    //===============wifi配置=================
    private WifiManager mWifiManager;
    private boolean mScanning = false;
    private List<android.net.wifi.ScanResult> mWifiList;
    private List<BlufiScanResult> mBlufiScanWifiList;

    /// 是否回调连接设备失败 主动断开设备就不需要回调消息
    boolean isSendMessage = true;
    boolean isSendWifiMessage = true;

    private static final int[] OP_MODE_VALUES = {
            BlufiParameter.OP_MODE_STA,
            BlufiParameter.OP_MODE_SOFTAP,
            BlufiParameter.OP_MODE_STASOFTAP
    };

    private String _ssidName = "";
    private String _ssidPwd = "";


    public static synchronized EspBlufiUtil getInstance(Context context) {
        if (instance == null) {
            instance = new EspBlufiUtil(context);
        }
        return instance;
    }

    public EspBlufiUtil(Context context) {
        _context = (MainActivity) context;

        mScanCallback = new ScanCallback();
        mThreadPool = Executors.newSingleThreadExecutor();

        mWifiManager = (WifiManager) _context.getApplicationContext().getSystemService(WIFI_SERVICE);

        mWifiList = new ArrayList<>();
        mBlufiScanWifiList = new ArrayList<>();
        Observable.just(this).subscribeOn(Schedulers.io()).doOnNext(EspBlufiUtil::updateWifi).subscribe();

        isSendMessage = true;
        isSendWifiMessage = true;

        mDeviceMap = new HashMap<>();

        Log.d(TAG, "EspBlufiUtil: 初始化成功");
    }

    public void setStreamHandle(EventChannel.EventSink _eventSink) {
        eventSink = _eventSink;
    }

    public void scan() {
        _isStop = false;
        mDeviceMap.clear();
        BluetoothAdapter adapter = BluetoothAdapter.getDefaultAdapter();
        BluetoothLeScanner scanner = adapter.getBluetoothLeScanner();
        if (!adapter.isEnabled() || scanner == null) {
            _context.runOnUiThread(() -> {
                if (eventSink != null) {
                    String result = "{ \"status\":\"fail\",\"tag\":\"scan\",\"data\":\"请打开蓝牙\"}";
                    eventSink.success(result);
                }
            });
            Log.d(TAG, "scan: 请打开蓝牙");
            return;
        }

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            boolean flag = isLocationEnabled();
            if (!flag) {
                _context.runOnUiThread(() -> {
                    if (eventSink != null) {
                        String result = "{ \"status\":\"fail\",\"tag\":\"scan\",\"data\":\"请打开定位\"}";
                        eventSink.success(result);
                    }
                });
                Log.d(TAG, "scan: 请打开定位");
                return;
            }
        }

        // 限制只能扫描到名字开头是 BlufiConstants.BLUFI_PREFIX 的设备
        mBlufiFilter = (String) BlufiApp.getInstance(_context).settingsGet(SettingsConstants.PREF_SETTINGS_KEY_BLE_PREFIX, BlufiConstants.BLUFI_PREFIX);
        mScanStartTime = SystemClock.elapsedRealtime();
//        Log.d(TAG, "scan: 安卓版本：" + Build.VERSION.SDK_INT + " == " + Build.VERSION_CODES.S);

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
//            Log.d(TAG, "scan: 开始扫描硬件12+：" + ContextCompat.checkSelfPermission(_context, Manifest.permission.BLUETOOTH_SCAN));

            // Android 12+ 要动态申请 BLUETOOTH_SCAN 权限
            if (ContextCompat.checkSelfPermission(_context, Manifest.permission.BLUETOOTH_SCAN) == PackageManager.PERMISSION_GRANTED) {
                scanner.startScan(null, new ScanSettings.Builder().setScanMode(ScanSettings.SCAN_MODE_LOW_LATENCY).build(), mScanCallback);
            }
        } else {
//            Log.d(TAG, "scan: 开始扫描硬件1：" + ContextCompat.checkSelfPermission(_context, Manifest.permission.ACCESS_FINE_LOCATION));
//            Log.d(TAG, "scan: 开始扫描硬件2：" + ContextCompat.checkSelfPermission(_context, Manifest.permission.BLUETOOTH));

            // Android 11- 要动态申请 ACCESS_FINE_LOCATION
            if (ContextCompat.checkSelfPermission(_context, Manifest.permission.ACCESS_FINE_LOCATION) == PackageManager.PERMISSION_GRANTED) {
                scanner.startScan(null, new ScanSettings.Builder().setScanMode(ScanSettings.SCAN_MODE_LOW_LATENCY).build(), mScanCallback);
            }
        }


        mUpdateFuture = mThreadPool.submit(() -> {
            while (!Thread.currentThread().isInterrupted()) {
                try {
                    Thread.sleep(1000);
                } catch (InterruptedException e) {
                    Log.e(TAG, "scan: " + e);
                    break;
                }

                long scanCost = SystemClock.elapsedRealtime() - mScanStartTime;
                if (scanCost > TIMEOUT_SCAN) {
                    break;
                }

            }

            if (!_isStop) {
                BluetoothLeScanner inScanner = BluetoothAdapter.getDefaultAdapter().getBluetoothLeScanner();
                if (inScanner != null) {
                    _context.runOnUiThread(() -> {
                        if (eventSink != null) {
                            String result = "{ \"status\":\"stop\",\"tag\":\"scan\",\"data\":\"停止扫描\"}";
                            eventSink.success(result);
                        }
                    });
                    inScanner.stopScan(mScanCallback);
                }

                Log.d(TAG, "scan: 扫描被中断：" + inScanner);
            }

            return true;
        });
    }

    public void stopScan() {
        BluetoothLeScanner scanner = BluetoothAdapter.getDefaultAdapter().getBluetoothLeScanner();
        if (scanner != null) {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
                // Android 12+ 要动态申请 BLUETOOTH_SCAN 权限
                if (ContextCompat.checkSelfPermission(_context, Manifest.permission.BLUETOOTH_SCAN) == PackageManager.PERMISSION_GRANTED) {
                    scanner.stopScan(mScanCallback);
                }
            } else {
                // Android 11- 要动态申请 ACCESS_FINE_LOCATION
                if (ContextCompat.checkSelfPermission(_context, Manifest.permission.ACCESS_FINE_LOCATION) == PackageManager.PERMISSION_GRANTED) {
                    scanner.stopScan(mScanCallback);
                }
            }

        }
        if (mUpdateFuture != null) {
            mUpdateFuture.cancel(true);
        }

        Log.d(TAG, "stopScan: 停止扫描");
    }

    public boolean isLocationEnabled() {
        LocationManager locationManager = (LocationManager) _context.getSystemService(Context.LOCATION_SERVICE);
        if (locationManager == null) {
            return false;
        }

        // 检查权限（可选，避免安全警告）
        if (ActivityCompat.checkSelfPermission(_context, Manifest.permission.ACCESS_FINE_LOCATION) != PackageManager.PERMISSION_GRANTED && ActivityCompat.checkSelfPermission(_context, Manifest.permission.ACCESS_COARSE_LOCATION) != PackageManager.PERMISSION_GRANTED) {
            return false;
        }

        return LocationManagerCompat.isLocationEnabled(locationManager);
    }

    private class ScanCallback extends android.bluetooth.le.ScanCallback {

        @Override
        public void onScanFailed(int errorCode) {
            super.onScanFailed(errorCode);
            _context.runOnUiThread(() -> {
                if (eventSink != null) {
                    String result = "{ \"status\":\"fail\",\"tag\":\"scan\",\"data\":\"设备扫描失败\"}";
                    eventSink.success(result);
                }
            });
        }

        @Override
        public void onBatchScanResults(List<ScanResult> results) {
            for (ScanResult result : results) {
                onLeScan(result);
            }
        }

        @Override
        public void onScanResult(int callbackType, ScanResult result) {
            onLeScan(result);
        }

        private void onLeScan(ScanResult scanResult) {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
                if (ActivityCompat.checkSelfPermission(_context, Manifest.permission.BLUETOOTH_CONNECT) != PackageManager.PERMISSION_GRANTED) {
                    if (eventSink != null) {
                        String result = "{ \"status\":\"fail\",\"tag\":\"scan\",\"data\":\"请允许BLUETOOTH_CONNECT权限\"}";
                        eventSink.success(result);
                    }
                    return;
                }
            }
            String name = scanResult.getDevice().getName();

            if (!TextUtils.isEmpty(mBlufiFilter)) {
                if (name == null || !name.startsWith(mBlufiFilter)) {
                    return;
                }
            }

            // 停止后直接拦截，防止多次回调(之前做只要检测到设备就停着，现在要扫码全部设备)
            //if (_isStop) return;
            //_isStop = true;
            //stopScan();

            // 保存扫描到的设备信息
            //_device = scanResult.getDevice();

            mDeviceMap.put(scanResult.getDevice().getName(), scanResult.getDevice());

            _context.runOnUiThread(() -> {
                if (eventSink != null) {
                    String result = "{ \"status\":\"success\",\"tag\":\"scan\",\"data\":\"设备扫描成功=" + scanResult.getDevice().getName() + "\"}";
                    eventSink.success(result);
                }
            });

        }
    }


    /*======================连接逻辑=============================*/
    public void connectDevice(String deviceName) {
        if (mBlufiClient != null) {
            mBlufiClient.close();
            mBlufiClient = null;
        }
        Log.d(TAG, deviceName + " === " + mDeviceMap);
        _device = mDeviceMap.get(deviceName);
        if (_device == null) {
            _context.runOnUiThread(() -> {
                if (eventSink != null) {
                    String result = "{ \"status\":\"fail\",\"tag\":\"connect\",\"data\":\"获取设备信息失败\"}";
                    eventSink.success(result);
                }
            });
            return;
        }

        mBlufiClient = new BlufiClient(_context, _device);
        mBlufiClient.setGattCallback(new GattCallback());
        mBlufiClient.setBlufiCallback(new BlufiCallbackMain());
        mBlufiClient.setGattWriteTimeout(BlufiConstants.GATT_WRITE_TIMEOUT);
        mBlufiClient.connect();
    }

    private class GattCallback extends BluetoothGattCallback {

        @Override
        public void onConnectionStateChange(BluetoothGatt gatt, int status, int newState) {
            String devAddress = gatt.getDevice().getAddress();
            Log.d(TAG, String.format(Locale.ENGLISH, "onConnectionStateChange addr=%s, status=%d, newState=%d", devAddress, status, newState));
            if (status == BluetoothGatt.GATT_SUCCESS) {
                switch (newState) {
                    case BluetoothProfile.STATE_CONNECTED:
                        onGattConnected();
                        break;
                    case BluetoothProfile.STATE_DISCONNECTED:
                        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
                            // Android 12+ 要动态申请 BLUETOOTH_SCAN 权限
                            if (ContextCompat.checkSelfPermission(_context, Manifest.permission.BLUETOOTH_SCAN) == PackageManager.PERMISSION_GRANTED) {
                                gatt.close();
                            }
                        } else {
                            // Android 11- 要动态申请 ACCESS_FINE_LOCATION
                            if (ContextCompat.checkSelfPermission(_context, Manifest.permission.ACCESS_FINE_LOCATION) == PackageManager.PERMISSION_GRANTED) {
                                gatt.close();
                            }
                        }
                        onGattDisconnected();
                        break;
                }
            } else {
                gatt.close();
                onGattDisconnected();
            }
        }

        @Override
        public void onMtuChanged(BluetoothGatt gatt, int mtu, int status) {
            Log.d(TAG, String.format(Locale.ENGLISH, "onMtuChanged status=%d, mtu=%d", status, mtu));
            if (status != BluetoothGatt.GATT_SUCCESS) {
                mBlufiClient.setPostPackageLengthLimit(20);
            }

            onGattServiceCharacteristicDiscovered();
        }


        @Override
        public void onServicesDiscovered(BluetoothGatt gatt, int status) {
            if (status != BluetoothGatt.GATT_SUCCESS) {
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
                    // Android 12+ 要动态申请 BLUETOOTH_SCAN 权限
                    if (ContextCompat.checkSelfPermission(_context, Manifest.permission.BLUETOOTH_SCAN) == PackageManager.PERMISSION_GRANTED) {
                        gatt.disconnect();
                    }
                } else {
                    // Android 11- 要动态申请 ACCESS_FINE_LOCATION
                    if (ContextCompat.checkSelfPermission(_context, Manifest.permission.ACCESS_FINE_LOCATION) == PackageManager.PERMISSION_GRANTED) {
                        gatt.disconnect();
                    }
                }
            }
        }

        @Override
        public void onDescriptorWrite(BluetoothGatt gatt, BluetoothGattDescriptor descriptor, int status) {
            if (descriptor.getUuid().equals(BlufiParameter.UUID_NOTIFICATION_DESCRIPTOR) && descriptor.getCharacteristic().getUuid().equals(BlufiParameter.UUID_NOTIFICATION_CHARACTERISTIC)) {
                String msg = String.format(Locale.ENGLISH, "Set notification enable %s", (status == BluetoothGatt.GATT_SUCCESS ? " complete" : " failed"));
            }
        }

        @Override
        public void onCharacteristicWrite(BluetoothGatt gatt, BluetoothGattCharacteristic characteristic, int status) {
            if (status != BluetoothGatt.GATT_SUCCESS) {
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
                    // Android 12+ 要动态申请 BLUETOOTH_SCAN 权限
                    if (ContextCompat.checkSelfPermission(_context, Manifest.permission.BLUETOOTH_SCAN) == PackageManager.PERMISSION_GRANTED) {
                        gatt.disconnect();
                    }
                } else {
                    // Android 11- 要动态申请 ACCESS_FINE_LOCATION
                    if (ContextCompat.checkSelfPermission(_context, Manifest.permission.ACCESS_FINE_LOCATION) == PackageManager.PERMISSION_GRANTED) {
                        gatt.disconnect();
                    }
                }
            }
        }
    }

    private void onGattConnected() {
        mConnected = true;
        isSendMessage = false;
        _context.runOnUiThread(() -> {
            if (eventSink != null) {
                String result = "{ \"status\":\"success\",\"tag\":\"connect\",\"data\":\"连接设备成功\"}";
                eventSink.success(result);
            }
        });
        Log.d(TAG, "onGattConnected: ");
    }

    private void onGattDisconnected() {
        mConnected = false;
        if (!isSendMessage) return;
        _context.runOnUiThread(() -> {
            if (eventSink != null) {
                String result = "{ \"status\":\"fail\",\"tag\":\"connect\",\"data\":\"连接设备失败\"}";
                eventSink.success(result);
            }
        });
        Log.d(TAG, "onGattDisconnected: ");
    }

    private void onGattServiceCharacteristicDiscovered() {

    }

    public void checkStatus() {
        if (mBlufiClient != null && mConnected) {
            mBlufiClient.requestDeviceStatus();
        }
    }

    public void closeConnect() {
        if (mBlufiClient != null) {
            isSendMessage = false;
            mBlufiClient.requestCloseConnection();
        }
    }

    private class BlufiCallbackMain extends BlufiCallback {
        @Override
        public void onGattPrepared(BlufiClient client, BluetoothGatt gatt, BluetoothGattService service, BluetoothGattCharacteristic writeChar, BluetoothGattCharacteristic notifyChar) {
            if (service == null) {
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
                    // Android 12+ 要动态申请 BLUETOOTH_SCAN 权限
                    if (ContextCompat.checkSelfPermission(_context, Manifest.permission.BLUETOOTH_SCAN) == PackageManager.PERMISSION_GRANTED) {
                        gatt.disconnect();
                    }
                } else {
                    // Android 11- 要动态申请 ACCESS_FINE_LOCATION
                    if (ContextCompat.checkSelfPermission(_context, Manifest.permission.ACCESS_FINE_LOCATION) == PackageManager.PERMISSION_GRANTED) {
                        gatt.disconnect();
                    }
                }

                return;
            }
            if (writeChar == null) {
                gatt.disconnect();
                return;
            }
            if (notifyChar == null) {
                gatt.disconnect();
                return;
            }

            int mtu = BlufiConstants.DEFAULT_MTU_LENGTH;
            boolean requestMtu = gatt.requestMtu(mtu);
            if (!requestMtu) {
                onGattServiceCharacteristicDiscovered();
            }
        }

        @Override
        public void onPostConfigureParams(BlufiClient client, int status) {
            if (status == STATUS_SUCCESS) {
//                mBlufiClient.requestDeviceStatus();
                _context.runOnUiThread(() -> {
                    if (eventSink != null) {
                        String result = "{ \"status\":\"success\",\"tag\":\"configureWifi\",\"data\":\"wifi配置成功\"}";
                        eventSink.success(result);
                    }
                });
            } else {
                _context.runOnUiThread(() -> {
                    if (eventSink != null) {
                        String result = "{ \"status\":\"fail\",\"tag\":\"configureWifi\",\"data\":\"wifi配置失败\"}";
                        eventSink.success(result);
                    }
                });
            }

            Log.d(TAG, "onPostConfigureParams: " + status);
        }


        @Override
        public void onDeviceStatusResponse(BlufiClient client, int status, BlufiStatusResponse response) {
            /*
            按理说3的时候是连接不成功的，但是设备也提示成功了，所以只检测3即可
            response.getStaConnectionStatus() == 0 才是真正的配置成功，但是每次都是先返回3才返回0
            */
            if (!isSendWifiMessage) return;
            if (response.getStaConnectionStatus() == 0 || response.getStaConnectionStatus() == 3) {
                _context.runOnUiThread(() -> {
                    isSendWifiMessage = false;
                    if (eventSink != null) {
                        String result = "{ \"status\":\"success\",\"tag\":\"connectWifi\",\"data\":\"Wi-Fi连接成功\"}";
                        eventSink.success(result);
                    }
                });
            } else if (response.getStaConnectionStatus() == 1) {
                _context.runOnUiThread(() -> {
                    isSendWifiMessage = false;
                    if (eventSink != null) {
                        String result = "{ \"status\":\"fail\",\"tag\":\"connectWifi\",\"data\":\"Wi-Fi连接失败\"}";
                        eventSink.success(result);
                    }
                });
            } else if (response.getStaConnectionStatus() == 2) {
                _context.runOnUiThread(() -> {
                    if (eventSink != null) {
                        String result = "{ \"status\":\"retry\",\"tag\":\"connectWifi\",\"data\":\"Wi-Fi正在重连\"}";
                        eventSink.success(result);
                    }
                });
            }
            Log.d(TAG, "onDeviceStatusResponse: " + response.getStaConnectionStatus() + " === " + response.generateValidInfo() + "status：" + status);
        }

        @Override
        public void onDeviceScanResult(BlufiClient client, int status, List<BlufiScanResult> results) {
            Log.d(TAG, "wifi扫描结果: " + status);
            //乐鑫芯片扫描wifi列表结果返回
            if (status == STATUS_SUCCESS) {
                for (BlufiScanResult scanResult : results) {
                    Log.d(TAG, "onDeviceScanResult: " + scanResult.getSsid() + "==" + scanResult.getRssi());
                }
                mBlufiScanWifiList.clear();
                mBlufiScanWifiList.addAll(results);

                List<String> _temp = new ArrayList<>();
                for (int i = 0; i < results.size(); i++) {
                    BlufiScanResult sr = results.get(i);
                    // 过滤掉5G的wifi
                    //if (_is5GHz(sr.getRssi())) continue;
                    _temp.add("\"" + sr.getSsid() + "\"");
                }
                //Log.d(TAG, "updateWifi: " + scans);
                if (!_temp.isEmpty()) {
                    _context.runOnUiThread(() -> {
                        if (eventSink != null) {
                            String result = "{ \"status\":\"success\",\"tag\":\"wifi\",\"data\":" + _temp + "}";
                            eventSink.success(result);
                        }
                    });
                } else {
                    _context.runOnUiThread(() -> {
                        if (eventSink != null) {
                            String result = "{ \"status\":\"fail\",\"tag\":\"wifi\",\"data\":\"wifi扫描失败\"}";
                            eventSink.success(result);
                        }
                    });
                }

            } else {
                _context.runOnUiThread(() -> {
                    if (eventSink != null) {
                        String result = "{ \"status\":\"fail\",\"tag\":\"wifi\",\"data\":\"wifi扫描失败\"}";
                        eventSink.success(result);
                    }
                });
            }
        }

        @Override
        public void onError(BlufiClient client, int errCode) {
            Log.d(TAG, "BlufiCallback onError: " + errCode);
            if (errCode == CODE_GATT_WRITE_TIMEOUT) {
//                _context.runOnUiThread(() -> {
//                    if (eventSink != null) {
//                        String result = "{ \"status\":\"fail\",\"tag\":\"timeout\",\"data\":\"处理超时\"}";
//                        eventSink.success(result);
//                    }
//                });
                client.close();
                onGattDisconnected();
                mBlufiClient = null;
            }
        }
    }

    /*=====================wifi配置=======================*/
    public void scanWifi() {
//        if (!mWifiManager.isWifiEnabled()) {
//            _context.runOnUiThread(() -> {
//                if (eventSink != null) {
//                    String result = "{ \"status\":\"fail\",\"tag\":\"wifi\",\"data\":\"请打开Wi-Fi\"}";
//                    eventSink.success(result);
//                }
//            });
//            return;
//        }

//        mScanning = true;
//
//        Observable.just(mWifiManager).subscribeOn(Schedulers.io()).doOnNext(wm -> {
//            wm.startScan();
//            try {
//                Thread.sleep(1500L);
//            } catch (InterruptedException e) {
//                e.printStackTrace();
//            }
//
//            updateWifi();
//        }).observeOn(AndroidSchedulers.mainThread()).doOnComplete(() -> {
//            mScanning = false;
//        }).subscribe();
        //使用乐鑫芯片扫描wifi列表
        Log.d(TAG, "scanWifi: 扫描wifi");
        mBlufiClient.requestDeviceWifiScan();
    }

    private void updateWifi() {
        final List<android.net.wifi.ScanResult> scans = new LinkedList<>();

        if (ActivityCompat.checkSelfPermission(_context, Manifest.permission.ACCESS_FINE_LOCATION) != PackageManager.PERMISSION_GRANTED) {
            _context.runOnUiThread(() -> {
                if (eventSink != null) {
                    String result = "{ \"status\":\"fail\",\"tag\":\"wifi\",\"data\":\"请允许位置权限\"}";
                    eventSink.success(result);
                }
            });
            return;
        }
        Observable.fromIterable(mWifiManager.getScanResults()).filter(scanResult -> {
//            if (TextUtils.isEmpty(scanResult.SSID)) {
//                _context.runOnUiThread(() -> {
//                    if (eventSink != null) {
//                        List<String> _temp = new ArrayList<>();
//                        String result = "{ \"status\":\"success\",\"tag\":\"wifi\",\"data\":" + _temp + "}";
//                        eventSink.success(result);
//                    }
//                });
//
//                return false;
//            }

            boolean contain = false;
            for (android.net.wifi.ScanResult srScan : scans) {
                if (srScan.SSID.equals(scanResult.SSID)) {
                    contain = true;
                    break;
                }
            }
            return !contain;
        }).doOnNext(scans::add).observeOn(AndroidSchedulers.mainThread()).doOnComplete(() -> {
            mWifiList.clear();
            mWifiList.addAll(scans);

            List<String> _temp = new ArrayList<>();
            for (int i = 0; i < scans.size(); i++) {
                android.net.wifi.ScanResult sr = scans.get(i);
                // 过滤掉5G的wifi
                if (_is5GHz(sr.frequency)) continue;
                _temp.add("\"" + sr.SSID + "\"");
            }
            //Log.d(TAG, "updateWifi: " + scans);
            _context.runOnUiThread(() -> {
                if (eventSink != null) {
                    String result = "{ \"status\":\"success\",\"tag\":\"wifi\",\"data\":" + _temp + "}";
                    eventSink.success(result);
                }
            });

        }).subscribe();
    }

    private boolean _is5GHz(int freq) {
        return freq > 4900 && freq < 5900;
    }

    /// 给设备配置wifi
    public void configureWifi(String ssidName, String ssidPwd) {
        if(mBlufiClient == null) return;
        _ssidName = ssidName;
        _ssidPwd = ssidPwd;

        final BlufiConfigureParams params = checkInfo();
        if (params == null) {
            Log.d(TAG, "configureWifi: 配置参数不能为空");
            return;
        }
        isSendWifiMessage = true;
        Log.d(TAG, "configureWifi: " + params + "==" + _ssidName + "==" + _ssidPwd);
        mBlufiClient.configure(params);
    }

    private BlufiConfigureParams checkInfo() {
        BlufiConfigureParams params = new BlufiConfigureParams();
        int deviceMode = OP_MODE_VALUES[0];
        Log.d(TAG, "checkInfo: " + deviceMode);
        params.setOpMode(deviceMode);
        if (checkSta(params)) {
            return params;
        } else {
            return null;
        }
    }

    private boolean checkSta(BlufiConfigureParams params) {
        String ssid = _ssidName;
        if (TextUtils.isEmpty(ssid)) {
            Log.d(TAG, "checkSta: wifi名字不能为空");
            return false;
        }
        byte[] ssidBytes = _ssidName.getBytes();
        params.setStaSSIDBytes(ssidBytes != null ? ssidBytes : ssid.getBytes());
        String password = _ssidPwd;
        params.setStaPassword(password);

        int freq = -1;
        if (ssid.equals(getConnectionSSID())) {
            freq = getConnectionFrequncy();
        }
        if (freq == -1) {
            for (android.net.wifi.ScanResult sr : mWifiList) {
                if (ssid.equals(sr.SSID)) {
                    freq = sr.frequency;
                    break;
                }
            }
        }
        /// 如果wifi是4G和5G合并的，会出错
//        if (_is5GHz(freq)) {
//            Log.d(TAG, "checkSta: 设备不支持5G Wi-Fi");
//            return false;
//        }

        return true;
    }

    private String getConnectionSSID() {
        if (!mWifiManager.isWifiEnabled()) {
            return null;
        }

        WifiInfo wifiInfo = mWifiManager.getConnectionInfo();
        if (wifiInfo == null) {
            return null;
        }

        String ssid = wifiInfo.getSSID();
        if (ssid.startsWith("\"") && ssid.endsWith("\"") && ssid.length() >= 2) {
            ssid = ssid.substring(1, ssid.length() - 1);
        }

        return ssid;
    }

    private int getConnectionFrequncy() {
        if (!mWifiManager.isWifiEnabled()) {
            return -1;
        }

        WifiInfo wifiInfo = mWifiManager.getConnectionInfo();
        if (wifiInfo == null) {
            return -1;
        }

        return wifiInfo.getFrequency();
    }

}
