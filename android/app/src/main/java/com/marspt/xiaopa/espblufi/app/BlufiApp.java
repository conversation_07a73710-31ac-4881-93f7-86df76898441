package com.marspt.xiaopa.espblufi.app;

import android.content.Context;
import android.content.SharedPreferences;

import com.marspt.xiaopa.espblufi.constants.SettingsConstants;

import java.util.HashSet;
import java.util.Set;

public class BlufiApp {
    private static BlufiApp instance;

    private final SharedPreferences mSettingsShared;

    public static BlufiApp getInstance(Context context) {
        if (instance == null) {
            instance = new BlufiApp(context);
        }
        return instance;
    }

    public BlufiApp(Context context) {
        instance = this;
        mSettingsShared = context.getSharedPreferences(SettingsConstants.PREF_SETTINGS_NAME, Context.MODE_PRIVATE);
    }

    public void settingsPut(String key, Object value) {
        SharedPreferences.Editor editor = mSettingsShared.edit();
        if (value instanceof String) {
            editor.putString(key, (String) value);
        } else if (value instanceof Boolean) {
            editor.putBoolean(key, (Boolean) value);
        } else if (value instanceof Float) {
            editor.putFloat(key, (Float) value);
        } else if (value instanceof Integer) {
            editor.putInt(key, (Integer) value);
        } else if (value instanceof Long) {
            editor.putLong(key, (Long) value);
        } else if (value instanceof Set) {
            Set set = (Set) value;
            Set<String> newSet = new HashSet<>();
            for (Object object : set) {
                newSet.add((String) object);
            }
            editor.putStringSet(key, newSet);
        } else {
            throw new IllegalArgumentException("Unsupported value type");
        }

        editor.apply();
    }

    public Object settingsGet(String key, Object defaultValue) {
        if (defaultValue instanceof String) {
            return mSettingsShared.getString(key, (String) defaultValue);
        } else if (defaultValue instanceof Boolean) {
            return mSettingsShared.getBoolean(key, (Boolean) defaultValue);
        } else if (defaultValue instanceof Float) {
            return mSettingsShared.getFloat(key, (Float) defaultValue);
        } else if (defaultValue instanceof Integer) {
            return mSettingsShared.getInt(key, (Integer) defaultValue);
        } else if (defaultValue instanceof Long) {
            return mSettingsShared.getLong(key, (Long) defaultValue);
        } else if (defaultValue instanceof Set) {
            //noinspection unchecked
            return mSettingsShared.getStringSet(key, (Set<String>) defaultValue);
        } else {
            return null;
        }
    }
}
