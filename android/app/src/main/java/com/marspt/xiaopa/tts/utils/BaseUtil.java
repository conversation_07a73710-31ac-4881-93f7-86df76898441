package com.marspt.xiaopa.tts.utils;

import android.content.Context;
import android.os.Environment;
import android.util.Log;

import java.io.File;

public class BaseUtil {
    private String mDebugPath;
    public  String getDebugPath(Context context) {
        if (mDebugPath != null) {
            return mDebugPath;
        }
        String state = Environment.getExternalStorageState();
        if (Environment.MEDIA_MOUNTED.equals(state)) {
            Log.d("SpeechDemo", "External storage can be read and write.");
        } else {
            Log.e("SpeechDemo", "External storage can't write.");
            return "";
        }
        File debugDir = context.getExternalFilesDir(null);
        if (debugDir == null) {
            return "";
        }
        if (!debugDir.exists()) {
            if (debugDir.mkdirs()) {
                Log.d("SpeechDemo", "Create debug path successfully.");
            } else {
                Log.e("SpeechDemo", "Failed to create debug path.");
                return "";
            }
        }
        mDebugPath = debugDir.getAbsolutePath();
        return mDebugPath;
    }
}
