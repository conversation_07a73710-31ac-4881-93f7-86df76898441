package com.marspt.xiaopa.tts;

import android.app.Application;
import android.content.Context;
import android.media.AudioManager;
import android.os.Handler;
import android.util.Log;


import com.marspt.xiaopa.MainActivity;
import com.bytedance.speech.speechengine.SpeechEngine;
import com.bytedance.speech.speechengine.SpeechEngineDefines;
import com.bytedance.speech.speechengine.SpeechEngineGenerator;
import com.bytedance.speech.speechengine.SpeechResourceManager;
import com.bytedance.speech.speechengine.SpeechResourceManagerGenerator;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import io.flutter.plugin.common.EventChannel;

public class ByteTtsUtil implements SpeechEngine.SpeechListener {

    private final int[] mTtsWorkModeArray = {
            SpeechEngineDefines.TTS_WORK_MODE_ONLINE,
            SpeechEngineDefines.TTS_WORK_MODE_OFFLINE,
            SpeechEngineDefines.TTS_WORK_MODE_ALTERNATE,
            SpeechEngineDefines.TTS_WORK_MODE_FILE,
    };

    // Engine
    private SpeechEngine mSpeechEngine = null;

    // Engine State
    private boolean mEngineInit = false;
    private boolean mEngineStarted = false;
    private boolean mEngineErrorOccurred = false;
    private boolean mPlayerPaused = false;

    // Paths
    private static String mDebugPath = "";

    // Offline Resource Manager
    private SpeechResourceManager mResourceManager = null;

    // Options Default Value
    private static String mCurAppId = "";
    private static String mCurToken = "";
    private static String mCurCluster = "";
    private static final String mCurAddress = "wss://openspeech.bytedance.com";
    private static final String mCurUri = "/api/v1/tts/ws_binary";
    private static final Double mCurSpeakSpeed = 1.0;


    private String mCurVoiceOnline = "";
    private String mCurVoiceTypeOnline = "";
    private int mCurTtsWorkMode = SpeechEngineDefines.TTS_WORK_MODE_ONLINE;


    // Novel Scenario Related
    private boolean stopSynthesis = false;
    private boolean mTtsSynthesisFromPlayer = false;
    private double mTtsPlayingProgress = 0.0;
    private Integer mTtsPlayingIndex = -1;
    private Integer mTtsSynthesisIndex = 0;
    private List<String> mTtsSynthesisText;
    private Map<String, Integer> mTtsSynthesisMap;

    // Android Audio Manager
    private AudioManager.OnAudioFocusChangeListener mAFChangeListener = null;
    private AudioManager mAudioManager = null;
    private boolean mResumeOnFocusGain = true;
    private boolean mPlaybackNowAuthorized = false;

    private static final int TTS_MAX_RETRY_COUNT = 3;
    private int mRetryCount = TTS_MAX_RETRY_COUNT;
    private Handler mHandler = null;

    // State shared between Init Engine and Start Engine
    private final Boolean mDisablePlayerReuse = false;

    private final MainActivity _context;

    private static ByteTtsUtil instance;

    private EventChannel.EventSink eventSink;

    public static synchronized ByteTtsUtil getInstance(Context context, String appId, String token, String cluster,String ttsPath) {
        mCurAppId = appId;
        mCurToken = token;
        mCurCluster = cluster;
        mDebugPath = ttsPath;
        if (instance == null) {
            instance = new ByteTtsUtil(context);
        }
        return instance;
    }

    public ByteTtsUtil(Context context) {
        _context = (MainActivity) context;
        SpeechEngineGenerator.PrepareEnvironment(context, (Application) context.getApplicationContext());
        mHandler = new Handler();
//        mDebugPath = new BaseUtil().getDebugPath(context);

        mAFChangeListener = focusChange -> {
            switch (focusChange) {
                case AudioManager.AUDIOFOCUS_GAIN:
                    Log.d("SpeechTtsDemo", "onAudioFocusChange: AUDIOFOCUS_GAIN, " + mResumeOnFocusGain);
                    if (mResumeOnFocusGain) {
                        mResumeOnFocusGain = false;
                        resumePlayback();
                    }
                    break;
                case AudioManager.AUDIOFOCUS_LOSS:
                    Log.d("SpeechTtsDemo", "onAudioFocusChange: AUDIOFOCUS_LOSS");
                    mResumeOnFocusGain = false;
                    pausePlayback();
                    mPlaybackNowAuthorized = false;
                    break;
                case AudioManager.AUDIOFOCUS_LOSS_TRANSIENT:
                    Log.d("SpeechTtsDemo", "onAudioFocusChange: AUDIOFOCUS_LOSS_TRANSIENT");
                    mResumeOnFocusGain = mEngineStarted;
                    pausePlayback();
                    break;
            }
        };

        mAudioManager = (AudioManager) context.getSystemService(Context.AUDIO_SERVICE);
    }

    public void setStreamHandle(EventChannel.EventSink _eventSink, String yinse) {
        mCurVoiceTypeOnline = yinse;
        eventSink = _eventSink;
    }

    public void stopByteSpeechStream() {
        stopEngine();
        resetTtsContext();
        _context.runOnUiThread(() -> eventSink.success("finishPlay"));
        stopSynthesis = false;
    }

    public void initEngine() {
        mCurTtsWorkMode = mTtsWorkModeArray[0];
        Log.i("SpeechTtsDemo", "调用初始化接口前的语音合成工作模式为 " + mCurTtsWorkMode);
        if (mCurTtsWorkMode == SpeechEngineDefines.TTS_WORK_MODE_ONLINE || mCurTtsWorkMode == SpeechEngineDefines.TTS_WORK_MODE_FILE) {
            // 当使用纯在线模式时，不需要下载离线合成所需资源
            initEngineInternal();
        } else {
            try {
                if (mResourceManager == null) {
                    mResourceManager = SpeechResourceManagerGenerator.getInstance();
                }
            } catch (RuntimeException e) {
                speechEngineInitFailed(e.getMessage());
                return;
            }
//            // 下载离线合成所需资源需要区分多音色资源和单音色资源，下载这两种资源所调用的方法略有不同
//            if (mSettings.getOptionsValue(R.string.tts_offline_resource_format_title, _context).equals("MultipleVoice")) {
//                // 多音色资源是指一个资源文件中包含了多个离线音色，这种资源一般是旧版(V2)离线合成所用资源
//                Log.i("SpeechTtsDemo", "当前所用资源类别为多音色资源，开始准备多音色资源");
//                prepareMultipleVoiceResource();
//            } else if (mSettings.getOptionsValue(R.string.tts_offline_resource_format_title, _context).equals("SingleVoice")) {
//                // 单音色资源是指一个资源文件仅包含一个离线音色，新版(V4 及以上)离线合成用的就是单音色资源
//                Log.i("SpeechTtsDemo", "当前所用资源类别为单音色资源，开始准备单音色资源");
//                prepareSingleVoiceResource();
//            }
            // 单音色资源是指一个资源文件仅包含一个离线音色，新版(V4 及以上)离线合成用的就是单音色资源
//            Log.i("SpeechTtsDemo", "当前所用资源类别为单音色资源，开始准备单音色资源");
//            prepareSingleVoiceResource();
        }
    }

//    private void prepareMultipleVoiceResource() {
//        Log.i("SpeechTtsDemo", "初始化模型资源管理器");
//        mResourceManager.initResourceManager(_context.getApplicationContext(), "0", mCurAppId, SensitiveDefines.APP_VERSION, true, mDebugPath);
//        // 因为多音色资源的一个文件包含了多个音色，导致资源的名字和音色的名字无法一一对应
//        // 所以下载资源需要显式指定资源名字
//        String resourceName = "";
//        Log.i("SpeechTtsDemo", "检查本地是否存在可用资源");
//        if (!mResourceManager.checkResourceDownload(resourceName)) {
//            Log.i("SpeechTtsDemo", "本地没有资源，开始下载");
//            fetchMultipleVoiceResource(resourceName);
//        } else {
//            Log.i("SpeechTtsDemo", "资源存在，检查资源是否需要升级");
//            mResourceManager.checkResourceUpdate(resourceName, new SpeechResourceManager.CheckResouceUpdateListener() {
//                @Override
//                public void onCheckResult(boolean needUpdate) {
//                    if (needUpdate) {
//                        Log.i("SpeechTtsDemo", "存在可用升级，开始升级");
//                        fetchMultipleVoiceResource(resourceName);
//                    } else {
//                        Log.i("SpeechTtsDemo", "不存在可用升级，使用本地已有模型");
//                        initEngineInternal();
//                    }
//                }
//            });
//        }
//    }

//    private void fetchMultipleVoiceResource(final String resourceName) {
//        Log.i("SpeechTtsDemo", "需要下载的资源名为: " + resourceName);
//        mResourceManager.fetchResourceByName(resourceName,
//                new SpeechResourceManager.FetchResourceListener() {
//                    @Override
//                    public void onSuccess() {
//                        Log.i("SpeechTtsDemo", "资源下载成功");
//                        initEngineInternal();
//                    }
//
//                    @Override
//                    public void onFailed(String errorMsg) {
//                        Log.i("SpeechTtsDemo", "资源下载失败，错误：" + errorMsg);
//                        speechEngineInitFailed("Download tts resource failed.");
//                    }
//                });
//    }

//    private void prepareSingleVoiceResource() {
//        mResourceManager.setAppVersion("1.0");
//        mResourceManager.setAppId(mCurAppId);
//        @SuppressLint("HardwareIds") String androidId = Settings.Secure.getString(_context.getContentResolver(), android.provider.Settings.Secure.ANDROID_ID);
//        Log.i("SpeechTtsDemo", "Current device android id: " + androidId);
//        mResourceManager.setDeviceId(androidId);
//        mResourceManager.setUseOnlineModel(true);
//        mResourceManager.setEngineName(SpeechEngineDefines.TTS_ENGINE);
//        Log.i("SpeechTtsDemo", "初始化模型资源管理器");
//        mResourceManager.initResourceManager(_context.getApplicationContext(), mDebugPath);
//
//        String[] needDownloadVoiceType = new String[]{};
//        List<String> voiceTypeArray = mSettings.getOptions(R.string.config_voice_type_offline).arrayObj;
//        if (voiceTypeArray != null && !voiceTypeArray.isEmpty()) {
//            needDownloadVoiceType = voiceTypeArray.toArray(new String[0]);
//        }
//        Log.d("SpeechTtsDemo", "离线合成将会使用的音色有： " + Arrays.toString(needDownloadVoiceType));
//        mResourceManager.setTtsVoiceType(needDownloadVoiceType);
//        String offlineLanguage = "tts language offline";
//
//        Log.d("SpeechTtsDemo", "需要下载的离线合成语种资源有： " + offlineLanguage);
//        mResourceManager.setTtsLanguage(new String[]{offlineLanguage});
//
//        Log.i("SpeechTtsDemo", "检查本地是否存在可用资源");
//        if (!mResourceManager.checkResourceDownload()) {
//            Log.i("SpeechTtsDemo", "本地没有资源，开始下载");
//            fetchSingleVoiceResource();
//        } else {
//            Log.i("SpeechTtsDemo", "资源存在，检查资源是否需要升级");
//            mResourceManager.checkResourceUpdate(needUpdate -> {
//                if (needUpdate) {
//                    Log.i("SpeechTtsDemo", "存在可用升级，开始升级");
//                    fetchSingleVoiceResource();
//                } else {
//                    Log.i("SpeechTtsDemo", "不存在可用升级，使用本地已有模型");
//                    initEngineInternal();
//                }
//            });
//        }
//    }

//    private void fetchSingleVoiceResource() {
//        mResourceManager.fetchResource(new SpeechResourceManager.FetchResourceListener() {
//            @Override
//            public void onSuccess() {
//                Log.i("SpeechTtsDemo", "资源下载成功");
//                initEngineInternal();
//            }
//
//            @Override
//            public void onFailed(String errorMsg) {
//                Log.i("SpeechTtsDemo", "资源下载失败，错误：" + errorMsg);
//                speechEngineInitFailed("Download tts resource failed.");
//            }
//        });
//    }

    private void initEngineInternal() {
        int ret = SpeechEngineDefines.ERR_NO_ERROR;
        if (mSpeechEngine == null) {
            Log.i("SpeechTtsDemo", "创建引擎.");
            mSpeechEngine = SpeechEngineGenerator.getInstance();
            mSpeechEngine.createEngine();
        }
        Log.d("SpeechTtsDemo", "SDK 版本号: " + mSpeechEngine.getVersion());

        Log.i("SpeechTtsDemo", "配置初始化参数.");
        configInitParams();

        long startInitTimestamp = System.currentTimeMillis();
        Log.i("SpeechTtsDemo", "引擎初始化.");
        ret = mSpeechEngine.initEngine();
        if (ret != SpeechEngineDefines.ERR_NO_ERROR) {
            String errMessage = "初始化失败，返回值: " + ret;
            Log.e("SpeechTtsDemo", errMessage);
            speechEngineInitFailed(errMessage);
            return;
        }
        Log.i("SpeechTtsDemo", "设置消息监听");
        mSpeechEngine.setListener(this);

        long cost = System.currentTimeMillis() - startInitTimestamp;
        Log.d("SpeechTtsDemo", String.format("初始化耗时 %d 毫秒", cost));
        speechEnginInitSucceeded();
    }

    public void unInitEngine() {
        if (mSpeechEngine != null) {
            Log.i("SpeechTtsDemo", "引擎析构.");
            mSpeechEngine.destroyEngine();
            mSpeechEngine = null;
            Log.i("SpeechTtsDemo", "引擎析构完成!");
        }
    }

    public void startEngine() {
        Log.d("SpeechTtsDemo", "Start engine, current status: " + mEngineStarted);
        if (!mEngineStarted) {
            AcquireAudioFocus();
            if (!mPlaybackNowAuthorized) {
                Log.w("SpeechTtsDemo", "Acquire audio focus failed, can't play audio");
                return;
            }
            mEngineErrorOccurred = false;

            // Directive：启动引擎前调用SYNC_STOP指令，保证前一次请求结束。
            Log.i("SpeechTtsDemo", "关闭引擎（同步）");
            Log.i("SpeechTtsDemo", "Directive: DIRECTIVE_SYNC_STOP_ENGINE");
            int ret = mSpeechEngine.sendDirective(SpeechEngineDefines.DIRECTIVE_SYNC_STOP_ENGINE, "");
            if (ret != SpeechEngineDefines.ERR_NO_ERROR) {
                Log.e("SpeechTtsDemo", "send directive sync stop failed, " + ret);
            } else {
                configStartTtsParams();
                Log.i("SpeechTtsDemo", "启动引擎");
                Log.i("SpeechTtsDemo", "Directive: DIRECTIVE_START_ENGINE");
                ret = mSpeechEngine.sendDirective(SpeechEngineDefines.DIRECTIVE_START_ENGINE, "");
                if (ret != SpeechEngineDefines.ERR_NO_ERROR) {
                    String message = "发送启动引擎指令失败, " + ret;
                    sendStartEngineDirectiveFailed(message);
                }
            }
        }
    }

    public void stopEngine() {
        Log.i("SpeechTtsDemo", "关闭引擎（异步）");
        Log.i("SpeechTtsDemo", "Directive: DIRECTIVE_STOP_ENGINE");
        if (mEngineStarted) {
            mSpeechEngine.sendDirective(SpeechEngineDefines.DIRECTIVE_STOP_ENGINE, "");
        }
    }

    public void pausePlayback() {
        Log.i("SpeechTtsDemo", "暂停播放");
        Log.i("SpeechTtsDemo", "Directive: DIRECTIVE_PAUSE_PLAYER");
        int ret = mSpeechEngine.sendDirective(SpeechEngineDefines.DIRECTIVE_PAUSE_PLAYER, "");
        if (ret == SpeechEngineDefines.ERR_NO_ERROR) {
            mPlayerPaused = true;
        }
        Log.d("SpeechTtsDemo", "Pause playback status:" + ret);
    }

    public void resumePlayback() {
        Log.i("SpeechTtsDemo", "继续播放");
        Log.i("SpeechTtsDemo", "Directive: DIRECTIVE_RESUME_PLAYER");
        int ret = mSpeechEngine.sendDirective(SpeechEngineDefines.DIRECTIVE_RESUME_PLAYER, "");
        if (ret == SpeechEngineDefines.ERR_NO_ERROR) {
            mPlayerPaused = false;
        }
        Log.d("SpeechTtsDemo", "Resume playback status:" + ret);
    }

    @Override
    public void onSpeechMessage(int type, byte[] data, int len) {
        String stdData = "";
        stdData = new String(data);

        switch (type) {
            case SpeechEngineDefines.MESSAGE_TYPE_ENGINE_START:
                // Callback: 引擎启动成功回调
                Log.i("SpeechTtsDemo", "Callback: 引擎启动成功: data: " + stdData);
                speechStart();
                break;
            case SpeechEngineDefines.MESSAGE_TYPE_ENGINE_STOP:
                // Callback: 引擎关闭回调
                Log.i("SpeechTtsDemo", "Callback: 引擎关闭: data: " + stdData);
                speechStop();
                break;
            case SpeechEngineDefines.MESSAGE_TYPE_ENGINE_ERROR:
                // Callback: 错误信息回调
                Log.e("SpeechTtsDemo", "Callback: 错误信息: " + stdData);
                speechError(stdData);
                break;
            case SpeechEngineDefines.MESSAGE_TYPE_TTS_SYNTHESIS_BEGIN:
                // Callback: 合成开始回调
                Log.e("SpeechTtsDemo", "Callback: 合成开始: " + stdData);
                speechStartSynthesis(stdData);
                break;
            case SpeechEngineDefines.MESSAGE_TYPE_TTS_SYNTHESIS_END:
                // Callback: 合成结束回调
                Log.e("SpeechTtsDemo", "Callback: 合成结束: " + stdData);
                speechFinishSynthesis();
                break;
            case SpeechEngineDefines.MESSAGE_TYPE_TTS_START_PLAYING:
                if (!stopSynthesis) {
                    stopSynthesis = true;
                    _context.runOnUiThread(() -> eventSink.success("playStart"));
                }
                // Callback: 播放开始回调
                Log.e("SpeechTtsDemo", "Callback: 播放开始: " + stdData);
//                speechStartPlaying(stdData);
                break;
            case SpeechEngineDefines.MESSAGE_TYPE_TTS_PLAYBACK_PROGRESS:
                // Callback: 播放进度回调
//                Log.e("SpeechTtsDemo", "Callback: 播放进度");
                // updatePlayingProgress(stdData);
                break;
            case SpeechEngineDefines.MESSAGE_TYPE_TTS_FINISH_PLAYING:
                // Callback: 播放结束回调
                Log.e("SpeechTtsDemo", "Callback: 播放结束");
                speechFinishPlaying(stdData);
                break;

            case SpeechEngineDefines.MESSAGE_TYPE_TTS_AUDIO_DATA:
            case SpeechEngineDefines.MESSAGE_TYPE_TTS_AUDIO_DATA_END:
                // Callback: 音频数据回调
                Log.e("SpeechTtsDemo", String.format("Callback: 音频数据，长度 %d 字节", stdData.length()));
                speechTtsAudioData();
                break;
            default:
                break;
        }
    }

    private void speechEnginInitSucceeded() {
        Log.i("SpeechTtsDemo", "引擎初始化成功!");
        mEngineInit = true;
    }

    private void speechEngineInitFailed(String tipText) {
        Log.e("SpeechTtsDemo", "引擎初始化失败: " + tipText);
        mEngineInit = false;
    }

    private void sendStartEngineDirectiveFailed(String tipText) {
        Log.e("SpeechTtsDemo", tipText);
        mEngineStarted = false;
    }

    private void sendSynthesisDirectiveFailed(String tipText) {
        Log.e("SpeechTtsDemo", tipText);
        _context.runOnUiThread(new Runnable() {
            @Override
            public void run() {
                mSpeechEngine.sendDirective(SpeechEngineDefines.DIRECTIVE_STOP_ENGINE, "");

            }
        });


    }

    private void speechStart() {
        mEngineStarted = true;
        mRetryCount = TTS_MAX_RETRY_COUNT;
    }

    private void speechStop() {
        mEngineStarted = false;
        mPlayerPaused = false;

        // Abandon audio focus when playback complete
        mAudioManager.abandonAudioFocus(mAFChangeListener);
        mPlaybackNowAuthorized = false;
    }

    private void speechError(final String data) {
        _context.runOnUiThread(() -> {
            boolean needStop = false;
            try {
                JSONObject reader = new JSONObject(data);
                if (!reader.has("err_code") || !reader.has("err_msg")) {
                    return;
                }
                int code = reader.getInt("err_code");
                switch (code) {
                    case SpeechEngineDefines.CODE_TTS_LIMIT_QPS:
                    case SpeechEngineDefines.CODE_TTS_LIMIT_COUNT:
                    case SpeechEngineDefines.CODE_TTS_SERVER_BUSY:
                    case SpeechEngineDefines.CODE_TTS_LONG_TEXT:
                    case SpeechEngineDefines.CODE_TTS_INVALID_TEXT:
                    case SpeechEngineDefines.CODE_TTS_SYNTHESIS_TIMEOUT:
                    case SpeechEngineDefines.CODE_TTS_SYNTHESIS_ERROR:
                    case SpeechEngineDefines.CODE_TTS_SYNTHESIS_WAITING_TIMEOUT:
                    case SpeechEngineDefines.CODE_TTS_ERROR_UNKNOWN:
                        Log.w("SpeechTtsDemo", "When meeting this kind of error, continue to synthesize.");
                        synthesisNextSentence();
                        break;
                    case SpeechEngineDefines.CODE_CONNECT_TIMEOUT:
                    case SpeechEngineDefines.CODE_RECEIVE_TIMEOUT:
                    case SpeechEngineDefines.CODE_NET_LIB_ERROR:
                        // 遇到网络错误时建议重试，重试次数不超过 3 次
                        needStop = !retrySynthesis();
                        if (needStop) {
                            mEngineErrorOccurred = true;
                        }
                        break;
                    default:
                        mEngineErrorOccurred = true;
                        needStop = true;
                        break;
                }
                if (mEngineErrorOccurred) {
                    _context.runOnUiThread(() -> eventSink.success("error"));
                }
            } catch (JSONException e) {
//                    e.printStackTrace();
                needStop = true;
            }
            if (needStop) {
                mSpeechEngine.sendDirective(SpeechEngineDefines.DIRECTIVE_STOP_ENGINE, "");
            }
        });

    }

    private void updateSynthesisMap(String synthesisId) {
        if (mTtsSynthesisIndex < mTtsSynthesisText.size()) {
            if (mTtsSynthesisMap == null) {
                mTtsSynthesisMap = new HashMap<>();
            }
            mTtsSynthesisMap.put(synthesisId, mTtsSynthesisIndex);
        }
    }

    private void speechStartSynthesis(final String data) {
        _context.runOnUiThread(new Runnable() {

            @Override
            public void run() {
                updateSynthesisMap(data);

            }
        });
    }

    private void speechFinishSynthesis() {
        if (mRetryCount < TTS_MAX_RETRY_COUNT) {
            mRetryCount = TTS_MAX_RETRY_COUNT;
        }
        //新开一个线程来使用
        _context.runOnUiThread(() -> {
            if (!mTtsSynthesisFromPlayer) {
                synthesisNextSentence();
            }
        });
    }

    private void speechStartPlaying(final String data) {

        _context.runOnUiThread(() -> {
            mTtsPlayingProgress = 0.0;
        });
    }

    private void speechFinishPlaying(final String data) {
        mTtsSynthesisMap.remove(data);
        Log.d("====", "====" + mTtsSynthesisMap.size());
        if (mTtsSynthesisMap.isEmpty()) {
            stopSynthesis = false;
            resetTtsContext();
            _context.runOnUiThread(() -> eventSink.success("finishPlay"));

        } else {
            Log.d("SpeechTtsDemo", "continue to play next sentence" + mTtsSynthesisText);
        }

        if (mTtsSynthesisFromPlayer) {
            triggerSynthesis();
            mTtsSynthesisFromPlayer = false;
        }

    }

    private void speechTtsAudioData() {
        // Handle audio data if necessary
    }

    private boolean retrySynthesis() {
        boolean ret = false;
        if (mEngineStarted && mRetryCount > 0) {
            Log.w("SpeechTtsDemo", "Retry synthesis for text: " + mTtsSynthesisText.get(mTtsSynthesisIndex));
            mHandler.postDelayed(this::triggerSynthesis, 1000);
            mRetryCount -= 1;
            ret = true;
        }
        return ret;
    }

    private void synthesisNextSentence() {
        if (mEngineStarted) {
            mTtsSynthesisIndex = (1 + mTtsSynthesisIndex) % mTtsSynthesisText.size();
            if (!mTtsSynthesisFromPlayer) {
                triggerSynthesis();
            }
        }
    }

    public void triggerSynthesis() {
        if (mTtsSynthesisIndex == 0 && stopSynthesis) return;
        configSynthesisParams();
        // DIRECTIVE_SYNTHESIS 是连续合成必需的一个指令，在成功调用 DIRECTIVE_START_ENGINE 之后，每次合成新的文本需要再调用 DIRECTIVE_SYNTHESIS 指令
        // DIRECTIVE_SYNTHESIS 需要在当前没有正在合成的文本时才可以成功调用，否则就会报错 -901，可以在收到 MESSAGE_TYPE_TTS_SYNTHESIS_END 之后调用
        // 当使用 SDK 内置的播放器时，为了避免缓存过多的音频导致内存占用过高，SDK 内部限制缓存的音频数量不超过 5 次合成的结果，
        // 如果 DIRECTIVE_SYNTHESIS 后返回 -902, 就需要在下一次收到 MESSAGE_TYPE_TTS_FINISH_PLAYING 再去调用 MESSAGE_TYPE_TTS_FINISH_PLAYING
        Log.i("SpeechTtsDemo", "触发合成");
        Log.i("SpeechTtsDemo", "Directive: DIRECTIVE_SYNTHESIS");
        int ret = mSpeechEngine.sendDirective(SpeechEngineDefines.DIRECTIVE_SYNTHESIS, "");
        if (ret != 0) {
            Log.e("SpeechTtsDemo", "Synthesis faile: " + ret);
            if (ret == SpeechEngineDefines.ERR_SYNTHESIS_PLAYER_IS_BUSY) {
                mTtsSynthesisFromPlayer = true;
            } else {
                String message = "发送合成指令失败, " + ret;
                sendSynthesisDirectiveFailed(message);
            }
        }
    }

    public void addSentence(final String text) {
        String tmp = text.trim();
        if (!tmp.isEmpty()) {
            if (mTtsSynthesisText == null) {
                mTtsSynthesisText = new ArrayList<>();
            }

            // 使用下面几个标点符号来分句，会让通过 MESSAGE_TYPE_TTS_PLAYBACK_PROGRESS 返回的播放进度更加准确
            String[] temp = tmp.split("[;|!?。！？；…，,]");
//            for (String s : temp) {
//                mTtsSynthesisText.add(s);
//            }
            mTtsSynthesisText.addAll(Arrays.asList(temp));
        }
    }

    private void resetTtsContext() {
        mTtsPlayingIndex = -1;
        mTtsSynthesisIndex = 0;
        mTtsSynthesisFromPlayer = false;
        if (mTtsSynthesisText != null) {
            mTtsSynthesisText.clear();
        } else {
            mTtsSynthesisText = new ArrayList<>();
        }
        if (mTtsSynthesisMap != null) {
            mTtsSynthesisMap.clear();
        } else {
            mTtsSynthesisMap = new HashMap<>();
        }
    }

    public void AcquireAudioFocus() {
        // 向系统请求 Audio Focus 并记录返回结果
        int res = mAudioManager.requestAudioFocus(mAFChangeListener, AudioManager.STREAM_MUSIC,
                AudioManager.AUDIOFOCUS_GAIN);
        if (res == AudioManager.AUDIOFOCUS_REQUEST_FAILED) {
            mPlaybackNowAuthorized = false;
        } else if (res == AudioManager.AUDIOFOCUS_REQUEST_GRANTED) {
            mPlaybackNowAuthorized = true;
        }
    }

    private void configInitParams() {
        //【必需配置】Engine Name
        mSpeechEngine.setOptionString(SpeechEngineDefines.PARAMS_KEY_ENGINE_NAME_STRING,
                SpeechEngineDefines.TTS_ENGINE);

        //【必需配置】Work Mode, 可选值如下
        // SpeechEngineDefines.TTS_WORK_MODE_ONLINE, 只进行在线合成，不需要配置离线合成相关参数；
        // SpeechEngineDefines.TTS_WORK_MODE_OFFLINE, 只进行离线合成，不需要配置在线合成相关参数；
        // SpeechEngineDefines.TTS_WORK_MODE_ALTERNATE, 先发起在线合成，失败后（网络超时），启动离线合成引擎开始合成；
        mSpeechEngine.setOptionInt(SpeechEngineDefines.PARAMS_KEY_TTS_WORK_MODE_INT, mCurTtsWorkMode);

        //【可选配置】Debug & Log
        mSpeechEngine.setOptionString(SpeechEngineDefines.PARAMS_KEY_DEBUG_PATH_STRING, mDebugPath);
        mSpeechEngine.setOptionString(SpeechEngineDefines.PARAMS_KEY_LOG_LEVEL_STRING, SpeechEngineDefines.LOG_LEVEL_DEBUG);

        //【可选配置】User ID（用以辅助定位线上用户问题）
        mSpeechEngine.setOptionString(SpeechEngineDefines.PARAMS_KEY_UID_STRING, "user");
        mSpeechEngine.setOptionString(SpeechEngineDefines.PARAMS_KEY_DEVICE_ID_STRING, "user_phone");

        //【可选配置】是否将合成出的音频保存到设备上，为 true 时需要正确配置 PARAMS_KEY_TTS_AUDIO_PATH_STRING 才会生效
        mSpeechEngine.setOptionBoolean(SpeechEngineDefines.PARAMS_KEY_TTS_ENABLE_DUMP_BOOL,
                true);
        // TTS 音频文件保存目录，必须在合成之前创建好且 APP 具有访问权限，保存的音频文件名格式为 tts_{reqid}.wav, {reqid} 是本次合成的请求 id
        // PARAMS_KEY_TTS_ENABLE_DUMP_BOOL 配置为 true 的音频时为【必需配置】，否则为【可选配置】
        mSpeechEngine.setOptionString(SpeechEngineDefines.PARAMS_KEY_TTS_AUDIO_PATH_STRING, mDebugPath);

        //【可选配置】是否禁止播放器对象的复用，如果禁用则每次 Start Engine 都会重新创建播放器对象
        mSpeechEngine.setOptionBoolean(SpeechEngineDefines.PARAMS_KEY_PLAYER_DISABLE_REUSE_BOOL, false);
        //【可选配置】用于控制 SDK 播放器所用的音源,默认为媒体音源
        // 如果不禁用播放器的复用，必须在 SDK 初始化之前配置音源，其他时机配置无法生效
        mSpeechEngine.setOptionInt(SpeechEngineDefines.PARAMS_KEY_AUDIO_STREAM_TYPE_INT,
                3);

        //【可选配置】合成出的音频的采样率，默认为 24000
        mSpeechEngine.setOptionInt(SpeechEngineDefines.PARAMS_KEY_TTS_SAMPLE_RATE_INT,
                24000);
        //【可选配置】打断播放时使用多长时间淡出停止，单位：毫秒。默认值 0 表示不淡出
        mSpeechEngine.setOptionInt(SpeechEngineDefines.PARAMS_KEY_AUDIO_FADEOUT_DURATION_INT,
                0);

        // ------------------------ 在线合成相关配置 -----------------------
        //【必需配置】在线合成鉴权相关：Appid
        mSpeechEngine.setOptionString(SpeechEngineDefines.PARAMS_KEY_APP_ID_STRING, mCurAppId);

        //【必需配置】在线合成鉴权相关：Token
        mSpeechEngine.setOptionString(SpeechEngineDefines.PARAMS_KEY_APP_TOKEN_STRING, mCurToken);

        //【必需配置】语音合成服务域名
        mSpeechEngine.setOptionString(SpeechEngineDefines.PARAMS_KEY_TTS_ADDRESS_STRING, mCurAddress);
        Log.i("SpeechTtsDemo", "Current address: " + mCurAddress);
        //【必需配置】语音合成服务Uri
        mSpeechEngine.setOptionString(SpeechEngineDefines.PARAMS_KEY_TTS_URI_STRING, mCurUri);
        Log.i("SpeechTtsDemo", "Current address: " + mCurUri);
        //【必需配置】语音合成服务所用集群
        mSpeechEngine.setOptionString(SpeechEngineDefines.PARAMS_KEY_TTS_CLUSTER_STRING, mCurCluster);

        //【可选配置】在线合成下发的 opus-ogg 音频的压缩倍率
        mSpeechEngine.setOptionInt(SpeechEngineDefines.PARAMS_KEY_TTS_COMPRESSION_RATE_INT, 10);

        // ------------------------ 离线合成相关配置 -----------------------

//        if (mCurTtsWorkMode != SpeechEngineDefines.TTS_WORK_MODE_ONLINE && mCurTtsWorkMode != SpeechEngineDefines.TTS_WORK_MODE_FILE) {
//            String ttsResourcePath = "";
//            if (mResourceManager != null && mSettings.getOptionsValue(R.string.tts_offline_resource_format_title, _context).equals("MultipleVoice")) {
//                ttsResourcePath = mResourceManager.getResourcePath(mSettings.getString(R.string.config_tts_model_name));
//            } else if (mSettings.getOptionsValue(R.string.tts_offline_resource_format_title, _context).equals("SingleVoice")) {
//                ttsResourcePath = mResourceManager.getResourcePath();
//            }
//            Log.d("SpeechTtsDemo", "tts resource root path:" + ttsResourcePath);
//            //【必需配置】离线合成所需资源存放路径
//            mSpeechEngine.setOptionString(SpeechEngineDefines.PARAMS_KEY_TTS_OFFLINE_RESOURCE_PATH_STRING,
//                    ttsResourcePath);
//        }

        //【必需配置】离线合成鉴权相关：证书文件存放路径
//        mSpeechEngine.setOptionString(SpeechEngineDefines.PARAMS_KEY_LICENSE_DIRECTORY_STRING, mDebugPath);
//        String curAuthenticateType = mAuthenticationTypeArray[mSettings
//                .getOptions(R.string.config_authenticate_type).chooseIdx];
//        //【必需配置】Authenticate Type
//        mSpeechEngine.setOptionString(SpeechEngineDefines.PARAMS_KEY_AUTHENTICATE_TYPE_STRING, curAuthenticateType);
//        if (curAuthenticateType.equals(SpeechEngineDefines.AUTHENTICATE_TYPE_PRE_BIND)) {
//            // 按包名授权，获取到授权的 APP 可以不限次数、不限设备数的使用离线合成
//            String ttsLicenseName = mSettings.getString(R.string.config_license_name);
//            String ttsLicenseBusiId = mSettings.getString(R.string.config_license_busi_id);
//            // 证书名和业务 ID, 离线合成鉴权相关，使用火山提供的证书下发服务时为【必需配置】, 否则为【无需配置】
//            // 证书名，用于下载按报名授权的证书文件
//            mSpeechEngine.setOptionString(SpeechEngineDefines.PARAMS_KEY_LICENSE_NAME_STRING, ttsLicenseName);
//            // 业务 ID, 用于下载按报名授权的证书文件
//            mSpeechEngine.setOptionString(SpeechEngineDefines.PARAMS_KEY_LICENSE_BUSI_ID_STRING, ttsLicenseBusiId);
//        } else if (curAuthenticateType.equals(SpeechEngineDefines.AUTHENTICATE_TYPE_LATE_BIND)) {
//            // 按装机量授权，不限制 APP 的包名和使用次数，但是限制使用离线合成的设备数量
//            //【必需配置】离线合成鉴权相关：Authenticate Address
//            mSpeechEngine.setOptionString(SpeechEngineDefines.PARAMS_KEY_AUTHENTICATE_ADDRESS_STRING,
//                    SensitiveDefines.AUTHENTICATE_ADDRESS);
//            //【必需配置】离线合成鉴权相关：Authenticate Uri
//            mSpeechEngine.setOptionString(SpeechEngineDefines.PARAMS_KEY_AUTHENTICATE_URI_STRING,
//                    SensitiveDefines.AUTHENTICATE_URI);
//            String businessKey = mSettings.getString(R.string.config_business_key);
//            String authenticateSecret = mSettings.getString(R.string.config_authenticate_secret);
//            //【必需配置】离线合成鉴权相关：Business Key
//            mSpeechEngine.setOptionString(SpeechEngineDefines.PARAMS_KEY_BUSINESS_KEY_STRING, businessKey);
//            //【必需配置】离线合成鉴权相关：Authenticate Secret
//            mSpeechEngine.setOptionString(SpeechEngineDefines.PARAMS_KEY_AUTHENTICATE_SECRET_STRING,
//                    authenticateSecret);
//        }
    }

    private void configStartTtsParams() {
        //【必需配置】TTS 使用场景
        mSpeechEngine.setOptionString(SpeechEngineDefines.PARAMS_KEY_TTS_SCENARIO_STRING, SpeechEngineDefines.TTS_SCENARIO_TYPE_NOVEL);


        if (mDisablePlayerReuse) {
            //【可选配置】用于控制 SDK 播放器所用的音源,默认为媒体音源
            // 只有禁用了播放器的复用，在 Start Engine 前配置音源才是生效的
            mSpeechEngine.setOptionInt(SpeechEngineDefines.PARAMS_KEY_AUDIO_STREAM_TYPE_INT,
                    3);
        }
        //【可选配置】是否使用 SDK 内置播放器播放合成出的音频，默认为 true
        mSpeechEngine.setOptionBoolean(SpeechEngineDefines.PARAMS_KEY_TTS_ENABLE_PLAYER_BOOL,
                true);
        //【可选配置】是否令 SDK 通过回调返回合成的音频数据，默认不返回。
        // 开启后，SDK 会流式返回音频，收到 MESSAGE_TYPE_TTS_AUDIO_DATA_END 回调表示当次合成所有的音频已经全部返回
        mSpeechEngine.setOptionInt(SpeechEngineDefines.PARAMS_KEY_TTS_DATA_CALLBACK_MODE_INT,
                0);
    }

    private void configSynthesisParams() {

        //【可选配置】需合成的文本的类型，支持直接传文本(TTS_TEXT_TYPE_PLAIN)和传 SSML 形式(TTS_TEXT_TYPE_SSML)的文本
        mSpeechEngine.setOptionString(SpeechEngineDefines.PARAMS_KEY_TTS_TEXT_TYPE_STRING,
                "Plain");
        String text = mTtsSynthesisText.get(mTtsSynthesisIndex);
        Log.e("SpeechTtsDemo", "Synthesis Text: " + text);
        //【必需配置】需合成的文本，不可超过 80 字
        mSpeechEngine.setOptionString(SpeechEngineDefines.PARAMS_KEY_TTS_TEXT_STRING, text);
        //【可选配置】用于控制 TTS 音频的语速，支持的配置范围参考火山官网 语音技术/语音合成/离在线语音合成SDK/参数说明 文档
        mSpeechEngine.setOptionDouble(SpeechEngineDefines.PARAMS_KEY_TTS_SPEED_RATIO_DOUBLE, mCurSpeakSpeed);
        //【可选配置】用于控制 TTS 音频的音量，支持的配置范围参考火山官网 语音技术/语音合成/离在线语音合成SDK/参数说明 文档
        mSpeechEngine.setOptionDouble(SpeechEngineDefines.PARAMS_KEY_TTS_VOLUME_RATIO_DOUBLE, 1.0);
        //【可选配置】用于控制 TTS 音频的音高，支持的配置范围参考火山官网 语音技术/语音合成/离在线语音合成SDK/参数说明 文档
        mSpeechEngine.setOptionDouble(SpeechEngineDefines.PARAMS_KEY_TTS_PITCH_RATIO_DOUBLE, 1.0);
        //【可选配置】是否在文本的每句结尾处添加静音段，单位：毫秒，默认为 0ms
        mSpeechEngine.setOptionInt(SpeechEngineDefines.PARAMS_KEY_TTS_SILENCE_DURATION_INT, 0);

        // ------------------------ 在线合成相关配置 -----------------------
        mCurVoiceOnline = "TTS ONLINE VOICE";
        Log.d("SpeechTtsDemo", "Current online voice: " + mCurVoiceOnline);
        //【必需配置】在线合成使用的发音人代号
        mSpeechEngine.setOptionString(SpeechEngineDefines.PARAMS_KEY_TTS_VOICE_ONLINE_STRING, mCurVoiceOnline);

        Log.d("SpeechTtsDemo", "Current online voice type: " + mCurVoiceTypeOnline);
        //【必需配置】在线合成使用的音色代号
        mSpeechEngine.setOptionString(SpeechEngineDefines.PARAMS_KEY_TTS_VOICE_TYPE_ONLINE_STRING,
                mCurVoiceTypeOnline);

        //【可选配置】是否打开在线合成的服务端缓存，默认关闭
        mSpeechEngine.setOptionBoolean(SpeechEngineDefines.PARAMS_KEY_TTS_ENABLE_CACHE_BOOL,
                false);
        //【可选配置】指定在线合成的语种，默认为空，即不指定
        mSpeechEngine.setOptionString(SpeechEngineDefines.PARAMS_KEY_TTS_LANGUAGE_ONLINE_STRING, "");
        //【可选配置】是否启用在线合成的情感预测功能
        mSpeechEngine.setOptionBoolean(SpeechEngineDefines.PARAMS_KEY_TTS_WITH_INTENT_BOOL,
                true);
        //【可选配置】指定在线合成的情感，例如 happy, sad 等
        mSpeechEngine.setOptionString(SpeechEngineDefines.PARAMS_KEY_TTS_EMOTION_STRING, "");
        //【可选配置】需要返回详细的播放进度或需要启用断点续播功能时应配置为 1, 否则配置为 0 或不配置
        mSpeechEngine.setOptionInt(SpeechEngineDefines.PARAMS_KEY_TTS_WITH_FRONTEND_INT, 1);
        //【可选配置】需要返回字粒度的播放进度时应配置为 simple, 同时要求 PARAMS_KEY_TTS_WITH_FRONTEND_INT 也配置为 1; 默认为空
        mSpeechEngine.setOptionString(SpeechEngineDefines.PARAMS_KEY_TTS_FRONTEND_TYPE_STRING, "simple");
        //【可选配置】使用复刻音色
        mSpeechEngine.setOptionBoolean(SpeechEngineDefines.PARAMS_KEY_TTS_USE_VOICECLONE_BOOL, false);
        //【可选配置】在开启前述使用复刻音色的开关后，制定复刻音色所用的后端集群
        mSpeechEngine.setOptionString(SpeechEngineDefines.PARAMS_KEY_TTS_BACKEND_CLUSTER_STRING, "YOUR TTS BACKEND CLUSTER");

        // ------------------------ 离线合成相关配置 -----------------------

//        String curVoiceOffline = mSettings.getString(R.string.config_voice_offline);
//        if (curVoiceOffline.isEmpty()) {
//            curVoiceOffline = mSettings.getOptionsValue(R.string.config_voice_offline);
//        }
//        mCurVoiceOffline = curVoiceOffline;
//        Log.d("SpeechTtsDemo", "Current offline voice: " + mCurVoiceOffline);
//        //【必需配置】离线合成使用的发音人代号
//        mSpeechEngine.setOptionString(SpeechEngineDefines.PARAMS_KEY_TTS_VOICE_OFFLINE_STRING,
//                mCurVoiceOffline);
//        String curVoiceTypeOffline = mSettings.getString(R.string.config_voice_type_offline);
//        if (curVoiceTypeOffline.isEmpty()) {
//            curVoiceTypeOffline = mSettings.getOptionsValue(R.string.config_voice_type_offline);
//        }
//        mCurVoiceTypeOffline = curVoiceTypeOffline;
//        Log.d("SpeechTtsDemo", "Current offline voice type: " + mCurVoiceTypeOffline);
//        //【必需配置】离线合成使用的音色代号
//        mSpeechEngine.setOptionString(SpeechEngineDefines.PARAMS_KEY_TTS_VOICE_TYPE_OFFLINE_STRING,
//                mCurVoiceTypeOffline);
//
//        //【可选配置】是否降低离线合成的 CPU 利用率，默认关闭
//        // 打开该配置会使离线合成的实时率变大，仅当必要（例如为避免系统主动杀死CPU占用持续过高的进程）时才应开启
//        mSpeechEngine.setOptionBoolean(SpeechEngineDefines.PARAMS_KEY_TTS_LIMIT_CPU_USAGE_BOOL,
//                mSettings.getBoolean(R.string.tts_limit_cpu_usage));
    }
}