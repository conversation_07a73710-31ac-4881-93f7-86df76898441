package com.marspt.xiaopa.tts;

import android.Manifest;
import android.content.Context;
import android.util.Log;

import com.marspt.xiaopa.MainActivity;
import com.marspt.xiaopa.tts.utils.BaseUtil;
import com.marspt.xiaopa.tts.utils.SpeechStreamRecorder;
import com.bytedance.speech.speechengine.SpeechEngine;
import com.bytedance.speech.speechengine.SpeechEngineDefines;
import com.bytedance.speech.speechengine.SpeechEngineGenerator;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.Collections;
import java.util.List;
import java.util.Objects;

import io.flutter.plugin.common.EventChannel;

public class ByteAsrUtil implements SpeechEngine.SpeechListener {

    // Options Default Value
    private static String mCurAppId = "";
    private static String mCurToken = "";
    private static String mCurCluster = "volcengine_streaming_common";
    private static final String mCurAddress = "wss://openspeech.bytedance.com";
    private static final String mCurUri = "/api/v2/asr";
    private static final Double mCurSpeakSpeed = 1.0;

    // Paths
    private String mDebugPath = "";

    // Engine
    private SpeechEngine mSpeechEngine = null;
    private boolean mEngineStarted = false;

    // Permissions
    private static final List<String> ASR_PERMISSIONS = Collections.singletonList(
            Manifest.permission.RECORD_AUDIO
    );

    // StreamRecorder
    private SpeechStreamRecorder mStreamRecorder;

    // Statistics
    private long mFinishTalkingTimestamp = 0;

    private final MainActivity _context;

    private static ByteAsrUtil instance;

    private EventChannel.EventSink eventSink;
    private EventChannel.EventSink volumeSink;

    public static synchronized ByteAsrUtil getInstance(Context context, String appId, String token, String cluster) {
        mCurAppId = appId;
        mCurToken = token;
        mCurCluster = cluster;
        if (instance == null) {
            instance = new ByteAsrUtil(context);
        }
        return instance;
    }

    public ByteAsrUtil(Context context) {
        _context = (MainActivity) context;
        mStreamRecorder = new SpeechStreamRecorder();
        mDebugPath = new BaseUtil().getDebugPath(_context);
    }


    private void configInitParams() {
        //【必需配置】Engine Name
        mSpeechEngine.setOptionString(SpeechEngineDefines.PARAMS_KEY_ENGINE_NAME_STRING, SpeechEngineDefines.ASR_ENGINE);

        //【可选配置】Debug & Log
        Log.i("SpeechAsrDemo", "mDebugPath: " + mDebugPath);
        mSpeechEngine.setOptionString(SpeechEngineDefines.PARAMS_KEY_DEBUG_PATH_STRING, mDebugPath);
        mSpeechEngine.setOptionString(SpeechEngineDefines.PARAMS_KEY_LOG_LEVEL_STRING, SpeechEngineDefines.LOG_LEVEL_DEBUG);

        //【可选配置】User ID（用以辅助定位线上用户问题）
        mSpeechEngine.setOptionString(SpeechEngineDefines.PARAMS_KEY_UID_STRING, "user");

        //【必需配置】配置音频来源
        mSpeechEngine.setOptionString(SpeechEngineDefines.PARAMS_KEY_RECORDER_TYPE_STRING, "Recorder");

        //【可选配置】录音文件保存路径，如配置，SDK会将录音保存到该路径下，文件格式为 .wav
        mSpeechEngine.setOptionString(SpeechEngineDefines.PARAMS_KEY_ASR_REC_PATH_STRING, mDebugPath);

//        if (mSettings.getBoolean(R.string.config_asr_rec_save)) {
//            //【可选配置】录音文件保存路径，如配置，SDK会将录音保存到该路径下，文件格式为 .wav
//            mSpeechEngine.setOptionString(SpeechEngineDefines.PARAMS_KEY_ASR_REC_PATH_STRING, mDebugPath);
//        }

        //【可选配置】音频采样率，默认16000
        mSpeechEngine.setOptionInt(SpeechEngineDefines.PARAMS_KEY_SAMPLE_RATE_INT, 16000);
        //【可选配置】音频通道数，默认1，可选1或2
        mSpeechEngine.setOptionInt(SpeechEngineDefines.PARAMS_KEY_CHANNEL_NUM_INT, 1);
        //【可选配置】上传给服务的音频通道数，默认1，可选1或2，一般与PARAMS_KEY_CHANNEL_NUM_INT保持一致即可
        mSpeechEngine.setOptionInt(SpeechEngineDefines.PARAMS_KEY_UP_CHANNEL_NUM_INT, 1);

        // 当音频来源为 RECORDER_TYPE_STREAM 时，如输入音频采样率不等于 16K，需添加如下配置
//        if (mSettings.getOptionsValue(R.string.config_recorder_type, this).equals(SpeechEngineDefines.RECORDER_TYPE_STREAM)) {
//            if (mStreamRecorder.GetStreamSampleRate() != 16000 || mStreamRecorder.GetStreamChannel() != 1) {
//                // 当音频来源为 RECORDER_TYPE_STREAM 时【必需配置】，否则【无需配置】
//                // 启用 SDK 内部的重采样
//                mSpeechEngine.setOptionBoolean(SpeechEngineDefines.PARAMS_KEY_ENABLE_RESAMPLER_BOOL, true);
//                // 将重采样所需的输入采样率设置为 APP 层输入的音频的实际采样率
//                mSpeechEngine.setOptionInt(SpeechEngineDefines.PARAMS_KEY_CUSTOM_SAMPLE_RATE_INT, mStreamRecorder.GetStreamSampleRate());
//                mSpeechEngine.setOptionInt(SpeechEngineDefines.PARAMS_KEY_CUSTOM_CHANNEL_INT, mStreamRecorder.GetStreamChannel());
//            }
//        }

        Log.i("SpeechAsrDemo", "Current address: " + mCurAddress);
        //【必需配置】识别服务域名
        mSpeechEngine.setOptionString(SpeechEngineDefines.PARAMS_KEY_ASR_ADDRESS_STRING, mCurAddress);

        Log.i("SpeechAsrDemo", "Current uri: " + mCurUri);
        //【必需配置】识别服务Uri
        mSpeechEngine.setOptionString(SpeechEngineDefines.PARAMS_KEY_ASR_URI_STRING, mCurUri);

        //【必需配置】鉴权相关：Appid
        mSpeechEngine.setOptionString(SpeechEngineDefines.PARAMS_KEY_APP_ID_STRING, mCurAppId);

        //【必需配置】鉴权相关：Token
        mSpeechEngine.setOptionString(SpeechEngineDefines.PARAMS_KEY_APP_TOKEN_STRING, mCurToken);

        Log.i("SpeechAsrDemo", "Current cluster: " + mCurCluster);
        //【必需配置】识别服务所用集群
        mSpeechEngine.setOptionString(SpeechEngineDefines.PARAMS_KEY_ASR_CLUSTER_STRING, mCurCluster);

        //【可选配置】在线请求的建连与接收超时，一般不需配置使用默认值即可
        mSpeechEngine.setOptionInt(SpeechEngineDefines.PARAMS_KEY_ASR_CONN_TIMEOUT_INT, 3000);
        mSpeechEngine.setOptionInt(SpeechEngineDefines.PARAMS_KEY_ASR_RECV_TIMEOUT_INT, 5000);

        //【可选配置】在线请求断连后，重连次数，默认值为0，如果需要开启需要设置大于0的次数
        mSpeechEngine.setOptionInt(SpeechEngineDefines.PARAMS_KEY_ASR_MAX_RETRY_TIMES_INT, 0);
    }

    private void configStartAsrParams() {
        //【可选配置】是否开启顺滑(DDC)
        mSpeechEngine.setOptionBoolean(SpeechEngineDefines.PARAMS_KEY_ASR_ENABLE_DDC_BOOL, false);
        //【可选配置】是否开启文字转数字(ITN)
        mSpeechEngine.setOptionBoolean(SpeechEngineDefines.PARAMS_KEY_ASR_ENABLE_ITN_BOOL, false);
        //【可选配置】是否开启标点
        mSpeechEngine.setOptionBoolean(SpeechEngineDefines.PARAMS_KEY_ASR_SHOW_NLU_PUNC_BOOL, true);
        //【可选配置】设置识别语种
        mSpeechEngine.setOptionString(SpeechEngineDefines.PARAMS_KEY_ASR_LANGUAGE_STRING, "en-US");
        //【可选配置】是否隐藏句尾标点
        mSpeechEngine.setOptionBoolean(SpeechEngineDefines.PARAMS_KEY_ASR_DISABLE_END_PUNC_BOOL, false);
        //【可选配置】是否返回用户说话的语种
        mSpeechEngine.setOptionBoolean(SpeechEngineDefines.PARAMS_KEY_ASR_SHOW_LANG_BOOL, false);
        // 【可选配置】直接传递自定义的ASR请求JSON，若使用此参数需自行确保JSON格式正确
        mSpeechEngine.setOptionString(SpeechEngineDefines.PARAMS_KEY_ASR_REQ_PARAMS_STRING, "{\"reqid\":\"20a3d61f-1a5d-4874-bf46-4bb65216fa46\",\"sequence\":\"10\",\"params\":\"abc\"}");

        //【可选配置】控制识别结果返回的形式，全量返回或增量返回，默认为全量
        mSpeechEngine.setOptionString(SpeechEngineDefines.PARAMS_KEY_ASR_RESULT_TYPE_STRING, "full");

        //【可选配置】设置VAD头部静音时长，用户多久没说话视为空音频，即静音检测时长
        mSpeechEngine.setOptionInt(SpeechEngineDefines.PARAMS_KEY_ASR_VAD_START_SILENCE_TIME_INT, 0);
        //【可选配置】设置VAD尾部静音时长，用户说话后停顿多久视为说话结束，即自动判停时长
        mSpeechEngine.setOptionInt(SpeechEngineDefines.PARAMS_KEY_ASR_VAD_END_SILENCE_TIME_INT, 0);
        //【可选配置】设置VAD模式，用于定制VAD场景，默认为空
        mSpeechEngine.setOptionString(SpeechEngineDefines.PARAMS_KEY_ASR_VAD_MODE_STRING, "");
        //【可选配置】用户音频输入最大时长，仅一句话识别场景生效，单位毫秒，默认为 60000ms.
        mSpeechEngine.setOptionInt(SpeechEngineDefines.PARAMS_KEY_VAD_MAX_SPEECH_DURATION_INT, 60000);

        //【可选配置】控制是否返回录音音量，在 APP 需要显示音频波形时可以启用
        mSpeechEngine.setOptionBoolean(SpeechEngineDefines.PARAMS_KEY_ENABLE_GET_VOLUME_BOOL, true);

        String correctWords = "{\"素质人\":\"数字人\",}";
        //【可选配置】设置纠错词表，识别结果会根据设置的纠错词纠正结果，例如："{\"古爱玲\":\"谷爱凌\"}"，当识别结果中出现"古爱玲"时会替换为"谷爱凌"
        mSpeechEngine.setOptionString(SpeechEngineDefines.PARAMS_KEY_ASR_CORRECT_WORDS_STRING, correctWords);

        //【可选配置】更新 ASR 热词
        String hotWords = "{\"hotwords\":[{\"word\":\"过秦论\",\"scale\":2.0}]}";
        setHotWords(hotWords);


//        if (mSettings.getOptionsValue(R.string.config_recorder_type, this).equals(SpeechEngineDefines.RECORDER_TYPE_STREAM)) {
//            if (!mStreamRecorder.Start()) {
//                requestPermission(ASR_PERMISSIONS);
//            }
//        } else if (mSettings.getOptionsValue(R.string.config_recorder_type, this).equals(SpeechEngineDefines.RECORDER_TYPE_FILE)) {
//            // 使用音频文件识别时，需要设置文件的绝对路径
//            String test_file_path = mDebugPath + "/asr_rec_file.pcm";
//            Log.d("SpeechAsrDemo", "输入的音频文件路径: " + test_file_path);
//            // 使用音频文件识别时【必须配置】，否则【无需配置】
//            mSpeechEngine.setOptionString(SpeechEngineDefines.PARAMS_KEY_RECORDER_FILE_STRING, test_file_path);
//        }
    }

    private void setHotWords(String hotWords) {
        if (mSpeechEngine != null) {
            // 更新 ASR 热词，例如："{\"hotwords\":[{\"word\":\"过秦论\",\"scale\":2.0}]}"
            // scale为float类型参数，其中叠词的范围为[1.0,2.0]，非叠词的范围为[1.0,50.0]，scale值越大，结果中出现热词的概率越大
            mSpeechEngine.sendDirective(SpeechEngineDefines.DIRECTIVE_UPDATE_ASR_HOTWORDS, hotWords);
        }
    }

    public void initEngine() {
        if (mSpeechEngine == null) {
            Log.i("SpeechAsrDemo", "创建引擎.");
            mSpeechEngine = SpeechEngineGenerator.getInstance();
            mSpeechEngine.createEngine();
            mSpeechEngine.setContext(_context);
        }
        Log.d("SpeechAsrDemo", "SDK 版本号: " + mSpeechEngine.getVersion());

        Log.i("SpeechAsrDemo", "配置初始化参数.");
        configInitParams();

        Log.i("SpeechAsrDemo", "引擎初始化.");
        int ret = mSpeechEngine.initEngine();
        if (ret != SpeechEngineDefines.ERR_NO_ERROR) {
            String errMessage = "初始化失败，返回值: " + ret;
            Log.e("SpeechAsrDemo", errMessage);
            speechEngineInitFailed(errMessage);
            return;
        }
        Log.i("SpeechAsrDemo", "设置消息监听");
        mSpeechEngine.setListener(this);
        speechEnginInitSucceeded();
    }

    public void unInitEngine() {
        if (mSpeechEngine != null) {
            Log.i("SpeechAsrDemo", "引擎析构.");
            mSpeechEngine.destroyEngine();
            mSpeechEngine = null;
            Log.i("SpeechAsrDemo", "引擎析构完成!");
        }
    }

    public void setStreamHandle(EventChannel.EventSink _eventSink, EventChannel.EventSink _volumeSink) {
        eventSink = _eventSink;
        volumeSink = _volumeSink;
    }

    public void startRecord(String  isHold) {
        mFinishTalkingTimestamp = 0;

        Log.i("SpeechAsrDemo", "配置启动参数." + isHold);
        configStartAsrParams();
        //【可选配置】该按钮为长按模式，预期是按下开始录音，抬手结束录音，需要关闭云端自动判停功能。
        boolean temp = !Objects.equals(isHold, "0");
        mSpeechEngine.setOptionBoolean(SpeechEngineDefines.PARAMS_KEY_ASR_AUTO_STOP_BOOL, temp);

        // Directive：启动引擎前调用SYNC_STOP指令，保证前一次请求结束。
        Log.i("SpeechAsrDemo", "关闭引擎（同步）");
        Log.i("SpeechAsrDemo", "Directive: DIRECTIVE_SYNC_STOP_ENGINE");
        int ret = mSpeechEngine.sendDirective(SpeechEngineDefines.DIRECTIVE_SYNC_STOP_ENGINE, "");
        if (ret != SpeechEngineDefines.ERR_NO_ERROR) {
            Log.e("SpeechAsrDemo", "send directive syncstop failed, " + ret);
        } else {
            Log.i("SpeechAsrDemo", "启动引擎");
            Log.i("SpeechAsrDemo", "Directive: DIRECTIVE_START_ENGINE");
            ret = mSpeechEngine.sendDirective(SpeechEngineDefines.DIRECTIVE_START_ENGINE, "");
            if (ret == SpeechEngineDefines.ERR_REC_CHECK_ENVIRONMENT_FAILED) {
                Log.e("SpeechAsrDemo", "无权限, " + ret);
            } else if (ret != SpeechEngineDefines.ERR_NO_ERROR) {
                Log.e("SpeechAsrDemo", "send directive start failed, " + ret);
            }
        }
    }

    public void stopEngine() {
        Log.i("SpeechAsrDemo", "关闭引擎（异步）");
        Log.i("SpeechAsrDemo", "Directive: DIRECTIVE_STOP_ENGINE");
        mSpeechEngine.sendDirective(SpeechEngineDefines.DIRECTIVE_STOP_ENGINE, "");
    }

    public void stopRecord() {
        Log.i("SpeechAsrDemo", "AsrTouch: Finish");
        mFinishTalkingTimestamp = System.currentTimeMillis();
        // Directive：结束用户音频输入。
        Log.i("SpeechAsrDemo", "Directive: DIRECTIVE_FINISH_TALKING");
        mSpeechEngine.sendDirective(SpeechEngineDefines.DIRECTIVE_FINISH_TALKING, "");
        mStreamRecorder.Stop();
    }

    @Override
    public void onSpeechMessage(int type, byte[] data, int len) {
        String stdData = new String(data);
        switch (type) {
            case SpeechEngineDefines.MESSAGE_TYPE_ENGINE_START:
                // Callback: 引擎启动成功回调
                Log.i("SpeechAsrDemo", "Callback: 引擎启动成功: data: " + stdData);
                speechStart();
                break;
            case SpeechEngineDefines.MESSAGE_TYPE_ENGINE_STOP:
                // Callback: 引擎关闭回调
                Log.i("SpeechAsrDemo", "Callback: 引擎关闭: data: " + stdData);
                speechStop();
                break;
            case SpeechEngineDefines.MESSAGE_TYPE_ENGINE_ERROR:
                // Callback: 错误信息回调
                Log.e("SpeechAsrDemo", "Callback: 错误信息: " + stdData);
                speechError(stdData);
                break;
            case SpeechEngineDefines.MESSAGE_TYPE_CONNECTION_CONNECTED:
                Log.i("SpeechAsrDemo", "Callback: 建连成功: data: " + stdData);
                break;
            case SpeechEngineDefines.MESSAGE_TYPE_PARTIAL_RESULT:
                // Callback: ASR 当前请求的部分结果回调
                Log.d("SpeechAsrDemo", "Callback: ASR 当前请求的部分结果");
//                speechAsrResult(stdData, false);
                break;
            case SpeechEngineDefines.MESSAGE_TYPE_FINAL_RESULT:
                // Callback: ASR 当前请求最终结果回调
                Log.i("SpeechAsrDemo", "Callback: ASR 当前请求最终结果");
                speechAsrResult(stdData, true);
                break;
            case SpeechEngineDefines.MESSAGE_TYPE_VOLUME_LEVEL:
                // Callback: 录音音量回调
                Log.d("SpeechAsrDemo", "Callback: 录音音量" + stdData);
                volumeChange(stdData);
                break;
            default:
                break;
        }
    }

    private void volumeChange(String data) {
        _context.runOnUiThread(() -> {
            volumeSink.success(data);
        });

    }

    public void speechEnginInitSucceeded() {
        Log.i("SpeechAsrDemo", "引擎初始化成功!");
        mStreamRecorder.SetSpeechEngine("ASR", mSpeechEngine);
    }

    public void speechEngineInitFailed(String tipText) {
        Log.e("SpeechAsrDemo", "引擎初始化失败: " + tipText);
    }

    public void speechStart() {
        mEngineStarted = true;
    }

    public void speechStop() {
        mEngineStarted = false;

        _context.runOnUiThread(() -> {
            mStreamRecorder.Stop();
        });
    }

    public void speechAsrResult(final String data, boolean isFinal) {
        // 计算由录音结束到 ASR 最终结果之间的延迟
        long delay = 0;
        if (isFinal && mFinishTalkingTimestamp > 0) {
            delay = System.currentTimeMillis() - mFinishTalkingTimestamp;
        }
        final long response_delay = delay;
        _context.runOnUiThread(() -> {
            try {
                // 从回调的 json 数据中解析 ASR 结果
                JSONObject reader = new JSONObject(data);
                if (!reader.has("result")) {
                    eventSink.success("");
                    return;
                }
                String text = reader.getJSONArray("result").getJSONObject(0).getString("text");
                if (text.isEmpty()) {
                    eventSink.success("");
                    return;
                }
//                text = "result: " + text;
//                text += "\nreqid: " + reader.getString("reqid");
//                if (isFinal && response_delay > 0) {
//                    text += "\nresponse_delay: " + response_delay;
//                }
                eventSink.success(text);
            } catch (JSONException e) {
//                e.printStackTrace();
            }
        });
    }

    public void speechError(final String data) {
        _context.runOnUiThread(() -> {
            try {
                // 从回调的 json 数据中解析错误码和错误详细信息
                JSONObject reader = new JSONObject(data);
                if (!reader.has("err_code") || !reader.has("err_msg")) {
                    return;
                }
                int errCode = reader.getInt("err_code");
                String errMsg = reader.getString("err_msg");
                if (errCode == 1012 && errMsg.equals("No valid data found in input audio")) {
                    eventSink.success("");
                }
                if (errCode == 1013 && errMsg.equals("No valid speeches found in input audio")) {
                    eventSink.success("");
                }
            } catch (JSONException e) {
//                e.printStackTrace();
            }
        });
    }

}
