<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    package="com.marspt.xiaopa">

    <!--互联网权限-->
    <uses-permission android:name="android.permission.INTERNET" />
    <!--存储权限 Android 11 (API level 29) or low-->
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE"
        android:maxSdkVersion="32"
        tools:ignore="ScopedStorage" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE"
        android:maxSdkVersion="32" />

<!--    <uses-permission android:name="android.permission.MANAGE_EXTERNAL_STORAGE"-->
<!--        tools:ignore="ScopedStorage" />-->

    <!--存储权限 Android 13 (API level 33) or higher-->
    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
<!--    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO" />-->
<!--    <uses-permission android:name="android.permission.READ_MEDIA_AUDIO"/>-->
    <!--    &lt;!&ndash;存储权限 Android 14 (API level 34) or higher&ndash;&gt;-->
    <uses-permission android:name="android.permission.READ_MEDIA_VISUAL_USER_SELECTED" />

    <uses-permission android:name="android.permission.ACCESS_MEDIA_LOCATION" />


    <!--通知权限 -->
    <uses-permission android:name="android.permission.VIBRATE" />
    <uses-permission android:name="android.permission.ACCESS_NOTIFICATION_POLICY" />
    <!--获取手机录音机使用权限，听写、识别、语义理解需要用到此权限 -->
    <uses-permission android:name="android.permission.RECORD_AUDIO" />
    <uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />

    <!--打开和安装apk 上架谷歌商店要去掉-->
   <uses-permission android:name="android.permission.REQUEST_INSTALL_PACKAGES" />

    <!-- 蓝牙权限 -->
    <uses-permission android:name="android.permission.BLUETOOTH" />
    <uses-permission android:name="android.permission.BLUETOOTH_ADMIN" />

    <!--蓝牙扫码设备权限 Android 12+ 新权限-->
    <uses-permission android:name="android.permission.BLUETOOTH_CONNECT"/>
    <uses-permission android:name="android.permission.BLUETOOTH_SCAN"/>
    <uses-permission android:name="android.permission.BLUETOOTH_ADVERTISE" />

    <!-- 老版本用于扫描 -->
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />

    <!--定位权限-->
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
    <!--wifi状态修改权限-->
    <uses-permission android:name="android.permission.CHANGE_WIFI_STATE"/>
    <!--震动权限-->
    <uses-permission android:name="android.permission.VIBRATE"/>
    <!--允许应用在系统启动完成后接收广播 保证设备重启后，极光 SDK 可以自动恢复推送服务（重新建立连接）-->
    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED"/>

    <application
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:networkSecurityConfig="@xml/network_security_config"
        tools:replace="android:networkSecurityConfig,android:label">
        <activity
            android:name=".MainActivity"
            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
            android:exported="true"
            android:hardwareAccelerated="true"
            android:launchMode="singleTask"
            android:taskAffinity=""
            android:theme="@style/LaunchTheme"
            android:windowSoftInputMode="adjustResize">
            <!-- Specifies an Android theme to apply to this Activity as soon as
                 the Android process has started. This theme is visible to the user
                 while the Flutter UI initializes. After that, this theme continues
                 to determine the Window background behind the Flutter UI. -->
            <meta-data
                android:name="io.flutter.embedding.android.NormalTheme"
                android:resource="@style/NormalTheme" />
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>

        <activity
            android:name="com.yalantis.ucrop.UCropActivity"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.AppCompat.Light.NoActionBar" />

        <activity
            android:name="com.marspt.xiaopa.wxapi.WXPayEntryActivity"
            android:theme="@android:style/Theme.Translucent.NoTitleBar"
            android:exported="true"
            android:taskAffinity="com.marspt.xiaopa"
            android:launchMode="singleTask" />

        <!--上架谷歌  FOREGROUND_SERVICE_MEDIA_PROJECTION 权限的敏感  项目中为使用到录制屏幕的功能，彻底去除掉-->
        <!-- 或者 在 Play Console 的 权限声明里写明：-->
        <!-- “我们的应用使用了 volc_engine_rtc SDK，但未使用屏幕共享功能。用户无法触发屏幕共享或 MediaProjection 权限。”-->
        <activity
            android:name="com.ss.bytertc.engine.flutter.screencapture.BaseScreenCaptureRequestActivity"
            tools:node="remove"/>

        <!-- Don't delete the meta-data below.
             This is used by the Flutter tool to generate GeneratedPluginRegistrant.java -->
        <meta-data
            android:name="flutterEmbedding"
            android:value="2" />

        <provider
            android:name="vn.hunghd.flutterdownloader.DownloadedFileProvider"
            android:authorities="${applicationId}.flutter_downloader.provider"
            android:exported="false"
            android:grantUriPermissions="true">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/provider_paths" />
        </provider>

        <!-- Begin FlutterDownloader customization -->
        <!-- disable default Initializer -->
        <provider
            android:name="androidx.startup.InitializationProvider"
            android:authorities="${applicationId}.androidx-startup"
            android:exported="false"
            tools:node="merge">
            <meta-data
                android:name="androidx.work.WorkManagerInitializer"
                android:value="androidx.startup"
                tools:node="remove" />
        </provider>

        <!-- declare customized Initializer -->
        <provider
            android:name="vn.hunghd.flutterdownloader.FlutterDownloaderInitializer"
            android:authorities="${applicationId}.flutter-downloader-init"
            android:exported="false">
            <!-- changes this number to configure the maximum number of concurrent tasks -->
            <meta-data
                android:name="vn.hunghd.flutterdownloader.MAX_CONCURRENT_TASKS"
                android:value="5" />
        </provider>

        <!-- 👇加上这一段，移除 BOOT_COMPLETED -->
        <receiver
            android:name="androidx.work.impl.background.systemalarm.RescheduleReceiver"
            android:enabled="false"
            android:exported="false"
            tools:node="remove" />

        <provider
            android:name="androidx.core.content.FileProvider"
            android:authorities="${applicationId}.fileprovider"
            android:exported="false"
            android:grantUriPermissions="true">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/file_paths" />
        </provider>


    </application>
    <!-- Required to query activities that can process text, see:
         https://developer.android.com/training/package-visibility and
         https://developer.android.com/reference/android/content/Intent#ACTION_PROCESS_TEXT.

         In particular, this is used by the Flutter engine in io.flutter.plugin.text.ProcessTextPlugin. -->
    <queries>
        <intent>
            <action android:name="android.intent.action.PROCESS_TEXT" />
            <data android:mimeType="text/plain" />
        </intent>
    </queries>

    <queries>
        <package android:name="com.tencent.mm" />   <!-- 指定微信包名 -->
    </queries>



</manifest>
