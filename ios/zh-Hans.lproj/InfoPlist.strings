/* 
  InfoPlist.strings
  Runner

  Created by songguo on 2025/8/14.
  
*/
CFBundleDisplayName = "小耙AI";

// 相册权限
NSPhotoLibraryUsageDescription = "App希望获取访问相册权限，用于从相册选择图片设置头像";
NSPhotoLibraryAddUsageDescription = "App希望获取访问相册权限，用于从相册选择图片设置头像";
NSCameraUsageDescription = "App希望获取访问相册权限，用于从相册选择图片设置头像";
NSAppleMusicUsageDescription = "App希望获取访问相册权限，用于从相册选择图片设置头像";
// 麦克风权限
NSMicrophoneUsageDescription = "App希望获取麦克风权限，用于识别语音指令，与小耙设备互动";
// 定位权限（使用期间）
NSLocationWhenInUseUsageDescription = "App希望获取您的位置，用于辅助蓝牙发现附近的小耙设备，完成配网";
// 定位权限（始终）
NSLocationAlwaysAndWhenInUseUsageDescription = "App希望获取您的位置，用于辅助蓝牙发现附近的小耙设备，完成配网";
// 蓝牙权限
NSBluetoothAlwaysUsageDescription = "App希望获取蓝牙权限，用于扫描并连接附近的小耙设备，完成配网";
NSBluetoothPeripheralUsageDescription = "App希望获取蓝牙权限，用于扫描并连接附近的小耙设备，完成配网";

