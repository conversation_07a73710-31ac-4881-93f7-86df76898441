/* 
  InfoPlist.strings
  Runner

  Created by song<PERSON><PERSON> on 2025/8/14.
  
*/
CFBundleDisplayName = "Xiaopa AI";

// Photo Library permission
NSPhotoLibraryUsageDescription = "This app requires access to your photo library to allow you to select a photo for your profile picture.";
NSPhotoLibraryAddUsageDescription = "This app requires access to your photo library to allow you to select a photo for your profile picture.";
NSCameraUsageDescription = "This app requires access to your photo library to allow you to select a photo for your profile picture.";
NSAppleMusicUsageDescription = "This app requires access to your photo library to allow you to select a photo for your profile picture.";

// Microphone permission
NSMicrophoneUsageDescription = "This app requires access to your microphone to recognize voice commands and interact with the Xiaopa device.";

// Location permission (When In Use)
NSLocationWhenInUseUsageDescription = "This app requires access to your location to help Bluetooth discover nearby Xiaopa devices for network setup.";

// Location permission (Always)
NSLocationAlwaysAndWhenInUseUsageDescription = "This app requires access to your location even in the background to help Blue<PERSON> discover nearby Xiaopa devices for network setup.";

// Bluetooth permission
NSBluetoothAlwaysUsageDescription = "This app requires access to Bluetooth to scan for and connect to nearby Xiaopa devices for network setup.";
NSBluetoothPeripheralUsageDescription = "This app requires access to Bluetooth to scan for and connect to nearby Xiaopa devices for network setup.";
