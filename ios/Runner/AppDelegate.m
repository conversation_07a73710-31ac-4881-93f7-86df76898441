#import "AppDelegate.h"
#import "GeneratedPluginRegistrant.h"
#import "RegisterPlugin.h"
#import "FlutterDownloaderPlugin.h"
#import <XiaoHongShuOpenSDK/XHSApi.h>
#import <WechatOpenSDK/WXApi.h>
#import "CrashHandler.h"

@interface AppDelegate ()<WXApiDelegate>

@end

@implementation AppDelegate

- (BOOL)application:(UIApplication *)application
    didFinishLaunchingWithOptions:(NSDictionary *)launchOptions {
  [GeneratedPluginRegistrant registerWithRegistry:self];
  
  // 完成网络环境等相关依赖配置，只需要调用一次。
  BOOL status =  [SpeechEngine prepareEnvironment];
  if (status) {
     [self setupResourceManager];
  }
      
  [RegisterPlugin registerWithRegistrar:[self registrarForPlugin:@"RegisterPlugin"]];
    
  [FlutterDownloaderPlugin setPluginRegistrantCallback:registerPlugins];

  installCrashHandlers();
    
  return [super application:application didFinishLaunchingWithOptions:launchOptions];
}

- (void) setupResourceManager {
    NSLog(@"初始化模型资源管理器");
    SpeechResourceManager *speechResourceManager = [SpeechResourceManager shareInstance];
    [speechResourceManager setAppId:@""];
    [speechResourceManager setAppVersion:@"1.0.0"];
    [speechResourceManager setDeviceId:@"user_phone"];
    [speechResourceManager setRootPath: [NSSearchPathForDirectoriesInDomains(NSDocumentDirectory, NSUserDomainMask, YES).firstObject stringByAppendingPathComponent:@"models"]];
    [speechResourceManager setOnlineModelEnable:YES];
    [speechResourceManager setup];
}

void registerPlugins(NSObject<FlutterPluginRegistry>* registry) {
  if (![registry hasPlugin:@"FlutterDownloaderPlugin"]) {
     [FlutterDownloaderPlugin registerWithRegistrar:[registry registrarForPlugin:@"FlutterDownloaderPlugin"]];
  }
}

/// iOS 13 前会走此方法
/// iOS13 之后会走 SceneDelegate 中的方法：
/// - (void)scene:(UIScene *)scene openURLContexts:(NSSet<UIOpenURLContext *> *)URLContexts
- (BOOL)application:(UIApplication *)application openURL:(nonnull NSURL *)url options:(nonnull NSDictionary<UIApplicationOpenURLOptionsKey,id> *)options {

    if ([XHSApi handleOpenURL:url] || [WXApi handleOpenURL:url delegate:self]) {
        return YES;
    }

    return YES;
}

/// iOS 13 前会走此方法
/// iOS 13v之后会走 SceneDelegate 中的方法：
/// - (void)scene:(UIScene *)scene continueUserActivity:(NSUserActivity *)userActivity
- (BOOL)application:(UIApplication *)application continueUserActivity:(NSUserActivity *)userActivity restorationHandler:(void(^)(NSArray<id<UIUserActivityRestoring>> * __nullable restorableObjects))restorationHandler {
    
    if ([XHSApi handleOpenUniversalLink:userActivity] || [WXApi handleOpenUniversalLink:userActivity delegate:self]) {
        return YES;
    }

    return YES;
}

@end
