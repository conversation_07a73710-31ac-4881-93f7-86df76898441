//
//  CrashHandler.m
//  Runner
//
//  Created by songguo on 2025/7/8.
//

#import "CrashHandler.h"
#import <Foundation/Foundation.h>
#import <signal.h>
#import <unistd.h>
#include <execinfo.h>

@implementation CrashHandler

static NSString* crashLogPath(void) {
    NSArray *dirs = NSSearchPathForDirectoriesInDomains(NSDocumentDirectory, NSUserDomainMask, YES);
    return [dirs[0] stringByAppendingPathComponent:@"crash_log.txt"];
}

// 异步信号安全的 crash 日志函数（使用低级 C 写入日志文件）
void writeNativeCrashLog(int signalCode) {
    // 当前时间
    time_t rawtime;
    struct tm *timeinfo;
    char timeStr[64];
    time(&rawtime);
    timeinfo = localtime(&rawtime);
    strftime(timeStr, sizeof(timeStr), "%Y-%m-%d %H:%M:%S", timeinfo);

    // 头部信息
    char header[512];
    snprintf(header, sizeof(header),
             "\n\n🚨 iOS Native Crash\n"
             "Time: %s\n"
             "Signal: %d\n"
             "Backtrace:\n",
             timeStr, signalCode);

    // 获取堆栈信息
    void *callstack[128];
    int frames = backtrace(callstack, 128);
    char **symbols = backtrace_symbols(callstack, frames);

    // 写入文件
    NSString *path = crashLogPath();
    int fd = open([path UTF8String], O_CREAT | O_WRONLY | O_APPEND, 0644);
    if (fd != -1) {
        write(fd, header, strlen(header));
        for (int i = 0; i < frames; i++) {
            write(fd, symbols[i], strlen(symbols[i]));
            write(fd, "\n", 1);
        }
        close(fd);
    }
 
    free(symbols);
    signal(signalCode, SIG_DFL);
    kill(getpid(), signalCode);
}

void installCrashHandlers(void) {
    signal(SIGABRT, writeNativeCrashLog);
    signal(SIGILL,  writeNativeCrashLog);
    signal(SIGSEGV, writeNativeCrashLog);
    signal(SIGFPE,  writeNativeCrashLog);
    signal(SIGBUS,  writeNativeCrashLog);
    signal(SIGPIPE, writeNativeCrashLog);
}

@end
