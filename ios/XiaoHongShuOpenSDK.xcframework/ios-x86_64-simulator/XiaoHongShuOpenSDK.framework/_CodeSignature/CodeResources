<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>Headers/XHSApi.h</key>
		<data>
		4/IKUT0vjIcmLIBx2MAu3x4QG0Q=
		</data>
		<key>Headers/XHSApiObject.h</key>
		<data>
		ePSh2ML91BCqtjV5fW5kgq8VrYk=
		</data>
		<key>Headers/XiaoHongShuOpenSDK-umbrella.h</key>
		<data>
		72p6D8qU8hw4+xS8xvZy93306vg=
		</data>
		<key>Headers/XiaoHongShuOpenSDK.h</key>
		<data>
		tfMefDEn66KuFw6RhE4XSB4HJsE=
		</data>
		<key>Info.plist</key>
		<data>
		tpPYi+jURNkWgb2Wb/KgLi0UZms=
		</data>
		<key>Modules/module.modulemap</key>
		<data>
		6FUw8Qc4Dxp8LOPm+vm1LqEwPU0=
		</data>
	</dict>
	<key>files2</key>
	<dict>
		<key>Headers/XHSApi.h</key>
		<dict>
			<key>hash</key>
			<data>
			4/IKUT0vjIcmLIBx2MAu3x4QG0Q=
			</data>
			<key>hash2</key>
			<data>
			SFMXBXd2ViKayQeUHO+YrfbeftfNoZRPCVx+OZn7AZE=
			</data>
		</dict>
		<key>Headers/XHSApiObject.h</key>
		<dict>
			<key>hash</key>
			<data>
			ePSh2ML91BCqtjV5fW5kgq8VrYk=
			</data>
			<key>hash2</key>
			<data>
			X8G3TN8gbOQVwOfOWBPOWJxoHNpRp2oIsRxdjM2aPHA=
			</data>
		</dict>
		<key>Headers/XiaoHongShuOpenSDK-umbrella.h</key>
		<dict>
			<key>hash</key>
			<data>
			72p6D8qU8hw4+xS8xvZy93306vg=
			</data>
			<key>hash2</key>
			<data>
			6ptG9e21BZdRaF1Mws3L2HrofmMyqpTCaqbZdCOE1kI=
			</data>
		</dict>
		<key>Headers/XiaoHongShuOpenSDK.h</key>
		<dict>
			<key>hash</key>
			<data>
			tfMefDEn66KuFw6RhE4XSB4HJsE=
			</data>
			<key>hash2</key>
			<data>
			obdfkLA5tX6u+AK2112LNPHrxrl2o9/HGrM+YRcevXI=
			</data>
		</dict>
		<key>Modules/module.modulemap</key>
		<dict>
			<key>hash</key>
			<data>
			6FUw8Qc4Dxp8LOPm+vm1LqEwPU0=
			</data>
			<key>hash2</key>
			<data>
			ySbutt9dlYjxhkve5tkOT2yKQUBBQFo7+zNjqwHZBFI=
			</data>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
