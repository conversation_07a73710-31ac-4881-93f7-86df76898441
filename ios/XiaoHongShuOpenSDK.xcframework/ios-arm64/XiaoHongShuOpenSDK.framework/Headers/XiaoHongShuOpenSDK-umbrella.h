#ifdef __OBJC__
#import <UIKit/UIKit.h>
#else
#ifndef FOUNDATION_EXPORT
#if defined(__cplusplus)
#define FOUNDATION_EXPORT extern "C"
#else
#define FOUNDATION_EXPORT extern
#endif
#endif
#endif

#import <XiaoHongShuOpenSDK/XHSApi.h>
#import <XiaoHongShuOpenSDK/XHSApiObject.h>
#import <XiaoHongShuOpenSDK/XiaoHongShuOpenSDK.h>
#import <XiaoHongShuOpenSDK/XHSApi.h>
#import <XiaoHongShuOpenSDK/XHSApiObject.h>
#import <XiaoHongShuOpenSDK/XiaoHongShuOpenSDK.h>

FOUNDATION_EXPORT double XiaoHongShuOpenSDKVersionNumber;
FOUNDATION_EXPORT const unsigned char XiaoHongShuOpenSDKVersionString[];

