PODS:
  - audio_session (0.0.1):
    - Flutter
  - device_info_plus (0.0.1):
    - Flutter
  - DKImagePickerController/Core (4.3.9):
    - DKImagePickerController/ImageDataManager
    - DKImagePickerController/Resource
  - DKImagePickerController/ImageDataManager (4.3.9)
  - DKImagePickerController/PhotoGallery (4.3.9):
    - DKImagePickerController/Core
    - DKPhotoGallery
  - DKImagePickerController/Resource (4.3.9)
  - DKPhotoGallery (0.0.19):
    - DKPhotoGallery/Core (= 0.0.19)
    - DKPhotoGallery/Model (= 0.0.19)
    - DKPhotoGallery/Preview (= 0.0.19)
    - DKPhotoGallery/Resource (= 0.0.19)
    - SDWebImage
    - SwiftyGif
  - DKPhotoGallery/Core (0.0.19):
    - DKPhotoGallery/Model
    - DKPhotoGallery/Preview
    - SDWebImage
    - SwiftyGif
  - DKPhotoGallery/Model (0.0.19):
    - SDWebImage
    - SwiftyGif
  - DKPhotoGallery/Preview (0.0.19):
    - DKPhotoGallery/Model
    - DKPhotoGallery/Resource
    - SDWebImage
    - SwiftyGif
  - DKPhotoGallery/Resource (0.0.19):
    - SDWebImage
    - SwiftyGif
  - file_picker (0.0.1):
    - DKImagePickerController/PhotoGallery
    - Flutter
  - Flutter (1.0.0)
  - flutter_downloader (0.0.1):
    - Flutter
  - fluttertoast (0.0.2):
    - Flutter
  - image_cropper (0.0.4):
    - Flutter
    - TOCropViewController (~> 2.7.4)
  - image_gallery_saver (2.0.2):
    - Flutter
  - in_app_purchase_storekit (0.0.1):
    - Flutter
    - FlutterMacOS
  - just_audio (0.0.1):
    - Flutter
    - FlutterMacOS
  - open_file_ios (0.0.1):
    - Flutter
  - OpenSSL-Universal (3.3.3001)
  - package_info_plus (0.4.5):
    - Flutter
  - path_provider_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - permission_handler_apple (9.3.0):
    - Flutter
  - photo_manager (3.7.1):
    - Flutter
    - FlutterMacOS
  - record_ios (1.0.0):
    - Flutter
  - restart_app (0.0.1):
    - Flutter
  - SDWebImage (5.21.1):
    - SDWebImage/Core (= 5.21.1)
  - SDWebImage/Core (5.21.1)
  - sensors_plus (0.0.1):
    - Flutter
  - shared_preferences_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - SpeechEngineToB (0.0.4):
    - TTNetworkManager (= 5.0.29.22)
  - sqflite_darwin (0.0.4):
    - Flutter
    - FlutterMacOS
  - SwiftyGif (5.4.5)
  - TOCropViewController (2.7.4)
  - TTNetworkManager (5.0.29.22)
  - url_launcher_ios (0.0.1):
    - Flutter
  - video_player_avfoundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - volc_engine_rtc (3.58.2):
    - Flutter
    - VolcEngineRTC/Core (= 3.58.1.19400)
    - VolcEngineRTC/RealXBase (= 3.58.1.19400)
  - VolcEngineRTC/Core (3.58.1.19400)
  - VolcEngineRTC/RealXBase (3.58.1.19400)
  - wakelock_plus (0.0.1):
    - Flutter
  - webview_flutter_wkwebview (0.0.1):
    - Flutter
    - FlutterMacOS

DEPENDENCIES:
  - audio_session (from `.symlinks/plugins/audio_session/ios`)
  - device_info_plus (from `.symlinks/plugins/device_info_plus/ios`)
  - file_picker (from `.symlinks/plugins/file_picker/ios`)
  - Flutter (from `Flutter`)
  - flutter_downloader (from `.symlinks/plugins/flutter_downloader/ios`)
  - fluttertoast (from `.symlinks/plugins/fluttertoast/ios`)
  - image_cropper (from `.symlinks/plugins/image_cropper/ios`)
  - image_gallery_saver (from `.symlinks/plugins/image_gallery_saver/ios`)
  - in_app_purchase_storekit (from `.symlinks/plugins/in_app_purchase_storekit/darwin`)
  - just_audio (from `.symlinks/plugins/just_audio/darwin`)
  - open_file_ios (from `.symlinks/plugins/open_file_ios/ios`)
  - OpenSSL-Universal
  - package_info_plus (from `.symlinks/plugins/package_info_plus/ios`)
  - path_provider_foundation (from `.symlinks/plugins/path_provider_foundation/darwin`)
  - permission_handler_apple (from `.symlinks/plugins/permission_handler_apple/ios`)
  - photo_manager (from `.symlinks/plugins/photo_manager/ios`)
  - record_ios (from `.symlinks/plugins/record_ios/ios`)
  - restart_app (from `.symlinks/plugins/restart_app/ios`)
  - sensors_plus (from `.symlinks/plugins/sensors_plus/ios`)
  - shared_preferences_foundation (from `.symlinks/plugins/shared_preferences_foundation/darwin`)
  - SpeechEngineToB (= 0.0.4)
  - sqflite_darwin (from `.symlinks/plugins/sqflite_darwin/darwin`)
  - url_launcher_ios (from `.symlinks/plugins/url_launcher_ios/ios`)
  - video_player_avfoundation (from `.symlinks/plugins/video_player_avfoundation/darwin`)
  - volc_engine_rtc (from `.symlinks/plugins/volc_engine_rtc/ios`)
  - wakelock_plus (from `.symlinks/plugins/wakelock_plus/ios`)
  - webview_flutter_wkwebview (from `.symlinks/plugins/webview_flutter_wkwebview/darwin`)

SPEC REPOS:
  https://github.com/volcengine/volcengine-specs.git:
    - SpeechEngineToB
    - TTNetworkManager
    - VolcEngineRTC
  trunk:
    - DKImagePickerController
    - DKPhotoGallery
    - OpenSSL-Universal
    - SDWebImage
    - SwiftyGif
    - TOCropViewController

EXTERNAL SOURCES:
  audio_session:
    :path: ".symlinks/plugins/audio_session/ios"
  device_info_plus:
    :path: ".symlinks/plugins/device_info_plus/ios"
  file_picker:
    :path: ".symlinks/plugins/file_picker/ios"
  Flutter:
    :path: Flutter
  flutter_downloader:
    :path: ".symlinks/plugins/flutter_downloader/ios"
  fluttertoast:
    :path: ".symlinks/plugins/fluttertoast/ios"
  image_cropper:
    :path: ".symlinks/plugins/image_cropper/ios"
  image_gallery_saver:
    :path: ".symlinks/plugins/image_gallery_saver/ios"
  in_app_purchase_storekit:
    :path: ".symlinks/plugins/in_app_purchase_storekit/darwin"
  just_audio:
    :path: ".symlinks/plugins/just_audio/darwin"
  open_file_ios:
    :path: ".symlinks/plugins/open_file_ios/ios"
  package_info_plus:
    :path: ".symlinks/plugins/package_info_plus/ios"
  path_provider_foundation:
    :path: ".symlinks/plugins/path_provider_foundation/darwin"
  permission_handler_apple:
    :path: ".symlinks/plugins/permission_handler_apple/ios"
  photo_manager:
    :path: ".symlinks/plugins/photo_manager/ios"
  record_ios:
    :path: ".symlinks/plugins/record_ios/ios"
  restart_app:
    :path: ".symlinks/plugins/restart_app/ios"
  sensors_plus:
    :path: ".symlinks/plugins/sensors_plus/ios"
  shared_preferences_foundation:
    :path: ".symlinks/plugins/shared_preferences_foundation/darwin"
  sqflite_darwin:
    :path: ".symlinks/plugins/sqflite_darwin/darwin"
  url_launcher_ios:
    :path: ".symlinks/plugins/url_launcher_ios/ios"
  video_player_avfoundation:
    :path: ".symlinks/plugins/video_player_avfoundation/darwin"
  volc_engine_rtc:
    :path: ".symlinks/plugins/volc_engine_rtc/ios"
  wakelock_plus:
    :path: ".symlinks/plugins/wakelock_plus/ios"
  webview_flutter_wkwebview:
    :path: ".symlinks/plugins/webview_flutter_wkwebview/darwin"

SPEC CHECKSUMS:
  audio_session: 19e9480dbdd4e5f6c4543826b2e8b0e4ab6145fe
  device_info_plus: 97af1d7e84681a90d0693e63169a5d50e0839a0d
  DKImagePickerController: 946cec48c7873164274ecc4624d19e3da4c1ef3c
  DKPhotoGallery: b3834fecb755ee09a593d7c9e389d8b5d6deed60
  file_picker: b159e0c068aef54932bb15dc9fd1571818edaf49
  Flutter: e0871f40cf51350855a761d2e70bf5af5b9b5de7
  flutter_downloader: b7301ae057deadd4b1650dc7c05375f10ff12c39
  fluttertoast: 21eecd6935e7064cc1fcb733a4c5a428f3f24f0f
  image_cropper: e0bb0042e4404ff2ef134e5cf0492cbd892156cd
  image_gallery_saver: cb43cc43141711190510e92c460eb1655cd343cb
  in_app_purchase_storekit: a1ce04056e23eecc666b086040239da7619cd783
  just_audio: a42c63806f16995daf5b219ae1d679deb76e6a79
  open_file_ios: 461db5853723763573e140de3193656f91990d9e
  OpenSSL-Universal: 6082b0bf950e5636fe0d78def171184e2b3899c2
  package_info_plus: c0502532a26c7662a62a356cebe2692ec5fe4ec4
  path_provider_foundation: 2b6b4c569c0fb62ec74538f866245ac84301af46
  permission_handler_apple: 9878588469a2b0d0fc1e048d9f43605f92e6cec2
  photo_manager: 81954a1bf804b6e882d0453b3b6bc7fad7b47d3d
  record_ios: 1bbc430ab8406174d70332f23e06a3dc751239b4
  restart_app: 806659942bf932f6ce51c5372f91ce5e81c8c14a
  SDWebImage: f29024626962457f3470184232766516dee8dfea
  sensors_plus: 7229095999f30740798f0eeef5cd120357a8f4f2
  shared_preferences_foundation: fcdcbc04712aee1108ac7fda236f363274528f78
  SpeechEngineToB: a49185c07a099cdc052de97218bc10dc4ff60152
  sqflite_darwin: 5a7236e3b501866c1c9befc6771dfd73ffb8702d
  SwiftyGif: 706c60cf65fa2bc5ee0313beece843c8eb8194d4
  TOCropViewController: 80b8985ad794298fb69d3341de183f33d1853654
  TTNetworkManager: 47d93100d944e2ae807e035d8636df92fd5cc390
  url_launcher_ios: 5334b05cef931de560670eeae103fd3e431ac3fe
  video_player_avfoundation: 7c6c11d8470e1675df7397027218274b6d2360b3
  volc_engine_rtc: b601b46842d23f3e4a74747c1b528b73c4c83803
  VolcEngineRTC: f7b279b5fa3ee0d09881585218f0d1dcecc92dbf
  wakelock_plus: 76957ab028e12bfa4e66813c99e46637f367fc7e
  webview_flutter_wkwebview: a4af96a051138e28e29f60101d094683b9f82188

PODFILE CHECKSUM: 4526ad7ed5f59556344bfde2576adec568706699

COCOAPODS: 1.16.2
