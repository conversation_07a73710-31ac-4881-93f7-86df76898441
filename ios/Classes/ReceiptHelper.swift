//
//  ReceiptHelper.swift
//  Runner
//
//  Created by songguo on 2025/7/25.
//

import Foundation
import StoreKit
import CommonCrypto
import Flutter

@objc class ReceiptHelper: NSObject , SKRequestDelegate{
    
    private var completion: ((_ receipt: String?, _ error: NSError?) -> Void)?
    private var backgroundTask: UIBackgroundTaskIdentifier = .invalid
    
    private var maxRetryCount = 5
    private var retryDelay: TimeInterval = 2.0
    private var retryCount = 0
    private var cachedReceiptHash: String?
    
    @objc static let shared = ReceiptHelper()
    
    // MARK: - 外部调用入口
    /// 调用此方法来获取最新 receipt，如果是购买后调用，推荐设置 `forceRetryUntilChanged: true`
    @objc func fetchLatestReceipt(forceRetryUntilChanged: Bool = false,
                                  completion: @escaping (_ receipt: String?, _ error: NSError?) -> Void) {
        self.completion = completion
        self.cachedReceiptHash = getCurrentReceiptHash()

        // 开启后台任务
        backgroundTask = UIApplication.shared.beginBackgroundTask(withName: "SKReceiptRefresh") {
            UIApplication.shared.endBackgroundTask(self.backgroundTask)
            self.backgroundTask = .invalid
        }

        retryCount = 0
        maxRetryCount = forceRetryUntilChanged ? 5 : 2
        refreshReceipt()
    }

    /// 执行刷新请求
    private func refreshReceipt() {
        let request = SKReceiptRefreshRequest()
        request.delegate = self
        request.start()
    }
    
    // MARK: - SKRequestDelegate
    func requestDidFinish(_ request: SKRequest) {
        guard let receiptURL = Bundle.main.appStoreReceiptURL,
              let data = try? Data(contentsOf: receiptURL) else {
            handleCompletion(nil, error: NSError(domain: "Receipt", code: 0, userInfo: [NSLocalizedDescriptionKey: "No receipt found"]))
            return
        }

        let receipt = data.base64EncodedString()
        let newHash = sha256(data: data)
        if let cached = cachedReceiptHash, cached == newHash {
            // Receipt 未变更，考虑再次尝试
            if retryCount < maxRetryCount {
                retryCount += 1
                DispatchQueue.main.asyncAfter(deadline: .now() + retryDelay) {
                  
                    self.refreshReceipt()
                }
            } else {
                handleCompletion(receipt, error: nil)
            }
        } else {
            // Receipt 有更新
            handleCompletion(receipt, error: nil)
        }
    }

    func request(_ request: SKRequest, didFailWithError error: Error) {
        handleCompletion(nil, error: error as NSError)
    }

    private func handleCompletion(_ receipt: String?, error: NSError?) {
        // 结束后台任务
        UIApplication.shared.endBackgroundTask(backgroundTask)
        backgroundTask = .invalid

        completion?(receipt, error)
        completion = nil
    }

    private func getCurrentReceiptHash() -> String? {
        guard let receiptURL = Bundle.main.appStoreReceiptURL,
              let data = try? Data(contentsOf: receiptURL) else {
            return nil
        }
        return sha256(data: data)
    }

    private func sha256(data: Data) -> String {
        var hash = [UInt8](repeating: 0, count: Int(CC_SHA256_DIGEST_LENGTH))
        data.withUnsafeBytes {
            _ = CC_SHA256($0.baseAddress, CC_LONG(data.count), &hash)
        }
        return hash.map { String(format: "%02x", $0) }.joined()
    }
}
