//
//  ByteSpeechPlugin.h
//  Runner
//
//  Created by bodk on 2024/9/2.
//

#import <Flutter/Flutter.h>
#import <Foundation/Foundation.h>
#import "ByteTtsPlugin/ByteTtsPlugin.h"
#import "ByteAsrPlugin/ByteAsrPlugin.h"
#import "EspBlufiUtil/EspBlufiUtil.h"
#import "XhsUtil/XhsUtil.h"
#import "WxUtil/WxUtil.h"

NS_ASSUME_NONNULL_BEGIN

@interface RegisterPlugin : NSObject<FlutterPlugin, ByteTtsPluginDelegate, ByteAsrPluginDelegate, ByteVolumePluginDelegate, EspBlufiUtilDelegate,XhsUtilDelegate>

@property(nonatomic,strong) ByteTtsPlugin *byteTtsPlugin;
@property(nonatomic,strong) ByteAsrPlugin *byteAsrPlugin;

@property(nonatomic,strong) EspBlufiUtil *espBlufiUtil;
@property(nonatomic,strong) XhsUtil *xhsUtil;
@property(nonatomic,strong) WxUtil *wxUtil;

@end

NS_ASSUME_NONNULL_END
