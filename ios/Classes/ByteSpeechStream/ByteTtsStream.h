//
//  ByteSpeechStream.h
//  Runner
//
//  Created by bodk on 2024/7/19.
//

#import <Foundation/Foundation.h>
#import <Flutter/Flutter.h>

NS_ASSUME_NONNULL_BEGIN
@class ByteTtsStreamHanlder;
@interface ByteTtsStream : NSObject
+ (instancetype)sharedInstance ;
@property (nonatomic, strong) ByteTtsStreamHanlder* byteTtsStreamHandler;
@end

@interface ByteTtsStreamHanlder : NSObject<FlutterStreamHandler>
@property (nonatomic, strong,nullable) FlutterEventSink byteTtsEventSink;
@end

NS_ASSUME_NONNULL_END
