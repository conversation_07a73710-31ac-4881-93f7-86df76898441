//
//  ByteVolumeStream.m
//  Runner
//
//  Created by bodk on 2024/9/2.
//

#import "ByteVolumeStream.h"

@implementation ByteVolumeStream

+ (instancetype)sharedInstance {
    static dispatch_once_t onceToken;
    static ByteVolumeStream *manager = nil;
    dispatch_once(&onceToken, ^{
        manager = [[ByteVolumeStream alloc] init];
        ByteVolumeStreamHanlder * streamHandler = [[ByteVolumeStreamHanlder alloc] init];
        manager.byteVolumeStreamHandler = streamHandler;
    });
    
    return manager;
}

@end

@implementation ByteVolumeStreamHanlder

- (FlutterError*)onListenWithArguments:(id)arguments eventSink:(FlutterEventSink)eventSink {
    self.byteVolumeEventSink = eventSink;
    return nil;
}

- (FlutterError*)onCancelWithArguments:(id)arguments {
    self.byteVolumeEventSink = nil;
    return nil;
}

@end
