//
//  ByteSpeechStream.m
//  Runner
//
//  Created by bodk on 2024/7/19.
//

#import "ByteTtsStream.h"

@implementation ByteTtsStream

+ (instancetype)sharedInstance {
    static dispatch_once_t onceToken;
    static ByteTtsStream *manager = nil;
    dispatch_once(&onceToken, ^{
        manager = [[ByteTtsStream alloc] init];
        ByteTtsStreamHanlder * streamHandler = [[ByteTtsStreamHanlder alloc] init];
        manager.byteTtsStreamHandler = streamHandler;
    });
    
    return manager;
}

@end

@implementation ByteTtsStreamHanlder

- (FlutterError*)onListenWithArguments:(id)arguments eventSink:(FlutterEventSink)eventSink {
    self.byteTtsEventSink = eventSink;
    return nil;
}

- (FlutterError*)onCancelWithArguments:(id)arguments {
    self.byteTtsEventSink = nil;
    return nil;
}

@end
