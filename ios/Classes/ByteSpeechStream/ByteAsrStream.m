//
//  ByteAsrStream.m
//  Runner
//
//  Created by bodk on 2024/9/2.
//

#import "ByteAsrStream.h"

@implementation ByteAsrStream

+ (instancetype)sharedInstance {
    static dispatch_once_t onceToken;
    static ByteAsrStream *manager = nil;
    dispatch_once(&onceToken, ^{
        manager = [[ByteAsrStream alloc] init];
        ByteAsrStreamHanlder * streamHandler = [[ByteAsrStreamHanlder alloc] init];
        manager.byteAsrStreamHandler = streamHandler;
    });
    
    return manager;
}

@end

@implementation ByteAsrStreamHanlder

- (FlutterError*)onListenWithArguments:(id)arguments eventSink:(FlutterEventSink)eventSink {
    self.byteAsrEventSink = eventSink;
    return nil;
}

- (FlutterError*)onCancelWithArguments:(id)arguments {
    self.byteAsrEventSink = nil;
    return nil;
}

@end
