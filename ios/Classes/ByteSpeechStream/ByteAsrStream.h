//
//  ByteAsrStream.h
//  Runner
//
//  Created by bodk on 2024/9/2.
//

#import <Foundation/Foundation.h>
#import <Flutter/Flutter.h>

NS_ASSUME_NONNULL_BEGIN
@class ByteAsrStreamHanlder;
@interface ByteAsrStream : NSObject
+ (instancetype)sharedInstance ;
@property (nonatomic, strong) ByteAsrStreamHanlder* byteAsrStreamHandler;
@end

@interface ByteAsrStreamHanlder : NSObject<FlutterStreamHandler>
@property (nonatomic, strong, nullable) FlutterEventSink byteAsrEventSink;
@end

NS_ASSUME_NONNULL_END
