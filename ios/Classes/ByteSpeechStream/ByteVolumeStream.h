//
//  ByteVolumeStream.h
//  Runner
//
//  Created by bodk on 2024/9/2.
//

#import <Foundation/Foundation.h>
#import <Flutter/Flutter.h>

NS_ASSUME_NONNULL_BEGIN
@class ByteVolumeStreamHanlder;
@interface ByteVolumeStream : NSObject
+ (instancetype)sharedInstance ;
@property (nonatomic, strong) ByteVolumeStreamHanlder* byteVolumeStreamHandler;
@end

@interface ByteVolumeStreamHanlder : NSObject<FlutterStreamHandler>
@property (nonatomic, strong, nullable) FlutterEventSink byteVolumeEventSink;
@end

NS_ASSUME_NONNULL_END
