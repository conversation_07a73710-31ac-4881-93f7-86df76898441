//
//  ByteTtsPlugun.m
//  Runner
//
//  Created by bodk on 2024/7/22.
//

#import "ByteTtsPlugin.h"
#import "AppDelegate.h"

#import <AVFoundation/AVFoundation.h>

static int TTS_MAX_RETRY_COUNT = 3;

@interface ByteTtsPlugin()<SpeechEngineDelegate>


// Debug Path: 用于存放一些 SDK 相关的文件，比如模型、日志等
@property (strong, nonatomic) NSString *debugPath;
// SpeechEngine
@property (strong, nonatomic) SpeechEngine *curEngine;

// Engine State
@property (assign, nonatomic) BOOL engineInited;
@property (assign, nonatomic) BOOL engineStarted;
@property (assign, nonatomic) BOOL engineErrorOccurred;
@property (assign, nonatomic) BOOL playerPaused;

// 一些在线合成的配置
@property (strong, nonatomic) NSString *mCurAppId;
@property (strong, nonatomic) NSString *mCurToken;
@property (strong, nonatomic) NSString *mCurCluster;
@property (strong, nonatomic) NSString *mCurAddress;
@property (strong, nonatomic) NSString *mCurUri;
@property (assign, nonatomic) double mCurSpeakSpeek;

@property (strong, nonatomic) NSString *mCurVoiceOnline;
@property (strong, nonatomic) NSString *mCurVoiceTypeOnline;

@property (assign, nonatomic) int ttsSynthesisIndex;
@property (strong, nonatomic) NSMutableArray* ttsSynthesisText;
@property (assign, nonatomic) int ttsPlayingIndex;
@property (assign, nonatomic) double ttsPlayingProgress;
@property (strong, nonatomic) NSMutableDictionary* ttsSynthesisMap;

// 一些离线合成的配置
@property (strong, nonatomic) NSString *ttsVoiceOffline;
@property (strong, nonatomic) NSString *ttsVoiceTypeOffline;

@property (assign, nonatomic) BOOL ttsSynthesisFromPlayer;

@property (assign, nonatomic) int ttsRetryCount;

@property(assign,nonatomic) BOOL stopSynthesis;

@end



@implementation ByteTtsPlugin

+(instancetype)sharedInstance:(NSString *)appId token:(NSString *)token cluster:(NSString *)cluster {
    static dispatch_once_t onceToken;
    static ByteTtsPlugin *manager = nil;
    dispatch_once(&onceToken, ^{
        manager = [[ByteTtsPlugin alloc] init];
        manager.mCurAppId = appId;
        manager.mCurToken = token;
        manager.mCurCluster = cluster;
        manager.mCurAddress = @"wss://openspeech.bytedance.com";
        manager.mCurUri = @"/api/v1/tts/ws_binary";
        manager.mCurSpeakSpeek = 1.0;
    });
    return manager;
}

- (instancetype)init
{
    self = [super init];
    if (self) {
        self.engineInited = FALSE;
        self.engineStarted = FALSE;
        self.engineErrorOccurred = FALSE;
        self.playerPaused = FALSE;
        
        // 初始化和小说模式有关的字段
        self.stopSynthesis = FALSE;
        self.ttsSynthesisFromPlayer = FALSE;
        self.ttsSynthesisIndex = 0;
        self.ttsPlayingIndex = -1;
        self.ttsPlayingProgress = 0.0;
        self.ttsSynthesisText = [[NSMutableArray alloc] init];
        self.ttsSynthesisMap = [[NSMutableDictionary alloc]init];
        
        self.ttsRetryCount = TTS_MAX_RETRY_COUNT;
        
        self.debugPath = NSSearchPathForDirectoriesInDomains(NSDocumentDirectory, NSUserDomainMask, YES).firstObject;
        NSLog(@"当前调试路径 %@", self.debugPath);
        [[NSNotificationCenter defaultCenter] addObserver:self
                                                 selector:@selector(audioInterruptionHandler:)
                                                     name:AVAudioSessionInterruptionNotification
                                                   object:nil];
    }
    return self;
}


/**
 * 火山初始化SDK
 */
-(void)initEngine {
  
    NSLog(@"创建引擎");
    if (self.curEngine == nil) {
        self.curEngine = [[SpeechEngine alloc] init];
        if (![self.curEngine createEngineWithDelegate:self]) {
            NSLog(@"引擎创建失败.");
            return;
        }
    }
    NSLog(@"SDK 版本号: %@", [self.curEngine getVersion]);

    if ([self getTtsWorkMode] == SETtsWorkModeOnline || [self getTtsWorkMode] == SETtsWorkModeFile) {
        // 当使用纯在线模式时，不需要下载离线合成所需资源
        [self initEngineInternal];
    } else {
//        // 下载离线合成所需资源需要区分多音色资源和单音色资源，下载这两种资源所调用的方法略有不同
//        if ([[self.settings getOptionsValue:SETTING_TTS_OFFLINE_RESOURCE_FORMAT] isEqual: @"MultipleVoice"]) {
//            // 多音色资源是指一个资源文件中包含了多个离线音色，这种资源一般是旧版(V2)离线合成所用资源
//            NSLog(@"当前所用资源类别为多音色资源，开始准备多音色资源");
//            [self prepareMultipleVoiceResource];
//        } else if ([[self.settings getOptionsValue:SETTING_TTS_OFFLINE_RESOURCE_FORMAT] isEqual: @"SingleVoice"]) {
//            // 单音色资源是指一个资源文件仅包含一个离线音色，新版(V4 及以上)离线合成用的就是单音色资源
//            NSLog(@"当前所用资源类别为单音色资源，开始准备单音色资源");
//            [self prepareSingleVoiceResource];
//        }
    }
}
#pragma mark - Notifications

-(void)appWillTerminate:(NSNotification*)note {
    [[NSNotificationCenter defaultCenter] removeObserver:self
                                                    name:AVAudioSessionInterruptionNotification
                                                  object:nil];
}

- (void)audioInterruptionHandler:(NSNotification*)notification {
    AVAudioSessionInterruptionType interruptionType = (AVAudioSessionInterruptionType)[[notification.userInfo objectForKey:AVAudioSessionInterruptionTypeKey] unsignedIntegerValue];
    AVAudioSessionInterruptionOptions intertuptionOptions = [[notification.userInfo objectForKey:AVAudioSessionInterruptionOptionKey] unsignedIntValue];
    NSLog(@"Receive audio interruption notification, type: %lu, options: %lu.", (unsigned long)interruptionType, (unsigned long)intertuptionOptions);
    if (interruptionType == AVAudioSessionInterruptionTypeBegan) {
        NSLog(@"Audio session interruption began");
        @synchronized (self) {
            [self pausePlayback];
        }
    } else if (interruptionType == AVAudioSessionInterruptionTypeEnded) {
        @synchronized (self) {
            NSLog(@"Audio session interruption ended");
            if (intertuptionOptions == AVAudioSessionInterruptionOptionShouldResume) {
                AVAudioSession *session = [AVAudioSession sharedInstance];
                AVAudioSessionCategoryOptions cur_options = session.categoryOptions;
                if (!(cur_options & AVAudioSessionCategoryOptionMixWithOthers)) {
                    AVAudioSessionCategoryOptions readyOptions = AVAudioSessionCategoryOptionMixWithOthers | cur_options;
                    [session setCategory:AVAudioSessionCategoryPlayback withOptions:readyOptions error:nil];
                }
                [self resumePlayback];
                
                cur_options = session.categoryOptions;
                // Remove AVAudioSessionCategoryOptionMixWithOthers, or the playback will not be interrupted any more
                if (cur_options & AVAudioSessionCategoryOptionMixWithOthers) {
                    [session setCategory:AVAudioSessionCategoryPlayback withOptions:((~AVAudioSessionCategoryOptionMixWithOthers) & cur_options) error:nil];
                }
            }
        }
    }
}


#pragma mark - Message Callback

- (void)onMessageWithType:(SEMessageType)type andData:(NSData *)data {
    NSLog(@"Message Type: %d.", type);
    switch (type) {
        case SEEngineStart:
            NSLog(@"Callback: 引擎启动成功: data: %@", data);
            [self speechEngineStarted];
            break;
        case SEEngineStop:
            NSLog(@"Callback: 引擎关闭: data: %@", data);
            [self speechEngineStopped];
            break;
        case SEEngineError:
            NSLog(@"Callback: 错误信息: %@", data);
            [self speechEngineError:data];
            break;
        case SETtsSynthesisBegin:
            NSLog(@"Callback: 合成开始: %@", data);
            [self speechStartSynthesis:data];
            break;
        case SETtsSynthesisEnd:
            NSLog(@"Callback: 合成结束: %@", data);
            [self speechFinishSynthesis:data];
            break;
        case SETtsStartPlaying:
            NSLog(@"Callback: 播放开始: %@", data);
            if(self.stopSynthesis == FALSE){
                self.stopSynthesis = TRUE;
                [self sendDelegateMessage:@"playStart"];
            }
//            [self speechStartPlaying:data];
            break;
        case SETtsPlaybackProgress:
            NSLog(@"Callback: 播放进度");
//            [self updatePlayingProgress:data];
            break;
        case SETtsFinishPlaying:
            NSLog(@"Callback: 播放结束: %@", data);
            [self speechFinishPlaying:data];
            break;
        case SETtsAudioData:
            NSLog(@"Callback: 音频数据，长度 %lu 字节", (unsigned long)data.length);
//            [self speechTtsAudioData:data];
            break;
        default:
            break;
    }
}

- (void) pausePlayback {
    NSLog(@"暂停播放");
    NSLog(@"Directive: SEDirectivePausePlayer");
    SEEngineErrorCode ret = [self.curEngine sendDirective:SEDirectivePausePlayer];
    if (ret == SENoError) {
        self.playerPaused = TRUE;
    }
    NSLog(@"Pause playback status: %d", ret);
}

- (void) resumePlayback {
    NSLog(@"继续播放");
    NSLog(@"Directive: SEDirectiveResumePlayer");
    SEEngineErrorCode ret = [self.curEngine sendDirective:SEDirectiveResumePlayer];
    if (ret == SENoError) {
        self.playerPaused = FALSE;
    }
    NSLog(@"Resume playback status: %d", ret);
}
- (void)speechEngineStarted {
    self.ttsRetryCount = TTS_MAX_RETRY_COUNT;
    self.engineStarted = TRUE;
}

- (void)speechEngineStopped {
    self.engineStarted = FALSE;
    self.playerPaused = FALSE;
}

- (void)speechEngineError:(NSData *)data {
    dispatch_async(dispatch_get_main_queue(), ^{
        
        BOOL needStop = NO;
        id json_obj = [NSJSONSerialization JSONObjectWithData:data options:NSJSONReadingMutableContainers error:nil];
        if ([json_obj isKindOfClass:[NSDictionary class]]) {
            NSDictionary *error_info = json_obj;
            NSInteger code = [[error_info objectForKey:@"err_code"] intValue];
            switch (code) {
                case SETTSLimitQps:
                case SETTSLimitCount:
                case SETTSServerBusy:
                case SETTSLongText:
                case SETTSInvalidText:
                case SETTSSynthesisTimeout:
                case SETTSSynthesisError:
                case SETTSSynthesisWaitingTimeout:
                case SETTSErrorUnknown:
                    NSLog(@"When meeting this kind of error, continue to synthesize.");
                    [self synthesisNextSentence];
                    break;
                case SEConnectTimeout:
                case SEReceiveTimeout:
                case SENetLibError:
                    // 遇到网络错误时建议重试，重试次数不超过 3 次
                    needStop = ![self retrySynthesis];
                    if (needStop) {
                        self.engineErrorOccurred = TRUE;
                    }
                    break;
                default:
                    needStop = YES;
                    self.engineErrorOccurred = TRUE;
                    break;
            }
            if(self.engineErrorOccurred){
                [self sendDelegateMessage:@"finishPlay"];
            }
        } else {
            needStop = YES;
        }
        if (needStop) {
            [self.curEngine sendDirective:SEDirectiveStopEngine];
        }
    });
}

- (BOOL)retrySynthesis {
    BOOL ret = FALSE;
    if (self.engineStarted && self.ttsRetryCount > 0) {
        NSLog(@"Retry synthesis for text: %@", self.ttsSynthesisText[self.ttsSynthesisIndex]);
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, NSEC_PER_SEC), dispatch_get_main_queue(), ^{
            [self triggerSynthesis];
        });
        self.ttsRetryCount -= 1;
        ret = TRUE;
    }
    return ret;
}

- (void)synthesisNextSentence {
    self.ttsSynthesisIndex = (self.ttsSynthesisIndex + 1) % [self.ttsSynthesisText count];
    if (!self.ttsSynthesisFromPlayer) {
        [self triggerSynthesis];
    }
}

- (void)speechStartSynthesis:(NSData *)data {
    if (self.ttsSynthesisIndex < [self.ttsSynthesisText count]) {
        NSString* req_id = [[NSString alloc] initWithData:data encoding:NSUTF8StringEncoding];
        [self.ttsSynthesisMap setValue:[NSNumber numberWithInt:self.ttsSynthesisIndex] forKey:req_id];
    }
    
}

- (void)speechFinishSynthesis:(NSData *)data {
    if (self.ttsRetryCount < TTS_MAX_RETRY_COUNT) {
        self.ttsRetryCount = TTS_MAX_RETRY_COUNT;
    }
    
    [self synthesisNextSentence];
    
}

- (void)speechStartPlaying:(NSData *)data {
    NSString* playingId = [[NSString alloc] initWithData:data encoding:NSUTF8StringEncoding];
    NSLog(@"TTS start playing: %@", playingId);
}


- (void)speechFinishPlaying :(NSData *)data {
    NSString* playingId = [[NSString alloc] initWithData:data encoding:NSUTF8StringEncoding];
    NSLog(@"TTS finish playing: %@  %lu", playingId,(unsigned long)self.ttsSynthesisMap.count);
   
    [self.ttsSynthesisMap removeObjectForKey:playingId];
    if(self.ttsSynthesisMap.count == 0){
        self.stopSynthesis = FALSE;
        [self sendDelegateMessage:@"finishPlay"];
        [self resetTtsContext];
    }
    
    if (self.ttsSynthesisFromPlayer) {
        [self triggerSynthesis];
        self.ttsSynthesisFromPlayer = FALSE;
    }
}

-(void)sendDelegateMessage:(NSString *)message{
    if([self.byteTtsDelegate respondsToSelector:@selector(handleTtsResult:)]){
        [self.byteTtsDelegate handleTtsResult:message];
    }
}

#pragma mark --------引擎相关--------------

- (void)initEngineInternal {
    NSLog(@"配置初始化参数");
    [self configInitParams];

    NSLog(@"引擎初始化");
    SEEngineErrorCode ret = [self.curEngine initEngine];
    self.engineInited = (ret == SENoError);
    if (self.engineInited) {
        NSLog(@"初始化成功");
        [self speechEngineInitSucceeded];
    } else {
        NSLog(@"初始化失败，返回值: %d", ret);
        [self speechEngineInitFailed:ret];
    }
}

-(void)startEngine{
    NSLog(@"Start engine, current status: %d",self.engineStarted);
    if(!self.engineStarted){
        self.engineErrorOccurred = FALSE;

        // Directive：启动引擎前调用SYNC_STOP指令，保证前一次请求结束。
        NSLog(@"关闭引擎（同步）");
        NSLog(@"Directive: SEDirectiveSyncStopEngine");
        SEEngineErrorCode ret = [self.curEngine sendDirective:SEDirectiveSyncStopEngine];
        if (ret != SENoError) {
            NSLog(@"Send directive syncstop failed: %d", ret);
        } else {
            [self configStartTtsParams];

            NSLog(@"启动引擎.");
            NSLog(@"Directive: SEDirectiveStartEngine");
            SEEngineErrorCode ret = [self.curEngine sendDirective:SEDirectiveStartEngine];
            if (SENoError != ret) {
                NSString* message = [NSString stringWithFormat:@"发送启动引擎指令失败: %d", ret];
                [self sendStartEngineDirectiveFailed:message];
            }
        }
    }
  
}

-(void)stopByteSpeechStream{
    [self stopEngine];
    [self resetTtsContext];
    self.stopSynthesis = FALSE;
    [self sendDelegateMessage:@"finishPlay"];
}

-(void)stopEngine{
    NSLog(@"关闭引擎");
    NSLog(@"Directive: SEDirectiveStopEngine");
    if(self.engineStarted){
        [self.curEngine sendDirective:SEDirectiveStopEngine];
    }
}

- (void)speechEngineInitSucceeded {
    self.engineInited = TRUE;
}

- (void)speechEngineInitFailed:(int)initStatus {
    [self uninitEngine];
}

- (void)uninitEngine {
    if (self.curEngine != nil) {
        NSLog(@"引擎析构");
        [self.curEngine destroyEngine];
        self.curEngine = nil;
        NSLog(@"引擎析构完成");
    }
}

-(void)setYinse:(NSString *)yinse{
    self.mCurVoiceTypeOnline = yinse;
}


-(void)addSentence:(NSString*) text {
    NSCharacterSet* blankChar = [NSCharacterSet characterSetWithCharactersInString:@" "];
    NSString* tmp = [text stringByTrimmingCharactersInSet:blankChar];
    
    NSArray* temp = [tmp componentsSeparatedByCharactersInSet:[NSCharacterSet characterSetWithCharactersInString:@";!?。！？；…,，"]];
    for (int j = 0; j < temp.count; ++j) {
        [self.ttsSynthesisText addObject:temp[j]];
    }
}

-(void)triggerSynthesis {
    if(self.ttsSynthesisIndex == 0 && self.stopSynthesis) return;
    [self configSynthesisParams];
    // DIRECTIVE_SYNTHESIS 是连续合成必需的一个指令，在成功调用 DIRECTIVE_START_ENGINE 之后，每次合成新的文本需要再调用 DIRECTIVE_SYNTHESIS 指令
    // DIRECTIVE_SYNTHESIS 需要在当前没有正在合成的文本时才可以成功调用，否则就会报错 -901，可以在收到 MESSAGE_TYPE_TTS_SYNTHESIS_END 之后调用
    // 当使用 SDK 内置的播放器时，为了避免缓存过多的音频导致内存占用过高，SDK 内部限制缓存的音频数量不超过 5 次合成的结果，
    // 如果 DIRECTIVE_SYNTHESIS 后返回 -902, 就需要在下一次收到 MESSAGE_TYPE_TTS_FINISH_PLAYING 再去调用 MESSAGE_TYPE_TTS_FINISH_PLAYING
    NSLog(@"触发合成");
    NSLog(@"Directive: DIRECTIVE_SYNTHESIS");
    SEEngineErrorCode ret = [self.curEngine sendDirective:SEDirectiveSynthesis];
    if (ret != SENoError) {
        NSLog(@"Synthesis faile: %d", ret);
        if (ret == SESynthesisPlayerIsBusy) {
            self.ttsSynthesisFromPlayer = TRUE;
        } else {
            NSString* message = [NSString stringWithFormat:@"发送合成指令失败: %d", ret];
            [self sendSynthesisDirectiveFailed:message];
        }
    }
}

- (void)sendSynthesisDirectiveFailed:(NSString*)tipText {
    NSLog(@"%@", tipText);
    dispatch_async(dispatch_get_main_queue(), ^{
        [self.curEngine sendDirective:SEDirectiveStopEngine];
    });
}


- (int)getTtsWorkMode {
    switch (0) {
        case 0:
            return SETtsWorkModeOnline;
        case 1:
            return SETtsWorkModeOffline;
        case 2:
            return SETtsWorkModeAlternate;
        default:
            break;
    }
    return SETtsWorkModeOnline;;
}


- (NSString*)getAuthenticationType {
    switch (1) {
        case 0:
            return SE_AUTHENTICATE_TYPE_PRE_BIND;
        case 1:
            return SE_AUTHENTICATE_TYPE_LATE_BIND;
        default:
            break;
    }
    return SE_AUTHENTICATE_TYPE_PRE_BIND;
}

-(void)configStartTtsParams {
    //【必需配置】TTS 使用场景
    [self.curEngine setStringParam:SE_TTS_SCENARIO_TYPE_NOVEL forKey:SE_PARAMS_KEY_TTS_SCENARIO_STRING];

    //【可选配置】是否使用 SDK 内置播放器播放合成出的音频，默认为 true
    [self.curEngine setBoolParam:true
                          forKey:SE_PARAMS_KEY_TTS_ENABLE_PLAYER_BOOL];
    //【可选配置】是否令 SDK 通过回调返回合成的音频数据，默认不返回。
    // 开启后，SDK 会流式返回音频，收到 SETtsAudioData 回调表示当次合成所有的音频已经全部返回
    [self.curEngine setIntParam:SETtsDataCallbackModeNone forKey:SE_PARAMS_KEY_TTS_DATA_CALLBACK_MODE_INT];
}

- (void)sendStartEngineDirectiveFailed:(NSString*)tipText {
    NSLog(@"%@", tipText);
    dispatch_async(dispatch_get_main_queue(), ^{
        self.engineStarted = FALSE;
    });
}

-(void)resetTtsContext {
    self.ttsSynthesisIndex = 0;
    self.ttsPlayingIndex = -1;
    self.ttsSynthesisFromPlayer = FALSE;
    [self.ttsSynthesisText removeAllObjects];
    [self.ttsSynthesisMap removeAllObjects];
}

#pragma mark - Config & Init & Uninit Methods

-(void)configInitParams {
    //【必需配置】Engine Name
    [self.curEngine setStringParam:SE_TTS_ENGINE forKey:SE_PARAMS_KEY_ENGINE_NAME_STRING];

    //【必需配置】Work Mode, 可选值如下
    // SETtsWorkModeOnline, 只进行在线合成，不需要配置离线合成相关参数；
    // SETtsWorkModeOffline, 只进行离线合成，不需要配置在线合成相关参数；
    // SETtsWorkModeAlternate, 先发起在线合成，失败后（网络超时），启动离线合成引擎开始合成；
    [self.curEngine setIntParam:[self getTtsWorkMode] forKey:SE_PARAMS_KEY_TTS_WORK_MODE_INT];

    //【可选配置】Debug & Log
    [self.curEngine setStringParam:self.debugPath forKey:SE_PARAMS_KEY_DEBUG_PATH_STRING];
    [self.curEngine setStringParam:SE_LOG_LEVEL_DEBUG forKey:SE_PARAMS_KEY_LOG_LEVEL_STRING];

    //【可选配置】User ID（用以辅助定位线上用户问题）
    [self.curEngine setStringParam:@"user" forKey:SE_PARAMS_KEY_UID_STRING];
    [self.curEngine setStringParam:@"user_phone" forKey:SE_PARAMS_KEY_DEVICE_ID_STRING];

    //【可选配置】是否将合成出的音频保存到设备上，为 true 时需要正确配置 PARAMS_KEY_TTS_AUDIO_PATH_STRING 才会生效
    [self.curEngine setBoolParam:false
                          forKey:SE_PARAMS_KEY_TTS_ENABLE_DUMP_BOOL];
    // TTS 音频文件保存目录，必须在合成之前创建好且 APP 具有访问权限，保存的音频文件名格式为 tts_{reqid}.wav, {reqid} 是本次合成的请求 id
    // PARAMS_KEY_TTS_ENABLE_DUMP_BOOL 配置为 true 的音频时为【必需配置】，否则为【可选配置】
    [self.curEngine setStringParam:self.debugPath forKey:SE_PARAMS_KEY_TTS_AUDIO_PATH_STRING];

    //【可选配置】合成出的音频的采样率，默认为 24000
    [self.curEngine setIntParam:24000 forKey:SE_PARAMS_KEY_TTS_SAMPLE_RATE_INT];
    //【可选配置】打断播放时使用多长时间淡出停止，单位：毫秒。默认值 0 表示不淡出
    [self.curEngine setIntParam:0 forKey:SE_PARAMS_KEY_AUDIO_FADEOUT_DURATION_INT];
    //【可选配置】是否禁止创建播放器对象，不使用 SDK 内置播放器时可开启，默认为 false. 开启后将 SE_PARAMS_KEY_TTS_ENABLE_PLAYER_BOOL 设置为 true 不起作用。
    [self.curEngine setBoolParam:false forKey:SE_PARAMS_KEY_PREVENT_PLAYER_CREATION_BOOL];
    
    // ------------------------ 在线合成相关配置 -----------------------

    //【必需配置】在线合成鉴权相关：Appid
    [self.curEngine setStringParam:self.mCurAppId forKey:SE_PARAMS_KEY_APP_ID_STRING];

    //【必需配置】在线合成鉴权相关：Token
    [self.curEngine setStringParam:self.mCurToken forKey:SE_PARAMS_KEY_APP_TOKEN_STRING];
    
    //【必需配置】语音合成服务域名
    [self.curEngine setStringParam:self.mCurAddress forKey:SE_PARAMS_KEY_TTS_ADDRESS_STRING];
    
    //【必需配置】语音合成服务Uri
    [self.curEngine setStringParam:self.mCurUri forKey:SE_PARAMS_KEY_TTS_URI_STRING];

    //【必需配置】语音合成服务所用集群
    [self.curEngine setStringParam:self.mCurCluster forKey:SE_PARAMS_KEY_TTS_CLUSTER_STRING];

    // 【可选配置】是否允许在 websocket 建连失败时自动重连
    [self.curEngine setBoolParam:true forKey:SE_PARAMS_KEY_ENABLE_WS_RECONNECT_BOOL];

    //【可选配置】在线合成下发的 opus-ogg 音频的压缩倍率
    [self.curEngine setIntParam:10 forKey:SE_PARAMS_KEY_TTS_COMPRESSION_RATE_INT];

    // ------------------------ 离线合成相关配置 -----------------------

//    if ([self getTtsWorkMode] != SETtsWorkModeOnline && [self getTtsWorkMode] != SETtsWorkModeFile) {
//        NSString* resourcePath = @"";
//        if ([[self.settings getOptionsValue:SETTING_TTS_OFFLINE_RESOURCE_FORMAT] isEqual: @"SingleVoice"]) {
//            resourcePath = [[SpeechResourceManager shareInstance] getModelPath];
//        } else if ([[self.settings getOptionsValue:SETTING_TTS_OFFLINE_RESOURCE_FORMAT] isEqual: @"MultipleVoice"]) {
//            NSString *model_name = [self.settings getString:SETTING_TTS_MODEL_NAME];
//            resourcePath = [[SpeechResourceManager shareInstance] getModelPath:model_name];
//        }
//        NSLog(@"TTS resource root path: %@", resourcePath);
//        //【必需配置】离线合成所需资源存放路径
//        [self.curEngine setStringParam:resourcePath forKey:SE_PARAMS_KEY_TTS_OFF_RESOURCE_PATH_STRING];
//    }
//
//    //【必需配置】离线合成鉴权相关：证书文件存放路径
//    [self.curEngine setStringParam:self.debugPath forKey:SE_PARAMS_KEY_LICENSE_DIRECTORY_STRING];
//    NSString* authenticationType = [self getAuthenticationType];
//    //【必需配置】Authenticate Type
//    [self.curEngine setStringParam:authenticationType forKey:SE_PARAMS_KEY_AUTHENTICATE_TYPE_STRING];
//    if ([authenticationType isEqualToString:SE_AUTHENTICATE_TYPE_PRE_BIND]) {
//        // 按包名授权，获取到授权的 APP 可以不限次数、不限设备数的使用离线合成
//        NSString *licenseName = [self.settings getString:SETTING_LICENSE_NAME];
//        NSString *licenseBusiId = [self.settings getString:SETTING_LICENSE_BUSI_ID];
//        // 证书名和业务 ID, 离线合成鉴权相关，使用火山提供的证书下发服务时为【必需配置】, 否则为【无需配置】
//        // 证书名，用于下载按报名授权的证书文件
//        [self.curEngine setStringParam:licenseName forKey:SE_PARAMS_KEY_LICENSE_NAME_STRING];
//        // 业务 ID, 用于下载按报名授权的证书文件
//        [self.curEngine setStringParam:licenseBusiId forKey:SE_PARAMS_KEY_LICENSE_BUSI_ID_STRING];
//    } else if ([authenticationType isEqualToString:SE_AUTHENTICATE_TYPE_LATE_BIND]) {
//        // 按装机量授权，不限制 APP 的包名和使用次数，但是限制使用离线合成的设备数量
//        //【必需配置】离线合成鉴权相关：Authenticate Address
//        [self.curEngine setStringParam:SDEF_AUTHENTICATE_ADDRESS forKey:SE_PARAMS_KEY_AUTHENTICATE_ADDRESS_STRING];
//        //【必需配置】离线合成鉴权相关：Authenticate Uri
//        [self.curEngine setStringParam:SDEF_AUTHENTICATE_URI forKey:SE_PARAMS_KEY_AUTHENTICATE_URI_STRING];
//        NSString* curBusinessKey = [self.settings getString:SETTING_BUSINESS_KEY];
//        NSString* curAuthenticateSecret = [self.settings getString:SETTING_AUTHENTICATE_SECRET];
//        //【必需配置】离线合成鉴权相关：Business Key
//        [self.curEngine setStringParam:curBusinessKey forKey:SE_PARAMS_KEY_BUSINESS_KEY_STRING];
//        //【必需配置】离线合成鉴权相关：Authenticate Secret
//        [self.curEngine setStringParam:curAuthenticateSecret forKey:SE_PARAMS_KEY_AUTHENTICATE_SECRET_STRING];
//    }
//
//    // ------------------------ 在离线切换相关配置 -----------------------
//    if ([self getTtsWorkMode] == SETtsWorkModeAlternate) {
//        // 断点续播功能在断点处会发生由在线合成音频切换到离线合成音频，为了提升用户体验，SDK 支持
//        // 淡出地停止播放在线音频然后再淡入地开始播放离线音频，下面两个参数可以控制淡出淡入的长度
//        
//        //【可选配置】断点续播专用，切换到离线合成时淡入的音频长度，单位：毫秒
//        [self.curEngine setIntParam:30 forKey:SE_PARAMS_KEY_TTS_FADEIN_DURATION_INT];
//        //【可选配置】断点续播专用，在线合成停止播放时淡出的音频长度，单位：毫秒
//        [self.curEngine setIntParam:30 forKey:SE_PARAMS_KEY_TTS_FADEOUT_DURATION_INT];
//    }
}

- (void)configSynthesisParams {
    NSString* text = self.ttsSynthesisText[self.ttsSynthesisIndex];
    NSLog(@"Synthesis: %d, text: %@", self.ttsSynthesisIndex, text);
    //【必需配置】需合成的文本，不可超过 80 字
    [self.curEngine setStringParam:text forKey:SE_PARAMS_KEY_TTS_TEXT_STRING];
    //【可选配置】需合成的文本的类型，支持直接传文本(TTS_TEXT_TYPE_PLAIN)和传 SSML 形式(TTS_TEXT_TYPE_SSML)的文本
//    [self.curEngine setStringParam:[self getTtsTextType] forKey:SE_PARAMS_KEY_TTS_TEXT_TYPE_STRING];
    //【可选配置】用于控制 TTS 音频的语速，支持的配置范围参考火山官网 语音技术/语音合成/离在线语音合成SDK/参数说明 文档
    [self.curEngine setDoubleParam:self.mCurSpeakSpeek forKey:SE_PARAMS_KEY_TTS_SPEED_RATIO_DOUBLE];
    //【可选配置】用于控制 TTS 音频的音量，支持的配置范围参考火山官网 语音技术/语音合成/离在线语音合成SDK/参数说明 文档
    [self.curEngine setDoubleParam:1.0 forKey:SE_PARAMS_KEY_TTS_VOLUME_RATIO_DOUBLE];
    //【可选配置】用于控制 TTS 音频的音高，支持的配置范围参考火山官网 语音技术/语音合成/离在线语音合成SDK/参数说明 文档
    [self.curEngine setDoubleParam:1.0 forKey:SE_PARAMS_KEY_TTS_PITCH_RATIO_DOUBLE];
    //【可选配置】是否在文本的每句结尾处添加静音段，单位：毫秒，默认为 0ms
    [self.curEngine setIntParam:0 forKey:SE_PARAMS_KEY_TTS_SILENCE_DURATION_INT];

    // ------------------------ 在线合成相关配置 -----------------------

    //【必需配置】在线合成使用的发音人代号
    [self.curEngine setStringParam:self.mCurVoiceOnline forKey:SE_PARAMS_KEY_TTS_VOICE_ONLINE_STRING];
    //【必需配置】在线合成使用的音色代号
    [self.curEngine setStringParam:self.mCurVoiceTypeOnline forKey:SE_PARAMS_KEY_TTS_VOICE_TYPE_ONLINE_STRING];
    //【可选配置】是否打开在线合成的服务端缓存，默认关闭
    [self.curEngine setBoolParam:false forKey:SE_PARAMS_KEY_TTS_ENABLE_CACHE_BOOL];
    //【可选配置】指定在线合成的语种，默认为空，即不指定
    [self.curEngine setStringParam:@"" forKey:SE_PARAMS_KEY_TTS_LANGUAGE_ONLINE_STRING];
    //【可选配置】是否启用在线合成的情感预测功能
    [self.curEngine setBoolParam:true forKey:SE_PARAMS_KEY_TTS_WITH_INTENT_BOOL];
    //【可选配置】指定在线合成的情感，例如 happy, sad 等
    [self.curEngine setStringParam:@"" forKey:SE_PARAMS_KEY_TTS_EMOTION_STRING];
    //【可选配置】需要返回详细的播放进度或需要启用断点续播功能时应配置为 1, 否则配置为 0 或不配置
    [self.curEngine setIntParam:1 forKey:SE_PARAMS_KEY_TTS_WITH_FRONTEND_INT];
    //【可选配置】需要返回字粒度的播放进度时应配置为 simple, 同时要求 PARAMS_KEY_TTS_WITH_FRONTEND_INT 也配置为 1; 默认为空
    [self.curEngine setStringParam: @"simple" forKey:SE_PARAMS_KEY_TTS_FRONTEND_TYPE_STRING];
    //【可选配置】使用复刻音色
    [self.curEngine setBoolParam:false forKey:SE_PARAMS_KEY_TTS_USE_VOICECLONE_BOOL];
    //【可选配置】在开启前述使用复刻音色的开关后，制定复刻音色所用的后端集群
    [self.curEngine setStringParam:@"YOUR TTS BACKEND CLUSTER" forKey:SE_PARAMS_KEY_TTS_BACKEND_CLUSTER_STRING];
    
    // ------------------------ 离线合成相关配置 -----------------------

//    NSString *voiceOffline = [self.settings getString:SETTING_OFFLINE_VOICE];
//    if (voiceOffline.length <= 0) {
//        voiceOffline = [self.settings getOptionsValue:SETTING_OFFLINE_VOICE];
//    }
//    self.ttsVoiceOffline = voiceOffline;
//    //【必需配置】离线合成使用的发音人代号
//    [self.curEngine setStringParam:self.ttsVoiceOffline forKey:SE_PARAMS_KEY_TTS_VOICE_OFFLINE_STRING];
//    NSString *voiceTypeOffline = [self.settings getString:SETTING_OFFLINE_VOICE_TYPE];
//    if (voiceTypeOffline.length <= 0) {
//        voiceTypeOffline = [self.settings getOptionsValue:SETTING_OFFLINE_VOICE_TYPE];
//    }
//    self.ttsVoiceTypeOffline = voiceTypeOffline;
//    //【必需配置】离线合成使用的音色代号
//    [self.curEngine setStringParam:self.ttsVoiceTypeOffline forKey:SE_PARAMS_KEY_TTS_VOICE_TYPE_OFFLINE_STRING];
//
//    //【可选配置】是否降低离线合成的 CPU 利用率，默认关闭
//    // 打开该配置会使离线合成的实时率变大，仅当必要（例如为避免系统主动杀死CPU占用持续过高的进程）时才应开启
//    [self.curEngine setBoolParam:[self.settings getBool:SETTING_TTS_LIMIT_CPU_USAGE] forKey:SE_PARAMS_KEY_TTS_LIMIT_CPU_USAGE_BOOL];
}

//- (void) prepareSingleVoiceResource {
//    SpeechResourceManager *speechResourceManager = [SpeechResourceManager shareInstance];
//    NSString* offlineLanguage = [self.settings getString:SETTING_TTS_OFFLINE_LANGUAGE];
//    if (offlineLanguage.length <= 0) {
//        offlineLanguage = SDEF_TTS_DEFAULT_OFFLINE_LANGUAGE;
//    }
//    NSArray* ttsLanguageArray = @[offlineLanguage];
//    NSLog(@"需要下载的离线合成语种资源有： %@", ttsLanguageArray);
//    [speechResourceManager setTtsLanguage:ttsLanguageArray];
//    NSArray* needDownloadVoiceType = (NSArray *)SDEF_TTS_DEFAULT_DOWNLOAD_OFFLINE_VOICES();
//    NSArray* voiceTypeArray = [self.settings getOptions:SETTING_OFFLINE_VOICE_TYPE].optionsArray;
//    if (voiceTypeArray != nil && voiceTypeArray.count > 0) {
//        needDownloadVoiceType = voiceTypeArray;
//    }
//    NSLog(@"需要下载的离线合成音色资源有： %@", needDownloadVoiceType);
//    [speechResourceManager setTtsVoiceType:needDownloadVoiceType];
//
//    NSLog(@"检查本地是否存在可用模型");
//    if ([speechResourceManager checkModelExist]) {
//        NSLog(@"本地没有模型，开始下载");
//        [self fetchSingleVoiceResource];
//    } else {
//        NSLog(@"模型存在，检查是否需要更新模型");
//        [speechResourceManager checkModelVersion:^(SEResourceStatus status, BOOL needUpdate, NSData *data) {
//            if (status != kSERSuccess || needUpdate == NO) {
//                NSLog(@"无需更新，直接使用本地已有模型。");
//                [self initEngineInternal];
//            } else {
//                NSLog(@"存在更新，开始下载模型");
//                [self fetchSingleVoiceResource];
//            }
//        }];
//    }
//}

//- (void)fetchSingleVoiceResource {
//    SpeechResourceManager *speechResourceManager = [SpeechResourceManager shareInstance];
//    [speechResourceManager fetchModel:^(SEResourceStatus status, NSData* data) {
//        if (status == kSERSuccess) {
//            NSLog(@"下载成功");
//            [self initEngineInternal];
//        } else {
//            NSLog(@"下载失败，错误码: %d", status);
//            [self speechEngineInitFailed:kSERDownloadFailed];
//        }
//    }];
//}

@end
