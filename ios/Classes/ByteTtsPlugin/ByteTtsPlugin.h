//
//  ByteTtsPlugun.h
//  Runner
//
//  Created by bodk on 2024/7/22.
//

#import <Foundation/Foundation.h>


NS_ASSUME_NONNULL_BEGIN

@protocol ByteTtsPluginDelegate <NSObject>

@required

//协议方法
- (void)handleTtsResult:(NSString *)result;

@end


@interface ByteTtsPlugin : NSObject

//声明代理
@property (nonatomic,weak) id<ByteTtsPluginDelegate> byteTtsDelegate;

+(instancetype)sharedInstance:(NSString *)appId token:(NSString *)token cluster:(NSString *)cluster;

-(void)initEngine;
-(void)setYinse:(NSString *)yinse;
-(void)resumePlayback;
-(void)addSentence:(NSString *)text;
-(void)startEngine;
-(void)triggerSynthesis;

-(void)stopByteSpeechStream;
-(void)uninitEngine;
@end

NS_ASSUME_NONNULL_END
