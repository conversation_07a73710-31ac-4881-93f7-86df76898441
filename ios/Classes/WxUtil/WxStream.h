//
//  WxStream.h
//  Runner
//
//  Created by songguo on 2025/7/31.
//

#import <Foundation/Foundation.h>
#import <Flutter/Flutter.h>

NS_ASSUME_NONNULL_BEGIN
@class WxStreamHanlder;

@interface WxStream : NSObject

+ (instancetype)sharedInstance ;

@property (nonatomic, strong) WxStreamHanlder *wxStreamHandler;

@end

@interface WxStreamHanlder : NSObject<FlutterStreamHandler>
@property (nonatomic, strong,nullable) FlutterEventSink wxEventSink;
@end


NS_ASSUME_NONNULL_END
