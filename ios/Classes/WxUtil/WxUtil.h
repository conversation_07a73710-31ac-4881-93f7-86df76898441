//
//  WxUtil.h
//  Runner
//
//  Created by songguo on 2025/6/17.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@protocol WxUtilDelegate <NSObject>

@required

//协议方法
- (void)handleWxUtilResult:(NSString *)result;

@end

@interface WxUtil : NSObject

//声明代理
@property (nonatomic,weak) id<WxUtilDelegate> wxUtilDelegate;

/**
 * 单例构造方法
 * @return 共享实例
 */
+ (instancetype)sharedInstance;

- (BOOL)registerWx:(NSString *)appKey withUniversalLink:(NSString *)universalLink;

-(BOOL)isInstalledWx;

-(void)shareWxWithSession:(NSString *)imagePath;

-(void)shareWxWithTimeLine:(NSString *)imagePath;

-(BOOL)launchCustomerService:(NSString *)corpId withUrl:(NSString *)url;

@end

NS_ASSUME_NONNULL_END
