//
//  WxStream.m
//  Runner
//
//  Created by songguo on 2025/7/31.
//

#import "WxStream.h"

@implementation WxStream

+ (instancetype)sharedInstance {
    static dispatch_once_t onceToken;
    static WxStream *manager = nil;
    dispatch_once(&onceToken, ^{
        manager = [[WxStream alloc] init];
        WxStreamHanlder *streamHandler = [[WxStreamHanlder alloc] init];
        manager.wxStreamHandler = streamHandler;
    });
    
    return manager;
}

@end

@implementation WxStreamHanlder

- (FlutterError*)onListenWithArguments:(id)arguments eventSink:(FlutterEventSink)eventSink {
    self.wxEventSink = eventSink;
    return nil;
}

- (FlutterError*)onCancelWithArguments:(id)arguments {
    self.wxEventSink = nil;
    return nil;
}

@end
