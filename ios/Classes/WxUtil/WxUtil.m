//
//  WxUtil.m
//  Runner
//
//  Created by songguo on 2025/6/17.
//

#import "WxUtil.h"
#import <WechatOpenSDK/WXApi.h>

@interface WxUtil()<WXApiDelegate>

@end

@implementation WxUtil

+(instancetype)sharedInstance{
    static dispatch_once_t onceToken;
    static WxUtil *manager = nil;
    dispatch_once(&onceToken, ^{
        manager = [[WxUtil alloc] init];
    });
    return manager;
}

- (BOOL)registerWx:(NSString *)appKey withUniversalLink:(NSString *)universalLink{
    
    //在register之前打开log, 后续可以根据log排查问题
    [WXApi startLogByLevel:WXLogLevelNormal logBlock:^(NSString *log) {
//        NSLog(@"WeChatSDK: %@", log);
    }];
    BOOL flag = [WXApi registerApp:appKey universalLink:universalLink];
    NSLog(@"微信SDK初始化：%d", flag);
//    if(flag){
//        //调用自检函数（开启这个启动时会先跳到微信）
//        [WXApi checkUniversalLinkReady:^(WXULCheckStep step, WXCheckULStepResult* result) {
//            NSLog(@"%@, %u, %@, %@", @(step), result.success, result.errorInfo, result.suggestion);
//        }];
//    }
    return  flag;
}

-(BOOL)isInstalledWx{
    BOOL flag = [WXApi isWXAppInstalled];
    return flag;
}

///分享到会话
-(void)shareWxWithSession:(NSString *)imagePath{
    NSLog(@"%@",imagePath);
    UIImage *image = [UIImage imageWithContentsOfFile:imagePath];
    NSData *imageData = UIImagePNGRepresentation(image);
    
    WXImageObject *imageObject = [WXImageObject object];
    imageObject.imageData = imageData;
    
    WXMediaMessage *message = [WXMediaMessage message];
    
    // 设置缩略图（建议不超过 32KB）
    UIImage *thumbImage = [self resizeImage:image toSize:CGSizeMake(150, 150)];
    message.thumbData = UIImageJPEGRepresentation(thumbImage, 0.7);
    
    message.mediaObject = imageObject;
    
    SendMessageToWXReq *req = [[SendMessageToWXReq alloc] init];
    req.bText = NO;
    req.message = message;
    req.scene = WXSceneSession;
    [WXApi sendReq:req completion:^(BOOL success) {
        NSLog(@"分享会话结果：%d",success);
    }];
}

///分享到朋友圈
-(void)shareWxWithTimeLine:(NSString *)imagePath{
    UIImage *image = [UIImage imageWithContentsOfFile:imagePath];
    NSData *imageData = UIImagePNGRepresentation(image);
    
    WXImageObject *imageObject = [WXImageObject object];
    imageObject.imageData = imageData;
    
    WXMediaMessage *message = [WXMediaMessage message];
    
    // 设置缩略图（建议不超过 32KB）
    UIImage *thumbImage = [self resizeImage:image toSize:CGSizeMake(150, 150)];
    message.thumbData = UIImageJPEGRepresentation(thumbImage, 0.7);
    
    message.mediaObject = imageObject;
    
    SendMessageToWXReq *req = [[SendMessageToWXReq alloc] init];
    req.bText = NO;
    req.message = message;
    req.scene = WXSceneTimeline;
    [WXApi sendReq:req completion:^(BOOL success) {
        NSLog(@"分享朋友圈结果：%d",success);
    }];
}

- (UIImage *)resizeImage:(UIImage *)image toSize:(CGSize)size {
    UIGraphicsBeginImageContextWithOptions(size, NO, 0.0);
    [image drawInRect:CGRectMake(0, 0, size.width, size.height)];
    UIImage *resized = UIGraphicsGetImageFromCurrentImageContext();
    UIGraphicsEndImageContext();
    return resized;
}

///微信客服
-(BOOL)launchCustomerService:(NSString *)corpId withUrl:(NSString *)url {
    WXOpenCustomerServiceReq *req = [[WXOpenCustomerServiceReq alloc] init];
    req.corpid = corpId;
    req.url = url;
    [WXApi sendReq:req completion:nil];
    return TRUE;
}


-(void)sendDelegateMessage:(NSString *)message{
    if([self.wxUtilDelegate respondsToSelector:@selector(handleWxUtilResult:)]){
        [self.wxUtilDelegate handleWxUtilResult:message];
    }
}

# pragma mark---------------WXApiDelegate--------------------
-(void) onReq:(BaseReq*)reqonReq{
   
}

-(void) onResp:(BaseResp*)resp{
    if([resp isKindOfClass:[WXOpenCustomerServiceResp class]]){
        int errCode = resp.errCode;
        NSString *errString = resp.errStr;
        NSLog(@"微信客服：%d == %@",errCode,errString);
    }
    if([resp isKindOfClass:[PayResp class]]){
        int errCode = resp.errCode;
        NSString *errString = resp.errStr;
        NSLog(@"微信支付：%d == %@",errCode,errString);
    }
}
@end
