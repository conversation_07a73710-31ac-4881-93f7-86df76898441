//
//  ByteSpeechPlugin.m
//  Runner
//
//  Created by bodk on 2024/9/2.
//

#import "RegisterPlugin.h"
#import "ByteSpeechStream/ByteTtsStream.h"
#import "ByteSpeechStream/ByteAsrStream.h"
#import "ByteSpeechStream/ByteVolumeStream.h"
#import "EspBlufiUtil/EspBlufiStream.h"
#import "XhsUtil/XhsStream.h"
#import "WxUtil/WxStream.h"
#import "Runner-Swift.h"

@implementation RegisterPlugin

ByteTtsStream *byteTtsStreamInstance;
ByteAsrStream *byteAsrStreamInstance;
ByteVolumeStream *byteVolumeStreamInstance;
EspBlufiStream *espBlufiStreamInstance;
XhsStream *xhsStreamInstance;
WxStream *wxStreamInstance;

NSObject<FlutterPluginRegistrar> *flutterPluginRegistrar;

+ (void)registerWithRegistrar:(NSObject<FlutterPluginRegistrar>*)registrar {
    flutterPluginRegistrar = registrar;
    
    RegisterPlugin *instance = [[RegisterPlugin alloc] init];
    
    FlutterMethodChannel *asrChannel = [FlutterMethodChannel
      methodChannelWithName:@"byte_asr_init_config"
            binaryMessenger:[registrar messenger]];
    [registrar addMethodCallDelegate:instance channel:asrChannel];
    
    FlutterMethodChannel *ttsChannel = [FlutterMethodChannel
      methodChannelWithName:@"byte_tts_init_config"
            binaryMessenger:[registrar messenger]];
    [registrar addMethodCallDelegate:instance channel:ttsChannel];
    
    FlutterEventChannel *byteAsrEventChanel = [FlutterEventChannel eventChannelWithName:@"byte_asr_stream" binaryMessenger:[registrar messenger]];
    byteAsrStreamInstance = [ByteAsrStream sharedInstance];
    [byteAsrEventChanel setStreamHandler:[byteAsrStreamInstance byteAsrStreamHandler]];
    FlutterEventChannel *byteVolumeEventChanel = [FlutterEventChannel eventChannelWithName:@"byte_volume_stream" binaryMessenger:[registrar messenger]];
    byteVolumeStreamInstance = [ByteVolumeStream sharedInstance];
    [byteVolumeEventChanel setStreamHandler:[byteVolumeStreamInstance byteVolumeStreamHandler]];
    
    FlutterEventChannel *byteTtsEventChanel = [FlutterEventChannel eventChannelWithName:@"byte_tts_stream" binaryMessenger:[registrar messenger]];
    byteTtsStreamInstance = [ByteTtsStream sharedInstance];
    [byteTtsEventChanel setStreamHandler:[byteTtsStreamInstance byteTtsStreamHandler]];
    
    
    FlutterMethodChannel *espBlufiChannel = [FlutterMethodChannel
      methodChannelWithName:@"esp_blufi"
            binaryMessenger:[registrar messenger]];
    [registrar addMethodCallDelegate:instance channel:espBlufiChannel];
    
    FlutterEventChannel *espBlufiEventChanel = [FlutterEventChannel eventChannelWithName:@"esp_blufi_stream" binaryMessenger:[registrar messenger]];
    espBlufiStreamInstance = [EspBlufiStream sharedInstance];
    [espBlufiEventChanel setStreamHandler:[espBlufiStreamInstance espBlufiStreamHandler]];
    
    FlutterMethodChannel *xhsInitChannel = [FlutterMethodChannel
      methodChannelWithName:@"xhs_init_config"
            binaryMessenger:[registrar messenger]];
    [registrar addMethodCallDelegate:instance channel:xhsInitChannel];
    
    FlutterEventChannel *xhsEventChanel = [FlutterEventChannel eventChannelWithName:@"xhs_sdk_stream" binaryMessenger:[registrar messenger]];
    xhsStreamInstance = [XhsStream sharedInstance];
    [xhsEventChanel setStreamHandler:[xhsStreamInstance xhsStreamHandler]];
    
    FlutterMethodChannel *wxInitChannel = [FlutterMethodChannel
      methodChannelWithName:@"wx_init_config"
            binaryMessenger:[registrar messenger]];
    [registrar addMethodCallDelegate:instance channel:wxInitChannel];
    
    FlutterEventChannel *wxEventChanel = [FlutterEventChannel eventChannelWithName:@"wx_sdk_stream" binaryMessenger:[registrar messenger]];
    wxStreamInstance = [WxStream sharedInstance];
    [wxEventChanel setStreamHandler:[wxStreamInstance wxStreamHandler]];
    
    FlutterMethodChannel *getReceipt = [FlutterMethodChannel
      methodChannelWithName:@"get_receipt"
            binaryMessenger:[registrar messenger]];
    [registrar addMethodCallDelegate:instance channel:getReceipt];
    
    
}

- (void)handleMethodCall:(FlutterMethodCall*)call result:(FlutterResult)result {
    
    if ([@"initByteAsr" isEqualToString:call.method]) {
        [self byteAsrInit:call.arguments];
    } else if([@"stopByteAsr" isEqualToString:call.method]){
        [self byteAsrStopEngine];
    } else if([@"destroyByteAsr" isEqualToString:call.method]){
        [self byteAsrDestroy];
    } else if([@"byteStartRecord" isEqualToString:call.method]){
        [self byteAsrStartRecord:call.arguments];
    }else if([@"byteStopRecord" isEqualToString:call.method]){
        [self byteAsrStopRecord];
    } else if([@"initByteTts" isEqualToString:call.method]){
        [self byteTtsInit:call.arguments];
    } else if ([@"sendContentToByteTts" isEqualToString:call.method]){
        [self byteTtsStarSpeaking:call.arguments];
    } else if ([@"stopByteTts" isEqualToString:call.method]){
        [self byteTtsStopSpeaking];
    } else if ([@"destroyByteTts" isEqualToString:call.method]){
        [self byteTtsDestroy];
    }else if ([@"initEspBlufi" isEqualToString:call.method]) {
        [self initEspBlufi];
    } else if([@"scanEspBlufi" isEqualToString:call.method]){
        [self scanEspBlufi];
    } else if([@"stopEspBlufi" isEqualToString:call.method]){
        [self stopEspBlufi];
    } else if([@"connectDevice" isEqualToString:call.method]){
        [self connectDevice:call.arguments];
    }else if([@"scanWifi" isEqualToString:call.method]){
        [self scanWifi];
    } else if([@"configureWifi" isEqualToString:call.method]){
        [self configureWifi:call.arguments];
    } else if ([@"statusDevice" isEqualToString:call.method]){
        [self statusDevice];
    } else if ([@"cloneConnect" isEqualToString:call.method]){
        [self cloneConnect];
    } else if ([@"initXhs" isEqualToString:call.method]){
        if(_xhsUtil == nil){
            NSString *appKey = call.arguments[@"appKey"];
            _xhsUtil = [XhsUtil sharedInstance];
            BOOL flag = [_xhsUtil registerXhs:appKey];
            if(flag){
                [_xhsUtil setXhsUtilDelegate:self];
            }
            result(@(flag));
        }
    }else if ([@"shareXhs" isEqualToString:call.method]){
        [self shareXhs:call.arguments];
    }else if ([@"supportShareNote" isEqualToString:call.method]){
        if (_xhsUtil) {
          BOOL flag = [_xhsUtil isSupportShareNote];
            
          result(@(flag));
        }
    }else if ([@"initWx" isEqualToString:call.method]){
        if(_wxUtil == nil){
            NSString *appKey = call.arguments[@"appKey"];
            NSString *universalLink = call.arguments[@"universalLink"];
            _wxUtil = [WxUtil sharedInstance];
            BOOL flag = [_wxUtil registerWx:appKey withUniversalLink:universalLink];
            result(@(flag));
        }
    }else if ([@"isInstalledWx" isEqualToString:call.method]){
        if(_wxUtil != nil){
            BOOL flag = [_wxUtil isInstalledWx];
            result(@(flag));
        }
    }else if ([@"shareSession" isEqualToString:call.method]){
        if(_wxUtil != nil){
            NSString *imagePath = call.arguments[@"imagePath"];
            [_wxUtil shareWxWithSession:imagePath];
        }
    }else if ([@"shareTimeLine" isEqualToString:call.method]){
        if(_wxUtil != nil){
            NSString *imagePath = call.arguments[@"imagePath"];
            [_wxUtil shareWxWithTimeLine:imagePath];
        }
    }else if ([@"launchCustomerService" isEqualToString:call.method]){
        if(_wxUtil != nil){
            NSString *corpId = call.arguments[@"corpId"];
            NSString *url = call.arguments[@"url"];
            [_wxUtil launchCustomerService:corpId withUrl:url];
        }
    }else if ([@"getLatestReceipt" isEqual:call.method]){
        bool forceRetry = [call.arguments[@"forceRetry"] boolValue];
        [[ReceiptHelper shared] fetchLatestReceiptWithForceRetryUntilChanged:forceRetry completion:^(NSString * receipt, NSError * error) {
            if(receipt){
                result(receipt);
            }
        }];
    }
    
}


/**
 火山asr引擎初始化
 */
-(void)byteAsrInit:(NSDictionary *)args{
    if(_byteAsrPlugin == nil){
        NSString *appId = args[@"appId"];
        NSString *token = args[@"token"];
        NSString *cluster = args[@"cluster"];
        _byteAsrPlugin = [ByteAsrPlugin sharedInstance:appId token:token cluster:cluster];
    }
    if(_byteAsrPlugin != nil){
        [_byteAsrPlugin setAsrDelegate:self];
        [_byteAsrPlugin setVolumeDelegate:self];
        
        [_byteAsrPlugin initEngine];
    }
    
}

/**
 火山asr停止引擎
 */
-(void)byteAsrStopEngine{
    if(_byteAsrPlugin != nil){
        [_byteAsrPlugin stopEngine];
    }
    
}

/**
 火山asr销毁引擎
 */
-(void)byteAsrDestroy{
    if(_byteAsrPlugin != nil){
        [_byteAsrPlugin unInitEngine];
    }
    
}

/**
 火山asr开始录音
 */
-(void)byteAsrStartRecord:(NSDictionary *)args{
    int isHold = [args[@"isHold"] intValue];
    if(_byteAsrPlugin != nil){
        [_byteAsrPlugin startRecord:isHold];
    }
    
}


/**
 火山asr停止录音
 */
-(void)byteAsrStopRecord{
    if(_byteAsrPlugin != nil){
        [_byteAsrPlugin stopRecord];
    }
    
}


/**
 火山tts引擎初始化
 */
-(void)byteTtsInit:(NSDictionary *)args{
    if(_byteTtsPlugin == nil){
        NSString *appId = args[@"appId"];
        NSString *token = args[@"token"];
        NSString *cluster = args[@"cluster"];
        _byteTtsPlugin = [ByteTtsPlugin sharedInstance:appId token:token cluster:cluster];
    }
    if(_byteTtsPlugin != nil){
        [_byteTtsPlugin setByteTtsDelegate:self];
        
        [_byteTtsPlugin initEngine];
        [_byteTtsPlugin startEngine];
    }
    
}

/**
 *火山tts文本合成语音
 */
-(void)byteTtsStarSpeaking:(NSDictionary *)args{
    if(_byteTtsPlugin != nil){
        bool isFirst = [args[@"isFirst"] boolValue];
        NSString *content = args[@"content"];
        NSString *yinse = args[@"yinse"];
        
        [_byteTtsPlugin setYinse:yinse];
        if(isFirst){
            //首次播放 加载资源
            [_byteTtsPlugin resumePlayback];
            [_byteTtsPlugin addSentence:content];
            [_byteTtsPlugin startEngine];
            [_byteTtsPlugin triggerSynthesis];
        }else{
            [_byteTtsPlugin addSentence:content];
        }
    }
}

/**
 火山tts停止合成
 */
-(void)byteTtsStopSpeaking{
    if(_byteTtsPlugin != nil){
        [_byteTtsPlugin stopByteSpeechStream];
    }
    
}

/**
 火山tts引擎销毁
 */
-(void)byteTtsDestroy{
    if(_byteTtsPlugin != nil){
        [_byteTtsPlugin uninitEngine];
    }
    
}


-(void)handleAsrResult:(NSString *)result{
    [byteAsrStreamInstance byteAsrStreamHandler].byteAsrEventSink(result);
}

- (void)handleTtsResult:(NSString *)result {
    [byteTtsStreamInstance byteTtsStreamHandler].byteTtsEventSink(result);
}

-(void)handleVolumeResult:(NSString *)result{
    [byteVolumeStreamInstance byteVolumeStreamHandler].byteVolumeEventSink(result);
}

-(void)handleEspBlufiUtilResult:(NSString *)result{
    [espBlufiStreamInstance espBlufiStreamHandler].espBlufiEventSink(result);
}

-(void)handleXhsUtilResult:(NSString *)result{
    [xhsStreamInstance xhsStreamHandler].xhsEventSink(result);
}

-(void)handleWxUtilResult:(NSString *)result{
    [wxStreamInstance wxStreamHandler].wxEventSink(result);
}

-(void)initEspBlufi{
    if(_espBlufiUtil == nil){
        _espBlufiUtil = [EspBlufiUtil sharedInstance];
        [_espBlufiUtil setEspBlufiDelegate:self];
    }
}

-(void)scanEspBlufi{
    if(_espBlufiUtil != nil){
        [_espBlufiUtil startScan];
    }
}

-(void)stopEspBlufi{
    if(_espBlufiUtil != nil){
        [_espBlufiUtil stopScan];
    }
}

-(void)connectDevice:(NSDictionary *)args{
    if(_espBlufiUtil != nil){
        NSString *deviceName = args[@"deviceName"];
        [_espBlufiUtil connectDevice:deviceName];
    }
}

-(void)scanWifi{
    if(_espBlufiUtil != nil){
        [_espBlufiUtil getWifiName];
    }
}

-(void)configureWifi:(NSDictionary *)args{
    if(_espBlufiUtil != nil){
        NSString *wifiName = args[@"wifiName"];
        NSString *wifiPwd = args[@"wifiPwd"];
        [_espBlufiUtil connectWifi:wifiName withPwd:wifiPwd];
    }
}

-(void)statusDevice{
    if(_espBlufiUtil != nil){
        [_espBlufiUtil statusDevice];
    }
}

-(void)cloneConnect{
    if(_espBlufiUtil != nil){
        [_espBlufiUtil onDisconnected];
    }
}

-(void)shareXhs:(NSDictionary *)args{
    if(_xhsUtil != nil){
        NSString *title = args[@"title"];
        NSString *content = args[@"content"];
        NSString *image = args[@"image"];
        [_xhsUtil shareNote:title withContent:content withImage:image];
    }
}


@end
