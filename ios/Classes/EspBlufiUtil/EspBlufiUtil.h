//
//  EspBlufiUtil.h
//  Runner
//
//  Created by bodk on 2025/4/11.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@protocol EspBlufiUtilDelegate <NSObject>

@required

//协议方法
- (void)handleEspBlufiUtilResult:(NSString *)result;

@end

@interface EspBlufiUtil : NSObject

//声明代理
@property (nonatomic,weak) id<EspBlufiUtilDelegate> espBlufiDelegate;


/**
 * 单例构造方法
 * @return ESPFBYLocalAPI共享实例
 */
+ (instancetype)sharedInstance;

//停止扫描
- (void)stopScan;
//开始扫描
- (void)startScan;

//开始连接设备
-(void)connectDevice:(NSString *)deviceName;
//断开设备连接
-(void)onDisconnected;
//设备状态
-(void)statusDevice;

//扫描wifi
-(void)getWifiName;
-(void)scanWifiInfos;

//配置wifi
-(void)connectWifi:(NSString *)wifiName withPwd:(NSString *)wifiPwd;
@end

NS_ASSUME_NONNULL_END
