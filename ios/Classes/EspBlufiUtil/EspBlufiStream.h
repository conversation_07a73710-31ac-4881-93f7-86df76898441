//
//  EspBlufiStream.h
//  Runner
//
//  Created by bodk on 2025/4/11.
//

#import <Foundation/Foundation.h>
#import <Flutter/Flutter.h>

NS_ASSUME_NONNULL_BEGIN
@class EspBlufiStreamHanlder;
@interface EspBlufiStream : NSObject
+ (instancetype)sharedInstance ;

@property (nonatomic, strong) EspBlufiStreamHanlder* espBlufiStreamHandler;

@end

@interface EspBlufiStreamHanlder : NSObject<FlutterStreamHandler>
@property (nonatomic, strong,nullable) FlutterEventSink espBlufiEventSink;
@end


NS_ASSUME_NONNULL_END
