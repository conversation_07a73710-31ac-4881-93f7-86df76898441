//
//  EspBlufiStream.m
//  Runner
//
//  Created by bodk on 2025/4/11.
//

#import "EspBlufiStream.h"

@implementation EspBlufiStream

+ (instancetype)sharedInstance {
    static dispatch_once_t onceToken;
    static EspBlufiStream *manager = nil;
    dispatch_once(&onceToken, ^{
        manager = [[EspBlufiStream alloc] init];
        EspBlufiStreamHanlder *streamHandler = [[EspBlufiStreamHanlder alloc] init];
        manager.espBlufiStreamHandler = streamHandler;
    });
    
    return manager;
}

@end


@implementation EspBlufiStreamHanlder

- (FlutterError*)onListenWithArguments:(id)arguments eventSink:(FlutterEventSink)eventSink {
    self.espBlufiEventSink = eventSink;
    return nil;
}

- (FlutterError*)onCancelWithArguments:(id)arguments {
    self.espBlufiEventSink = nil;
    return nil;
}

@end
