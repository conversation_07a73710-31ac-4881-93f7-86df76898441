//
//  EspBlufiUtil.m
//  Runner
//
//  Created by bodk on 2025/4/11.
//

#import "EspBlufiUtil.h"
#import "ESPDataConversion.h"
#import "BlufiClient.h"
#import "BlufiConfigureParams.h"
#import "ESPPeripheral.h"
#import <SystemConfiguration/CaptiveNetwork.h>
#import <CoreBluetooth/CoreBluetooth.h>
#import <NetworkExtension/NetworkExtension.h>


/// 超时时间 默认5秒
#define TIMEOUT_SCAN 3

@interface EspBlufiUtil()<CBCentralManagerDelegate,CBPeripheralDelegate, BlufiDelegate>

// 中心管理者(管理设备的扫描和连接)
@property (nonatomic, strong) CBCentralManager *centralManager;

// 外设状态
@property (nonatomic, assign) CBManagerState peripheralState;

@property(nonatomic, copy)   NSMutableArray<ESPPeripheral *> *peripheralArray;
@property(nonatomic, strong) NSString *filterContent;

@property (nonatomic, strong) NSDate *startTime;
@property (nonatomic, strong) NSTimer *checkTimer;

@property(strong, nonatomic)ESPPeripheral *device;

@property(strong, nonatomic)NSMutableDictionary *mDeviceMap;

@property(nonatomic, assign)Boolean isStop;

@property(strong, nonatomic)BlufiClient *blufiClient;
@property(assign, atomic)BOOL connected;


@end

@implementation EspBlufiUtil

+(instancetype)sharedInstance {
    static dispatch_once_t onceToken;
    static EspBlufiUtil *manager = nil;
    dispatch_once(&onceToken, ^{
        manager = [[EspBlufiUtil alloc] init];
        [manager ESPFBYBLEHelperInit];
    });
    return manager;
}

- (void)ESPFBYBLEHelperInit {
    NSDictionary *options = @{ CBCentralManagerOptionShowPowerAlertKey: @NO };
    self.centralManager = [[CBCentralManager alloc] initWithDelegate:self
                                                               queue:nil
                                                             options:options];
    // 设置过滤字段
    self.filterContent = [ESPDataConversion loadBlufiScanFilter];
    
    self.connected = NO;
    
    self.mDeviceMap = [[NSMutableDictionary alloc] init];
}

- (void)stopScan {
    NSLog(@"停止扫描设备");
    [self.centralManager stopScan];
}

- (void)startScan {
    _isStop = false;
    [self.mDeviceMap removeAllObjects];
    NSLog(@"扫描设备");
    if (self.peripheralState ==  CBManagerStatePoweredOn){
        [self keepTimeAction];
        [self.centralManager scanForPeripheralsWithServices:nil options:nil];
    }else{
        NSString *result = @"{\"status\":\"fail\",\"tag\":\"scan\",\"data\":\"设备扫描失败\"}";
        [self sendDelegateMessage:result];
    }
}


-(void)keepTimeAction{
    // 记录当前时间
    self.startTime = [NSDate date];
    
    // 如果有旧的定时器，先取消
    [self.checkTimer invalidate];
    
    // 开启一个定时器每秒检查一次
    self.checkTimer = [NSTimer scheduledTimerWithTimeInterval:1.0
                                                           target:self
                                                         selector:@selector(checkIfTenSecondsPassed)
                                                         userInfo:nil
                                                          repeats:YES];
}

-(void)checkIfTenSecondsPassed{
    NSTimeInterval elapsed = [[NSDate date] timeIntervalSinceDate:self.startTime];
        if (elapsed >= TIMEOUT_SCAN) {
            [self.checkTimer invalidate];
            NSString *result = @"{\"status\":\"stop\",\"tag\":\"scan\",\"data\":\"设备扫描超时\"}";
            [self sendDelegateMessage:result];
            [self stopScan];
        }
}


- (BOOL)shouldAddToSource:(ESPPeripheral *)device {
    NSArray *source = [self dataSource];
    // Check filter
    if (_filterContent && _filterContent.length > 0) {
        if (!device.name || ![device.name hasPrefix:_filterContent]) {
            // The device name has no filter prefix
            return NO;
        }
    }
    
    // Check exist
    for (int i = 0; i < source.count; i++) {
        ESPPeripheral *existDevice = source[i];
        if ([device.uuid isEqual:existDevice.uuid]) {
            // The device exists in source already
            return NO;
        }
    }
    
    return YES;
}

- (NSMutableArray *)dataSource {
    if (!_peripheralArray) {
        _peripheralArray = [[NSMutableArray alloc] init];
    }
    return _peripheralArray;
}

-(void)sendDelegateMessage:(NSString *)message{
    if([self.espBlufiDelegate respondsToSelector:@selector(handleEspBlufiUtilResult:)]){
        [self.espBlufiDelegate handleEspBlufiUtilResult:message];
    }
}

/**
 扫描到设备
 
 @param central 中心管理者
 @param peripheral 扫描到的设备
 @param advertisementData 广告信息
 @param RSSI 信号强度
 */
- (void)centralManager:(CBCentralManager *)central didDiscoverPeripheral:(CBPeripheral *)peripheral advertisementData:(NSDictionary<NSString *,id> *)advertisementData RSSI:(NSNumber *)RSSI {
    
//    if(_isStop) return;
    
    ESPPeripheral *espPeripheral = [[ESPPeripheral alloc] initWithPeripheral:peripheral];
    espPeripheral.name = [advertisementData objectForKey:@"kCBAdvDataLocalName"];
    espPeripheral.rssi = RSSI.intValue;
    if([self shouldAddToSource:espPeripheral]){
        
//        _isStop = TRUE;
//        
//        [self.checkTimer invalidate];
//        
//        [self stopScan];
//        
//        _device = espPeripheral;
        
        [self.mDeviceMap setValue:espPeripheral forKey:espPeripheral.name];
        
        NSString *result = [NSString stringWithFormat:@"{\"status\":\"success\",\"tag\":\"scan\",\"data\":\"设备扫描成功=%@\"}",espPeripheral.name];
        [self sendDelegateMessage:result];
        
        NSLog(@"扫描结果:%@",espPeripheral.name);
    }
    
}

// 状态更新时调用
- (void)centralManagerDidUpdateState:(CBCentralManager *)central
{
    switch (central.state) {
        case CBManagerStateUnknown:{
            NSLog(@"未知状态");
            self.peripheralState = central.state;
        }
            break;
        case CBManagerStateResetting:
        {
            NSLog(@"重置状态");
            self.peripheralState = central.state;
        }
            break;
        case CBManagerStateUnsupported:
        {
            NSLog(@"不支持的状态");
            self.peripheralState = central.state;
        }
            break;
        case CBManagerStateUnauthorized:
        {
            NSLog(@"未授权的状态");
            self.peripheralState = central.state;
        }
            break;
        case CBManagerStatePoweredOff:
        {
            NSLog(@"关闭状态");
            self.peripheralState = central.state;
        }
            break;
        case CBManagerStatePoweredOn:
        {
            NSLog(@"开启状态－可用状态");
            self.peripheralState = central.state;
        }
            break;
        default:
            break;
    }
}


#pragma mark===========================连接相关=================================
- (void)connectDevice:(NSString *)deviceName {
    if (_blufiClient) {
        [_blufiClient close];
        _blufiClient = nil;
    }
    
    _device = self.mDeviceMap[deviceName];
    
    _blufiClient = [[BlufiClient alloc] init];
    _blufiClient.centralManagerDelete = self;
    _blufiClient.peripheralDelegate = self;
    _blufiClient.blufiDelegate = self;
    [_blufiClient connect:_device.uuid.UUIDString];
}

-(void)statusDevice{
    if (_blufiClient && self.connected) {
        [_blufiClient requestDeviceStatus];
    }
}

- (void)onDisconnected {
    if (_blufiClient) {
        [_blufiClient close];
    }
}

- (void)centralManager:(CBCentralManager *)central didConnectPeripheral:(CBPeripheral *)peripheral {
    NSLog(@"didConnectPeripheral 连接设备成功");
    self.connected = YES;
    NSString *result = @"{\"status\":\"success\",\"tag\":\"connect\",\"data\":\"设备连接成功\"}";
    [self sendDelegateMessage:result];
}

- (void)centralManager:(CBCentralManager *)central didFailToConnectPeripheral:(CBPeripheral *)peripheral error:(NSError *)error {
    NSLog(@"连接设备失败");
    self.connected = NO;
    NSString *result = @"{\"status\":\"fail\",\"tag\":\"connect\",\"data\":\"设备连接失败\"}";
    [self sendDelegateMessage:result];
}

- (void)centralManager:(CBCentralManager *)central didDisconnectPeripheral:(CBPeripheral *)peripheral error:(NSError *)error {
    [self onDisconnected];
    NSLog(@"断开设备连接");
    self.connected = NO;
}

- (void)blufi:(BlufiClient *)client gattPrepared:(BlufiStatusCode)status service:(CBService *)service writeChar:(CBCharacteristic *)writeChar notifyChar:(CBCharacteristic *)notifyChar {
    NSLog(@"Blufi gattPrepared status:%d", status);
    if (status == StatusSuccess) {
        NSLog(@"设备连接已准备");
    } else {
        [self onDisconnected];
        if (!service) {
            NSLog(@"发现服务失败");
        } else if (!writeChar) {
            NSLog(@"发现写入失败");
        } else if (!notifyChar) {
            NSLog(@"发现通知失败");
        }
    }
}


- (void)blufi:(BlufiClient *)client didPostConfigureParams:(BlufiStatusCode)status {
    NSLog(@"wifi写入状态:%d",status);
    if (status == StatusSuccess) {
        NSString *result = @"{\"status\":\"success\",\"tag\":\"configureWifi\",\"data\":\"wifi配置成功\"}";
        [self sendDelegateMessage:result];
    } else {
        NSString *result = @"{\"status\":\"fail\",\"tag\":\"configureWifi\",\"data\":\"wifi配置失败\"}";
        [self sendDelegateMessage:result];
    }
}

- (void)blufi:(BlufiClient *)client didReceiveDeviceScanResponse:(NSArray<BlufiScanResponse *> *)scanResults status:(BlufiStatusCode)status {
    NSLog(@"获取wifi列表状态:%d",status);
    if (status == StatusSuccess) {
        NSMutableArray *_mArr = [NSMutableArray array];
        for (BlufiScanResponse *response in scanResults) {
            NSLog(@"SSID: %@, RSSI: %d\n", response.ssid, response.rssi);
            if(response.ssid){
                [_mArr addObject:response.ssid];
            }
        }
        if (_mArr.count != 0) {
            NSDictionary *dic = @{@"status":@"success",@"tag":@"wifi",@"data":_mArr ?:@[]};
            NSError *error = nil;
            if([NSJSONSerialization isValidJSONObject:dic]){
                NSData *jsonData = [NSJSONSerialization dataWithJSONObject:dic options:NSJSONWritingPrettyPrinted error:&error];
                if(!error){
                    NSString *jsonString = [[NSString alloc] initWithData:jsonData encoding:NSUTF8StringEncoding];
                    [self sendDelegateMessage:jsonString];
                }else{
                    NSString *result = @"{\"status\":\"fail\",\"tag\":\"wifi\",\"data\":\"wifi扫描失败\"}";
                    [self sendDelegateMessage:result];
                }
            }else{
                NSString *result = @"{\"status\":\"fail\",\"tag\":\"wifi\",\"data\":\"wifi扫描失败\"}";
                [self sendDelegateMessage:result];
            }
        }else{
            NSString *result = @"{\"status\":\"fail\",\"tag\":\"wifi\",\"data\":\"wifi扫描失败\"}";
            [self sendDelegateMessage:result];
        }
    } else {
            NSString *result = @"{\"status\":\"fail\",\"tag\":\"wifi\",\"data\":\"wifi扫描失败\"}";
            [self sendDelegateMessage:result];
    }
}


- (void)blufi:(BlufiClient *)client didReceiveDeviceStatusResponse:(BlufiStatusResponse *)response status:(BlufiStatusCode)status {
    NSLog(@"设备状态：%d\n%@",response.staConnectionStatus,response.getStatusInfo);
    if (status == StatusSuccess) {
        if(response.staConnectionStatus == 3){
            NSString *result = @"{\"status\":\"success\",\"tag\":\"connectWifi\",\"data\":\"Wi-Fi连接成功\"}";
            [self sendDelegateMessage:result];
        }else if (response.staConnectionStatus == 1){
            NSString *result = @"{\"status\":\"fail\",\"tag\":\"connectWifi\",\"data\":\"Wi-Fi连接失败\"}";
            [self sendDelegateMessage:result];
        }else if (response.staConnectionStatus == 2){
            NSString *result = @"{\"status\":\"retry\",\"tag\":\"connectWifi\",\"data\":\"Wi-Fi正在重连\"}";
            [self sendDelegateMessage:result];
        }
    } else {
        NSString *result = @"{\"status\":\"fail\",\"tag\":\"connectWifi\",\"data\":\"Wi-Fi连接失败\"}";
        [self sendDelegateMessage:result];
    }
}

#pragma mark==========================wifi相关===================================
-(void)connectWifi:(NSString *)wifiName withPwd:(NSString *)wifiPwd{
    NSLog(@"%@,%@", wifiName, wifiPwd);
    BlufiConfigureParams *params = [[BlufiConfigureParams alloc] init];
    params.opMode = OpModeSta;
    params.staSsid = wifiName;
    params.staPassword = wifiPwd;
    [_blufiClient configure:params];
}

//获取wifi名称
- (void)getWifiName { 
//    NSString *wifiName = nil;
//    CFArrayRef wifiInterfaces = CNCopySupportedInterfaces();
//    if (!wifiInterfaces) {
//        NSString *result = @"{ \"status\":\"fail\",\"tag\":\"wifi\",\"data\":\"请打开Wi-Fi\"}";
//        [self sendDelegateMessage:result];
//        return;
//    }
//    NSArray *interfaces = (__bridge NSArray *)wifiInterfaces;
//    for (NSString *interfaceName in interfaces) {
//        CFDictionaryRef dictRef = CNCopyCurrentNetworkInfo((__bridge CFStringRef)(interfaceName));
//        if (dictRef) {
//            NSDictionary *networkInfo = (__bridge NSDictionary *)dictRef;
//            NSLog(@"network info -> %@", networkInfo);
//            wifiName = [networkInfo objectForKey:(__bridge NSString *)kCNNetworkInfoKeySSID];
//            CFRelease(dictRef);
//        }
//    }
//    CFRelease(wifiInterfaces);
//    if(wifiName != nil){
//        NSString *result = [NSString stringWithFormat:@"{ \"status\":\"success\",\"tag\":\"wifi\",\"data\":[\"%@\"]}",wifiName];
//        [self sendDelegateMessage:result];
//    }
    
    [_blufiClient requestDeviceScan];
}

- (void)scanWifiInfos{
    NSLog(@"1.Start");

    NSMutableDictionary* options = [[NSMutableDictionary alloc] init];
    [options setObject:@"EFNEHotspotHelperDemo" forKey: kNEHotspotHelperOptionDisplayName];
    dispatch_queue_t queue = dispatch_queue_create("EFNEHotspotHelperDemo", NULL);

    NSLog(@"2.Try");
    BOOL returnType = [NEHotspotHelper registerWithOptions: options queue: queue handler: ^(NEHotspotHelperCommand * cmd) {

        NSLog(@"4.Finish");
        NEHotspotNetwork* network;
        if (cmd.commandType == kNEHotspotHelperCommandTypeEvaluate || cmd.commandType == kNEHotspotHelperCommandTypeFilterScanList) {
            // 遍历 WiFi 列表，打印基本信息
            for (network in cmd.networkList) {
                NSString* wifiInfoString = [[NSString alloc] initWithFormat: @"---------------------------\nSSID: %@\nMac地址: %@\n信号强度: %f\nCommandType:%ld\n---------------------------\n\n", network.SSID, network.BSSID, network.signalStrength, (long)cmd.commandType];
                NSLog(@"%@", wifiInfoString);

                // 检测到指定 WiFi 可设定密码直接连接
                if ([network.SSID isEqualToString: @"测试 WiFi"]) {
                    [network setConfidence: kNEHotspotHelperConfidenceHigh];
                    [network setPassword: @"123456789"];
                    NEHotspotHelperResponse *response = [cmd createResponse: kNEHotspotHelperResultSuccess];
                    NSLog(@"Response CMD: %@", response);
                    [response setNetworkList: @[network]];
                    [response setNetwork: network];
                    [response deliver];
                }
            }
        }
    }];

    // 注册成功 returnType 会返回一个 Yes 值，否则 No
    NSLog(@"3.Result: %@", returnType == YES ? @"Yes" : @"No");
}


@end
