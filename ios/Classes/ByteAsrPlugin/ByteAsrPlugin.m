//
//  ByteAsrPlugin.m
//  Runner
//
//  Created by bodk on 2024/9/2.
//

#import "ByteAsrPlugin.h"
#import "AppDelegate.h"
#import "../utils/StreamRecorder.h"

@interface ByteAsrPlugin()<SpeechEngineDelegate>
// 一些在线合成的配置
@property (strong, nonatomic) NSString *mCurAppId;
@property (strong, nonatomic) NSString *mCurToken;
@property (strong, nonatomic) NSString *mCurCluster;
@property (strong, nonatomic) NSString *mCurAddress;
@property (strong, nonatomic) NSString *mCurUri;
@property (assign, nonatomic) double mCurSpeakSpeek;

@property (strong, nonatomic) NSString *mCurVoiceOnline;
@property (strong, nonatomic) NSString *mCurVoiceTypeOnline;

// Debug Path: 用于存放一些 SDK 相关的文件，比如模型、日志等
@property (strong, nonatomic) NSString *debugPath;
// SpeechEngine
@property (strong, nonatomic) SpeechEngine *curEngine;
@property (assign, nonatomic) BOOL engineStarted;
// APP 层自定义的录音机，在音频来源为 Stream 时使用
@property (strong, nonatomic) StreamRecorder *streamRecorder;

// 一些用于统计的字段
@property (nonatomic, assign) long talkingFinisheTimestamp;

@end


@implementation ByteAsrPlugin

+(instancetype)sharedInstance:(NSString *)appId token:(NSString *)token cluster:(NSString *)cluster {
    static dispatch_once_t onceToken;
    static ByteAsrPlugin *manager = nil;
    dispatch_once(&onceToken, ^{
        manager = [[ByteAsrPlugin alloc] init];
        manager.mCurAppId = appId;
        manager.mCurToken = token;
        manager.mCurCluster = @"volcengine_streaming_common";
        manager.mCurAddress = @"wss://openspeech.bytedance.com";
        manager.mCurUri = @"/api/v2/asr";
        manager.mCurSpeakSpeek = 1.0;
    });
    return manager;
}

- (instancetype)init
{
    self = [super init];
    if (self) {
        self.streamRecorder = [[StreamRecorder alloc] init];
        self.debugPath = NSSearchPathForDirectoriesInDomains(NSDocumentDirectory, NSUserDomainMask, YES).firstObject;
        NSLog(@"当前调试路径 %@", self.debugPath);
    }
    return self;
}

#pragma mark - Config & Init & Uninit Methods

-(void)configInitParams{
    //【必需配置】Engine Name
    [self.curEngine setStringParam:SE_ASR_ENGINE forKey:SE_PARAMS_KEY_ENGINE_NAME_STRING];
    
    //【可选配置】Debug & Log
    [self.curEngine setStringParam:self.debugPath forKey:SE_PARAMS_KEY_DEBUG_PATH_STRING];
    [self.curEngine setStringParam:SE_LOG_LEVEL_DEBUG forKey:SE_PARAMS_KEY_LOG_LEVEL_STRING];

    //【可选配置】UID & deviceID: 用于定位线上问题
    [self.curEngine setStringParam:@"use" forKey:SE_PARAMS_KEY_UID_STRING];
    [self.curEngine setStringParam:@"user_phone" forKey:SE_PARAMS_KEY_DEVICE_ID_STRING];
    
    //【必需配置】配置音频来源
    [self.curEngine setStringParam:[self getRecorderType] forKey:SE_PARAMS_KEY_RECORDER_TYPE_STRING];

//    if ([self.settings getBool:SETTING_ASR_RECORDER_SAVE]) {
//        //【可选配置】录音文件保存路径，如配置，SDK会将录音保存到该路径下，文件格式为 .wav
//        [self.curEngine setStringParam:self.debugPath forKey:SE_PARAMS_KEY_ASR_REC_PATH_STRING];
//    }
    
    // 当音频来源为 RECORDER_TYPE_STREAM 时，如输入音频采样率不等于 16K，需添加如下配置
    if ([[self getRecorderType] isEqualToString:SE_RECORDER_TYPE_STREAM]) {
        if ([self.streamRecorder getSampleRate] != 16000 || [self.streamRecorder getChannel] != 1) {
            // 当音频来源为 RECORDER_TYPE_STREAM 时【必需配置】，否则【无需配置】
            // 启用 SDK 内部的重采样
            [self.curEngine setBoolParam:TRUE forKey:SE_PARAMS_KEY_ENABLE_RESAMPLER_BOOL];
            // 将重采样所需的输入采样率设置为 APP 层输入的音频的实际采样率
            [self.curEngine setIntParam:[self.streamRecorder getSampleRate] forKey:SE_PARAMS_KEY_CUSTOM_SAMPLE_RATE_INT];
            [self.curEngine setIntParam:[self.streamRecorder getChannel] forKey:SE_PARAMS_KEY_CUSTOM_CHANNEL_INT];
        }
    }
    
    //【必需配置】识别服务域名
    [self.curEngine setStringParam:self.mCurAddress forKey:SE_PARAMS_KEY_ASR_ADDRESS_STRING];
    
    //【必需配置】识别服务Uri
    [self.curEngine setStringParam:self.mCurUri forKey:SE_PARAMS_KEY_ASR_URI_STRING];
    
    //【必需配置】鉴权相关：Appid
    [self.curEngine setStringParam:self.mCurAppId forKey:SE_PARAMS_KEY_APP_ID_STRING];
    //【必需配置】鉴权相关：Token
    [self.curEngine setStringParam:self.mCurToken forKey:SE_PARAMS_KEY_APP_TOKEN_STRING];

    //【必需配置】识别服务所用集群
    [self.curEngine setStringParam:self.mCurCluster forKey:SE_PARAMS_KEY_ASR_CLUSTER_STRING];
    
    //【可选配置】在线请求的建连与接收超时，一般不需配置使用默认值即可
    [self.curEngine setIntParam:3000 forKey:SE_PARAMS_KEY_ASR_CONN_TIMEOUT_INT];
    [self.curEngine setIntParam:5000 forKey:SE_PARAMS_KEY_ASR_RECV_TIMEOUT_INT];
    
    //【可选配置】在线请求断连后，重连次数，默认值为0，如果需要开启需要设置大于0的次数
    [self.curEngine setIntParam:0 forKey:SE_PARAMS_KEY_ASR_MAX_RETRY_TIMES_INT];
    
    //【可选配置】音频采样率，默认16000
    [self.curEngine setIntParam:16000 forKey:SE_PARAMS_KEY_SAMPLE_RATE_INT];
    //【可选配置】音频通道数，默认1，可选1或2
    [self.curEngine setIntParam:1 forKey:SE_PARAMS_KEY_CHANNEL_NUM_INT];
    //【可选配置】上传给服务的音频通道数，默认1，可选1或2，一般与SE_PARAMS_KEY_SAMPLE_RATE_INT保持一致即可
    [self.curEngine setIntParam:1 forKey:SE_PARAMS_KEY_UP_CHANNEL_NUM_INT];
}

-(void)configStartAsrParams{
    //【可选配置】是否开启顺滑(DDC)
    [self.curEngine setBoolParam:FALSE forKey:SE_PARAMS_KEY_ASR_ENABLE_DDC_BOOL];
    //【可选配置】是否开启文字转数字(ITN)
    [self.curEngine setBoolParam:FALSE forKey:SE_PARAMS_KEY_ASR_ENABLE_ITN_BOOL];
    //【可选配置】是否开启标点
    [self.curEngine setBoolParam:TRUE forKey:SE_PARAMS_KEY_ASR_SHOW_NLU_PUNC_BOOL];
    //【可选配置】是否隐藏句尾标点
    [self.curEngine setBoolParam:FALSE forKey:SE_PARAMS_KEY_ASR_DISABLE_END_PUNC_BOOL];
    // 【可选配置】直接传递自定义的ASR请求JSON，若使用此参数需自行确保JSON格式正确
    [self.curEngine setStringParam:@"{\"reqid\":\"20a3d61f-1a5d-4874-bf46-4bb65216fa46\",\"sequence\":\"10\",\"params\":\"abc\"}" forKey:SE_PARAMS_KEY_ASR_REQ_PARAMS_STRING];

    //【可选配置】设置识别语种
    [self.curEngine setStringParam:@"en-US" forKey:SE_PARAMS_KEY_ASR_LANGUAGE_STRING];

    //【可选配置】控制识别结果返回的形式，全量返回或增量返回，默认为全量
    [self.curEngine setStringParam:@"full" forKey:SE_PARAMS_KEY_ASR_RESULT_TYPE_STRING];

    //【可选配置】设置VAD头部静音时长，用户多久没说话视为空音频，即静音检测时长
    [self.curEngine setIntParam:0 forKey:SE_PARAMS_KEY_ASR_VAD_START_SILENCE_TIME_INT];
    //【可选配置】设置VAD尾部静音时长，用户说话后停顿多久视为说话结束，即自动判停时长
    [self.curEngine setIntParam:0 forKey:SE_PARAMS_KEY_ASR_VAD_END_SILENCE_TIME_INT];
    //【可选配置】设置VAD模式，用于制定VAD场景，默认为空
    [self.curEngine setStringParam:@"" forKey:SE_PARAMS_KEY_ASR_VAD_MODE_STRING];
    //【可选配置】用户音频输入最大时长，仅一句话识别场景生效，单位毫秒，默认为 150000ms.
    [self.curEngine setIntParam:60000 forKey:SE_PARAMS_KEY_VAD_MAX_SPEECH_DURATION_INT];
    
    //【可选配置】控制是否返回录音音量，在 APP 需要显示音频波形时可以启用
    [self.curEngine setBoolParam:TRUE forKey:SE_PARAMS_KEY_ENABLE_GET_VOLUME_BOOL];
    
    //【可选配置】更新 ASR 热词
    [self setHotWords:@"{\"hotwords\":[{\"word\":\"过秦论\",\"scale\":2.0}]}"];
    
    //【可选配置】设置纠错词表，识别结果会根据设置的纠错词纠正结果，例如："{\"古爱玲\":\"谷爱凌\"}"，当识别结果中出现"古爱玲"时会替换为"谷爱凌"
    [self.curEngine setStringParam:@"{\"素质人\":\"数字人\",}" forKey:SE_PARAMS_KEY_ASR_CORRECT_WORDS_STRING];
    
    NSString* recorderType = [self getRecorderType];
    NSLog(@"录音模式: %@", recorderType);
    
    if ([recorderType isEqualToString:SE_RECORDER_TYPE_STREAM]) {
        if (![self.streamRecorder start]) {
            [self speechEngineNoPermission];
        }
    } else if ([recorderType isEqualToString:SE_RECORDER_TYPE_FILE]) {
        // 使用音频文件识别时，需要设置文件的绝对路径
        NSString* file_path = [NSString stringWithFormat:@"%@/%@", self.debugPath, @"asr_rec_file.pcm"];
        NSLog(@"输入的音频文件路径: %@", file_path);
        // 使用音频文件识别时【必须配置】，否则【无需配置】
        [self.curEngine setStringParam:file_path forKey:SE_PARAMS_KEY_RECORDER_FILE_STRING];
    }
}

- (void)setHotWords:(NSString*) hotWords {
    // 更新 ASR 热词，例如："{\"hotwords\":[{\"word\":\"过秦论\",\"scale\":2.0}]}"
    // scale为float类型参数，其中叠词的范围为[1.0,2.0]，非叠词的范围为[1.0,50.0]，scale值越大，结果中出现热词的概率越大
    [self.curEngine sendDirective:SEDirectiveUpdateAsrHotWords data: hotWords];
}

- (void)initEngine {
    
    NSLog(@"创建引擎");
    if (self.curEngine == nil) {
        self.curEngine = [[SpeechEngine alloc] init];
        if (![self.curEngine createEngineWithDelegate:self]) {
            NSLog(@"引擎创建失败.");
            return;
        }
    }
    
    NSLog(@"SDK 版本号: %@", [self.curEngine getVersion]);
    
    self.debugPath = NSSearchPathForDirectoriesInDomains(NSDocumentDirectory, NSUserDomainMask, YES).firstObject;
    NSLog(@"当前调试路径: %@", self.debugPath);
    
    NSLog(@"配置初始化参数");
    [self configInitParams];

    
    NSLog(@"引擎初始化");
    SEEngineErrorCode ret = [self.curEngine initEngine];
    if (ret == SENoError) {
        NSLog(@"初始化成功");
        [self speechEngineInitSucceeded];
    } else {
        NSLog(@"初始化失败，返回值: %d", ret);
        [self speechEngineInitFailed:ret];
    }
}

- (void)stopEngine {
    NSLog(@"关闭引擎");
    NSLog(@"Directive: SEDirectiveStopEngine");
    [self.curEngine sendDirective:SEDirectiveStopEngine];
}

- (void)unInitEngine {
    if (self.curEngine != nil) {
        NSLog(@"引擎析构");
        [self.curEngine destroyEngine];
        self.curEngine = nil;
        NSLog(@"引擎析构完成");
    }
}


- (void)speechEngineInitSucceeded {
    [self.streamRecorder setSpeechEngine:@"ASR" engine:self.curEngine];
}

- (void)speechEngineInitFailed:(int)initStatus {
    dispatch_async(dispatch_get_main_queue(), ^{
        [self unInitEngine];
    });
}

- (void)speechEngineNoPermission {
    dispatch_async(dispatch_get_main_queue(), ^{
        [self unInitEngine];
    });
}


- (void)startRecord:(int)isHold {
    
    self.talkingFinisheTimestamp = 0;
    
    NSLog(@"配置启动参数");
    [self configStartAsrParams];

    //【可选配置】是否启用云端自动判停，仅一句话识别场景生效
    NSLog(@"开启 ASR 云端自动判停 %d",isHold);
    BOOL temp = FALSE;
    if(isHold == 0){
        temp = FALSE;
    }else{
        temp = TRUE;
    }
    [self.curEngine setBoolParam:temp forKey:SE_PARAMS_KEY_ASR_AUTO_STOP_BOOL];

    // Directive：启动引擎前调用SYNC_STOP指令，保证前一次请求结束。
    NSLog(@"Directive: SEDirectiveSyncStopEngine");
    SEEngineErrorCode ret = [self.curEngine sendDirective:SEDirectiveSyncStopEngine];
    if (ret != SENoError) {
        NSLog(@"Send directive syncstop failed: %d", ret);
    } else {
        NSLog(@"启用引擎.");
        NSLog(@"Directive: SEDirectiveStartEngine");
        SEEngineErrorCode ret = [self.curEngine sendDirective:SEDirectiveStartEngine];
        if (ret == SERecCheckEnvironmentFailed) {
            [self speechEngineNoPermission];
        }
    }
}
- (void)stopRecord {
    NSLog(@"完成说话");
    NSLog(@"Directive: SEDirectiveFinishTalking");
    [self.curEngine sendDirective:SEDirectiveFinishTalking];
    if ([[self getRecorderType] isEqualToString:SE_RECORDER_TYPE_STREAM]) {
        [self.streamRecorder stop];
    }
}


-(void)sendAsrDelegateMessage:(NSString *)message{
    if([self.asrDelegate respondsToSelector:@selector(handleAsrResult:)]){
        [self.asrDelegate handleAsrResult:message];
    }
}

-(void)sendVolumeDelegateMessage:(NSString *)message{
    if([self.volumeDelegate respondsToSelector:@selector(handleVolumeResult:)]){
        [self.volumeDelegate handleVolumeResult:message];
    }
}

#pragma mark - Message Callback
- (void)onMessageWithType:(SEMessageType)type andData:(NSData *)data {
    NSLog(@"Message Type: %d.", type);
    switch (type) {
        case SEEngineStart:
            // Callback: 引擎启动成功回调
            NSLog(@"Callback: 引擎启动成功");
            [self speechEngineStarted];
            break;
        case SEEngineStop:
            // Callback: 引擎关闭回调
            NSLog(@"Callback: 引擎关闭");
            [self speechEngineStopped];
            break;
        case SEEngineError:
            // Callback: 错误信息回调
            NSLog(@"Callback: 错误信息: %@", data);
            [self speechEngineError:data];
            break;
        case SEConnectionConnected:
            NSLog(@"Callback: 建连成功");
            break;
        case SEAsrPartialResult:
            // Callback: ASR 当前请求的部分结果回调
//            NSLog(@"Callback: ASR 当前请求的部分结果");
//            [self speechEngineResult:data isFinal:FALSE];
            break;
        case SEFinalResult:
            // Callback: ASR 当前请求最终结果回调
            NSLog(@"Callback: ASR 当前请求最终结果");
            [self speechEngineResult:data isFinal:TRUE];
            break;
        case SEVolumeLevel:
            // Callback: 录音音量回调
            NSLog(@"Callback: 录音音量：%@",data);
            [self volumeChange:data];
            break;
        default:
            break;
    }
}

-(void)volumeChange:(NSData *)data{
    NSString *volume = [[NSString alloc] initWithData:data encoding:NSUTF8StringEncoding];
    dispatch_async(dispatch_get_main_queue(), ^{
        [self sendVolumeDelegateMessage:volume];
    });
}

- (void)speechEngineStarted {
    dispatch_async(dispatch_get_main_queue(), ^{
        self.engineStarted = true;
    });
}

- (void)speechEngineStopped {
    dispatch_async(dispatch_get_main_queue(), ^{
        if ([[self getRecorderType] isEqualToString:SE_RECORDER_TYPE_STREAM]) {
            [self.streamRecorder stop];
        }
        self.engineStarted = FALSE;
    });
}

- (void)speechEngineResult:(NSData *)data isFinal:(BOOL)isFinal {
    dispatch_async(dispatch_get_main_queue(), ^{
        // 计算由录音结束到 ASR 最终结果之间的延迟
        long response_delay = 0;
        if (isFinal && self.talkingFinisheTimestamp > 0) {
            response_delay = [self timeDelayFrom:self.talkingFinisheTimestamp];
            self.talkingFinisheTimestamp = 0;
        }
        
        // 从回调的 json 数据中解析 ASR 结果
        NSError *error;
        NSDictionary *jsonResult = [NSJSONSerialization JSONObjectWithData:data
                                                                   options:NSJSONReadingMutableContainers
                                                                     error:&error];
        if (![jsonResult objectForKey:@"result"]) {
            [self sendAsrDelegateMessage:@""];
            return;
        }
        
        // 在 UI 显示 ASR 结果和延迟信息
        NSString *result = [[[jsonResult objectForKey:@"result"] firstObject] objectForKey:@"text"];
        if (result.length == 0) {
            [self sendAsrDelegateMessage:@""];
            return;
        }
//        NSMutableString *text = [[NSMutableString alloc] initWithString:@""];
//        [text appendFormat:@"result: %@", result];
//        if (isFinal) {
//            [text appendFormat:@"\nreqid: %@", [jsonResult objectForKey:@"reqid"]];
//            [text appendFormat:@"\nresponse_delay: %ld", response_delay];
//        }
        [self sendAsrDelegateMessage:result];
    });
}

- (void)speechEngineError:(NSData *)data {
    dispatch_async(dispatch_get_main_queue(), ^{
        // 从回调的 json 数据中解析错误码和错误详细信息
        id error_json = [NSJSONSerialization JSONObjectWithData:data options:NSJSONReadingMutableContainers error:nil];
        if ([error_json isKindOfClass:[NSDictionary class]]) {
            //
            int error_code = [[error_json objectForKey:@"err_code"] intValue];
            NSString *error_msg = [error_json objectForKey:@"err_msg"];
            
            if(error_code == 1012 && [error_msg  isEqual: @"No valid data found in input audio"]){
                [self sendAsrDelegateMessage:@""];
            }
            
            if(error_code == 1013 && [error_msg  isEqual: @"No valid speeches found in input audio"]){
                [self sendAsrDelegateMessage:@""];
            }
            
        }
    });
}

#pragma mark - Helper

- (NSString *)getRecorderType {
    switch (0) {
        case 0:
            return SE_RECORDER_TYPE_RECORDER;
        case 1:
            return SE_RECORDER_TYPE_FILE;
        case 2:
            return SE_RECORDER_TYPE_STREAM;
        default:
            break;
    }
    return @"";
}


- (long)timeDelayFrom:(long)pastTimestamp {
    return [[NSDate date] timeIntervalSince1970] * 1000 - pastTimestamp;
}

#pragma mark - UITextViewDelegate

- (BOOL)textView:(UITextView *)textView shouldChangeTextInRange:(NSRange)range replacementText:(NSString *)text {
    if([text isEqualToString:@"\n"]) {
        [textView resignFirstResponder];
        return NO;
    }
    return YES;
}


@end
