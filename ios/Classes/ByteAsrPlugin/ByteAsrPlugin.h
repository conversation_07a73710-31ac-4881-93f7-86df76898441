//
//  ByteAsrPlugin.h
//  Runner
//
//  Created by bodk on 2024/9/2.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@protocol ByteAsrPluginDelegate <NSObject>

@required
//协议方法
- (void)handleAsrResult:(NSString *)result;
@end

@protocol ByteVolumePluginDelegate <NSObject>

@required
- (void)handleVolumeResult:(NSString *)result;
@end


@interface ByteAsrPlugin : NSObject

//声明代理
@property (nonatomic,weak) id<ByteAsrPluginDelegate> asrDelegate;
@property (nonatomic,weak) id<ByteVolumePluginDelegate> volumeDelegate;


+(instancetype)sharedInstance:(NSString *)appId token:(NSString *)token cluster:(NSString *)cluster;

-(void)initEngine;
-(void)stopEngine;
-(void)unInitEngine;
-(void)startRecord:(int)isHold;
-(void)stopRecord;

@end

NS_ASSUME_NONNULL_END
