//
//  XhsUtil.h
//  Runner
//
//  Created by songguo on 2025/6/13.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN


@protocol XhsUtilDelegate <NSObject>

@required

//协议方法
- (void)handleXhsUtilResult:(NSString *)result;

@end

@interface XhsUtil : NSObject

//声明代理
@property (nonatomic,weak) id<XhsUtilDelegate> xhsUtilDelegate;

/**
 * 单例构造方法
 * @return 共享实例
 */
+ (instancetype)sharedInstance;

- (BOOL)registerXhs:(NSString *)appKey;

- (BOOL)isSupportShareNote;

- (void)shareNote:(NSString *)title withContent:(NSString *)content withImage:(NSString *)image;

@end

NS_ASSUME_NONNULL_END
