//
//  XhsStream.m
//  Runner
//
//  Created by songguo on 2025/6/30.
//

#import "XhsStream.h"

@implementation XhsStream

+ (instancetype)sharedInstance {
    static dispatch_once_t onceToken;
    static XhsStream *manager = nil;
    dispatch_once(&onceToken, ^{
        manager = [[XhsStream alloc] init];
        XhsStreamHanlder *streamHandler = [[XhsStreamHanlder alloc] init];
        manager.xhsStreamHandler = streamHandler;
    });
    
    return manager;
}

@end

@implementation XhsStreamHanlder

- (FlutterError*)onListenWithArguments:(id)arguments eventSink:(FlutterEventSink)eventSink {
    self.xhsEventSink = eventSink;
    return nil;
}

- (FlutterError*)onCancelWithArguments:(id)arguments {
    self.xhsEventSink = nil;
    return nil;
}

@end
