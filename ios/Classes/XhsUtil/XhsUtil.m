//
//  XhsUtil.m
//  Runner
//
//  Created by songguo on 2025/6/13.
//

#import "XhsUtil.h"
#import <XiaoHongShuOpenSDK/XHSApi.h>
#import <Photos/Photos.h>
#import <UIKit/UIKit.h>

@interface XhsUtil()<XHSApiDelegate,XHSSDKLogDelegate>

@end

@implementation XhsUtil

+(instancetype)sharedInstance{
    static dispatch_once_t onceToken;
    static XhsUtil *manager = nil;
    dispatch_once(&onceToken, ^{
        manager = [[XhsUtil alloc] init];
    });
    return manager;
}

- (BOOL)registerXhs:(NSString *)appKey {
   
    BOOL flag = [XHSApi registerApp:appKey universalLink:@"" delegate:self];
    NSLog(@"小红书SDK初始化：%d", flag);
    [XHSApi startLogByLevel:XHSOpenSDKLogLevelNormal logDelegate:self];
    return  flag;
}

- (BOOL)isSupportShareNote{
    return [XHSApi isXHSAppInstalled];
}

-(void)shareNote:(NSString *)title withContent:(NSString *)content withImage:(NSString *)image{
    NSLog(@"图片路径：%@",image);
    [self saveImageAtPathToAlbumAndGetImageID:image completion:^(NSString *imageID) {
        if(imageID){
            NSMutableArray<XHSShareInfoImageItem *> *imageResources = [NSMutableArray array];
            XHSShareInfoImageItem *imageObject = [[XHSShareInfoImageItem alloc] init];
            imageObject.imageID = imageID;
            [imageResources addObject:imageObject];
            
            XHSOpenSDKShareRequest *shareRequest = [[XHSOpenSDKShareRequest alloc] init];
            shareRequest.mediaType = XHSOpenSDKShareMediaTypeImage;
            shareRequest.imageInfoItems = imageResources;
            [XHSApi sendRequest:shareRequest completion:^(BOOL success) {
                NSLog(@"小红书分享结果：%d",success);
             }];
        }
    }];
    
}

-(void)sendDelegateMessage:(NSString *)message{
    if([self.xhsUtilDelegate respondsToSelector:@selector(handleXhsUtilResult:)]){
        [self.xhsUtilDelegate handleXhsUtilResult:message];
    }
}

#pragma mark - XHSApiDelegate
/// 发送一个request后，收到小红书终端的回应
/// @param response 具体的回应内容，回应类型详见XHSApiObject.h
- (void)XHSApiDidReceiveResponse:(__kindof XHSOpenSDKBaseResponse *)response {
    // 在这里处理回应的信息：
    NSLog(@"XHSOpenSDKDemo---Response: %ld == %@", response.errorCode,response.errorString);
    if(response.errorCode != 0){
        NSString *result = [NSString stringWithFormat:@"{\"error\":\"%@\"}",response.errorString];
        [self sendDelegateMessage:result];
    }
}

#pragma mark - XHSSDKLogDelegate
- (void)onLog:(NSString*)log logLevel:(XHSOpenSDKLogLevel)level {
    NSLog(@"XHSOpenSDKDemo---Log: %@", log);
}


- (void)saveImageAtPathToAlbumAndGetImageID:(NSString *)filePath completion:(void (^)(NSString *imageID))completion {
    // 去掉 file:// 前缀（如果有）
    if ([filePath hasPrefix:@"file://"]) {
        filePath = [filePath substringFromIndex:7];
    }

    UIImage *image = [UIImage imageWithContentsOfFile:filePath];
    if (!image) {
        NSLog(@"❌ 无法从路径加载图片：%@", filePath);
        if (completion) completion(nil);
        return;
    }

    // 保存图片到系统相册
    __block NSString *localIdentifier = nil;
    [[PHPhotoLibrary sharedPhotoLibrary] performChanges:^{
        PHAssetChangeRequest *req = [PHAssetChangeRequest creationRequestForAssetFromImage:image];
        localIdentifier = req.placeholderForCreatedAsset.localIdentifier;
    } completionHandler:^(BOOL success, NSError * _Nullable error) {
        if (success && localIdentifier) {
            NSLog(@"✅ 图片保存成功，imageID = %@", localIdentifier);
            if (completion) completion(localIdentifier);
        } else {
            NSLog(@"❌ 图片保存失败: %@", error);
            if (completion) completion(nil);
        }
    }];
}

@end
