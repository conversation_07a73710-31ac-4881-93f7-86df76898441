//
//  XhsStream.h
//  Runner
//
//  Created by songguo on 2025/6/30.
//

#import <Foundation/Foundation.h>
#import <Flutter/Flutter.h>

NS_ASSUME_NONNULL_BEGIN
@class XhsStreamHanlder;

@interface XhsStream : NSObject
+ (instancetype)sharedInstance ;

@property (nonatomic, strong) XhsStreamHanlder* xhsStreamHandler;

@end

@interface XhsStreamHanlder : NSObject<FlutterStreamHandler>
@property (nonatomic, strong, nullable) FlutterEventSink xhsEventSink;
@end


NS_ASSUME_NONNULL_END
